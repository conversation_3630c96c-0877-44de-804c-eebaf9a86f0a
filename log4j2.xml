<?xml version="1.0" encoding="UTF-8"?>
<!-- 禁止log4j2输出日志 -->
<configuration status="off" monitorInterval="30">
    <loggers>
        <!-- 日志根节点 -->
        <root level="info">
            <appender-ref ref="console"/>
        </root>

        <logger name="sparrow">
            <appender-ref ref="info" level="info"/>
            <appender-ref ref="error" level="error"/>
        </logger>
    </loggers>

    <appenders>
        <console name="console" target="SYSTEM_OUT">
            <Filters>
                <!-- 输出debug+级别的日志 -->
                <ThresholdFilter level="debug"/>
            </Filters>
            <!-- 输出日志模式（时间 日志级别 类 消息） -->
            <PatternLayout pattern="[%d{yyyy-MM-dd HH:mm:ss}] [%p] [%c] %m%n"/>
        </console>

        <JDBC name="info" tableName="set_infologs">
            <ConnectionFactory class="com.chinamobile.sparrow.domain.infra.log.Log4j2Configurer" method="getDatabaseConnection"/>
            <Column name="level" pattern="%level"/>
            <Column name="transactionId" pattern="%X{id}"/>
            <Column name="stack" pattern="%X{stack}"/>
            <Column name="method" pattern="%message"/>
            <Column name="responseCode" pattern="%X{responseCode}"/>
            <Column name="request" pattern="%X{request}"/>
            <Column name="response" pattern="%X{response}"/>
            <Column name="ip" pattern="%X{ip}"/>
            <Column name="actorId" pattern="%X{actorId}"/>
            <Column name="realmType" pattern="%X{realmType}"/>
            <Column name="startTime" pattern="%X{startTime}"/>
            <Column name="endTime" pattern="%X{endTime}"/>
        </JDBC>

        <JDBC name="error" tableName="set_errorlogs">
            <ConnectionFactory class="com.chinamobile.sparrow.domain.infra.log.Log4j2Configurer" method="getDatabaseConnection"/>
            <Column name="level" pattern="%level"/>
            <Column name="transactionId" pattern="%X{id}"/>
            <Column name="method" pattern="%message"/>
            <Column name="stack" pattern="%X{stack}"/>
            <Column name="request" pattern="%X{request}"/>
            <Column name="response" pattern="%X{response}"/>
            <Column name="exception" pattern="%ex{full}"/>
            <Column name="ip" pattern="%X{ip}"/>
            <Column name="actorId" pattern="%X{actorId}"/>
            <Column name="realmType" pattern="%X{realmType}"/>
            <Column name="startTime" pattern="%X{startTime}"/>
            <Column name="endTime" pattern="%X{endTime}"/>
        </JDBC>
    </appenders>
</configuration>