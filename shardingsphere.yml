databaseName: health_code_dev

dataSources:
  main:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
#    jdbcUrl: jdbc:mysql://**************:3306/health_code_dev?allowMultiQueries=true&characterEncoding=UTF-8&serverTimezone=GMT%2B8&useUnicode=TRUE&zeroDateTimeBehavior=CONVERT_TO_NULL
#    username: gmccai
#    password: LPDY!iLrUd8irpGpz
    jdbcUrl: *****************************************************************************************************************************************************************
    username: health_code_user
    password: pLu9i2zq1r&98K1K
    maxPoolSize: 2

rules:
- !SHARDING
  tables:
    sys_info_logs:
      actualDataNodes: main.sys_info_logs
      tableStrategy:
        standard:
          shardingColumn: startTime
          shardingAlgorithmName: shardingLogsByMonths
    sys_error_logs:
      actualDataNodes: main.sys_error_logs
      tableStrategy:
        standard:
          shardingColumn: startTime
          shardingAlgorithmName: shardingLogsByMonths
  shardingAlgorithms:
    shardingLogsByMonths:
      type: SHARDING_LOGS_BY_MONTHS

props:
  sql-show: true
  check-table-metadata-enabled: true