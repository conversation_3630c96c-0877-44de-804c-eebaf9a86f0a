# 事件驱动批量操作日志解决方案

## 问题描述

在使用AOP切面记录数据操作日志时，批量导入数据无法触发新增或删除的操作日志记录。这是因为：

1. **AOP切面限制**：AOP只能拦截单个方法的调用，无法感知批量操作中的每个实体
2. **事务边界**：批量操作通常在一个事务中执行，AOP切面无法区分单个操作
3. **性能考虑**：为每个实体单独记录日志会影响批量操作的性能

## 解决方案：事件驱动方式

采用Spring事件驱动机制，通过发布和监听事件来异步记录批量操作日志，具有以下优势：

- **解耦性**：日志记录与业务逻辑完全分离
- **异步处理**：不影响主业务流程性能
- **灵活性**：可以精确控制日志记录的内容和时机
- **可扩展性**：易于添加新的日志处理逻辑

## 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   批量操作      │    │   事件发布器      │    │   事件监听器      │
│   (Service)     │───▶│   EventPublisher │───▶│   EventListener  │
│                 │    │                  │    │                  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   事件总线        │    │   日志存储      │
                       │   EventBus       │    │   Repository    │
                       └──────────────────┘    └─────────────────┘
```

## 核心组件

### 1. 数据操作日志事件 (DataOperationLogEvent)

```java
public class DataOperationLogEvent extends ApplicationEvent {
    private final DataOperationLogEntity logEntity;
    
    public DataOperationLogEvent(Object source, DataOperationLogEntity logEntity) {
        super(source);
        this.logEntity = logEntity;
    }
    
    public DataOperationLogEntity getLogEntity() {
        return logEntity;
    }
}
```

### 2. 事件发布器 (DataOperationLogEventPublisher)

```java
@Component
public class DataOperationLogEventPublisher {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 发布单个操作日志事件
     */
    public void publishLogEvent(Object source, DataOperationLogEntity logEntity) {
        DataOperationLogEvent event = new DataOperationLogEvent(source, logEntity);
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 发布批量操作日志事件
     */
    public void publishBatchLogEvent(Object source, DataOperationLogEntity batchLogEntity) {
        DataOperationLogEvent event = new DataOperationLogEvent(source, batchLogEntity);
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 发布错误操作日志事件
     */
    public void publishErrorLogEvent(Object source, DataOperationLogEntity errorLogEntity) {
        DataOperationLogEvent event = new DataOperationLogEvent(source, errorLogEntity);
        eventPublisher.publishEvent(event);
    }
}
```

### 3. 事件监听器 (DataOperationLogEventListener)

```java
@Component
public class DataOperationLogEventListener {
    
    @Autowired
    private DataOperationLogRepository dataOperationLogRepository;
    
    /**
     * 处理数据操作日志事件
     */
    @EventListener
    @Async
    public void handleDataOperationLogEvent(DataOperationLogEvent event) {
        try {
            DataOperationLogEntity logEntity = event.getLogEntity();
            dataOperationLogRepository.saveLogAsync(logEntity);
        } catch (Exception e) {
            // 记录日志保存失败
            logger.error("保存操作日志失败: {}", e.getMessage(), e);
        }
    }
}
```

### 4. 批量操作日志工具类 (BatchOperationLogUtil)

```java
public class BatchOperationLogUtil {
    
    /**
     * 创建批量操作日志实体
     */
    public static DataOperationLogEntity createBatchLog(DataOperationLog.OperationType operationType,
                                                       String description,
                                                       String entityName,
                                                       User user,
                                                       int totalCount,
                                                       int successCount,
                                                       int failedCount,
                                                       List<String> entityIds) {
        // 构建批量操作日志实体
    }
    
    /**
     * 创建单个操作日志实体
     */
    public static DataOperationLogEntity createSingleLog(DataOperationLog.OperationType operationType,
                                                        String description,
                                                        String entityName,
                                                        String entityId,
                                                        Object beforeData,
                                                        Object afterData,
                                                        User user,
                                                        boolean success,
                                                        String errorMessage) {
        // 构建单个操作日志实体
    }
    
    /**
     * 创建错误操作日志实体
     */
    public static DataOperationLogEntity createErrorLog(DataOperationLog.OperationType operationType,
                                                       String description,
                                                       String entityName,
                                                       String entityId,
                                                       Object entity,
                                                       User user,
                                                       Exception e) {
        // 构建错误操作日志实体
    }
}
```

## 使用方法

### 1. 批量导入数据

```java
@Service
public class TaskService {
    
    @Autowired
    private DataOperationLogEventPublisher logEventPublisher;
    
    /**
     * 批量导入任务
     */
    @Transactional
    public Result<BatchImportResult> batchImportTasks(List<Task> tasks, User user) {
        Result<BatchImportResult> result = new Result<>();
        BatchImportResult batchResult = new BatchImportResult();
        
        List<String> successIds = new ArrayList<>();
        List<String> failedIds = new ArrayList<>();
        
        // 记录批量操作开始日志
        DataOperationLogEntity batchLog = BatchOperationLogUtil.createBatchLog(
            DataOperationLog.OperationType.CREATE,
            "批量导入任务",
            "Task",
            user,
            tasks.size(),
            0,
            0,
            new ArrayList<>()
        );
        
        for (Task task : tasks) {
            try {
                // 调用实际的保存逻辑
                Result<String> saveResult = taskRepository.save(task, user);
                if (saveResult.isOK()) {
                    successIds.add(saveResult.data);
                    
                    // 记录单个操作日志
                    DataOperationLogEntity singleLog = BatchOperationLogUtil.createSingleLog(
                        DataOperationLog.OperationType.CREATE,
                        "导入单个任务",
                        "Task",
                        task.getId(),
                        null,
                        task,
                        user,
                        true,
                        null
                    );
                    logEventPublisher.publishLogEvent(this, singleLog);
                } else {
                    failedIds.add(task.getId());
                }
            } catch (Exception e) {
                failedIds.add(task.getId());
                
                // 记录错误日志
                DataOperationLogEntity errorLog = BatchOperationLogUtil.createErrorLog(
                    DataOperationLog.OperationType.CREATE,
                    "导入任务失败",
                    "Task",
                    task.getId(),
                    task,
                    user,
                    e
                );
                logEventPublisher.publishErrorLogEvent(this, errorLog);
            }
        }
        
        // 更新批量操作日志结果
        batchLog.setSuccessCount(successIds.size());
        batchLog.setFailedCount(failedIds.size());
        batchLog.setSuccess(failedIds.isEmpty());
        
        // 发布批量操作日志事件
        logEventPublisher.publishBatchLogEvent(this, batchLog);
        
        // 设置返回结果
        batchResult.setSuccessCount(successIds.size());
        batchResult.setFailedCount(failedIds.size());
        batchResult.setSuccessIds(successIds);
        batchResult.setFailedIds(failedIds);
        
        result.data = batchResult;
        return result;
    }
}
```

### 2. 批量删除数据

```java
/**
 * 批量删除任务
 */
@Transactional
public Result<BatchDeleteResult> batchDeleteTasks(List<String> taskIds, User user) {
    Result<BatchDeleteResult> result = new Result<>();
    BatchDeleteResult batchResult = new BatchDeleteResult();
    
    List<String> successIds = new ArrayList<>();
    List<String> failedIds = new ArrayList<>();
    List<Task> deletedTasks = new ArrayList<>();
    
    // 记录批量操作开始日志
    DataOperationLogEntity batchLog = BatchOperationLogUtil.createBatchLog(
        DataOperationLog.OperationType.DELETE,
        "批量删除任务",
        "Task",
        user,
        taskIds.size(),
        0,
        0,
        taskIds
    );
    
    for (String taskId : taskIds) {
        try {
            // 获取删除前的数据
            Task task = taskRepository.findById(taskId);
            if (task != null) {
                deletedTasks.add(task);
                
                // 调用实际的删除逻辑
                Result deleteResult = taskRepository.remove(taskId, user);
                if (deleteResult.isOK()) {
                    successIds.add(taskId);
                    
                    // 记录单个删除日志
                    DataOperationLogEntity singleLog = BatchOperationLogUtil.createSingleLog(
                        DataOperationLog.OperationType.DELETE,
                        "批量删除单个任务",
                        "Task",
                        taskId,
                        task, // 删除前数据
                        null,
                        user,
                        true,
                        null
                    );
                    logEventPublisher.publishLogEvent(this, singleLog);
                } else {
                    failedIds.add(taskId);
                }
            } else {
                failedIds.add(taskId);
            }
        } catch (Exception e) {
            failedIds.add(taskId);
            
            // 记录错误日志
            DataOperationLogEntity errorLog = BatchOperationLogUtil.createErrorLog(
                DataOperationLog.OperationType.DELETE,
                "批量删除失败",
                "Task",
                taskId,
                null,
                user,
                e
            );
            logEventPublisher.publishErrorLogEvent(this, errorLog);
        }
    }
    
    // 更新批量操作日志结果
    batchLog.setSuccessCount(successIds.size());
    batchLog.setFailedCount(failedIds.size());
    batchLog.setSuccess(failedIds.isEmpty());
    
    // 发布批量操作日志事件
    logEventPublisher.publishBatchLogEvent(this, batchLog);
    
    // 设置返回结果
    batchResult.setSuccessCount(successIds.size());
    batchResult.setFailedCount(failedIds.size());
    batchResult.setSuccessIds(successIds);
    batchResult.setFailedIds(failedIds);
    batchResult.setDeletedTasks(deletedTasks);
    
    result.data = batchResult;
    return result;
}
```

## 日志记录内容

### 1. 批量操作日志

- **操作类型**：CREATE、UPDATE、DELETE
- **操作描述**：如"批量导入任务"
- **实体名称**：如"Task"
- **总数**：批量操作的总数量
- **成功数量**：成功操作的数量
- **失败数量**：失败操作的数量
- **实体ID列表**：所有操作的实体ID
- **批量操作标识**：标记为批量操作

### 2. 单个操作日志

- **操作类型**：CREATE、UPDATE、DELETE
- **操作描述**：如"导入单个任务"
- **实体名称**：如"Task"
- **实体ID**：具体实体的ID
- **操作前后数据**：数据变更的详细信息
- **成功状态**：操作是否成功
- **错误信息**：失败时的错误详情

### 3. 错误操作日志

- **操作类型**：CREATE、UPDATE、DELETE
- **操作描述**：如"导入任务失败"
- **实体名称**：如"Task"
- **实体ID**：具体实体的ID
- **错误信息**：详细的异常信息
- **操作前数据**：失败前的数据状态

## 配置要求

### 1. 启用异步支持

在启动类中添加`@EnableAsync`注解：

```java
@SpringBootApplication
@EnableAsync
public class HealthCodeApplication {
    public static void main(String[] args) {
        SpringApplication.run(HealthCodeApplication.class, args);
    }
}
```

### 2. 配置异步线程池

```java
@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {
    
    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("LogAsync-");
        executor.initialize();
        return executor;
    }
    
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new SimpleAsyncUncaughtExceptionHandler();
    }
}
```

## 优势总结

### 1. 性能优势

- **异步处理**：日志记录不阻塞主业务流程
- **批量优化**：减少数据库连接和事务开销
- **内存友好**：避免大量日志对象占用内存

### 2. 功能优势

- **完整性**：记录批量操作的整体信息和每个实体的详细操作
- **可追溯性**：支持按批量操作ID或单个实体ID查询日志
- **错误处理**：详细记录每个失败操作的错误信息

### 3. 维护优势

- **代码清晰**：业务逻辑与日志记录逻辑分离
- **易于扩展**：可以轻松添加新的日志处理逻辑
- **配置灵活**：支持动态配置日志记录策略

## 注意事项

### 1. 事务管理

- 批量操作日志记录在事务提交后进行
- 确保日志记录失败不影响主业务流程
- 考虑使用消息队列保证日志记录的可靠性

### 2. 性能监控

- 监控异步日志处理的性能
- 设置合理的线程池大小
- 定期清理过期的日志数据

### 3. 存储优化

- 为日志表添加合适的索引
- 考虑日志数据的分表策略
- 实现日志数据的归档和清理机制

## 总结

事件驱动方式解决了批量导入数据时无法触发操作日志记录的问题，通过异步处理保证了性能，通过详细记录提供了完整的审计追踪能力。这种方式既保持了代码的清晰性，又提供了强大的日志记录功能，是解决批量操作日志记录问题的最佳方案。
