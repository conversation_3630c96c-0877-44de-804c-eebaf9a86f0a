# 数据操作日志系统使用说明

## 概述

本系统基于Spring AOP切面技术实现，通过注解方式自动记录数据操作日志，支持数据的新增、修改（修改前和修改后）、删除、查询等操作的日志记录。

## 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   @DataOperationLog  │    │   AOP切面      │    │   日志存储      │
│   注解标记      │───▶│   DataOperation  │───▶│   DataOperation │
│                 │    │   LogAspect      │    │   LogEntity     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   日志服务       │
                       │ DataOperation    │
                       │ LogService       │
                       └──────────────────┘
```

## 核心组件

### 1. 数据操作日志注解 (@DataOperationLog)

用于标记需要记录日志的方法，支持以下配置：

- `type`: 操作类型（CREATE、UPDATE、DELETE、QUERY）
- `description`: 操作描述
- `entityName`: 实体类名称
- `entityIdParam`: 实体ID参数名
- `logParams`: 是否记录请求参数
- `logResult`: 是否记录响应结果

### 2. 日志实体类 (DataOperationLogEntity)

存储操作日志的详细信息，包括：

- 操作类型、描述、实体信息
- 操作前后数据（JSON格式）
- 请求参数、响应结果
- 操作人信息、时间、IP等
- 执行时长、成功状态、错误信息

### 3. AOP切面 (DataOperationLogAspect)

自动拦截带有@DataOperationLog注解的方法，记录：

- 方法执行前后的数据状态
- 执行时长和结果
- 异常信息
- 请求上下文信息

### 4. 日志服务 (DataOperationLogService)

提供日志的保存、查询、删除等基础功能。

## 使用方法

### 1. 在Service方法上添加注解

```java
@Service
public class TaskService {

    @DataOperationLog(
        type = DataOperationLog.OperationType.CREATE,
        description = "新增任务",
        entityName = "Task",
        entityIdParam = "id"
    )
    @Transactional
    public Result<String> createTask(Task task, User user) {
        // 业务逻辑
        return result;
    }

    @DataOperationLog(
        type = DataOperationLog.OperationType.UPDATE,
        description = "更新任务",
        entityName = "Task",
        entityIdParam = "id"
    )
    @Transactional
    public Result<String> updateTask(Task task, User user) {
        // 业务逻辑
        return result;
    }

    @DataOperationLog(
        type = DataOperationLog.OperationType.DELETE,
        description = "删除任务",
        entityName = "Task",
        entityIdParam = "id"
    )
    @Transactional
    public Result<Void> deleteTask(String taskId, User user) {
        // 业务逻辑
        return result;
    }
}
```

### 2. 注解参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| type | OperationType | 是 | 操作类型：CREATE、UPDATE、DELETE、QUERY |
| description | String | 否 | 操作描述，用于日志记录 |
| entityName | String | 否 | 实体类名称，用于分类统计 |
| entityIdParam | String | 否 | 实体ID参数名，默认为"id" |
| logParams | boolean | 否 | 是否记录请求参数，默认true |
| logResult | boolean | 否 | 是否记录响应结果，默认true |

### 3. 操作类型说明

- **CREATE**: 新增操作，记录新增后的数据
- **UPDATE**: 修改操作，记录修改前和修改后的数据
- **DELETE**: 删除操作，记录删除前的数据
- **QUERY**: 查询操作，通常只记录查询参数

## 日志记录内容

### 基本信息
- 操作类型、描述、实体名称、实体ID
- 操作时间、执行时长、成功状态
- 错误信息（如果失败）

### 数据内容
- **新增**: 记录新增后的完整数据
- **修改**: 记录修改前和修改后的数据，便于对比
- **删除**: 记录删除前的完整数据
- **查询**: 记录查询参数

### 上下文信息
- 操作人ID、姓名、部门
- 客户端IP、用户代理、请求URL、请求方法
- 请求参数、响应结果

## 数据库表结构

```sql
CREATE TABLE `data_operation_logs` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型',
  `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `entity_name` varchar(100) DEFAULT NULL COMMENT '实体类名称',
  `entity_id` varchar(36) DEFAULT NULL COMMENT '实体ID',
  `before_data` text COMMENT '操作前数据（JSON格式）',
  `after_data` text COMMENT '操作后数据（JSON格式）',
  `request_params` text COMMENT '请求参数（JSON格式）',
  `response_result` text COMMENT '响应结果（JSON格式）',
  `operator_id` varchar(36) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `operator_dept` varchar(200) DEFAULT NULL COMMENT '操作人部门',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `execution_time` bigint DEFAULT NULL COMMENT '执行时长（毫秒）',
  `success` tinyint(1) DEFAULT NULL COMMENT '是否成功',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `creator_id` varchar(36) DEFAULT NULL COMMENT '创建人ID',
  `updater_id` varchar(36) DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_entity_name` (`entity_name`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据操作日志表';
```

## 配置说明

### 1. 启用AOP支持

项目已经在`HealthCodeApplication`中启用了AOP支持：

```java
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
```

### 2. 日志级别配置

在`logback-spring.xml`中可以配置日志级别：

```xml
<logger name="com.chinamobile.healthcode.aspect" level="INFO"/>
<logger name="com.chinamobile.healthcode.service.DataOperationLogService" level="INFO"/>
```

## 扩展功能

### 1. 异步日志记录

系统支持异步记录日志，避免影响主业务流程性能。

### 2. 日志清理

提供日志清理功能，可以定期清理过期的日志数据。

### 3. 日志查询

提供REST API接口，支持按条件查询和分页查询日志。

## 注意事项

### 1. 性能考虑
- 日志记录采用异步方式，不影响主业务流程
- 大量数据操作时，建议适当控制日志记录的详细程度

### 2. 存储空间
- 日志数据会占用一定的存储空间
- 建议定期清理过期日志，或设置日志保留策略

### 3. 敏感信息
- 注意不要在日志中记录敏感信息（如密码、身份证号等）
- 可以通过配置控制某些字段不记录

### 4. 异常处理
- 日志记录失败不会影响主业务流程
- 系统会记录日志记录失败的错误信息

## 常见问题

### 1. 日志没有记录
- 检查方法是否正确添加了@DataOperationLog注解
- 检查AOP是否正常启用
- 检查数据库连接是否正常

### 2. 日志记录不完整
- 检查注解参数配置是否正确
- 检查实体对象是否有getId方法
- 检查JSON序列化是否正常

### 3. 性能问题
- 检查是否启用了异步日志记录
- 检查日志记录的详细程度是否过高
- 考虑定期清理过期日志

## 总结

本数据操作日志系统提供了完整的数据操作审计功能，通过简单的注解配置即可实现自动化的日志记录。系统具有良好的扩展性和性能，可以满足大多数业务场景的需求。

如有问题或建议，请联系开发团队。 