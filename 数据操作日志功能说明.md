# 数据操作日志功能说明

## 概述

本项目实现了完整的数据操作日志功能，可以自动记录所有的增删改操作，包括新增、修改、删除、导入等操作。该功能不使用AOP切面，而是通过继承Repository基类的方式实现，更加灵活和可控。

## 功能特性

- **自动日志记录**：继承`LoggableRepository`的Repository会自动记录所有数据操作
- **详细操作信息**：记录操作前后的数据、操作人、时间、IP地址等详细信息
- **多种操作类型**：支持CREATE（新增）、UPDATE（修改）、DELETE（删除）、IMPORT（导入）、EXPORT（导出）
- **操作结果跟踪**：记录操作成功或失败的结果，失败时记录错误信息
- **灵活的查询**：支持按实体类型、操作类型、操作人、时间范围等多种条件查询
- **日志清理**：支持清理指定时间之前的日志数据

## 架构设计

### 核心组件

1. **OperationLog**：操作日志实体类
2. **OperationLogRepository**：操作日志数据访问层
3. **OperationLogService**：操作日志业务逻辑层
4. **OperationLogController**：操作日志REST接口
5. **LoggableRepository**：带日志功能的Repository基类

### 继承关系

```
AbstractEntityRepository
    ↓
DefaultAbstractEntityRepository
    ↓
InstabilityBaseRepository
    ↓
LoggableRepository (新增)
    ↓
具体的Repository实现类
```

## 使用方法

### 1. 创建带日志功能的Repository

要让现有的Repository具备日志功能，只需要：

1. 继承`LoggableRepository`而不是原来的基类
2. 实现`getCurrentUser`方法
3. 注入`OperationLogService`

示例：

```java
@Repository
public class LoggableBasicInfoRepository extends LoggableRepository<BasicInfo> {

    private final DefaultUserRepository userRepository;

    public LoggableBasicInfoRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            Validator validator,
            DefaultDepartmentRepository departmentRepository,
            DefaultResultParser resultParser,
            OperationLogService operationLogService,
            DefaultUserRepository userRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, BasicInfo.class, validator, 
              departmentRepository, resultParser, operationLogService);
        this.userRepository = userRepository;
    }

    @Override
    protected User getCurrentUser(String userId) {
        try {
            Result<DefaultUser> userResult = userRepository.getBriefByIdOrAccountOrMp(userId, null);
            if (userResult.isOK()) {
                return userResult.data;
            }
        } catch (Exception e) {
            // 处理异常
        }
        return null;
    }
}
```

### 2. 自动日志记录

继承`LoggableRepository`后，以下方法会自动记录日志：

- `add()` - 新增操作
- `update()` - 修改操作  
- `remove()` - 删除操作
- `saveOrUpdate()` - 保存或更新操作
- `importFromExcel()` - 导入操作

### 3. 手动记录日志

如果需要手动记录日志，可以直接使用`OperationLogService`：

```java
@Autowired
private OperationLogService operationLogService;

// 记录新增日志
operationLogService.logCreate("BasicInfo", "123", entity, user, "新增基础信息");

// 记录修改日志
operationLogService.logUpdate("BasicInfo", "123", beforeEntity, afterEntity, user, "修改基础信息");

// 记录删除日志
operationLogService.logDelete("BasicInfo", "123", entity, user, "删除基础信息");

// 记录失败日志
operationLogService.logFailure("BasicInfo", "123", OperationType.CREATE, user, "新增失败", "数据验证失败");
```

## API接口

### 查询操作日志

**POST** `/api/operation-logs/query`

请求体：
```json
{
    "entityType": "BasicInfo",
    "entityId": "123",
    "operationType": "CREATE",
    "operatorId": "user123",
    "operatorName": "张三",
    "deptId": "dept123",
    "result": "SUCCESS",
    "startTime": "2024-01-01 00:00:00",
    "endTime": "2024-12-31 23:59:59",
    "page": 1,
    "size": 20
}
```

### 根据实体查询日志

**GET** `/api/operation-logs/entity/{entityType}/{entityId}`

### 根据操作人查询日志

**GET** `/api/operation-logs/operator/{operatorId}`

### 根据部门查询日志

**GET** `/api/operation-logs/department/{deptId}`

### 根据时间范围查询日志

**GET** `/api/operation-logs/time-range?startTime=2024-01-01 00:00:00&endTime=2024-12-31 23:59:59`

### 清理日志

**DELETE** `/api/operation-logs/clean?date=2024-01-01 00:00:00`

## 前端页面

提供了完整的操作日志查询页面：`/templates/sys/operation-logs.htm`

功能包括：
- 多条件搜索
- 分页显示
- 日志详情查看
- 日志导出
- 日志清理

## 数据库表结构

操作日志表：`sys_operation_logs`

主要字段：
- `id`: 主键
- `entityType`: 实体类型
- `entityId`: 实体ID
- `operationType`: 操作类型
- `description`: 操作描述
- `beforeData`: 操作前数据（JSON）
- `afterData`: 操作后数据（JSON）
- `operatorId`: 操作人ID
- `operatorName`: 操作人姓名
- `deptId`: 部门ID
- `deptName`: 部门名称
- `operateTime`: 操作时间
- `ipAddress`: IP地址
- `userAgent`: 用户代理
- `result`: 操作结果
- `errorMessage`: 错误信息

## 配置说明

### 1. 数据库配置

确保在`application.yml`中配置了正确的数据库连接信息。

### 2. 依赖注入

确保以下Bean被正确配置：
- `OperationLogService`
- `OperationLogRepository`
- `ObjectMapper`

### 3. 事务配置

日志记录操作使用`@Transactional`注解，确保在事务中执行。

## 注意事项

1. **性能考虑**：日志记录会增加一定的性能开销，建议在生产环境中监控性能影响
2. **存储空间**：操作前后的数据以JSON格式存储，会占用较多存储空间
3. **数据安全**：敏感数据在记录日志前应该进行脱敏处理
4. **日志清理**：建议定期清理过期的日志数据，避免数据库过大
5. **异常处理**：日志记录失败不应影响主要业务流程

## 扩展功能

### 1. 自定义日志字段

可以在`OperationLog`实体类中添加自定义字段，如：
- 业务模块标识
- 操作来源（Web、API、定时任务等）
- 关联的业务ID

### 2. 日志级别

可以添加日志级别字段，区分重要程度：
- INFO：一般操作
- WARNING：警告操作
- ERROR：错误操作

### 3. 日志聚合

可以实现日志聚合功能，将相同操作的多次日志合并显示。

### 4. 实时监控

可以结合WebSocket实现实时日志监控，及时发现问题。

## 故障排除

### 1. 日志记录失败

检查：
- 数据库连接是否正常
- 事务配置是否正确
- 用户信息是否完整

### 2. 性能问题

优化建议：
- 使用异步日志记录
- 批量插入日志
- 定期清理过期日志

### 3. 存储空间不足

解决方案：
- 压缩JSON数据
- 只记录关键字段
- 定期归档日志

## 总结

本数据操作日志功能提供了完整的数据操作追踪能力，通过继承的方式实现，既保持了代码的简洁性，又提供了强大的日志记录功能。该功能可以帮助开发人员和运维人员更好地了解系统的使用情况，快速定位问题，提高系统的可维护性和安全性。
