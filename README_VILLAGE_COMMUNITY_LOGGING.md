# 村社区管理手动日志记录功能使用说明

## 概述

本功能为村社区管理系统提供了手动操作日志记录能力，支持新增、更新等操作的自动日志记录，解决了批量导入数据时无法触发AOP切面日志记录的问题。

## 功能特点

### 1. 自动日志记录
- **新增操作**：自动记录新增的村社区信息
- **更新操作**：自动记录更新前后的数据对比
- **批量操作**：支持批量导入时的逐条日志记录

### 2. 完整的日志信息
- 操作类型（新增/更新）
- 操作描述（如"新增村社区基础信息"）
- 实体信息（实体名称、实体ID）
- 操作前后数据（JSON格式）
- 操作人信息（ID、姓名、部门）
- 请求上下文（IP、URL、方法等）
- 操作时间和执行状态

### 3. 智能实体识别
系统能够自动识别不同类型的村社区实体，并生成相应的中文描述：

| 实体类型 | 中文描述 |
|---------|---------|
| BasicInfo | 基础信息 |
| PersonnelInfo | 人员信息 |
| InstabilityNotification | 通知信息 |
| EventCommitteesDisciplinaryInfo | 事件信息 |
| EventOnlineOpinionInfo | 网络舆情信息 |
| EventOtherInfo | 其他情况信息 |

## 使用方法

### 1. 继承InstabilityBaseRepository

```java
@Repository
public class BasicInfoRepository extends InstabilityBaseRepository<BasicInfo> {
    
    public BasicInfoRepository(EntityManagerFactory entityManagerFactory,
                              JinqJPAStreamProvider jinqJPAStreamProvider,
                              Validator validator,
                              DefaultDepartmentRepository departmentRepository,
                              DefaultResultParser resultParser) {
        super(entityManagerFactory, jinqJPAStreamProvider, BasicInfo.class, 
              validator, departmentRepository, resultParser);
    }
}
```

### 2. 调用父类方法自动记录日志

```java
@Service
public class BasicInfoService {
    
    @Autowired
    private BasicInfoRepository basicInfoRepository;
    
    /**
     * 新增村社区基础信息
     */
    @Transactional
    public Result<String> createBasicInfo(BasicInfo basicInfo, User user) {
        // 调用父类的add方法，会自动记录新增日志
        return basicInfoRepository.add(basicInfo, user.getId());
    }
    
    /**
     * 更新村社区基础信息
     */
    @Transactional
    public Result<String> updateBasicInfo(BasicInfo basicInfo, User user) {
        // 调用父类的update方法，会自动记录更新日志
        return basicInfoRepository.update(basicInfo, user.getId());
    }
}
```

### 3. 批量导入支持

```java
@Transactional
public Result<BatchImportResult> batchImportBasicInfo(List<BasicInfo> basicInfoList, User user) {
    BatchImportResult result = new BatchImportResult();
    List<String> successIds = new ArrayList<>();
    List<String> failedIds = new ArrayList<>();
    
    for (BasicInfo basicInfo : basicInfoList) {
        try {
            if (isDuplicate(basicInfo)) {
                // 更新现有记录，自动记录更新日志
                Result updateResult = update(basicInfo, user.getId());
                if (updateResult.isOK()) {
                    successIds.add(basicInfo.getId());
                } else {
                    failedIds.add(basicInfo.getId());
                }
            } else {
                // 新增记录，自动记录新增日志
                Result addResult = add(basicInfo, user.getId());
                if (addResult.isOK()) {
                    successIds.add(basicInfo.getId());
                } else {
                    failedIds.add(basicInfo.getId());
                }
            }
        } catch (Exception e) {
            LOGGER.error("处理村社区基础信息失败: {}", e.getMessage(), e);
            failedIds.add(basicInfo.getId());
        }
    }
    
    // 设置结果
    result.setSuccessCount(successIds.size());
    result.setFailedCount(failedIds.size());
    result.setSuccessIds(successIds);
    result.setFailedIds(failedIds);
    
    return result;
}
```

## 日志记录内容

### 新增操作日志示例

```json
{
  "operationType": "CREATE",
  "description": "新增村社区基础信息",
  "entityName": "BasicInfo",
  "entityId": "123456789",
  "beforeData": null,
  "afterData": "{\"id\":\"123456789\",\"regionId\":\"region001\",\"area\":100.5,\"registeredPopulation\":1000}",
  "operatorId": "user001",
  "operatorName": "张三",
  "operatorDept": "村社区管理部",
  "operationTime": "2024-01-15T10:30:00",
  "clientIp": "*************",
  "requestUrl": "http://localhost:8080/api/basic-info",
  "requestMethod": "POST",
  "success": true
}
```

### 更新操作日志示例

```json
{
  "operationType": "UPDATE",
  "description": "更新村社区基础信息",
  "entityName": "BasicInfo",
  "entityId": "123456789",
  "beforeData": "{\"id\":\"123456789\",\"regionId\":\"region001\",\"area\":100.5,\"registeredPopulation\":1000}",
  "afterData": "{\"id\":\"123456789\",\"regionId\":\"region001\",\"area\":120.0,\"registeredPopulation\":1200}",
  "operatorId": "user001",
  "operatorName": "张三",
  "operatorDept": "村社区管理部",
  "operationTime": "2024-01-15T11:00:00",
  "clientIp": "*************",
  "requestUrl": "http://localhost:8080/api/basic-info/123456789",
  "requestMethod": "PUT",
  "success": true
}
```

## 配置要求

### 1. 依赖注入

确保项目中已配置`DataOperationLogRepository`：

```java
@Repository
public class DataOperationLogRepository extends AbstractEntityRepository<DataOperationLogEntity> {
    // 实现代码
}
```

### 2. 数据库表

确保`data_operation_logs`表已创建，表结构参考`DataOperationLogEntity`类。

### 3. 事务配置

建议在Service层使用`@Transactional`注解，确保日志记录与业务操作在同一事务中。

## 优势对比

### 手动日志记录 vs AOP切面

| 特性 | 手动日志记录 | AOP切面 |
|------|-------------|---------|
| 批量操作支持 | ✅ 完全支持 | ❌ 无法支持 |
| 性能影响 | 低 | 中等 |
| 灵活性 | 高 | 中等 |
| 维护成本 | 低 | 中等 |
| 异常处理 | 精确控制 | 统一处理 |

## 注意事项

### 1. 异常处理
- 日志记录失败不会影响主业务流程
- 系统会记录日志记录失败的错误信息
- 建议在生产环境中监控日志记录的成功率

### 2. 性能考虑
- 日志记录采用异步方式，避免影响主业务流程
- 大量数据操作时，日志数据量会相应增加
- 建议定期清理过期日志数据

### 3. 数据安全
- 注意不要在日志中记录敏感信息
- 可以通过配置控制某些字段不记录
- 建议对日志数据进行加密存储

## 扩展功能

### 1. 自定义日志描述
可以通过重写`getEntityDisplayName`方法来自定义实体的显示名称：

```java
@Override
protected String getEntityDisplayName(T item) {
    if (item instanceof BasicInfo) {
        return "基础信息";
    } else if (item instanceof PersonnelInfo) {
        return "人员信息";
    }
    return super.getEntityDisplayName(item);
}
```

### 2. 自定义操作人信息获取
可以通过重写`getOperatorName`和`getOperatorDept`方法来集成用户服务：

```java
@Override
protected String getOperatorName(String operatorId) {
    // 调用用户服务获取用户名
    return userService.getUserName(operatorId);
}

@Override
protected String getOperatorDept(String operatorId) {
    // 调用用户服务获取用户部门
    return userService.getUserDept(operatorId);
}
```

## 总结

村社区管理手动日志记录功能提供了：

1. **完整的操作审计**：记录所有新增、更新操作的详细信息
2. **批量操作支持**：解决批量导入时无法记录日志的问题
3. **灵活的配置**：支持自定义日志内容和格式
4. **高性能**：异步记录，不影响主业务流程
5. **易于维护**：继承即可使用，无需额外配置

通过使用此功能，可以确保村社区管理系统的所有数据操作都有完整的审计日志，满足合规性要求，同时提供良好的用户体验。
