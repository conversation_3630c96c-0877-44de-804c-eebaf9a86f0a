<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${amazeui_color_css}"/>
    <script src="${amazeui_color_js}"></script>
    <script src="${component_searchbar_js}"></script>
    <script src="${component_form_js}"></script>
    <script src="${component_pagination_js}"></script>
    <script src="${component_table_js}"></script>
    <style>
        .color-steps-item {
            width: 25%;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/truck/ticket.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body class="am-padding-sm">
<div id="app" class="am-form am-form-horizontal" v-cloak>
    <fieldset disabled>
        <legend>行程信息</legend>
        <div v-for="(val, key) in form" v-if="val.hasOwnProperty('title') && val.group === '行程信息'"
             class="am-form-group">
            <label v-text="val.title" class="am-u-sm-2 am-form-label"></label>
            <div class="am-u-sm-10">
                <input :value="val.value" type="text"/>
            </div>
        </div>
    </fieldset>
    <fieldset disabled>
        <legend>司机信息</legend>
        <div v-for="(val, key) in form" v-if="val.hasOwnProperty('title') && val.group === '司机信息'"
             class="am-form-group">
            <label v-text="val.title" class="am-u-sm-2 am-form-label"></label>
            <div class="am-u-sm-10">
                <input :value="val.value" type="text"/>
            </div>
        </div>
    </fieldset>
    <fieldset disabled>
        <legend>乘客信息</legend>
        <table class="am-table am-table-centered">
            <thead>
            <tr>
                <th>姓名</th>
                <th>身份证号码</th>
                <th>手机号码</th>
                <th>健康码</th>
                <th>核酸阴性</th>
            </tr>
            </thead>
            <tbody>
            <tr v-if="form.passengerList.length === 0">
                <td colspan="5">暂无</td>
            </tr>
            <tr v-else v-for="i in form.passengerList">
                <td v-text="i.name"></td>
                <td v-text="i.identityCardNumber"></td>
                <td v-text="i.mobileNumber"></td>
                <td v-text="i.greenHealthCode === true ? '是' : '否'"></td>
                <td v-text="i.nucleicAcidNegative === true ? '是' : '否'"></td>
            </tr>
            </tbody>
        </table>
    </fieldset>
    <fieldset disabled>
        <legend>办理记录</legend>
        <div v-if="trace.length === 0" class="am-text-center">暂无</div>
        <ul v-else class="am-comments-list">
            <li v-for="i in trace" :class="i.opinion ? 'am-comment-highlight' : ''" class="am-comment">
                <a>
                    <img src="${path}/img/avatar.png" class="am-comment-avatar" width="48" height="48"/>
                </a>
                <div class="am-comment-main">
                    <header class="am-comment-hd">
                        <div class="am-comment-meta">
                            <a class="am-comment-author">{{i.creatorName}}</a>
                            {{i.eventDesc}}
                            <time v-text="i.createTime"></time>
                        </div>
                        <div v-if="i.creatorMp" class="am-comment-actions">
                            <a :href="'tel:' + i.creatorMp" :title="i.creatorMp">
                                <span class="am-icon-phone"></span>
                            </a>
                        </div>
                    </header>
                    <div v-if="i.opinion" class="am-comment-bd">
                        <p v-text="i.opinion"></p>
                    </div>
                </div>
            </li>
        </ul>
    </fieldset>
</div>
</body>
</html>