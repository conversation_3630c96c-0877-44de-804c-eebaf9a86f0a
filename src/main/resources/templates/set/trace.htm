<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${amazeui_color_css}"/>
    <script src="${amazeui_color_js}"></script>
    <script>
        document.write('<script src="${path}/view/set/trace.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body>
<div id="app" class="am-margin" v-cloak>
    <div v-if="items.length === 0" class="am-text-center">暂无</div>
    <ul v-else class="am-comments-list">
        <li v-for="i in items" :class="i.opinion ? 'am-comment-highlight' : ''" class="am-comment">
            <a>
                <img src="${path}/img/avatar.png" class="am-comment-avatar" width="48" height="48"/>
            </a>
            <div class="am-comment-main">
                <header class="am-comment-hd">
                    <div class="am-comment-meta">
                        <a class="am-comment-author">{{i.creatorName}}</a>
                        {{i.eventDesc}}
                        <time v-text="i.createTime"></time>
                    </div>
                    <div v-if="i.creatorMp" class="am-comment-actions">
                        <a :href="'tel:' + i.creatorMp" :title="i.creatorMp">
                            <span class="am-icon-phone"></span>
                        </a>
                    </div>
                </header>
                <div v-if="i.opinion" class="am-comment-bd">
                    <p v-text="i.opinion"></p>
                </div>
            </div>
        </li>
    </ul>
</div>
</body>
</html>