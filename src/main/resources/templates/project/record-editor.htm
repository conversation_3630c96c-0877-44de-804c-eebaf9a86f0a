<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${amazeui_color_css}"/>
    <script src="${amazeui_color_js}"></script>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <link rel="stylesheet" href="${datetimepicker_css}"/>
    <script src="${datetimepicker_js}"></script>
    <script src="${datetimepicker_cn_js}"></script>
    <script src="${component_member_js}"></script>
    <script src="${path}/view/project/common.js"></script>
    <style>
        fieldset:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
        }

        legend {
            margin-bottom: 0;
        }

        .am-list li {
            display: flex;
            justify-content: space-between;
        }

        .am-list li div:nth-child(2) {
            display: flex;
        }

        .am-list li div:nth-child(2) span {
            margin-right: 0.6rem;
        }

        .am-list li div.custom-field-left {
            display: flex;
            align-items: baseline;
        }

        .am-ucheck-icons {
            top: -6px;
        }

        .alias {
            border-left: none !important;
            border-top: none !important;
            border-right: none !important;
            margin-right: 10px;
            padding: 0 !important;
            width: 100px !important;
            font-size: 12px !important;
            text-align: center;
        }

        .color-switch {
            height: 20px;
        }

        .color-switch label {
            margin-bottom: 0;
        }

        .color-switch div {
            margin: 0 !important;
        }

        .legend-img-container {
            position: relative;
            display: inline-block;
            margin: 4px;
        }

        .legend-img-overlay {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .legend-img-overlay:hover {
            opacity: 1;
        }

        .legend-img-close-btn {
            position: absolute;
            right: 5px;
            top: 5px;
            width: 18px;
            line-height: 18px;
            font-size: 12px !important;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/project/record-editor.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body>
<div id="app" v-cloak>
    <!-- 操作 -->
    <template v-if="!form.readonly && form.status !== 'CLOSED'">
        <div ref="topbar" class="am-g am-padding-sm" style="background-color: white; border-bottom: solid 1px #c7c7c7">
            <template v-if="form.status === 'PUBLISHED'">
                <button @click="_close" type="button" class="am-btn am-btn-danger am-btn-sm am-fr">
                    <span class="mdi-stop-circle-outline"></span> 关闭
                </button>
                <button @click="_updateDeadline" type="button"
                        class="am-btn am-btn-default am-btn-sm am-fr am-margin-right-sm">
                    <span class="mdi-content-save-outline"></span> 保存
                </button>
            </template>
            <template v-if="form.status === 'DRAFT'">
                <button @click="_launch" type="button" class="am-btn am-btn-primary am-btn-sm am-fr">
                    <span class="mdi-launch"></span> 启动
                </button>
                <button @click="_save" type="button" class="am-btn am-btn-default am-btn-sm am-fr am-margin-right-sm">
                    <span class="mdi-content-save-outline"></span> 保存
                </button>
            </template>
        </div>
    </template>

    <!-- 表单 -->
    <form ref="form" class="am-form am-form-horizontal am-padding-sm">
        <fieldset>
            <legend>工作描述</legend>
            <div class="am-form-group am-form-icon am-form-feedback">
                <label class="am-u-sm-2 am-form-label">工作要求</label>
                <div class="am-u-sm-10">
                    <input v-model="form.title" :disabled="layout.readonly" name="title" type="text"/>
                </div>
            </div>
            <div class="am-form-group">
                <label class="am-u-sm-2 am-form-label">任务类别</label>
                <div class="am-u-sm-10" style="padding-top: 0.6em">
                    <div class="am-margin-bottom-sm">
                        <span>重点类别：</span>
                        <div style="display: table-cell">
                            <template v-for="(val, key) in type">
                                <span :class="form.type === key ? 'am-badge-primary' : ''" @click="_selectType(key);"
                                      class="am-badge am-radius am-padding-sm am-margin-top-sm am-margin-right-sm">
                                    {{key}}
                                </span>
                            </template>
                        </div>
                    </div>
                    <div>
                        <span>专项类别：</span>
                        <div v-if="!form.type == '' && type.hasOwnProperty(form.type)" style="display: table-cell">
                            <template v-for="(val, key) in type[form.type]">
                                <span :class="form.subtype === key ? 'am-badge-primary' : ''"
                                      @click="_selectSubtype(key);"
                                      class="am-badge am-radius am-padding-sm am-margin-top-sm am-margin-right-sm">
                                    {{val}}
                                </span>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            <div class="am-form-group am-form-icon am-form-feedback">
                <label class="am-u-sm-2 am-form-label">是否初始化数据</label>
                <div class="am-u-sm-10">
                    <div class="color-switch" style="padding-top: 0.6em;">
                        <label>
                            <input v-model="form.initialized" type="checkbox"
                                   class="ios-switch" :disabled="layout.readonly" />
                            <div>
                                <div></div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
            <div class="am-form-group am-form-icon am-form-feedback">
                <label class="am-u-sm-2 am-form-label">具体内容</label>
                <div class="am-u-sm-10">
                    <textarea v-model="form.description" :disabled="layout.readonly" name="description"
                              rows="5"></textarea>
                </div>
            </div>
            <div class="am-form-group am-form-file">
                <label class="am-u-sm-2 am-form-label">图例</label>
                <div class="am-u-sm-10">
                    <button type="button" class="am-btn am-btn-default am-btn-sm">
                        <i class="am-icon-cloud-upload">选择...</i>
                    </button>
                    <input id="legend-files" :disabled="layout.readonly" type="file" title="" accept="image/*" multiple/>
                </div>
            </div>
            <div class="am-form-group" hidden>
                <div id="legend-img-list" class="am-u-sm-10 am-u-sm-offset-2"></div>
            </div>
            <div class="am-form-group">
                <label class="am-u-sm-2 am-form-label">个人填报上限</label>
                <div class="am-u-sm-10">
                    <input v-model="form.quota" :disabled="layout.readonly" type="number"/>
                </div>
            </div>
            <div class="am-form-group">
                <label class="am-u-sm-2 am-form-label">完成期限</label>
                <div class="am-u-sm-10">
                    <div ref="deadline" class="am-input-group date form_datetime-3">
                        <span class="add-on am-input-group-label">
                            <i class="icon-th mdi-calendar-outline"></i>
                        </span>
                        <input v-model.trim="form.deadline" :disabled="form.readonly || form.status === 'CLOSED'"
                               type="text" name="deadline" class="am-form-field" readonly/>
                        <template v-if="!form.readonly && form.status !== 'CLOSED'">
                            <span class="add-on am-input-group-label">
                                <i class="icon-remove mdi-close"></i>
                            </span>
                        </template>
                    </div>
                </div>
            </div>
            <div class="am-form-group am-form-icon am-form-feedback">
                <label class="am-u-sm-2 am-form-label">操作指引</label>
                <div class="am-u-sm-10">
                    <input v-model="form.guide" :disabled="layout.readonly" type="text"/>
                </div>
            </div>
        </fieldset>
        <fieldset :disabled="layout.readonly">
            <legend class="am-margin-bottom-0">任务落实单位</legend>
            <div class="am-form-group">
                <button @click="_chooseAssignee" type="button" class="am-btn am-btn-sm am-btn-default am-fr">
                    <span class="mdi-account"></span> 选择
                </button>
            </div>
            <template v-if="form.assignees.length > 0">
                <ul class="am-list am-list-static">
                    <li v-for="(i, index) in form.assignees" class="am-text-primary">
                        {{i.name}}
                        <template v-if="!layout.readonly">
                            <button @click="_removeAssignee(index);" type="button" class="am-close am-fr">
                                <span class="mdi-close"></span>
                            </button>
                        </template>
                    </li>
                </ul>
            </template>
        </fieldset>
        <fieldset :disabled="layout.readonly">
            <legend>任务内容</legend>

            <div v-show="form.formDefinition.length > 0" class="am-panel am-panel-default am-margin-bottom-0">
                <div class="am-panel-hd">
                    <h3 class="am-panel-title">选中字段</h3>
                </div>
                <div class="am-panel-bd am-padding-top-0">
                    <template v-for="(i, index) in form.formDefinition">
                        <span :class="i.validator.rule.required === true ? 'am-badge-danger' : ''"
                              class="am-badge am-radius am-padding-sm am-margin-top-sm am-margin-right-sm">
                            {{i.alias ? i.alias : i.title}}
                            <template v-if="!layout.readonly">
                                <template v-if="index > 0">
                                    <span @click="_preField(index);" class="mdi-arrow-left"></span>
                                </template>
                                <template v-if="index < form.formDefinition.length - 1">
                                    <span @click="_postField(index);" class="mdi-arrow-right"></span>
                                </template>
                                <span @click="_removeField(index);" class="mdi-close"></span>
                            </template>
                        </span>
                    </template>
                </div>
            </div>

            <div v-show="!layout.readonly" ref="tabs" class="am-tabs am-margin-top-sm">
                <ul class="am-tabs-nav am-nav am-nav-tabs">
                    <li v-for="i in definition.groups">
                        <a>{{i}}</a>
                    </li>
                </ul>

                <div class="am-tabs-bd">
                    <div v-for="i in definition.groups" class="am-tab-panel">
                        <ul class="am-list am-list-static">
                            <template v-for="(j, index) in definition.fields">
                                <li v-if="i === j.group" class="am-text-primary">
                                    <template v-if="i === '自定义'">
                                        <div class="custom-field-left">
                                            <label class="am-checkbox-inline">
                                                <input v-model="form.formDefinition" :value="j" type="checkbox" data-am-ucheck>
                                            </label>
                                            <input v-model="j.alias" type="text" class="alias" placeholder="别名"/>
                                            {{j.title}}
                                        </div>

                                        <!--<div class="am-fr">
                                            <span style="color: #c7c7c7">是否必填</span>
                                            <label class="am-checkbox-inline">
                                                <input v-model="j.validator.rule.required" type="checkbox" data-am-ucheck>
                                            </label>
                                        </div>-->
                                        <div>
                                            <span class="am-text-xs">是否必填</span>
                                            <div class="color-switch">
                                                <label>
                                                    <input v-model="j.validator.rule.required" type="checkbox"
                                                           class="ios-switch"/>
                                                    <div>
                                                        <div></div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div>
                                            <label class="am-checkbox-inline">
                                                <input v-model="form.formDefinition" :value="j" type="checkbox" data-am-ucheck>
                                            </label>
                                            {{j.title}}
                                        </div>

                                        <!--<div class="am-fr">
                                            <span style="color: #c7c7c7">是否必填</span>
                                            <label class="am-checkbox-inline">
                                                <input v-model="j.validator.rule.required" type="checkbox" data-am-ucheck>
                                            </label>
                                        </div>-->
                                        <div>
                                            <input v-model="j.alias" type="text" class="alias" placeholder="别名"/>
                                            <div>
                                                <span class="am-text-xs">是否必填</span>
                                                <div class="color-switch">
                                                    <label>
                                                        <input v-model="j.validator.rule.required" type="checkbox"
                                                               class="ios-switch"/>
                                                        <div>
                                                            <div></div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
        </fieldset>
    </form>
</div>
</body>
</html>
