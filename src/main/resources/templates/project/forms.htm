<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <link rel="stylesheet" href="${datetimepicker_css}"/>
    <script src="${echarts_js}"></script>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <script src="${datetimepicker_js}"></script>
    <script src="${datetimepicker_cn_js}"></script>
    <script src="${component_searchbar_js}"></script>
    <script src="${component_member_js}"></script>
    <script src="${component_pagination_js}"></script>
    <script src="${component_table_js}"></script>
    <script>
        document.write('<script src="${path}/view/project/forms.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body>
<div class="am-tabs am-margin-sm" data-am-tabs="{noSwipe: 1}">
    <ul class="am-tabs-nav am-nav am-nav-tabs">
        <li class="am-active">
            <a>明细</a>
        </li>
        <li>
            <a>统计</a>
        </li>
    </ul>

    <div class="am-tabs-bd">
        <div class="am-tab-panel am-active">
            <div id="searchbar"></div>
            <div class="am-panel am-panel-default">
                <div id="table" class="am-panel-bd"></div>
            </div>
        </div>
        <div class="am-tab-panel">
            <div id="statistic" class="am-panel am-panel-default">
                <div ref="chart" class="am-panel-bd" style="height: 300px">
                </div>
                <div class="am-panel-footer am-text-center">
                    <template v-if="deptIdQueue.length > 0">
                        <button @click="_back();" class="am-btn am-btn-primary am-btn-sm">
                            <span class="mdi-arrow-u-left-top"></span> 返回
                        </button>
                    </template>
                    <template v-else>
                        <button @click="query();" class="am-btn am-btn-primary am-btn-sm">
                            <span class="mdi-reload"></span> 刷新
                        </button>
                    </template>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
