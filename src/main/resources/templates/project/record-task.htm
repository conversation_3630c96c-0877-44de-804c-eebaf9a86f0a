<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <script src="${component_member_js}"></script>
    <script>
        document.write('<script src="${path}/view/project/record-task.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body>
<div id="app" v-cloak>
    <div ref="topbar" v-if="!layout.readonly && status !== 'CLOSED'" class="am-g am-padding-sm" style="background-color: white; border-bottom: solid 1px #c7c7c7">
        <template v-if="assignee === 'GROUP'">
            <template v-if="status === 'TODO'">
                <button @click="_submit" type="button" class="am-btn am-btn-primary am-btn-sm am-fr">
                    <span class="mdi-check"></span> 办理
                </button>
            </template>
            <template v-else>
                <button @click="_issue" type="button" class="am-btn am-btn-primary am-btn-sm am-fr">
                    <span class="mdi-source-branch-plus"></span> 增发
                </button>
            </template>
        </template>
        <template v-else>
            <!-- 支持撤回 -->
            <template v-if="status !== 'TODO'">
                <button @click="_retract" type="button" class="am-btn am-btn-danger am-btn-sm am-fr">
                    <span class="mdi-restore"></span> 撤回
                </button>
            </template>
            <!-- 村居管理员办理 -->
            <template v-else-if="layout.supportTarget">
                <button @click="_close" type="button" class="am-btn am-btn-primary am-btn-sm am-fr">
                    <span class="mdi-check"></span> 办理
                </button>
            </template>
        </template>
    </div>

    <div class="am-panel-group am-padding-sm">
        <div class="am-panel am-panel-default">
            <div class="am-panel-hd">
                <h3 class="am-panel-title">工作描述</h3>
            </div>
            <div class="am-panel-bd">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-2 am-form-label">工作要求</label>
                        <div class="am-u-sm-10">
                            <input :value="title" type="text" readonly>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-2 am-form-label">任务类型</label>
                        <div class="am-u-sm-10">
                            <input :value="type" type="text" readonly>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-2 am-form-label">个人填报上限</label>
                        <div class="am-u-sm-10">
                            <input :value="quota" type="number" readonly>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-2 am-form-label">完成期限</label>
                        <div class="am-u-sm-10">
                            <input :value="deadline" type="text" readonly>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-2 am-form-label">具体内容</label>
                        <div class="am-u-sm-10">
                            <textarea :value="description" rows="5" readonly></textarea>
                        </div>
                    </div>
                    <template v-if="layout.supportTarget">
                        <div class="am-form-group">
                            <label class="am-u-sm-2 am-form-label">目标值</label>
                            <div class="am-u-sm-10">
                                <template v-if="layout.readonly || status !== 'TODO'">
                                    <input :value="target.min" type="number" readonly>
                                </template>
                                <template v-else>
                                    <div class="am-input-group">
                                        <input v-model="target.min" type="number">
                                        <span class="am-input-group-btn">
                                            <template v-if="layout.supportReceive">
                                                <button @click="_receive" class="am-btn am-btn-primary am-fr">
                                                    <span class="mdi-tray-arrow-down"></span> 签收
                                                </button>
                                            </template>
                                            <template v-else>
                                                <button @click="_saveTarget" class="am-btn am-btn-primary am-fr">
                                                    <span class="mdi-content-save-outline"></span> 保存
                                                </button>
                                            </template>
                                        </span>
                                    </div>
                                </template>
                            </div>
                            <!--<div v-if="!layout.readonly" class="am-u-sm-2">

                            </div>-->
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- 组任务需选择办理单位 -->
        <template v-if="assignee === 'GROUP'">
            <div class="am-panel am-panel-default">
                <div class="am-panel-hd">
                    <h3 class="am-panel-title">任务落实单位</h3>
                </div>
                <div class="am-panel-bd">
                    <div class="am-g">
                        <button @click="_choose" :disabled="layout.readonly" type="button" class="am-btn am-btn-sm am-btn-default am-fr">
                            <span class="mdi-account"></span> 选择
                        </button>
                    </div>
                    <template v-if="originalDepartments.length > 0 || departments.length > 0">
                        <ul class="am-list am-list-static am-margin-top-sm">
                            <li v-for="(i, index) in originalDepartments">
                                {{i.name}}
                            </li>
                            <li v-for="(i, index) in departments" class="am-text-primary">
                                {{i.name}}
                                <template v-if="!layout.readonly">
                                    <button @click="_remove(index);" type="button" class="am-close am-fr">
                                        <span class="mdi-close"></span>
                                    </button>
                                </template>
                            </li>
                        </ul>
                    </template>
                </div>
            </div>
        </template>

        <div class="am-panel am-panel-default">
            <div class="am-panel-hd">
                <h3 class="am-panel-title">办理意见</h3>
            </div>
            <div class="am-panel-bd">
                <textarea v-model="memo" :readonly="layout.readonly || (assignee === 'GROUP' && status !== 'TODO') || (assignee === 'USER' && (status !== 'TODO' || !layout.supportTarget))" class="am-form-field" rows="5"></textarea>
                </textarea>
            </div>
        </div>
    </div>
</body>
</html>
