<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志查询</title>
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        .search-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .log-table {
            margin-top: 20px;
        }
        .operation-type {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            color: white;
        }
        .operation-type.create { background-color: #5cb85c; }
        .operation-type.update { background-color: #5bc0de; }
        .operation-type.delete { background-color: #d9534f; }
        .operation-type.import { background-color: #f0ad4e; }
        .operation-type.export { background-color: #6c757d; }
        .operation-result {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            color: white;
        }
        .operation-result.success { background-color: #5cb85c; }
        .operation-result.failure { background-color: #d9534f; }
    </style>
</head>
<body>
    <div class="layui-container">
        <div class="layui-row">
            <div class="layui-col-md12">
                <h2>操作日志查询</h2>
                
                <!-- 搜索表单 -->
                <div class="search-form">
                    <form class="layui-form" id="searchForm">
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">实体类型</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="entityType" placeholder="请输入实体类型" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">实体ID</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="entityId" placeholder="请输入实体ID" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">操作类型</label>
                                    <div class="layui-input-block">
                                        <select name="operationType" lay-filter="operationType">
                                            <option value="">全部</option>
                                            <option value="CREATE">新增</option>
                                            <option value="UPDATE">修改</option>
                                            <option value="DELETE">删除</option>
                                            <option value="IMPORT">导入</option>
                                            <option value="EXPORT">导出</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">操作人</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="operatorName" placeholder="请输入操作人姓名" class="layui-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">部门</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="deptId" placeholder="请输入部门ID" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">操作结果</label>
                                    <div class="layui-input-block">
                                        <select name="result" lay-filter="result">
                                            <option value="">全部</option>
                                            <option value="SUCCESS">成功</option>
                                            <option value="FAILURE">失败</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">开始时间</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="startTime" id="startTime" placeholder="请选择开始时间" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">结束时间</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="endTime" id="endTime" placeholder="请选择结束时间" class="layui-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="searchSubmit">查询</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 操作按钮 -->
                <div class="layui-btn-group">
                    <button class="layui-btn layui-btn-sm" id="exportBtn">导出日志</button>
                    <button class="layui-btn layui-btn-sm layui-btn-danger" id="cleanBtn">清理日志</button>
                </div>

                <!-- 日志表格 -->
                <table class="layui-table log-table" id="logTable" lay-filter="logTable"></table>
            </div>
        </div>
    </div>

    <!-- 日志详情弹窗 -->
    <div id="logDetailModal" style="display: none; padding: 20px;">
        <div class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">实体类型</label>
                <div class="layui-input-block">
                    <input type="text" id="detailEntityType" class="layui-input" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">实体ID</label>
                <div class="layui-input-block">
                    <input type="text" id="detailEntityId" class="layui-input" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">操作类型</label>
                <div class="layui-input-block">
                    <input type="text" id="detailOperationType" class="layui-input" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">操作描述</label>
                <div class="layui-input-block">
                    <textarea id="detailDescription" class="layui-textarea" readonly></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">操作前数据</label>
                <div class="layui-input-block">
                    <textarea id="detailBeforeData" class="layui-textarea" readonly></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">操作后数据</label>
                <div class="layui-input-block">
                    <textarea id="detailAfterData" class="layui-textarea" readonly></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">操作人</label>
                <div class="layui-input-block">
                    <input type="text" id="detailOperatorName" class="layui-input" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">操作时间</label>
                <div class="layui-input-block">
                    <input type="text" id="detailOperateTime" class="layui-input" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">IP地址</label>
                <div class="layui-input-block">
                    <input type="text" id="detailIpAddress" class="layui-input" readonly>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">操作结果</label>
                <div class="layui-input-block">
                    <input type="text" id="detailResult" class="layui-input" readonly>
                </div>
            </div>
            <div class="layui-form-item" id="errorMessageDiv" style="display: none;">
                <label class="layui-form-label">错误信息</label>
                <div class="layui-input-block">
                    <textarea id="detailErrorMessage" class="layui-textarea" readonly></textarea>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/layui/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'laydate', 'layer'], function(){
            var table = layui.table;
            var form = layui.form;
            var laydate = layui.laydate;
            var layer = layui.layer;
            
            // 初始化日期选择器
            laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            
            // 初始化表格
            table.render({
                elem: '#logTable',
                url: '/api/operation-logs/query',
                method: 'post',
                contentType: 'application/json',
                where: {},
                page: true,
                cols: [[
                    {field: 'entityType', title: '实体类型', width: 120},
                    {field: 'entityId', title: '实体ID', width: 120},
                    {field: 'operationType', title: '操作类型', width: 100, templet: function(d){
                        var typeClass = d.operationType.toLowerCase();
                        return '<span class="operation-type ' + typeClass + '">' + d.operationType + '</span>';
                    }},
                    {field: 'description', title: '操作描述', width: 200},
                    {field: 'operatorName', title: '操作人', width: 100},
                    {field: 'deptName', title: '部门', width: 150},
                    {field: 'operateTime', title: '操作时间', width: 160},
                    {field: 'ipAddress', title: 'IP地址', width: 120},
                    {field: 'result', title: '操作结果', width: 100, templet: function(d){
                        var resultClass = d.result.toLowerCase();
                        return '<span class="operation-result ' + resultClass + '">' + d.result + '</span>';
                    }},
                    {title: '操作', width: 100, templet: function(d){
                        return '<button class="layui-btn layui-btn-xs" onclick="viewDetail(\'' + d.id + '\')">查看详情</button>';
                    }}
                ]],
                parseData: function(res){
                    return {
                        "code": res.code === 0 ? 0 : 1,
                        "msg": res.message || '',
                        "count": res.data ? res.data.totalElements : 0,
                        "data": res.data ? res.data.content : []
                    };
                }
            });
            
            // 搜索表单提交
            form.on('submit(searchSubmit)', function(data){
                table.reload('logTable', {
                    where: data.field
                });
                return false;
            });
            
            // 导出日志
            $('#exportBtn').click(function(){
                var searchData = form.val('searchForm');
                var params = new URLSearchParams();
                for(var key in searchData){
                    if(searchData[key]){
                        params.append(key, searchData[key]);
                    }
                }
                
                var url = '/api/operation-logs/export?' + params.toString();
                window.open(url);
            });
            
            // 清理日志
            $('#cleanBtn').click(function(){
                layer.prompt({
                    title: '请输入清理时间（格式：yyyy-MM-dd HH:mm:ss）',
                    formType: 0,
                    value: '2024-01-01 00:00:00'
                }, function(value, index){
                    layer.confirm('确定要清理' + value + '之前的所有日志吗？', function(index2){
                        $.ajax({
                            url: '/api/operation-logs/clean',
                            method: 'DELETE',
                            data: {date: value},
                            success: function(res){
                                if(res.code === 0){
                                    layer.msg('清理成功，共清理' + res.data + '条日志');
                                    table.reload('logTable');
                                } else {
                                    layer.msg('清理失败：' + res.message);
                                }
                            },
                            error: function(){
                                layer.msg('清理失败，请稍后重试');
                            }
                        });
                        layer.close(index2);
                    });
                    layer.close(index);
                });
            });
        });
        
        // 查看日志详情
        function viewDetail(logId){
            $.ajax({
                url: '/api/operation-logs/' + logId,
                method: 'GET',
                success: function(res){
                    if(res.code === 0){
                        var log = res.data;
                        
                        $('#detailEntityType').val(log.entityType);
                        $('#detailEntityId').val(log.entityId);
                        $('#detailOperationType').val(log.operationType);
                        $('#detailDescription').val(log.description);
                        $('#detailBeforeData').val(log.beforeData || '无');
                        $('#detailAfterData').val(log.afterData || '无');
                        $('#detailOperatorName').val(log.operatorName);
                        $('#detailOperateTime').val(log.operateTime);
                        $('#detailIpAddress').val(log.ipAddress);
                        $('#detailResult').val(log.result);
                        
                        if(log.errorMessage){
                            $('#detailErrorMessage').val(log.errorMessage);
                            $('#errorMessageDiv').show();
                        } else {
                            $('#errorMessageDiv').hide();
                        }
                        
                        layer.open({
                            type: 1,
                            title: '日志详情',
                            content: $('#logDetailModal'),
                            area: ['600px', '500px']
                        });
                    } else {
                        layer.msg('获取日志详情失败：' + res.message);
                    }
                },
                error: function(){
                    layer.msg('获取日志详情失败，请稍后重试');
                }
            });
        }
    </script>
</body>
</html>
