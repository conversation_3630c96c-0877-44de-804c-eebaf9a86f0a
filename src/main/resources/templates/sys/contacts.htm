<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<#include "/template-layout-lingling.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${datetimepicker_css}"/>
    <script src="${datetimepicker_js}"></script>
    <script src="${datetimepicker_cn_js}"></script>
    <script src="${exif_js}"></script>
    <link href="${tagsinput_css}" rel="stylesheet"/>
    <script src="${component_searchbar_js}"></script>
    <script src="${tagsinput_js}"></script>
    <script src="${typeahead_js}"></script>
    <script src="${component_auto_complete_js}"></script>
    <script src="${component_member_js}"></script>
    <script src="${component_form_js}"></script>
    <script src="${component_pagination_js}"></script>
    <script src="${component_table_js}"></script>
    <script src="${rsa_jsencrypt_js}"></script>
    <style>
        #departments a {
            cursor: pointer;
        }

        .am-list li {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    </style>
    <script src="${path}/view/sys/profile-editor.js"></script>
    <script>
        document.write('<script src="${path}/view/sys/contacts.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<@template_layout>
<script>
    // 重载函数
    function saveUser(user) {
        return new Promise(resolve => {
            new HttpRequest().ajax('/sys/user/save/default', user).then(result => {
                resolve(result.code === 'OK');
            });
        });
    }

    function saveDept(dept) {
        return new Promise(resolve => {
            new HttpRequest().ajax('/sys/dept/save/default', dept).then(result => {
                resolve(result.code === 'OK');
            });
        });
    }

    function editDept(item, superior) {
        let _index;

        const _id = Math.random().toString().replace(/\./g, '') + '_';
        $('body').append('<div id="' + _id + '" class="am-margin"></div>');

        const _form = $('#' + _id).form({
            'rows': [{
                'title': '名称',
                'field': 'name',
                'validator': {
                    'rule': {
                        'required': true
                    }
                }
            }, {
                'title': '面积（km²）',
                'field': 'area',
                'control': 'number'
            }, {
                'title': '描述',
                'field': 'description',
                'control': 'textarea'
            }, {
                'title': '排序',
                'field': 'seq',
                'control': 'number'
            }],
            // 按钮组
            'btns': [{
                'callback': data => {
                    if (data.seq === '') {
                        data.seq = null
                    }

                    saveDept(data).then(success => {
                        if (success) {
                            ModalUtil.close(_index);

                            if (superior !== null && superior.id) {
                                deptIdForMembers = superior.id;
                            }

                            departments.queryOrganization(deptIdForMembers);
                        }
                    });
                }
            }]
        });

        _form.vue.$nextTick(() => {
            _form.setData(item === null ? {
                'superiorId': superior.id
            } : item);

            _index = ModalUtil.open({
                'type': 1,
                'title': item === null ? (superior === null || !superior.name ? '在根节点下新建部门' : ('在“' + superior.name + '”下新建子部门')) : '修改部门',
                'content': $('#' + _id),
                'end': function () {
                    _form.vue.$destroy();

                    $('#' + _id).remove();
                }
            });
        });
    }
</script>

<div class="row">
    <div class="widget am-cf am-padding-left-0 am-padding-right-0 am-padding-bottom-0">
        <div class="widget-body am-cf">
            <div class="am-u-sm-12" id="searchbar"></div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-bd" id="departments" v-cloak>
                        <ol class="am-breadcrumb am-breadcrumb-slash am-margin-0" style="padding: 1rem 0">
                            <li>
                                <a @click="queryOrganization(null);">
                                    <span class="mdi-home"></span>
                                </a>
                            </li>
                            <template v-for="(i, index) in deptIdArr">
                                <li :class="{ 'am-active': index == deptIdArr.length - 1 }">
                                    <a @click="queryOrganization(i);" v-text="deptNameArr[index]"></a>
                                </li>
                            </template>
                        </ol>
                        <ul class="am-list">
                            <template v-for="i in subordinates">
                                <li>
                                    <template v-if="i[deptMapper.hasSubordinates]">
                                        <a @click="queryOrganization(i[deptMapper.id]);"class="am-text-truncate">
                                            <span class="mdi-bank-outline"></span> {{i[deptMapper.name]}}
                                        </a>
                                    </template>
                                    <template v-else>
                                        <label @click="_queryUsers(i);" class="am-margin-bottom-0 am-padding-top-sm am-padding-bottom-sm">
                                            <span class="mdi-bank-outline"></span> {{i[deptMapper.name]}}
                                        </label>
                                    </template>
                                    <div>
                                        <button @click="_removeDept(i);" class="mdi-close am-close" title="删除" type="button"></button>
                                        <button @click="_moveDept(i);" class="mdi-arrow-all am-close" title="移动" type="button"></button>
                                        <button @click="_editDept(i);" class="mdi-square-edit-outline am-close" title="编辑" type="button"></button>
                                        <button @click="_addDept(i);" class="mdi-plus am-close" title="新增" type="button"></button>
                                    </div>
                                </li>
                            </template>
                            <li style="justify-content: center">
                                <div class="am-margin-sm">
                                    <a @click="_addDept(dept);" style="cursor: pointer">
                                        <span class="mdi-plus"></span> 新建子部门</span>
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-bd" id="users"></div>
                </div>
            </div>
        </div>
    </div>
</div>
</@template_layout>
</body>
</html>