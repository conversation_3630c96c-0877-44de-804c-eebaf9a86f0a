<#macro template_resource>
    <#assign path=request.contextPath/>

    <#assign amazeui_color_css="${path}/js/amazeui-color/color.min.css"/>
    <#assign amazeui_color_js="${path}/js/amazeui-color/color.min.js"/>
    <#assign echarts_js="${path}/js/echarts/echarts.min.js"/>
    <#assign exif_js="${path}/js/exif.min.js">
    <#assign hls_js="${path}/js/hls.min.js">
    <#assign layui_css="${path}/js/layui/css/layui.css"/>
    <#assign layui_js="${path}/js/layui/layui.js"/>
    <#assign md5_js="${path}/js/jquery/jquery.md5.js"/>
    <#assign ofd_js="${path}/js/ofd/ofd.js">
    <#assign ofd_parser_js="${path}/js/ofd/ofd_parser.js">
    <#assign pdf_js="${path}/js/pdf/pdf.js">
    <#assign pdf_worker_js="${path}/js/pdf/pdf.worker.js">
    <#assign rsa_barrett_js="${path}/js/rsa/Barrett.js"/>
	<#assign rsa_bigint_js="${path}/js/rsa/BigInt.js"/>
	<#assign rsa_js="${path}/js/rsa/RSA.js"/>
	<#assign rsa_jsencrypt_js="${path}/js/rsa/jsencrypt.js"/>
    <#assign watermarker_js="${path}/js/watermark.js"/>

    <#assign datetimepicker_cn_js="${path}/js/amazeui-datetimepicker/js/locales/amazeui.datetimepicker.zh-CN.js"/>
    <#assign datetimepicker_css="${path}/js/amazeui-datetimepicker/css/amazeui.datetimepicker.css"/>
    <#assign datetimepicker_js="${path}/js/amazeui-datetimepicker/js/amazeui.datetimepicker.js"/>
    <#assign tagsinput_css="${path}/js/amazeui-tagsinput/amazeui.tagsinput.css"/>
    <#assign tagsinput_js="${path}/js/amazeui-tagsinput/amazeui.tagsinput.js"/>
    <#assign typeahead_js="${path}/js/amazeui-tagsinput/typeahead.min.js"/>

    <#assign component_auto_complete_js="${path}/js/sparrow.component.auto-complete.js"/>
    <#assign component_file_js="${path}/js/sparrow.component.file.js"/>
    <#assign component_form_js="${path}/js/sparrow.component.form.js"/>
    <#assign component_member_js="${path}/js/sparrow.component.member.js"/>
    <#assign component_pagination_js="${path}/js/sparrow.component.pagination.js"/>
    <#assign component_progressbar_js="${path}/js/sparrow.component.progressbar.js"/>
    <#assign component_reader_js="${path}/js/sparrow.component.reader.js"/>
    <#assign component_searchbar_js="${path}/js/sparrow.component.searchbar.js"/>
    <#assign component_table_js="${path}/js/sparrow.component.table.js"/>

	<meta http-equiv="Content-Type"; content="text/html; charset=utf-8"/>
	<meta name="renderer"; content="webkit"/>
	<meta http-equiv="X-UA-Compatible"; content="IE=edge"/>
	<meta name="viewport"; content="width=device-width, initial-scale=1, user-scalable=no"/>
    <link rel="shortcut icon"; type="images/x-icon"; href="${path}/img/favicon.ico">

	<script src="${path}/js/jquery/jquery.min.js"></script>
	<script src="${path}/js/jquery-validation/jquery.validate.js"></script>
	<script src="${path}/js/jquery-validation/localization/messages_zh.min.js"></script>
	<script src="${path}/js/vue.min.js"></script>
    <link rel="stylesheet" href="${path}/js/layui/css/layui.css"/>
	<link rel="stylesheet" href="${path}/js/amazeui/css/amazeui.flat.css"/>
	<link rel="stylesheet" href="${path}/css/admin.css"/>
	<link rel="stylesheet" href="${path}/js/amazeui-datatables/amazeui.datatables.css"/>
	<link rel="stylesheet" href="${path}/js/MaterialDesign/css/materialdesignicons.min.css"/>
	<script src="${path}/js/layui/layui.js"></script>
	<script src="${path}/js/amazeui/js/amazeui.min.js"></script>
    <script src="${path}/js/axios.min.js"></script>
	<script src="${path}/js/sparrow.util.js"></script>
	<script src="${path}/view/global.js"></script>

    <script>
        window.location.contextPath = '${path}';
    </script>

    <style>
        body {
            font-size: 1.4rem;
            overflow: auto;
        }

        [v-cloak] {
            display: none;
        }

        /* 表单 */
        .am-form .am-form-group:last-child {
            margin-bottom: 0px !important;
        }
        .am-form .am-form-group > .am-input-group {
            padding-left: 0px !important;
        }
        @media screen and (max-width:640px) {
            .am-form-icon label + div > [class*=am-icon-] {
                top: 70%
            }
        }

        /* 数据表格排序图标 */
        table.dataTable thead > tr > th.sorting, th.sorting_asc, th.sorting_desc {
            padding-right: 30px!important;
        }

        /* 下拉列表字体 */
        .am-selected-list {
            font-size: inherit;
        }
        .am-selected-list .am-selected-text {
            color: #333333;
        }

        /* 图标字体 */
        [class*=mdi-]:before {
            display: inline-block;
            font: normal normal normal 1.6rem/1 "Material Design Icons", sans-serif;
            font-size: inherit;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            -webkit-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
            transform: translate(0, 0);
        }

        /* 表单域图标字体 */
        .am-form-icon [class*=mdi-] {
            position: absolute;
            left: 1em;
            top: 50%;
            display: block;
            margin-top: -0.5em;
            line-height: 1;
            z-index: 2;
        }
        .am-form-icon label ~ [class*='mdi-'] {
            top: 70%;
        }

        /* 底部栏图标字体 */
        .am-navbar-nav [class*=mdi-] {
            width: 24px;
            height: 24px;
            margin: 4px auto 0;
            display: block;
            line-height: 24px;
        }
        .am-navbar-nav a [class*=mdi-]:before {
            font-size: 22px;
            vertical-align: middle;
        }

        /* 修正layui样式 */
        option {
            font-weight: normal;
        }

        /* layer关闭按钮图标 */
        .layui-layer-close2 {
            -webkit-box-sizing: unset;
        }
    </style>
</#macro>