<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <link rel="stylesheet" href="${datetimepicker_css}"/>
    <script src="${datetimepicker_js}"></script>
    <script src="${datetimepicker_cn_js}"></script>
    <script src="${component_member_js}"></script>
    <script src="${component_form_js}"></script>
    <style>
        .am-close {
            opacity: unset;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/subject/event.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body class="am-padding-sm">
<div class="am-panel-group">
    <div class="am-panel am-panel-default">
        <div class="am-panel-hd">
            <h3 class="am-panel-title">事件描述</h3>
        </div>
        <div class="am-panel-bd">
            <div id="form"></div>
        </div>
    </div>

    <div id="table" class="am-panel am-panel-default">
        <template v-if="table.eventId !== null">
            <div class="am-panel-hd">
                <h3 class="am-panel-title">当事人</h3>
            </div>
            <div class="am-panel-bd">
                <div class="am-scrollable-horizontal">
                    <table class="am-table am-table-striped am-table-centered am-table-hover">
                        <thead>
                        <tr>
                            <th><span>姓名</span></th>
                            <th><span>证件号</span></th>
                            <th>
                                <a @click="_add" class="am-vertical-align-middle" title="新增">
                                    <span class="mdi-plus"></span>
                                </a>
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-if="participants.length === 0">
                            <td colspan="3" class="am-text-center">
                                <div>
                                    <span class="mdi-tray-alert am-text-xxxl"></span>
                                </div>
                                暂无数据
                            </td>
                        </tr>
                        <template v-else v-for="(i, index) in participants">
                            <tr>
                                <td class="am-text-center">
                                    <input v-model="i.name" class="am-form-field"/>
                                </td>
                                <td>
                                    <input v-model="i.credentialNo" class="am-form-field"/>
                                </td>
                                <td class="am-text-middle">
                                    <a @click="_save(index)" class="am-close am-text-primary" title="保存">
                                        <span class="mdi-content-save"></span>
                                    </a>
                                    <a @click="_remove(index)" class="am-close am-text-danger" title="删除">
                                        <span class="mdi-close"></span>
                                    </a>
                                    <a v-if="i.id !== null" @click="_collapseProfile(i)" class="am-close"
                                       style="opacity: 0.2!important;" title="查询个人信息">
                                        <span :class="i.collapsed ? 'mdi-chevron-down' : 'mdi-chevron-up'"></span>
                                    </a>
                                </td>
                            </tr>
                            <tr v-show="i.collapsed === false">
                                <td v-if="$.isEmptyObject(i.profile) || i.profile.id === ''" colspan="3">
                                    <div>
                                        <span class="mdi-tray-alert am-text-xxxl"></span>
                                    </div>
                                    暂无数据
                                </td>
                                <td v-else colspan="3">
                                    <ul class="am-avg-sm-1 am-avg-md-4 am-text-left">
                                        <li v-for="(val, key) in profileMapping">
                                            {{val}}：{{i.profile[key]}}
                                        </li>
                                    </ul>
                                </td>
                            </tr>
                        </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </template>
    </div>
</div>
</div>
</body>
</html>