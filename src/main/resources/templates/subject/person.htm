<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <link rel="stylesheet" href="${datetimepicker_css}"/>
    <script src="${datetimepicker_js}"></script>
    <script src="${datetimepicker_cn_js}"></script>
    <script src="${component_member_js}"></script>
    <script>
        document.write('<script src="${path}/view/subject/person.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body class="am-padding-sm">
<form id="app" class="am-form" v-cloak>
    <template v-for="(val, key) in rows">
        <fieldset v-show="key === 'basic'
            || (data.type === '吸毒人员' && key === 'drug')
            || (data.type === '社区矫正人员' && key === 'rectification')
            || (data.type === '上访人员' && key === 'petition')
            || (data.type === '严重精神障碍患者' && key === 'psycho')
            || (data.type === '重点未成年人' && key === 'minors')">
            <template>
                <legend v-if="key === 'basic'">基本信息</legend>
                <legend v-else-if="key === 'drug'">吸毒人员</legend>
                <legend v-else-if="key === 'rectification'">社区矫正人员</legend>
                <legend v-else-if="key === 'petition'">上访人员</legend>
                <legend v-else-if="key === 'psycho'">严重精神障碍患者</legend>
                <legend v-else-if="key === 'minors'">重点未成年人</legend>
            </template>
            <div v-for="i in val" :class="$.inArray(i.control, layout.controlsNotSupportFormFeedback) === -1 ? 'am-form-icon am-form-feedback' : ''" class="am-form-group">
                <template>
                    <label v-if="i.validatorValue.rule && i.validatorValue.rule.required === true" v-show="!$.isFunction(i.hidden) || !i.hidden(data)" class="am-form-label am-text-warning am-padding-top-0">* {{i.title}}</label>
                    <label v-else v-show="!$.isFunction(i.hidden) || !i.hidden(data)" class="am-form-label am-padding-top-0">{{i.title}}</label>
                </template>
                <!-- 时间 -->
                <div v-if="i.control === 'datetime'" :ref="'datetime-' + i.field" class="am-input-group date form_datetime-3">
                    <span class="add-on am-input-group-label"><i class="icon-th mdi-calendar-outline"></i></span>
                    <input :name="i.field" v-model.trim="data[i.field]" type="text" class="am-form-field" readonly/>
                    <span class="add-on am-input-group-label"><i class="icon-remove mdi-close"></i></span>
                </div>
                <!-- 部门选择 -->
                <div v-else-if="i.control === 'department'" class="am-input-group">
                    <input :name="i.field" :value="_getText(i)" type="text" class="am-form-field" readonly/>
                    <span class="am-input-group-btn">
                        <button @click="_getMember(i, 'department');" class="am-btn am-btn-secondary" type="button">
                            <span class="mdi-sitemap-outline"></span>
                        </button>
                    </span>
                </div>
                <!-- 标签 -->
                <input v-else-if="i.control === 'label'" type="text" :value="data[i.field]" class="am-form-field" disabled/>
                <!-- 单选 -->
                <div v-else-if="i.control === 'radio'">
                    <template v-if="$.isArray(i.validatorValue.options)">
                        <label v-for="(val, idx) in i.validatorValue.options" class="am-radio">
                            <input v-model="data[i.field]" :name="i.field" :value="val.key" type="radio">
                            <span v-text="val.value" style="top:0;position:absolute"></span>
                        </label>
                    </template>
                    <template v-else>
                        <label v-for="(value, key) in i.validatorValue.options" class="am-radio">
                            <input v-model="data[i.field]" :name="i.field" :value="key" type="radio">
                            <span v-text="value" style="top:0;position:absolute"></span>
                        </label>
                    </template>
                </div>
                <!-- 下拉 -->
                <select v-else-if="i.control === 'select'" v-show="!$.isFunction(i.hidden) || !i.hidden(data)" :ref="i.field" :name="i.field" v-model="data[i.field]" :multiple="i.multiple" class="am-form-field">
                    <template v-if="$.isArray(i.validatorValue.options)">
                        <optgroup :label="'请选择' + i.title">
                            <option v-for="(val, idx) in i.validatorValue.options" :value="val.value">{{val.key}}</option>
                        </optgroup>
                    </template>
                    <template v-else>
                        <optgroup :label="'请选择' + i.title">
                            <option v-for="(key, val) in i.validatorValue.options" :value="val">{{key}}</option>
                        </optgroup>
                    </template>
                </select>
                <input v-else v-show="!$.isFunction(i.hidden) || !i.hidden(data)" :name="i.field" :type="i.control" v-model.trim="data[i.field]" :placeholder="i.placeholder" class="am-form-field" />
            </div>
        </fieldset>
    </template>
    <div class="am-text-right" style="padding:0 0.625em">
        <button @click="_save" type="button" class="am-btn am-btn-sm am-btn-primary">
            <span class="mdi-check"></span> 保存
        </button>
    </div>
</form>
</body>
</html>
