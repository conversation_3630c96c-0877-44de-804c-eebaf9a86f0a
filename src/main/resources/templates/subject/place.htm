<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <link rel="stylesheet" href="${datetimepicker_css}"/>
    <script src="${datetimepicker_js}"></script>
    <script src="${datetimepicker_cn_js}"></script>
    <script src="${component_member_js}"></script>
    <script src="${component_form_js}"></script>
    <style>
        #personsForm .am-form-group:nth-child(9) {
            max-height: 150px;
            overflow-y: auto;
        }

        #personsForm .am-form-group:nth-child(9) > div {
            display: flex;
            flex-wrap: wrap;
            align-self: start;
        }

        #personsForm .am-form-group:nth-child(9) .am-checkbox {
            padding-top: 0;
            width: 50%;
        }

        .am-close {
            opacity: unset;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/subject/place.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body class="am-padding-sm">
<div class="am-panel-group">
    <div class="am-panel am-panel-default">
        <div class="am-panel-hd">
            <h3 class="am-panel-title">场所信息</h3>
        </div>
        <div class="am-panel-bd">
            <div id="form"></div>
        </div>
    </div>

    <div id="table" class="am-panel am-panel-default">
        <template v-if="table.id !== null">
            <div class="am-panel-hd">
                <h3 class="am-panel-title">场所人员</h3>
            </div>
            <div class="am-panel-bd">
                <div id="personsForm"></div>
                <div class="am-scrollable-horizontal">
                    <table class="am-table am-table-striped am-table-centered am-table-hover">
                        <thead>
                        <tr>
                            <th><span>所属区域</span></th>
                            <th><span>姓名</span></th>
                            <th><span>身份证号码</span></th>
                            <th><span>联系电话</span></th>
                            <th><span>户籍地址</span></th>
                            <th><span>现居住地址</span></th>
                            <th><span>从业/学习情况</span></th>
                            <th><span>现工作/学习地点</span></th>
                            <th><span>重点人群类别</span></th>
                            <th><span>外出情况</span></th>
                            <th><span>人员状态值</span></th>
                            <th><span>备注</span></th>
                            <th><span>操作</span></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-if="persons.length === 0">
                            <td colspan="12" class="am-text-center">
                                <div>
                                    <span class="mdi-tray-alert am-text-xxxl"></span>
                                </div>
                                暂无数据
                            </td>
                        </tr>
                        <template v-else v-for="(i, index) in persons">
                            <tr>
                                <td class="am-text-center">
                                    {{i.regionFullName}}
                                </td>
                                <td class="am-text-center">
                                    {{i.name}}
                                </td>
                                <td>
                                    {{i.credentialNo}}
                                </td>
                                <td class="am-text-center">
                                    {{i.contact}}
                                </td>
                                <td class="am-text-center">
                                    {{i.hkLocation}}
                                </td>
                                <td class="am-text-center">
                                    {{i.address}}
                                </td>
                                <td class="am-text-center">
                                    {{i.careerSituation}}
                                </td>
                                <td class="am-text-center">
                                    {{i.workplace}}
                                </td>
                                <td class="am-text-center">
                                    {{i.subjectCategory}}
                                </td>
                                <td class="am-text-center">
                                    {{i.migrantSituation}}
                                </td>
                                <td class="am-text-center">
                                    {{i.occupancyState}}
                                </td>
                                <td class="am-text-center">
                                    {{i.remarks}}
                                </td>

                                <td class="am-text-middle">
                                    <a @click="_edit(index)" class="am-close am-text-primary" title="编辑">
                                        <span class="mdi-pencil"></span>
                                    </a>
                                    <a @click="_remove(index)" class="am-close am-text-danger" title="删除">
                                        <span class="mdi-close"></span>
                                    </a>
                                </td>
                            </tr>
                        </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </template>
    </div>
</div>
</div>
</body>
</html>
