<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<#include "/template-layout-lingling.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <script src="${component_searchbar_js}"></script>
    <script src="${component_member_js}"></script>
    <script src="${component_form_js}"></script>
    <script src="${component_pagination_js}"></script>
    <script src="${component_table_js}"></script>
    <script src="${hls_js}"></script>
    <style>
        video {
            display: none;
            height: 100%;
            width: 100%;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/subject/property.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body>
<@template_layout>
<div class="row">
    <div class="widget am-cf">
        <div class="widget-body am-cf">
            <div id="searchbar"></div>
            <div class="am-panel am-panel-default">
                <div id="table" class="am-panel-bd"></div>
            </div>
        </div>
    </div>
</div>

<div id="upload" style="display: none">
    <div class="am-form am-padding">
        <div class="am-alert am-alert-danger" data-am-alert>
            <p>经纬度需使用浮点数，例如：23.371527</p>
        </div>
        <p class="am-text-center">
            <script>
                document.write('<a href="https://jfm2-new.shantou.gov.cn/static/template/properties.xlsx?' + Math.random() + '" target="_blank">模板下载</a>');
            </script>
        </p>
        <div class="am-form-file">
            <button type="button" class="am-btn am-btn-sm am-btn-block am-center">
                <span class="mdi-upload"></span>
            </button>
            <input type="file" name="file"/>
        </div>
    </div>
</div>

<video controls="controls" style="display: none">
    您的浏览器不支持视频播放器
</video>
</@template_layout>
</body>
</html>