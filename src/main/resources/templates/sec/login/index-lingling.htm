<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <meta name="referrer" content="unsafe-url"/>
    <!--<script src="https://www.cmpassport.com/NumberAbility/jssdk/jssdk.min.js"></script>-->
    <script src="${rsa_jsencrypt_js}"></script>
    <style>
        .tpl-login-content {
            margin-top: 5%;
        }

        .tpl-login-logo {
            background-image: url("${path}/img/logo-cmcc.png");
            background-size: contain;
            background-repeat: no-repeat;
            height: 159px;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/sec/login/index.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body class="theme-white">
<div class="am-g tpl-g">
    <div class="tpl-login">
        <div class="tpl-login-content">
            <div class="tpl-login-logo"></div>
            <div id="app" v-cloak>
                <div v-if="options.auth.length > 0" class="am-margin-bottom am-text-center">
                    <a v-for="(i, index) in options.auth" v-if="i.show" @click="_switch(i.index);" :class="i.icon + (i.index === auth ? ' am-primary' : '')" :title="i.title" class="am-icon-btn am-icon-btn-sm am-margin-right-sm"></a>
                </div>
                <div class="am-form am-form-horizontal am-u-sm-centered">
                    <template v-if="auth === 0">
                        <div class="am-form-group am-form-icon">
                            <i class="mdi-account-outline"></i>
                            <input v-model="options.auth[0].username" type="text" class="am-form-field" placeholder="帐号" required/>
                        </div>
                        <div class="am-form-group am-form-icon">
                            <i class="mdi-lock-outline"></i>
                            <div style="position:relative;">
                                <input
                                    v-model="options.auth[0].password"
                                    :type="showPassword ? 'text' : 'password'"
                                    class="am-form-field"
                                    placeholder="密码"
                                    required
                                    style="width:100%;padding-right:38px;box-sizing:border-box;"
                                />
                                <button
                                    type="button"
                                    @click="togglePassword"
                                    style="position:absolute;top:50%;right:10px;transform:translateY(-50%);background:transparent;border:none;outline:none;cursor:pointer;padding:0;height:24px;width:24px;display:flex;align-items:center;justify-content:center;"
                                    tabindex="-1"
                                >
                                    <i :class="showPassword ? 'mdi-eye-outline' : 'mdi-eye-off-outline'"></i>
                                </button>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <div class="am-input-group">
                                <input v-model="options.auth[0].verCode" type="text" class="am-form-field" placeholder="图形验证码" required/>
                                <span class="am-input-group-label" style="padding:0;line-height:normal;">
                                    <img @click="_captcha" :src="captcha.image" style="cursor: pointer"/>
                                </span>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <div>
                                <label class="am-checkbox am-padding-top-0">
                                    <input v-model="options.auth[0].rememberMe" type="checkbox"> 保持登录状态
                                </label>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="auth === 1">
                        <div class="am-form-group am-form-icon">
                            <i class="mdi-cellphone"></i>
                            <input v-model="options.auth[1].mp" type="text" class="am-form-field" placeholder="手机号码" required/>
                        </div>
                        <div class="am-form-group">
                            <div class="am-input-group">
                                <input v-model="options.auth[1].smsCode" type="text" class="am-form-field" placeholder="短信验证码" required/>
                                <span v-if="options.auth[1].seconds !== -1" v-text="options.auth[1].seconds" class="am-input-group-label"></span>
                                <span v-else class="am-input-group-btn">
                                    <button @click="_smsCode" class="am-btn am-btn-default" type="button">
                                        <span class="mdi-email-outline"></span>
                                    </button>
                                </span>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <div>
                                <label class="am-checkbox am-padding-top-0">
                                    <input v-model="options.auth[1].rememberMe" type="checkbox"> 保持登录状态
                                </label>
                            </div>
                        </div>
                    </template>
                    <template v-if="auth < 2">
                        <div class="am-form-group">
                            <button @click="login" type="button" class="am-btn am-btn-primary am-btn-block" title="登录">
                                <span class="mdi-login"></span>
                            </button>
                        </div>
                    </template>
                    <div class="am-panel am-margin-top-xl" v-else>
                        <div class="am-panel-bd am-text-center">
                            <p>
                                <span :class="oauth.icon" class="am-icon-lg"></span>
                            </p>
                            <p v-text="oauth.msg" class="am-text-lg"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>