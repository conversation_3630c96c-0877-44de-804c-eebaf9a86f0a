<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<#include "/template-layout-lingling.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <link rel="stylesheet" href="${datetimepicker_css}"/>
    <script src="${datetimepicker_js}"></script>
    <script src="${datetimepicker_cn_js}"></script>
    <script src="${component_searchbar_js}"></script>
    <script src="${component_member_js}"></script>
    <script src="${component_form_js}"></script>
    <script src="${component_pagination_js}"></script>
    <script src="${component_table_js}"></script>
    <style>
        #departments a {
            cursor: pointer;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/emergency/unit/index.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body>
<@template_layout>
<div class="row">
    <div class="am-u-sm-12 am-u-md-3">
        <div class="widget am-cf">
            <div class="widget-head am-cf">
                <div class="widget-title am-fl">行政网格</div>
            </div>
            <div class="widget-body am-cf am-margin-top-sm">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-bd" id="departments" v-cloak>
                        <ol class="am-breadcrumb am-breadcrumb-slash am-margin-0" style="padding: 1rem 0">
                            <li>
                                <a @click="_queryOrganization(null);">
                                    <span class="mdi-home"></span>
                                </a>
                            </li>
                            <li :class="{ 'am-active': index == deptIdArr.length - 1 }" v-for="(i, index) in deptIdArr">
                                <a @click="_queryOrganization(i);" v-text="deptNameArr[index]"></a>
                            </li>
                        </ol>
                        <ul class="am-list">
                            <li v-for="i in subordinates" style="display: flex; align-items: center; justify-content: space-between">
                                <a @click="_queryOrganization(i[deptMapper.id]);" class="am-text-truncate"
                                   style="cursor:pointer" v-if="i[deptMapper.hasSubordinates]">
                                    <span class="mdi-bank-outline"></span> {{i[deptMapper.name]}}
                                </a>
                                <label v-else class="am-margin-bottom-0 am-padding-top-sm am-padding-bottom-sm">
                                    <span class="mdi-bank-outline"></span> {{i[deptMapper.name]}}
                                </label>
<!--                                <button @click="_qrcode(i);" class="mdi-qrcode am-close am-padding-top-0" type="button"></button>-->
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-u-sm-12 am-u-md-9">
        <div class="widget am-cf">
            <div class="widget-head am-cf">
                <div class="widget-title am-fl">应急单元</div>
            </div>
            <div class="widget-body am-cf am-margin-top-sm">
                <div id="searchbar"></div>
                <div class="am-panel am-panel-default">
                    <div id="table" class="am-panel-bd"></div>
                </div>
            </div>
        </div>
    </div>
</div>
</@template_layout>

<div id="qrcode" class="am-margin-sm am-text-center" style="display: none"></div>

</body>
</html>
