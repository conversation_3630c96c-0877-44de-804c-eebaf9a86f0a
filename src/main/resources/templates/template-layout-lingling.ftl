<#macro template_layout sections="false">
    <div id="layout_header" class="tpl-g am-g" v-cloak>
        <!-- 头部 -->
        <header>
            <!-- logo -->
            <div class="tpl-header-logo am-fl">
                <!--<a><img :src="layout.icon"/></a>-->
                <a>
                    <img :src="layout.icon" style="width: 48px"/>
                    <span class="am-text-md am-margin-left-sm">汕头市社会治理综合平台<br/>（内测版）</span>
                </a>
            </div>

            <div class="tpl-header-fluid">
                <!-- 导航按钮 -->
                <div @click="_nav" class="tpl-header-switch-button am-fl">
                    <span class="mdi-menu"></span>
                </div>

                <!-- 搜索 -->
                <!--<div class="tpl-header-search am-fl">
                    <div class="tpl-header-search-form">
                        <button class="tpl-header-search-btn">
                            <span class="mdi-magnify"></span>
                        </button>
                        <input class="tpl-header-search-box" type="text" placeholder="请输入关键字"/>
                    </div>
                </div>-->

                <!-- 工具栏 -->
                <div v-if="user.id !== null" class="tpl-header-navbar am-fr">
                    <ul>
                        <!-- 欢迎语 -->
                        <li class="tpl-header-navbar-welcome am-hide-sm-only am-text-sm">
                            <a class="am-padding-right-0">欢迎您，<span v-text="user.name"></span></a>
                        </li>

                        <li ref="me" class="tpl-dropdown am-dropdown">
                            <!-- 徽标 -->
                            <a ref="me-toggle" class="tpl-dropdown-toggle am-dropdown-toggle">
                                <span class="mdi-account am-show-sm-only"></span>
                                <span :class="layout.profileDropDown ? 'mdi-chevron-up' : 'mdi-chevron-down'"
                                      class="am-hide-sm-only"></span>
                            </a>

                            <!-- 列表 -->
                            <ul class="am-dropdown-content">
                                <li><a @click="_setting"><span class="mdi-account-settings-outline"></span> 个人设置</a>
                                </li>
                                <li class="am-divider"></li>
                                <li><a @click="_password"><span class="mdi-lock-outline"></span> 修改密码</a></li>
                                <li><a @click="_logout"><span class="mdi-power"></span> 注销</a></li>
                            </ul>
                        </li>

                        <!-- 通知列表 -->
                        <li ref="mailbox" class="tpl-dropdown am-dropdown">
                            <!-- 气泡 -->
                            <a ref="mailbox-toggle" class="tpl-dropdown-toggle am-dropdown-toggle">
                                <i class="mdi-email"></i>
                                <span v-if="messages.length > 0" v-text="messages.length"
                                      class="item-feed-badge am-badge am-badge-success am-round"></span>
                            </a>

                            <!-- 列表 -->
                            <ul class="tpl-dropdown-content am-dropdown-content">
                                <template v-if="messages.length === 0">
                                    <li class="tpl-dropdown-menu-messages am-text-center">
                                        <a class="tpl-dropdown-menu-messages-item am-cf">暂无消息</a>
                                    </li>
                                </template>
                                <template v-else>
                                    <li v-for="i in messages" class="tpl-dropdown-menu-messages">
                                        <a class="tpl-dropdown-menu-messages-item am-cf">
                                            <div class="menu-messages-ico">
                                                <img src="${path}/img/icon.png">
                                            </div>
                                            <div class="menu-messages-time">n小时前</div>
                                            <div class="menu-messages-content">
                                                <div class="menu-messages-content-title">
                                                    <i class="mdi-account"></i>
                                                    <span>这里是用户</span>
                                                </div>
                                                <div class="am-text-truncate">这里是标题</div>
                                                <div class="menu-messages-content-time">这里是时间</div>
                                            </div>
                                        </a>
                                    </li>

                                    <li class="tpl-dropdown-menu-messages">
                                        <a class="tpl-dropdown-menu-messages-item am-cf">
                                            <i class="mdi-dots-horizontal-circle-outline"></i> 更多
                                        </a>
                                    </li>
                                </template>
                            </ul>
                        </li>

                        <!-- 通知列表样式2 -->
                        <!--<li class="am-dropdown">
                            <a class="am-dropdown-toggle">
                                <i class="mdi-email"></i>
                                <span v-if="messages.length > 0" v-text="messages.length" class="am-round item-feed-badge am-badge am-badge-warning">99</span>
                            </a>

                            <ul class="tpl-dropdown-content am-dropdown-content">
                                <template v-if="messages.length === 0">
                                    <li class="tpl-dropdown-menu-notifications">
                                        <a class="tpl-dropdown-menu-notifications-item am-cf">暂无消息</a>
                                    </li>
                                </template>
                                <template v-else>
                                    <li v-for="i in messages" class="tpl-dropdown-menu-notifications">
                                        <a class="tpl-dropdown-menu-notifications-item am-cf">
                                            <div class="tpl-dropdown-menu-notifications-title">
                                                <i class="mdi-account"></i>
                                                <span>这里是标题</span>
                                            </div>
                                            <div class="tpl-dropdown-menu-notifications-time">这里是时间</div>
                                        </a>
                                    </li>

                                    <li class="tpl-dropdown-menu-notifications">
                                        <a class="tpl-dropdown-menu-notifications-item am-cf">
                                            <i class="mdi-dots-horizontal-circle-outline"></i> 更多
                                        </a>
                                    </li>
                                </template>
                            </ul>
                        </li>-->

                        <!-- 调色盘 -->
                        <!--<li class="tpl-dropdown am-dropdown" data-am-dropdown>
                            <a class="tpl-dropdown-toggle am-dropdown-toggle" data-am-dropdown-toggle>
                                <i class="mdi-palette"></i>
                            </a>

                            <ul class="tpl-dropdown-content am-dropdown-content" style="width: auto">
                                <li class="tpl-dropdown-menu-messages">
                                    <a class="tpl-dropdown-menu-messages-item am-cf">
                                        <div class="tpl-skiner-content-bar">
                                            <span @click="_changeTheme('theme-white');"
                                                  class="skiner-color skiner-white"></span>
                                            <span @click="_changeTheme('theme-black');"
                                                  class="skiner-color skiner-black"></span>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </li>-->

                        <!-- 退出 -->
                        <!--<li @click="_logout" class="am-text-sm">
                            <a>
                                <span class="mdi-power"></span> 注销
                            </a>
                        </li>-->
                    </ul>
                </div>
            </div>
        </header>

        <!-- 侧边栏 -->
        <div class="left-sidebar">
            <!-- 用户 -->
            <div class="tpl-sidebar-user-panel">
                <div class="tpl-user-panel-slide-toggleable">
                    <div class="tpl-user-panel-profile-picture">
                        <img :src="user.avatar"/>
                    </div>
                    <template v-if="user.id !== null">
                        <span class="user-panel-logged-in-text">
                            <i class="mdi-account-outline tpl-user-panel-status-icon am-text-success"></i> {{user.name}}
                        </span>
                        <span class="user-panel-logged-in-text">
                            <i class="mdi-sitemap-outline tpl-user-panel-status-icon am-text-success"></i> {{user.deptFullName}}
                        </span>
                        <span class="user-panel-logged-in-text">
                            <i class="mdi-cellphone tpl-user-panel-status-icon am-text-success"></i> {{user.mp === null ? '-' : user.mp}}
                        </span>
                        <!--<div>
                            <a @click="_setting" class="tpl-user-panel-action-link">
                                <span class="mdi-account-cog-outline"></span> 个人设置
                            </a>
                        </div>
                        <div>
                            <a @click="_password" class="tpl-user-panel-action-link">
                                <span class="mdi-lock-outline"></span> 修改密码
                            </a>
                        </div>-->
                    </template>
                </div>
            </div>

            <!-- 菜单 -->
            <ul class="sidebar-nav">
                <template v-for="i in menu.items">
                    <!-- 非多级菜单 -->
                    <template v-if="i.url && i.items.length === 0">
                        <li class="sidebar-nav-link">
                            <a :href="i.url" :class="menu.visited.x === i.id ? 'active sub-active' : ''">
                                <i :class="i.icon" class="sidebar-nav-link-logo"></i> {{i.title}}
                                <span v-show="$.isNumeric(i.badge) && i.badge > 0" v-text="_formatBadge(i.badge)"
                                      class="am-badge am-badge-warning sidebar-nav-link-logo-ico am-round am-fr am-margin-right-sm"></span>
                            </a>
                        </li>
                    </template>

                    <!-- 多级菜单 -->
                    <template v-else>
                        <!-- 一级菜单 -->
                        <li class="sidebar-nav-heading">
                            <span v-text="i.subtitle ? i.subtitle : ''"></span>
                            <span v-text="i.title" class="sidebar-nav-heading-info am-padding-left-0"></span>
                            <span v-show="$.isNumeric(i.badge) && i.badge > 0" v-text="_formatBadge(i.badge)"
                                  class="am-badge am-badge-warning sidebar-nav-link-logo-ico am-round am-fr am-margin-right-sm"></span>
                        </li>

                        <!-- 二级菜单 -->
                        <li v-for="j in i.items" class="sidebar-nav-link">
                            <template v-if="j.items.length > 0">
                                <a :class="menu.visited.y === j.id ? 'active' : ''" class="sidebar-nav-sub-title">
                                    <i :class="j.icon" class="sidebar-nav-link-logo"></i> {{j.title}}
                                    <span v-show="$.isNumeric(j.badge) && j.badge > 0" v-text="_formatBadge(j.badge)"
                                          class="am-badge am-badge-warning sidebar-nav-link-logo-ico am-round am-fr am-margin-right-sm"></span>
                                    <span class="am-icon-chevron-down am-margin-right-sm am-fr sidebar-nav-sub-ico"></span>
                                </a>
                                <ul class="sidebar-nav sidebar-nav-sub">
                                    <li v-for="k in j.items" class="sidebar-nav-link">
                                        <a :href="k.url" :class="menu.visited.z === k.id ? 'active sub-active' : ''">
                                            <span :class="k.icon" class="sidebar-nav-link-logo"></span> {{k.title}}
                                            <span v-show="$.isNumeric(k.badge) && k.badge > 0"
                                                  v-text="_formatBadge(k.badge)"
                                                  class="am-badge am-badge-warning sidebar-nav-link-logo-ico am-round am-fr am-margin-right-sm"></span>
                                        </a>
                                    </li>
                                </ul>
                            </template>
                            <a v-else :href="j.url" :class="menu.visited.y === j.id ? 'active sub-active' : ''">
                                <i :class="j.icon" class="sidebar-nav-link-logo"></i> {{j.title}}
                                <span v-show="$.isNumeric(j.badge) && j.badge > 0" v-text="_formatBadge(j.badge)"
                                      class="am-badge am-badge-warning sidebar-nav-link-logo-ico am-round am-fr am-margin-right-sm"></span>
                            </a>
                        </li>
                    </template>

                    <hr class="am-margin-0"/>
                </template>
            </ul>
        </div>

        <!-- 修改密码 -->
        <div name="password" class="am-panel am-panel-default am-margin-bottom-0" style="display: none">
            <div class="am-panel-bd">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <input v-model="password.original" type="password" class="am-form-field am-input-sm"
                               placeholder="原密码"/>
                    </div>
                    <div class="am-form-group">
                        <input v-model="password.input" type="password" class="am-form-field am-input-sm"
                               placeholder="新密码"/>
                    </div>
                    <div class="am-form-group">
                        <input v-model="password.confirm" type="password" class="am-form-field am-input-sm"
                               placeholder="确认新密码"/>
                    </div>
                    <div class="am-form-group">
                        <button @click="_changePassword" type="button"
                                class="am-btn am-btn-sm am-btn-block am-btn-primary am-center">
                            <span class="mdi-check"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 内容 -->
    <div class="tpl-content-wrapper">
        <#if sections == "true">
            <div class="container-fluid am-cf">
                <div class="row">
                    <#nested 1/>
                </div>
            </div>
        </#if>

        <div class="row-content am-cf">
            <#nested 2/>
        </div>
    </div>

    <style>
        /* 修正logo高度和对齐方式 */
        .tpl-header-logo > a {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 固定头部导航栏高度 */
        .tpl-header-navbar > ul > li {
            height: 56px;
        }

        /* 修正头部导航栏图标字体大小 */
        .tpl-header-navbar > ul > li > a > [class*=mdi-] {
            font-size: 2rem;
        }

        /* 修正个人中心内边距 */
        @media screen and (min-width: 641px) {
            .tpl-header-navbar-welcome + li > a {
                padding-left: 0;
            }
        }

        /* 修正个人中心下拉框外边距 */
        .tpl-header-navbar-welcome + li .am-dropdown-content {
            margin-top: 0px;
        }

        /* 修正个人中心下拉框边框 */
        .tpl-header-navbar-welcome + li .am-dropdown-content:before, .am-dropdown-content:after {
            border: unset;
        }

        /* 修正个人中心下拉框选项宽度和行高 */
        .tpl-header-navbar-welcome + li .am-dropdown-content li {
            width: 100%;
        }

        .tpl-header-navbar-welcome + li .am-dropdown-content li a {
            line-height: unset;
        }

        /* 修正侧边栏高度自适应内容和视口 */
        .left-sidebar {
            height: auto;
            min-height: 100%;
            border-right: 1px solid #eef1f5;
        }

        /* 修正个人信息字体颜色 */
        .user-panel-logged-in-text {
            color: #999;
        }

        /* 修正二级菜单内边距 */
        .sidebar-nav-heading {
            padding: 12px 17px;
        }

        .tpl-content-wrapper {
            min-height: calc(100vh - 57px); /* 修正最低高度适应视口 */
            z-index: inherit; /* 修正layer弹出层被遮挡 */
        }

        @media screen and (max-width: 1024px) {
            .tpl-content-wrapper {
                min-height: calc(100vh - 111px); /* 修正最低高度适应视口 */
            }
        }

        .widget-body {
            padding: 0;
        }

        /* 设置滑动导航为白色 */
        .am-offcanvas-bar {
            background-color: white;
        }

        .am-offcanvas-bar:after {
            background: inherit;
        }

        /* 修正搜索栏组件滑动导航被header遮挡 */
        .am-offcanvas-content {
            margin-top: 111px;
            padding-top: 15px !important;
        }

        /* 修正Actions被遮罩遮挡 */
        .am-modal-actions {
            z-index: 1210;
        }

        /* 修正select边框宽度为0 */
        .theme-white .am-form-field {
            border: 1px solid #c2cad8;
        }

        .theme-black .am-form-field {
            border: 1px solid #c2cad8;
        }
    </style>

    <script src="${rsa_jsencrypt_js}"></script>
    <script src="${watermarker_js}"></script>
    <script>
        new Vue({
            'el': '#layout_header',
            'data': {
                'layout': {
                    'title': '',
                    'icon': '${path}/img/logo-cmcc.png',
                    'profileDropDown': false
                },
                'user': {
                    'id': null,
                    'name': null,
                    'avatar': '${path}/img/avatar.png',
                    'deptFullName': null,
                    'mp': null
                },
                'password': {
                    'original': '',
                    'input': '',
                    'confirm': '',
                },

                'path': '${path}',

                // 菜单
                'menu': {
                    // 菜单项
                    'items': [],

                    // 当前菜单项索引
                    'visited': {
                        'x': '',
                        'y': '',
                        'z': ''
                    }
                },

                // 消息
                'messages': []
            },
            'methods': {
                // 点击导航按钮时显示/隐藏侧边栏
                '_nav': function () {
                    const that = this;

                    if ($(that.$el).find('.left-sidebar').is('.active')) {
                        if ($(window).width() > 1024) {
                            $('.tpl-content-wrapper').removeClass('active');
                        }

                        $(that.$el).find('.left-sidebar').removeClass('active');
                    } else {
                        if ($(window).width() > 1024) {
                            $('.tpl-content-wrapper').addClass('active');
                        }

                        $(that.$el).find('.left-sidebar').addClass('active');
                    }
                },

                // 改变主题
                '_changeTheme': function (theme) {
                    $('body').removeClass('theme-white').removeClass('theme-black').addClass(theme);
                },

                // 打开个人设置功能
                '_setting': function () {
                    window.open('${path}/sys/profile');
                },

                // 打开修改密码功能
                '_password': function () {
                    layer.open({
                        'title': '修改密码',
                        'type': 1,
                        'area': '300px',
                        'content': $(this.$el).find('div[name=password]')
                    });
                },
                // 修改密码
                '_changePassword': function () {
                    const that = this;

                    if (that.password.original === '' || that.password.input === '' || that.password.confirm === '') {
                        ModalUtil.alert('请输入有效的密码');
                        return;
                    }

                    if (that.password.input !== that.password.confirm) {
                        ModalUtil.alert('您两次输入的密码不一致，请修改');
                        return;
                    }

                    new HttpRequest().ajax('${path}/sec/login/crypto/rsa/public-key', null, {
                        'msg': {
                            'success': false
                        }
                    })
                        .then(result => {
                            return result.code === 'OK'
                                ? new HttpRequest().ajax('${path}/sys/user/password/change', {
                                    'originalPassword': CryptoUtil.encryptRsa(that.password.original, result.data),
                                    'newPassword': CryptoUtil.encryptRsa(that.password.input, result.data)
                                })
                                : Promise.resolve({
                                    'code': result.code
                                });
                        })
                        .then(result => {
                            if (result.code === 'OK') { // 修改密码成功则强制退出登录
                                that._logout();
                            }
                        });
                },

                // 注销
                '_logout': function () {
                    new HttpRequest().ajax('${path}/sec/login/cancel', null, {
                        'msg': {
                            'success': false
                        }
                    }).then(res => {
                        if (res.code === 'OK') {
                            layer.msg('注销成功，浏览器自动跳转中……', {
                                'title': false,
                                'icon': 1,
                                'time': 2000
                            }, function () {
                                window.location.replace('${path}');
                            });
                        }
                    });
                },

                // 统计
                '_statistic': function () {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('${path}/app/layout/badge', {
                            'root': false
                        }, {
                            'loading': false,
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            resolve(res.code === 'OK' ? res.data : []);
                        });
                    });
                },

                // 格式化徽标
                '_formatBadge': function (num) {
                    let _num = Math.floor(num / 10000);
                    if (_num > 0) {
                        return _num + 'w' + (_num % 10000 > 0 ? '+' : '');
                    }

                    _num = Math.floor(num / 1000);
                    if (_num > 0) {
                        return _num + 'k' + (_num % 1000 > 0 ? '+' : '');
                    }

                    return num;
                }
            },
            'mounted': function () {
                const that = this;

                $('body').addClass('theme-white');

                // 小屏自动隐藏侧边栏
                if ($(window).width() < 1025) {
                    that._nav();
                }

                new HttpRequest().ajax('${path}/app/layout/header', {
                    'root': false
                }, {
                    'loading': false,
                    'msg': {
                        'success': false
                    }
                }).then(res => {
                    if (res.code !== 'OK') {
                        return false;
                    }

                    // 设置应用名称
                    document.title = res.data.app;
                    that.layout.title = res.data.app;

                    // 设置用户
                    if (res.data.user != null) {
                        that.user.id = res.data.user.id;
                        that.user.name = res.data.user.name;
                        that.user.deptFullName = res.data.user.deptFullName;
                        that.user.mp = res.data.user.mp;

                        if (res.data.user.profilePictureId) {
                            that.user.avatar = '${path}/media/read?id=' + res.data.user.profilePictureId
                        }

                        // 添加水印
                        watermark.init({
                            'watermark_txt': res.data.user.name + (res.data.user.mp ? ('（' + res.data.user.mp + '）') : ''),
                            'watermark_width': 200
                        });
                    }

                    // 设置菜单
                    if ($.isArray(res.data.menus)) {
                        const _lv1 = [], _lv2 = [], _lv3 = [];
                        let _visited = {};

                        // 初始化各级菜单
                        $.each(res.data.menus, function (index, val) {
                            const _path = window.location.pathname;

                            if (val.url) {
                                val.url = '${path}' + val.url;

                                if (_path === val.url || _path === val.url.split('?')[0]) {
                                    _visited = val;
                                }
                            }

                            switch (val.level) {
                                case 1:
                                    val.items = [];
                                    _lv1.push(val);
                                    break;
                                case 2:
                                    val.items = [];
                                    _lv2.push(val);
                                    break;
                                case 3:
                                    _lv3.push(val);
                                    break;
                            }
                        });

                        // 更新三级菜单，设置父菜单；如果当前页面在三级菜单中则记录索引
                        $.each(_lv3, function (index, val) {
                            if (_visited.level === 3 && _visited.url == val.url) {
                                that.menu.visited.z = val.id;
                            }

                            for (let i = 0; i < _lv2.length; i++) {
                                if (val.parentId === _lv2[i].id) {
                                    _lv2[i].items.push(val);

                                    that.menu.visited.y = that.menu.visited.z === val.id ? _lv2[i].id : that.menu.visited.y;
                                    break;
                                }
                            }
                        });

                        // 更新二级菜单，排序子菜单；如果当前页面在二级菜单中则记录索引
                        $.each(_lv2, function (index, val) {
                            // 未指向页面且无子菜单，丢弃该菜单
                            if (!val.url && val.items.length === 0) {
                                return;
                            }

                            val.items.sort((i, j) => i.seq - j.seq);
                            if (_visited.level === 2 && _visited.url === val.url) {
                                that.menu.visited.y = val.id;
                            }

                            for (let i = 0; i < _lv1.length; i++) {
                                if (val.parentId === _lv1[i].id) {
                                    _lv1[i].items.push(val);

                                    that.menu.visited.x = that.menu.visited.y === val.id ? _lv1[i].id : that.menu.visited.x;
                                    break;
                                }
                            }
                        });

                        // 更新一级菜单，排序子菜单；如果当前页面在一级菜单中则记录索引
                        $.each(_lv1, function (index, val) {
                            val.items.sort((i, j) => i.seq - j.seq);
                            if (_visited.level === 1 && _visited.url === val.url) {
                                that.menu.visited.x = val.id;
                            }
                        });

                        _lv1.sort((i, j) => i.seq - j.seq);
                        $.each(_lv1, function (index, val) {
                            // 未指向页面且无子菜单，丢弃该菜单
                            if (!val.url && val.items.length === 0) {
                                return;
                            }

                            that.menu.items.push(val);
                        });
                    }

                    that.$nextTick(() => {
                        // 注册二级菜单的折叠事件
                        $(that.$el).find('.sidebar-nav-sub-title').on('click', function () {
                            $(this).siblings('.sidebar-nav-sub').slideToggle(80).end().find('.sidebar-nav-sub-ico').toggleClass('sidebar-nav-sub-ico-rotate');
                        });

                        // 打开当前的二级菜单
                        $(that.$el).find('.sidebar-nav-sub-title.active').click();

                        // 注册个人中心的折叠事件
                        $(that.$refs['me-toggle']).on('click', function () {
                            $(that.$refs.me).dropdown('toggle');
                        });

                        $(that.$refs.me).on('open.dropdown.amui', function () {
                            that.layout.profileDropDown = true;
                        });

                        $(that.$refs.me).on('close.dropdown.amui', function () {
                            that.layout.profileDropDown = false;
                        });

                        // 注册消息列表的折叠事件
                        $(that.$refs['mailbox-toggle']).on('click', function () {
                            $(that.$refs.mailbox).dropdown('toggle');
                        });
                    });

                    return true;
                }).then(success => {
                    if (!success) {
                        return;
                    }

                    that._statistic().then(items => {
                        if (items.length === 0) {
                            return;
                        }

                        // 初始化堆栈
                        let _stack = [];
                        $.each(that.menu.items, function (index, val) {
                            _stack.push(val);
                        });

                        // 前序遍历设置叶子节点徽标
                        while (_stack.length > 0) {
                            if (items.length === 0) {
                                break;
                            }

                            const _node = _stack.pop();

                            if (_node.url) {
                                for (let i = 0; i < items.length; i++) {
                                    if (_node.url === items[i].path) {
                                        _node.badge = items[i].count;

                                        break;
                                    }

                                    // 已匹配则删除
                                    if (_node.hasOwnProperty('badge')) {
                                        items.splice(i, 1);
                                    }
                                }
                            }

                            if (_node.hasOwnProperty('items') && _node.items.length > 0) {
                                $.each(_node.items, function (index, val) {
                                    _stack.push(val);
                                });
                            }
                        }

                        let _array = [];

                        _stack = []
                        $.each(that.menu.items, function (index, val) {
                            _stack.push(val);
                        });

                        // 前序遍历插入数组
                        while (_stack.length > 0) {
                            const _node = _stack.pop();

                            if (_node.hasOwnProperty('items') && _node.items.length > 0) {
                                $.each(_node.items, function (index, val) {
                                    _stack.push(val);
                                });
                            }

                            _array.push(_node);
                        }

                        // 翻转数组
                        _array = _array.reverse();

                        // 存储多级累计值
                        const _counts = [];

                        // 后序遍历设置父节点徽标
                        for (let i = 0; i < _array.length; i++) {
                            // 叶子节点则设置本级累计值
                            if ($.isNumeric(_array[i].badge)) {
                                _counts[_array[i].level - 1] = _counts[_array[i].level - 1] == null ? _array[i].badge : _counts[_array[i].level - 1] + _array[i].badge;
                            } else {
                                // 遇到新的分支则将下级累计值置空
                                if (i > 0 && _array[i - 1].level <= _array[i].level) {
                                    _counts.splice(_array[i - 1].level);
                                }

                                // 下级累计值不为空则设置当前值
                                if ($.isNumeric(_counts[_array[i].level])) {
                                    _array[i].badge = _counts[_array[i].level];

                                    // 同时设置本级累计值
                                    _counts[_array[i].level - 1] = _counts[_array[i].level - 1] == null ? _array[i].badge : _counts[_array[i].level - 1] + _array[i].badge;
                                }
                            }
                        }

                        that.$forceUpdate();
                    });
                });
            }
        });
    </script>
</#macro>