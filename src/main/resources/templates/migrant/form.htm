<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${amazeui_color_css}"/>
    <script src="${amazeui_color_js}"></script>
    <script src="${component_searchbar_js}"></script>
    <script src="${component_form_js}"></script>
    <script src="${component_pagination_js}"></script>
    <script src="${component_table_js}"></script>
    <style>
        .color-steps-item {
            width: 25%;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/migrant/form.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body class="am-padding-sm">
<div id="app" class="am-form am-form-horizontal" v-cloak>
    <fieldset disabled>
        <legend>报备单</legend>
        <div v-for="(val, key) in form" v-if="val.hasOwnProperty('title') && val.group === '报备单'"
             class="am-form-group">
            <label v-text="val.title" class="am-u-sm-2 am-form-label"></label>
            <div class="am-u-sm-10">
                <input :value="val.value" type="text"/>
            </div>
        </div>
    </fieldset>
    <fieldset disabled>
        <legend>办理记录</legend>
        <div v-if="trace.length === 0" class="am-text-center">暂无</div>
        <ul v-else class="am-comments-list">
            <li v-for="i in trace" :class="i.opinion ? 'am-comment-highlight' : ''" class="am-comment">
                <a>
                    <img src="${path}/img/avatar.png" class="am-comment-avatar" width="48" height="48"/>
                </a>
                <div class="am-comment-main">
                    <header class="am-comment-hd">
                        <div class="am-comment-meta">
                            <a class="am-comment-author">{{i.creatorName}}</a>
                            {{i.eventDesc}}
                            <time v-text="i.createTime"></time>
                        </div>
                        <div v-if="i.creatorMp" class="am-comment-actions">
                            <a :href="'tel:' + i.creatorMp" :title="i.creatorMp">
                                <span class="am-icon-phone"></span>
                            </a>
                        </div>
                    </header>
                    <div v-if="i.opinion" class="am-comment-bd">
                        <p v-text="i.opinion"></p>
                    </div>
                </div>
            </li>
        </ul>
    </fieldset>
</div>
</body>
</html>