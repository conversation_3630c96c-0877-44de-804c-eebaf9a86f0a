<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${amazeui_color_css}"/>
    <script src="${amazeui_color_js}"></script>
    <script src="${component_searchbar_js}"></script>
    <script src="${component_form_js}"></script>
    <script src="${component_pagination_js}"></script>
    <script src="${component_table_js}"></script>
    <style>
        .color-steps-item {
            width: 25%;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/citizen/form.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body class="am-padding-sm">
<div id="app" class="am-form am-form-horizontal" v-cloak>
    <div v-for="(val, key) in form" v-if="val.hasOwnProperty('title') && val.group === '报备单'"
         class="am-form-group">
        <label v-text="val.title" class="am-u-sm-2 am-form-label"></label>
        <div class="am-u-sm-10">
            <input :value="val.value" type="text"/>
        </div>
    </div>
</div>
</body>
</html>