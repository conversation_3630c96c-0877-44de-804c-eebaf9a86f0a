<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<#include "/template-layout-lingling.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <link rel="stylesheet" href="${datetimepicker_css}"/>
    <script src="${datetimepicker_js}"></script>
    <script src="${datetimepicker_cn_js}"></script>
    <script src="${component_searchbar_js}"></script>
    <script src="${component_member_js}"></script>
    <script src="${component_form_js}"></script>
    <script src="${component_pagination_js}"></script>
    <script src="${component_table_js}"></script>
    <style>
        .layui-layer-content .am-form-group:nth-child(6) {
            max-height: 150px;
            overflow-y: auto;
        }

        .layui-layer-content .am-form-group:nth-child(6) div {
            display: flex;
            flex-wrap: wrap;
            align-items: end;
        }

        .layui-layer-content .am-form-group:nth-child(6) .am-checkbox {
            width: 50%;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/citizen/grid/survey.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body>
<@template_layout>
<div class="row">
    <div class="widget am-cf">
        <div class="widget-body am-cf">
            <div id="searchbar"></div>
            <div class="am-panel am-panel-default">
                <div class="am-panel-bd">
                    <div id="app" class="am-scrollable-horizontal">
                        <table width="100%"
                               class="am-table am-table-striped am-table-centered am-table-hover dataTable">
                            <thead>
                            <tr>
                                <th class="am-text-middle am-text-primary am-text-nowrap">区县</th>
                                <th class="am-text-middle am-text-primary am-text-nowrap">镇/街道</th>
                                <th class="am-text-middle am-text-primary am-text-nowrap">社区/村居</th>
                                <th class="am-text-middle am-text-primary am-text-nowrap">网格</th>
                                <th class="am-text-middle am-text-primary am-text-nowrap">入格人数</th>
                                <th class="am-text-middle am-text-primary am-text-nowrap">已绘制网格</th>
                                <th class="am-text-middle am-text-primary am-text-nowrap">未绘制网格</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-if="items.length === 0">
                                <td colspan="7">
                                    <div>
                                        <span class="mdi-tray-alert am-text-xxxl"></span>
                                    </div>
                                    暂无数据
                                </td>
                            </tr>
                            <tr v-else v-for="i in items">
                                <td v-text="i.districtName"></td>
                                <td v-text="i.subdistrictName"></td>
                                <td v-text="i.communityName"></td>
                                <td v-text="i.gridName"></td>
                                <td v-text="i.population"></td>
                                <td v-text="i.done"></td>
                                <td v-text="i.undone"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</@template_layout>
</body>
</html>
