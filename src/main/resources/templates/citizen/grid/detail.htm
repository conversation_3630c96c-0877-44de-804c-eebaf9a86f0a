<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${tagsinput_css}"/>
    <script src="${typeahead_js}"></script>
    <script src="${tagsinput_js}"></script>
    <script src="${component_member_js}"></script>
    <style>
        .am-list li .am-close {
            padding: 1rem 0!important;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/citizen/grid/detail.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body>
<div class="am-panel am-panel-default">
    <div class="am-panel-bd">
        <div id="app" class="am-u-sm-12 am-u-md-6 am-u-lg-4 am-u-sm-centered am-padding-left-0 am-padding-right-0">
            <form class="am-form am-form-horizontal">
                <div class="am-form-group">
                    <div class="am-input-group">
                        <span class="am-input-group-label"><i class="mdi-grid"></i></span>
                        <input v-model="name" placeholder="名称" type="text" name="name" class="am-form-field">
                    </div>
                </div>
                <div class="am-form-group">
                    <div class="am-input-group">
                        <span class="am-input-group-label"><i class="mdi-archive"></i></span>
                        <input v-model="type" placeholder="专题类型" type="text" name="type" class="am-form-field">
                    </div>
                </div>
                <div class="am-form-group">
                    <div class="am-input-group">
                        <span class="am-input-group-label"><i class="mdi-sitemap"></i></span>
                        <input placeholder="成员" class="am-form-field" readonly/>
                        <span class="am-input-group-btn">
                            <button @click="_addMembers" class="am-btn am-btn-default" type="button">
                                <i class="mdi-plus"></i>
                            </button>
                        </span>
                    </div>
                </div>
                <div>
                    <ul class="am-list am-list-border">
                        <li v-for="(i, index) in members" class="am-g">
                            <a v-text="i.fullName ? i.fullName : i.name" class="am-fl"></a>
                            <a @click="_removeMember(index);" class="mdi-close am-close am-fr"></a>
                        </li>
                    </ul>
                </div>
                <div class="am-form-group">
                    <button @click="_save" type="button" class="am-btn am-btn-block am-btn-primary am-center">
                        <span class="mdi-check"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
</body>
</html>