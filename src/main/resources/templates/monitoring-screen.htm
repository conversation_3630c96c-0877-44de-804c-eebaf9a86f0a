<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<head>
    <@template_resource/>
    <style>
        body {
            overflow: hidden
        }

        #app {
            background: #fff;
            min-height: 100vh;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }

        iframe {
            height: 1080px;
            width: 1920px;
            overflow: hidden;
        }

        .layui-layer[contype=object] {
            background: rgba(8, 12, 23, 0.8);
        }

        #window {
            display: none;
        }

        #window .am-panel {
            background-color: unset;
            border: none;
            margin-bottom: 0;
        }

        #window > .am-panel-default > .am-panel-hd {
            background-color: unset;
            border: 1px solid #239CFF;
            color: #FFFFFF;
        }

        #window > .am-panel-default > .am-panel-bd {
            border: 1px solid #080C17;
        }

        .detail {
            padding-left: 0;
        }

        .detail > .am-panel {
            background: rgba(255, 0, 46, 0.1);
            border: 1px dashed rgba(255, 0, 46, 0.5) !important;
            color: #FFFFFF;
        }

        .detail-item {
            display: inline-block;
            width: 56px;
        }

        .detail-link {
            color: #FF002E;
            cursor: pointer;
        }

        .detail-link:hover {
            color: #DD514C;
        }

        .list {
            padding-right: 0;
        }

        .list .am-panel-bd {
            padding: 0;
        }

        .list .am-btn {
            padding: 0.5em;
            color: #FFFFFF;
        }

        .list .am-btn-default {
            background: rgba(67, 163, 252, 0.15);
            border: none;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/monitoring-screen.js?' + Math.random() + '"><' + '/script>');
    </script>
    <title>${title}</title>
</head>
<body>
<div id="app" v-cloak>
    <div v-if="message" class="am-alert am-alert-danger am-text-lg am-text-center" data-am-alert>
        {{ message }}
    </div>
    <template v-else-if="src">
        <iframe :src="src" ref="board" scrolling="false"></iframe>
    </template>
    <div v-else class="loading am-text-xxxl">
        <span class="mdi-loading mdi-spin"></span>
    </div>
</div>

<div id="window">
    <div class="am-panel am-panel-default">
        <div class="am-panel-hd">
            <span class="mdi-menu-right"></span> 实时告警信息
        </div>

        <div class="am-panel-bd am-g">
            <template v-if="records.length > 0">
                <div class="am-u-sm-8 detail">
                    <div class="am-panel am-panel-danger">
                        <div class="am-panel-bd">
                            <div>
                                <div class="detail-item">网&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;格</div>：{{ record.grid }}
                            </div>
                            <div>
                                <div class="detail-item">应急单元</div>：{{ record.unit }}
                            </div>
                            <div>
                                <div class="detail-item">告警类型</div>：{{ record.type }}
                            </div>
                            <div>
                                <div class="detail-item">告警时间</div>：{{ record.alarmTime }}
                            </div>

                            <div class="am-margin-top">
                                <a @click="_goto" class="detail-link">
                                    告警详情 <span class="mdi-arrow-right"></span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="am-u-sm-4 list">
                    <div class="am-panel">
                        <div class="am-panel-bd">
                            <template v-for="(i, index) in records">
                                <button :class="index === focus ? 'am-btn-primary' : 'am-btn-default'" @click="_select(index);" class="am-btn am-btn-sm am-btn-block">
                                    <div class="am-text-truncate">{{ i.deviceName }}</div>
                                </button>
                            </template>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- <div class="am-panel am-panel-danger">
        <div class="am-panel-hd">
            <span class="mdi-alarm"></span> 告警请及时处理
        </div>
        <div class="am-panel-bd">
            <div>
                <div class="desc">网格</div>
                ：XXX
            </div>
            <div>
                <div class="desc">应急单元</div>
                ：XXX
            </div>
            <div>
                <div class="desc">告警类型</div>
                ：XXX
            </div>

            <div class="am-margin-top">
                <a>
                    告警详情 <span class="mdi-arrow-right"></span>
                </a>
            </div>
        </div>
    </div> -->
</div>
</body>
</html>