<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<#include "/template-resource.ftl"/>
<#include "/template-layout-lingling.ftl"/>
<head>
    <@template_resource/>
    <link rel="stylesheet" href="${amazeui_color_css}"/>
    <script src="${amazeui_color_js}"></script>
    <script src="${component_pagination_js}"></script>
    <script src="${echarts_js}"></script>
    <style>
        .card-footer {
            display: table;
            border-top: 1px solid #e9e9e9;
            padding: 1rem;
            width: 100%;
        }

        .color-timeline-item-content {
            padding-bottom: 0;
        }

        .color-timeline-item-last {
            padding-bottom: 0;
        }

        .color-timeline-item-last .color-timeline-item-content {
            min-height: unset;
        }

        button {
            margin-left: 1rem;
        }

        .am-close {
            height: 20px;
            width: 20px;
            opacity: 0.9;
        }
    </style>
    <script>
        document.write('<script src="${path}/view/index.js?' + Math.random() + '"><' + '/script>');
    </script>
</head>
<body>
<@template_layout>
<!-- 系统指标 -->
<div id="statistics" class="row" v-cloak>
    <div class="am-u-sm-12 am-u-md-4">
        <div class="widget am-cf">
            <div class="widget-statistic-header" style="color: inherit">注册用户</div>
            <div class="widget-statistic-body">
                <div class="widget-statistic-value" style="color: inherit">{{ru}}人</div>
                <!--<div class="widget-statistic-description"></div>-->
                <span class="widget-statistic-icon mdi-card-account-details-outline"></span>
            </div>
        </div>
    </div>
    <div class="am-u-sm-12 am-u-md-4">
        <div class="widget widget-primary am-cf">
            <div class="widget-statistic-header">在线用户</div>
            <div class="widget-statistic-body">
                <div class="widget-statistic-value">{{cu}}人</div>
                <!--<div class="widget-statistic-description"></div>-->
                <span class="widget-statistic-icon mdi-account-multiple"></span>
            </div>
        </div>
    </div>
    <div class="am-u-sm-12 am-u-md-4">
        <div class="widget widget-purple am-cf">
            <div class="widget-statistic-header">用户留存率</div>
            <div class="widget-statistic-body">
                <div class="widget-statistic-value">{{urr}}%</div>
                <!--<div class="widget-statistic-description"></div>-->
                <span class="widget-statistic-icon mdi-account-clock-outline"></span>
            </div>
        </div>
    </div>
</div>

<!-- 工作台 -->
<div class="row">
    <div class="am-u-sm-12 am-u-md-12">
        <div class="widget am-cf">
            <div class="widget-head am-cf">
                <div class="widget-title am-fl am-text-primary">
                    <a class="mdi-checkbox-marked-circle-outline"></a> 工作台
                </div>
                <div class="widget-function am-fr">
                    <a id="reloadTask" class="am-close mdi-reload" title="刷新"></a>
                </div>
            </div>
            <div class="widget-body" style="min-height: 300px">
                <!-- <a id="reload" class="am-close am-margin-sm am-fr" title="刷新">
                    <span class="mdi-reload"></span>
                </a> -->
                <div class="am-tabs am-padding-sm" data-am-tabs="{noSwipe: 1}">
                    <ul class="am-tabs-nav am-nav am-nav-tabs">
                        <li class="am-active"><a>待办</a></li>
                        <li><a>已办</a></li>
                    </ul>
                    <div class="am-tabs-bd">
                        <div class="am-tab-panel am-active">
                            <div id="todo">
                                <template v-if="items.length === 0">
                                    <div class="am-text-center">
                                        <div>
                                            <span class="mdi-tray-alert am-text-xxxl"></span>
                                        </div>
                                        暂无数据
                                    </div>
                                </template>
                                <template v-else>
                                    <div v-for="i in items" class="color-margin-bottom">
                                        <div class="color-card color-card-bordered color-card-radius">
                                            <div class="color-card-head">
                                                <div class="color-card-head-title am-text-primary">
                                                    <span :class="i.subjectType === '设备告警' ? 'am-badge am-badge-warning' : 'am-badge am-badge-primary'">{{ i.subjectType }}</span>
                                                    {{i.title === null ? '未命名' : i.title}}
                                                </div>
                                            </div>
                                            <div class="color-card-body">
                                                <template v-if="i.subjectType === '村社专项管理'">
                                                    <div class="color-timeline-item-content">
                                                        <b>{{i.content}}</b>
                                                    </div>
                                                </template>
                                                <template v-else-if="i.subjectType === '综治专题管理'">
                                                    <div class="color-timeline-item-content">
                                                        <b>{{i.content}}<a href="/instability/comparison">（查看数据量对比详情）</a></b>
                                                    </div>
                                                </template>
                                                <template v-else>
                                                    <ul class="color-timeline">
                                                        <li :class="i.readTime === null ? 'color-timeline-item-last' : ''"
                                                            class="color-timeline-item">
                                                            <div class="color-timeline-item-tail"></div>
                                                            <div class="color-timeline-item-head color-timeline-item-head-blue"></div>
                                                            <div class="color-timeline-item-content">
                                                                于 <b>{{i.createTime}}</b> 派发
                                                            </div>
                                                        </li>
                                                        <template v-if="i.readTime !== null">
                                                            <li class="color-timeline-item color-timeline-item-last">
                                                                <div class="color-timeline-item-tail"></div>
                                                                <div class="color-timeline-item-head color-timeline-item-head-blue"></div>
                                                                <div class="color-timeline-item-content">
                                                                    于 <b>{{i.readTime}}</b> 签收
                                                                </div>
                                                            </li>
                                                        </template>
                                                    </ul>
                                                </template>
                                            </div>
                                            <div class="card-footer">
                                                <template v-if="i.assignee === 'USER' && (i.subjectType !== '设备告警' && i.subjectType !== '村社专项管理' && i.subjectType !== '综治专题管理')">
                                                    <button @click="_goto(i);" class="am-btn am-btn-sm am-btn-secondary am-fr">
                                                        <span class="mdi-tray-plus"></span> 收集
                                                    </button>
                                                </template>
                                                <template v-if="i.subjectType !== '村社专项管理' && i.subjectType !== '综治专题管理'">
                                                    <button @click="_complete(i);" class="am-btn am-btn-sm am-btn-secondary am-fr">
                                                        <span class="mdi-stamper"></span> 办理
                                                    </button>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                            <div name="pagination"></div>
                        </div>
                        <div class="am-tab-panel">
                            <div id="done">
                                <template v-if="items.length === 0">
                                    <div class="am-text-center">
                                        <div>
                                            <span class="mdi-tray-alert am-text-xxxl"></span>
                                        </div>
                                        暂无数据
                                    </div>
                                </template>
                                <template v-else>
                                    <div v-for="i in items" class="color-margin-bottom">
                                        <div class="color-card color-card-bordered color-card-radius">
                                            <div class="color-card-head">
                                                <div :class="i.status === 'CLOSED' ? 'am-text-danger' : 'am-text-primary'" class="color-card-head-title">
                                                    <template v-if="i.status === 'CLOSED'">
                                                        <span class="mdi-alert"></span>
                                                    </template>
                                                    <span :class="i.subjectType === '设备告警' ? 'am-badge am-badge-warning' : 'am-badge am-badge-primary'">{{ i.subjectType }}</span>
                                                    {{i.title === null ? '未命名' : i.title}}
                                                </div>
                                            </div>
                                            <div class="color-card-body">
                                                <ul class="color-timeline">
                                                    <li class="color-timeline-item">
                                                        <div class="color-timeline-item-tail"></div>
                                                        <div class="color-timeline-item-head color-timeline-item-head-blue"></div>
                                                        <div class="color-timeline-item-content">
                                                            于 <b>{{i.createTime}}</b> 派发
                                                        </div>
                                                    </li>
                                                    <template v-if="i.readTime !== null">
                                                        <li class="color-timeline-item">
                                                            <div class="color-timeline-item-tail"></div>
                                                            <div class="color-timeline-item-head color-timeline-item-head-blue"></div>
                                                            <div class="color-timeline-item-content">
                                                                于 <b>{{i.readTime}}</b> 签收
                                                            </div>
                                                        </li>
                                                    </template>
                                                    <li class="color-timeline-item color-timeline-item-last">
                                                        <div class="color-timeline-item-tail"></div>
                                                        <div class="color-timeline-item-head color-timeline-item-head-blue"></div>
                                                        <div class="color-timeline-item-content">
                                                            于 <b>{{i.completeTime}}</b> {{i.status === 'CLOSED' ? '关闭' : '完成'}}
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="card-footer">
                                                <button v-if="i.subjectType !== '设备告警'" @click="_goto(i);" class="am-btn am-btn-sm am-btn-secondary am-fr">
                                                    <span class="mdi-tray-plus"></span> 收集
                                                </button>
                                                <button @click="_view(i);" class="am-btn am-btn-sm am-btn-secondary am-fr">
                                                    <span class="mdi-magnify"></span> 查阅
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                            <div name="pagination"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!--    <div class="am-u-sm-12 am-u-md-4">-->
<!--        <div class="widget am-cf">-->
<!--            <div class="widget-head am-cf">-->
<!--                <div class="widget-title am-fl am-text-primary">-->
<!--                    <a class="mdi-tray-full"></a> 专项统计-->
<!--                </div>-->
<!--                <div class="widget-function am-fr">-->
<!--                    <a id="reloadPC" class="am-close mdi-reload" title="刷新"></a>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="widget-body">-->
<!--                <div id="pc" style="height: 300px"></div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
</div>

<!-- 数据统计 -->
<div id="index" class="row">
    <div class="am-u-sm-12">
        <div class="widget am-cf">
            <div class="widget-head am-cf">
                <div class="widget-title am-fl am-text-primary">
                    <a class="mdi-chart-box-outline"></a> 数据收集
                </div>
                <div class="widget-function am-fr">
                    <template v-if="deptIdQueue.length > 0">
                        <a @click="_back();" class="am-close mdi-arrow-u-left-top" title="刷新"></a>
                    </template>
                    <template v-else>
                        <a @click="query();" class="am-close mdi-reload" title="返回"></a>
                    </template>
                </div>
            </div>
            <div class="widget-body widget-body-lg am-fr">
                <div class="am-form am-form-horizontal am-padding am-padding-bottom-0">
                    <div style="display: flex;justify-content: end;align-items: center">
                        <label class="am-margin-bottom-0 am-padding-right-sm">
                            <a class="am-icon-btn am-icon-btn-sm">
                                <span class="mdi-filter-variant"></span>
                            </a>
                        </label>
                        <div style="width: 200px">
                            <select v-model="type" class="am-form-field">
                                <option value="profile">个人信息</option>
                                <option value="person">重点人员</option>
                                <option value="site">重点场所</option>
                                <option value="event">重点事件</option>
                                <option value="installation">重点设施</option>
                                <option value="unit">重点单位</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div ref="chart" style="height: 300px"></div>
            </div>
        </div>
    </div>
</div>
</@template_layout>
</body>
</html>
