const STATUS = {
    'options': [{
        'key': 'DRAFT',
        'value': '未审核'
    }, {
        'key': 'DENIED',
        'value': '已退回'
    }, {
        'key': 'DISPATCHED',
        'value': '任务指派'
    }, {
        'key': 'REJECTED',
        'value': '任务退回'
    }, {
        'key': 'RECEIVED',
        'value': '已接收'
    }, {
        'key': 'CLOSED',
        'value': '已关闭'
    }]
};

let searchbar, items;
let operates = [];

function operate() {
    return new Promise(resolve => {
        new HttpRequest().ajax('visit-form/operate', null, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function fuzzy(count, index, sortBy, name, credentialNo, mp, destinationRegionFullName, status, departureBeginTime, departureEndTime, beginTime, endTime) {
    const _data = {
        'count': count,
        'index': index,
        'sortBy': sortBy,
        'name': name,
        'credentialNo': credentialNo,
        'mp': mp,
        'destinationRegionFullName': destinationRegionFullName
    };

    if (status) {
        _data.status = status;
    }

    if (departureBeginTime) {
        _data.departureBeginTime = departureBeginTime;
    }

    if (departureEndTime) {
        _data.departureEndTime = departureEndTime;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('visit-form/fuzzy/all', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    switch (val.status) {
                        case 'DRAFT':
                        case 'REJECTED':
                            val['_ext_'] = {
                                'btns': [2, 3]
                            };
                            break;
                        case 'DISPATCHED':
                            val['_ext_'] = {
                                'btns': [4, 5]
                            };
                            break;
                        case 'RECEIVED':
                            val['_ext_'] = {
                                'btns': [4, 5]
                            };
                            break;
                        default:
                            val['_ext_'] = {
                                'btns': []
                            };
                    }

                    const _btns = $.grep(val['_ext_'].btns, function (val, index) {
                        switch (val) {
                            case 2:
                                return $.inArray('refuse', operates) >= 0;
                            case 3:
                                return $.inArray('close-by-subdistrict', operates) >= 0;
                            case 4:
                                return $.inArray('reject', operates) >= 0;
                            case 5:
                                return $.inArray('close-by-community', operates) >= 0;
                            default:
                                return false;
                        }
                    });

                    _btns.push(0);
                    val['_ext_'].btns = _btns;
                });
            }

            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

// 指派
function dispatch(ids, deptId, deptFullName) {
    return new Promise(resolve => {
        new HttpRequest().ajax('visit-form/dispatch', {
            'ids': ids,
            'deptId': deptId,
            'deptFullName': deptFullName
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

// 拒收
function refuse(id, opinion) {
    return new Promise(resolve => {
        new HttpRequest().ajax('visit-form/refuse', {
            'id': id,
            'opinion': opinion
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

// 任务退回
function reject(id, opinion) {
    return new Promise(resolve => {
        new HttpRequest().ajax('visit-form/reject', {
            'id': id,
            'opinion': opinion
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

// 接收
/*function receive(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('visit-form/receive', {
            'id': id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}*/

// 关闭
function close(id, opinion) {
    return new Promise(resolve => {
        new HttpRequest().ajax('visit-form/close', {
            'id': id,
            'opinion': opinion
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function exportTo(name, credentialNo, mp, destinationRegionFullName, status, departureBeginTime, departureEndTime, beginTime, endTime) {
    const _data = {
        'name': name,
        'credentialNo': credentialNo,
        'mp': mp,
        'destinationRegionFullName': destinationRegionFullName
    };

    if (status) {
        _data.status = status;
    }

    if (departureBeginTime) {
        _data.departureBeginTime = departureBeginTime;
    }

    if (departureEndTime) {
        _data.departureEndTime = departureEndTime;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    new HttpRequest().ajax('visit-form/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('报备单.xlsx', null, res.data);
        }
    });
}

function initItems() {
    operate().then(data => {
        operates = data;

        const _toolbar = []
        $.each(data, function (index, val) {
            switch (val) {
                case 'dispatch':
                    _toolbar.push({
                        'name': '派单',
                        'icon': 'mdi-target',
                        'requireSelection': true,
                        'callback': () => {
                            let _array = items.getSelected();
                            const _ids = [];
                            $.each(_array, function (index, val) {
                                _ids.push(val.id);
                            });

                            const _status = ['DENIED', 'RECEIVED', 'CLOSED'];
                            _array = $.grep(_array, function (val) {
                                return $.inArray(val.status, _status) !== -1;
                            });
                            if (_array.length > 0) {
                                ModalUtil.alert('[已退回/已接收/已关闭]状态的报备单不支持指派，请修改');
                                return;
                            }

                            Member.get({
                                'entity': 'department',
                                'max': 1,
                                'queryOrganization': deptId => {
                                    return new Promise(resolve => {
                                        new HttpRequest().ajax('/migrant/visit-form/organization', {
                                            'id': deptId
                                        }, {
                                            'msg': {
                                                'success': false
                                            }
                                        }).then(res => {
                                            const _data = res.code === 'OK' ? {
                                                'dept': res.data.dept,
                                                'subordinates': res.data.subordinates,
                                                'users': res.data.members
                                            } : {
                                                'dept': {},
                                                'subordinates': [],
                                                'users': []
                                            };

                                            resolve(_data);
                                        });
                                    });
                                },
                                'ok': departments => {
                                    return new Promise(resolve => {
                                        if (departments.length === 0) {
                                            ModalUtil.alert('请派单到社区');

                                            resolve(false);
                                        }

                                        dispatch(_ids, departments[0].id, departments[0].fullName).then(success => {
                                            if (success) {
                                                ModalUtil.msg('已成功指派任务给' + departments[0].name, {
                                                    'title': false,
                                                    'icon': 1,
                                                    'time': 1000
                                                }, function () {
                                                    items.query(items.getIndexCurrent());

                                                    resolve(success);
                                                });
                                            }
                                        });
                                    });
                                }
                            });
                        }
                    });
                    break;
            }
        });

        items = $('#table').table({
            'columns': [{
                'title': '姓名',
                'field': 'name',
                'control': 'label'
            }, {
                'title': '目的地行政区划',
                'field': 'destinationRegionFullName',
                'control': 'label'
            }, {
                'title': '指派社区/村居',
                'field': 'destinationDeptFullNameDispatched',
                'control': 'label'
            }, {
                'title': '状态',
                'field': 'status',
                'control': 'select',
                'validator': STATUS
            }, {
                'title': '创建时间',
                'field': 'createTime',
                'control': 'text',
                'sortable': true
            }],
            'multiple': true,
            'query': (count, index, sortBy) => {
                return fuzzy(count, index, sortBy, searchbar.name(), searchbar.credentialNo(), searchbar.mp(), searchbar.destinationRegionFullName(), searchbar.status(), searchbar.departureBeginTime(), searchbar.departureEndTime(), searchbar.beginTime(), searchbar.endTime());
            },
            'create': false,
            'btns': [{
                'callback': item => {
                    let _width = $(window).width();
                    if (_width < 641) {
                        _width = '90%';
                    } else if (_width < 1025) {
                        _width = '70%';
                    } else {
                        _width = '60%';
                    }

                    ModalUtil.open({
                        'type': 2,
                        'title': '报备单',
                        'area': [_width, '90%'],
                        'content': 'visit-form?id=' + item.id
                    });
                }
            }, null, {
                'name': '退回',
                'icon': 'mdi-arrow-u-left-top',
                'callback': item => {
                    ModalUtil.prompt({
                        'title': '请填写处理意见',
                        'formType': 2
                    }, function (val, index) {
                        refuse(item.id, val).then(success => {
                            if (success) {
                                items.query(items.getIndexCurrent());

                                ModalUtil.close(index);
                            }
                        });
                    });
                }
            }, {
                'name': '关闭',
                'icon': 'mdi-checkbox-marked-circle-outline',
                'callback': item => {
                    ModalUtil.prompt({
                        'title': '请填写处理意见',
                        'formType': 2
                    }, function (val, index) {
                        close(item.id, val).then(success => {
                            if (success) {
                                items.query(items.getIndexCurrent());

                                ModalUtil.close(index);
                            }
                        });
                    });
                }
            }, {
                'name': '退回',
                'icon': 'mdi-close',
                'callback': item => {
                    ModalUtil.prompt({
                        'title': '请填写处理意见',
                        'formType': 2
                    }, function (val, index) {
                        reject(item.id, val).then(success => {
                            if (success) {
                                items.query(items.getIndexCurrent());

                                ModalUtil.close(index);
                            }
                        });
                    });
                }
            }, /*{
            'name': '接收',
            'icon': 'mdi-download',
            'callback': item => {
                ModalUtil.confirm('是否接收该报备单进行处理？', {
                    'title': '拟接收'
                }, function () {
                    receive(item.id).then(success => {
                        if (success) {
                            items.query(items.getIndexCurrent());
                        }
                    });
                });
            }
        },*/ {
                'name': '关闭',
                'icon': 'mdi-checkbox-marked-circle-outline',
                'callback': item => {
                    ModalUtil.prompt({
                        'title': '请填写处理意见',
                        'formType': 2
                    }, function (val, index) {
                        close(item.id, val).then(success => {
                            if (success) {
                                items.query(items.getIndexCurrent());

                                ModalUtil.close(index);
                            }
                        });
                    });
                }
            }, {
                'name': '记录',
                'icon': 'mdi-history',
                'callback': item => {
                    let _width = $(window).width();
                    if (_width < 641) {
                        _width = '90%';
                    } else {
                        _width = '60%';
                    }

                    ModalUtil.open({
                        'title': '办理记录',
                        'type': 2,
                        'area': [_width, '90%'],
                        'content': '../form/trace?type=抵返汕人员报备&id=' + item.id
                    });
                }
            }],
            'toolbar': _toolbar
        });
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'layout': {
            'rows': 2
        },
        'conditions': [{
            'title': '姓名',
            'icon': 'mdi-account',
            'ref': 'name',
            'row': 1
        }, {
            'title': '证件号码',
            'icon': 'mdi-card-account-details-outline',
            'ref': 'credentialNo',
            'row': 1
        }, {
            'title': '手机号码',
            'icon': 'mdi-cellphone',
            'ref': 'mp',
            'row': 1
        }, {
            'title': '到访行政区及街道',
            'icon': 'mdi-map-marker-left',
            'ref': 'destinationRegionFullName',
            'row': 1
        }, {
            'title': '状态',
            'icon': 'mdi-state-machine',
            'control': 'select',
            'validator': STATUS,
            'val': 'DRAFT',
            'ref': 'status',
            'row': 1
        }, {
            'title': '抵汕开始时间',
            'icon': 'mdi-clock-start',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'departureBeginTime',
            'row': 2
        }, {
            'title': '抵汕结束时间',
            'icon': 'mdi-clock-end',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'departureEndTime',
            'row': 2
        }, {
            'title': '开始时间',
            'icon': 'mdi-clock-start',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'beginTime',
            'row': 2
        }, {
            'title': '结束时间',
            'icon': 'mdi-clock-end',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'endTime',
            'row': 2
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query',
            'row': 1
        }, {
            'title': '导出',
            'icon': 'mdi-download',
            'callback': () => {
                exportTo(searchbar.name(), searchbar.credentialNo(), searchbar.mp(), searchbar.destinationRegionFullName(), searchbar.status(), searchbar.departureBeginTime(), searchbar.departureEndTime(), searchbar.beginTime(), searchbar.endTime());
            },
            'ref': 'export',
            'row': 1
        }]
    });

    initItems();

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});
