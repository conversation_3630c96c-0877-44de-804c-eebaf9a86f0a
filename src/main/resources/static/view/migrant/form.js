function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('visit-form/get', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function trace(id, type) {
    return new Promise(resolve => {
        new HttpRequest().ajax('../form/trace', {
            'id': id,
            'type': type
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                });
            }

            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function init() {
    new Vue({
        'el': '#app',
        'data': {
            'id': window.location.querystring('id'),
            'form': {
                'name': {
                    'title': '姓名',
                    'group': '报备单',
                    'value': ''
                },
                'mp': {
                    'title': '手机号码',
                    'group': '报备单',
                    'value': ''
                },
                'credentialType': {
                    'title': '证件类型',
                    'group': '报备单',
                    'value': ''
                },
                'credentialNo': {
                    'title': '证件号码',
                    'group': '报备单',
                    'value': ''
                },
                'departure': {
                    'title': '出发地',
                    'group': '报备单',
                    'value': ''
                },
                'destinationRegionFullName': {
                    'title': '目的地行政区划',
                    'group': '报备单',
                    'value': ''
                },
                'address': {
                    'title': '抵汕地址',
                    'group': '报备单',
                    'value': ''
                },
                'destinationDeptFullNameDispatched': {
                    'title': '指派社区/村居',
                    'group': '报备单',
                    'value': ''
                },
                'departureDate': {
                    'title': '抵汕时间',
                    'group': '报备单',
                    'value': ''
                },
                'path': {
                    'title': '途径城市',
                    'group': '报备单',
                    'value': ''
                },
                'transport': {
                    'title': '使用交通工具',
                    'group': '报备单',
                    'value': ''
                },
                'lastNucleicAcidDate': {
                    'title': '最后一次核酸日期',
                    'group': '报备单',
                    'value': ''
                },
                'memo': {
                    'title': '其它说明事项',
                    'group': '报备单',
                    'value': ''
                },
                'createTime': {
                    'title': '创建时间',
                    'group': '报备单',
                    'value': ''
                }
            },
            'trace': []
        },
        'mounted': function () {
            const that = this;

            if (!that.id) {
                ModalUtil.msg('表单数据错误，即将为您重定向', {
                    'time': 2000
                }, function () {
                    window.location.replace('form');
                });
            }

            get(that.id).then(data => {
                if (data === null) {
                    return;
                }

                for (const prop in data) {
                    if (that.form.hasOwnProperty(prop)) {
                        if (that.form[prop].hasOwnProperty('value')) {
                            that.form[prop].value = data[prop];
                        } else {
                            that.form[prop] = data[prop];
                        }
                    }
                }
            });

            trace(that.id, '抵返汕人员报备').then(items => {
                that.trace.splice(0, that.trace.length);

                $.each(items, function (index, val) {
                    switch (val.event) {
                        case '任务指派':
                            val.eventDesc = val.event;
                            if (val.address) {
                                val.eventDesc += '给' + val.address;
                            }
                            break;
                        case '扫码':
                        case '现场确认':
                            val.eventDesc = val.event;
                            if (val.address) {
                                val.eventDesc += '于' + val.address;
                            }
                            break
                            break;
                        default:
                            val.eventDesc = val.event;
                    }

                    that.trace.push(val);
                });
            });
        }
    });
}

$(function () {
    init();
});