function desensitization(str,begin,end) {
    if (!str) {
        return str;
    } else if ((begin + end) >= str.length) {
        return '*';
    }

    let leftStr = str.substring(0, begin);
    let rightStr = str.substring(str.length - end);
    let midStr = str.substring(begin, str.length - end);
    let strCon = '';
    for (let i = 0; i < midStr.length; i++) {
        strCon += '*';
    }
    return leftStr + strCon + rightStr;
}

// project
function getDefinition(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('../record/get/definition', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                resolve(res.data);
            } else {
                resolve(null);
            }
        });
    });
}

// form
function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('get', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function save(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('save', item).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function init() {
    let _projectId = window.location.querystring('projectId');
    let _id = window.location.querystring('id');

    let _columns = [{
        'title': '所属行政区划',
        'field': 'regionFullName',
        'control': 'label'
    }];

    getDefinition(_projectId).then(function (data) {
        if (data) {
            let _extColumns = data;
            $.each(_extColumns, function (idx, val) {
                if (val.field === 'attachment') {
                    val.hidden = true;
                }
            });
            _columns = _columns.concat(_extColumns);
        }

        const form = $('#form').form({
            'contextPath': '../../',
            // 表单定义
            'rows': _columns
        });

        if (_id) {
            get(_id).then(data => {
                if (data) {
                    $.each(data, function(key, value) {
                        if (key.toLowerCase().indexOf('contact') > -1) {
                            data[key] = desensitization(value, 3, 4);
                        } else if (key.toLowerCase().indexOf('idcardno') > -1 || key.toLowerCase().indexOf('credentialno') > -1) {
                            data[key] = desensitization(value, 2, 2);
                        }
                    });
                    form.setData(data);

                    if (data.attachmentIds) {
                        let $_gallery = $('<ul data-am-widget="gallery" class="am-gallery am-avg-sm-2 am-avg-md-3 am-avg-lg-4 am-gallery-imgbordered" data-am-gallery="{ pureview: {target: "a"} }" ></ul>');
                        $.each(data.attachmentIds, (idx, val) => {
                            $_gallery.append(`<li>
                                <div class="am-gallery-item">
                                    <a href="/media/read?id=${val}" target="_blank" title="${val}">
                                    <img src="/media/read?id=${val}" alt="${val}"/>
                                    <h3 class="am-gallery-title">${val}</h3>
                                </div>
                            </li>`)
                        });
                        $('.am-form-file').html('').append($_gallery);

                        // DOM ready之后需重新渲染组件
                        $.each(['gallery'], function(i, m) {
                            let module = $.AMUI[m];
                            module && module.init && module.init();
                        })
                    }
                }
            });
        }
    });

}

$(function () {
    init();
});
