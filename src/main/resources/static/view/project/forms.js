let table;

function desensitization(str,begin,end) {
    if (!str) {
        return str;
    } else if ((begin + end) >= str.length) {
        return '*';
    }

    let leftStr = str.substring(0, begin);
    let rightStr = str.substring(str.length - end);
    let midStr = str.substring(begin, str.length - end);
    let strCon = '';
    for (let i = 0; i < midStr.length; i++) {
        strCon += '*';
    }
    return leftStr + strCon + rightStr;
}

function getDefinition(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('record/get/definition', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function fuzzy(count, index, projectId, creator, regions, startTime, endTime) {
    const _data = {
        count,
        index,
        projectId
    };

    if (creator) {
        _data.creator = creator;
    }

    if (regions && regions.length) {
        _data.regionFullName = regions[0]['fullName'];
    }

    if (startTime) {
        _data.startTime = startTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('form/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    $.each(val, function(key, value) {
                        if (key.toLowerCase().indexOf('contact') > -1) {
                            val[key] = desensitization(value, 3, 4);
                        } else if (key.toLowerCase().indexOf('idcardno') > -1 || key.toLowerCase().indexOf('credentialno') > -1) {
                            val[key] = desensitization(value, 2, 2);
                        }
                    });
                });

                resolve(res.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function stat(projectId, deptId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('form/statistic', {
            'projectId': projectId,
            'deptId': deptId
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            const _x = [], _y = [];

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    const _array = val.three.split('/');
                    _x.push({
                        'superiorId': val.one,
                        'id': val.two,
                        'name': _array[_array.length - 1]
                    });

                    _y.push(val.four);
                });
            }

            resolve({
                'x': _x,
                'y': _y
            });
        });
    });
}

function targetStat(projectId, deptId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('record/stat/task/target', {
            'id': projectId,
            'rootDeptId': deptId
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            const _x = [], _y = [];

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    const _array = val.three.split('/');
                    _x.push({
                        'superiorId': val.one,
                        'id': val.two,
                        'name': _array[_array.length - 1]
                    });

                    _y.push(val.four);
                });
            }

            resolve({
                'x': _x,
                'y': _y
            });
        });
    });
}

function calculatePercentage(val, total) {
    if (!total || total === 0) {
        return 0;
    } else if (val > total) {
        return 100;
    }
    let percentage = (val / total) * 100;
    const roundedPercentage = Math.round(percentage * 100) / 100;

    return roundedPercentage;
}

function init() {
    const projectId = window.location.querystring('projectId');
    if (!projectId) {
        ModalUtil.msg('参数错误，即将为您重定向', {
            'icon': 0,
            'time': 1000
        }, function () {
            window.history.go(-1);
        });

        return;
    }
    const taskId = window.location.querystring('taskId');

    const searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }, {
            'title': '创建人',
            'icon': 'mdi-account-outline',
            'control': 'text',
            'ref': 'creator'
        }, {
            'title': '开始时间',
            'icon': 'mdi-clock-start',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'startTime'
        }, {
            'title': '结束时间',
            'icon': 'mdi-clock-end',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'endTime'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                table.query();
            },
            'ref': 'query'
        }]
    });
    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            searchbar.query();
        }
    });

    const _columns = [{
        'title': '所属行政区划',
        'field': 'regionFullName',
        'control': 'label'
    }, {
        'title': '创建人员',
        'field': 'creatorName',
        'control': 'label'
    }, {
        'title': '创建时间',
        'field': 'createTime',
        'control': 'label'
    }];

    getDefinition(projectId).then(function (dto) {
        if (dto.length > 0) {
            $.each(dto, function (index, val) {
                if (val.field === 'attachment') {
                    val.hidden = true;
                }

                _columns.push(val);
            });
        }

        table = $('#table').table({
            'columns': _columns,
            'query': (count, index) => {
                return fuzzy(count, index, projectId, searchbar.creator(), searchbar.regions(), searchbar.startTime(), searchbar.endTime());
            },
            'btns': [{
                'name': '查看',
                'icon': 'mdi-text-search',
                'callback': item => {
                    ModalUtil.open({
                        'title': '查看详情',
                        'type': 2,
                        'content': 'form/view?projectId=' + item.projectId + '&id=' + item.id,
                        'end': function () {
                            table.query(table.getIndexCurrent());
                        }
                    });
                }
            }]
        });

        $('#table th').removeClass('am-text-nowrap');
    });

    const statistic = new Vue({
        'el': '#statistic',
        'data': {
            'chart': null,
            'deptIdQueue': []
        },
        'methods': {
            'query': function () {
                const that = this;

                that.chart.showLoading();

                const _deptId = that.deptIdQueue.length === 0 ? null : that.deptIdQueue[that.deptIdQueue.length - 1];

                Promise.all([stat(projectId, _deptId), targetStat(projectId, _deptId)]).then(data => {
                    if (data[0].x.length === 0) {
                        ModalUtil.msg('系统不存在下层数据，即将返回上层', null, function () {
                            that._back();
                        });
                    }

                    that.__render(data[0].x, data[0].y, data[1].y,'bar');

                    that.chart.hideLoading();
                });

                // stat(projectId, _deptId).then(data => {
                //     if (data.x.length === 0) {
                //         ModalUtil.msg('系统不存在下层数据，即将返回上层', null, function () {
                //             that._back();
                //         });
                //     }
                //
                //     that.__render(data.x, data.y, 'bar');
                //
                //     that.chart.hideLoading();
                // });
            },
            'redraw': function () {
                this.chart.resize();
            },
            '_back': function () {
                this.deptIdQueue.pop();

                this.query();
            },
            '__render': function (x, y1, y2, type) {
                const that = this;

                const _x = [];
                $.each(x, function (index, val) {
                    _x.push({
                        'value': JSON.stringify(val)
                    });
                });

                const _percentages = [];
                const _percentageMarkPoints = [];
                $.each(y2, function (idx, val) {
                    if (!val) {
                        y2[idx] = 0;
                    }
                    _percentages.push(calculatePercentage(y1[idx], val));
                    _percentageMarkPoints.push({
                        'coord': [idx, Math.max(val, y1[idx])]
                    })
                });

                const _y = [{
                    'name': '目标数',
                    'data': y2,
                    'type': type ? type : 'line',
                    'smooth': true,
                    'label': {
                        'show': true,
                        'position': type ? 'inside' : 'center'
                    },
                    'areaStyle': {},
                    'barGap': 0,
                    // markPoint: {
                    //     symbol: 'pin',
                    //     data: _percentageMarkPoints,
                    //     label: {
                    //         show: true, //开启显示
                    //         position: '',
                    //         formatter: function (params) {
                    //             // dataIndex是当前柱状图的索引
                    //             return _percentages[params.dataIndex] + '%';
                    //         },
                    //     },
                    //     symbolOffset: ['50%', '-10%'] // 偏移量
                    // }
                }, {
                    'name': '完成率',
                    'type': 'bar',
                    'barWidth': 0.01,
                    'barGap': 0,
                    'markPoint': {
                        'symbol': 'roundRect',
                        'symbolSize': [60, 40],
                        'data': _percentageMarkPoints,
                        'label': {
                            'show': true, //开启显示
                            'position': '',
                            'formatter': function (params) {
                                // dataIndex是当前柱状图的索引
                                return _percentages[params.dataIndex] + '%';
                            },
                        },
                        'symbolOffset': [0, '-70%'] // 偏移量
                    },
                    'silent': true
                }, {
                    'name': '采集数',
                    'data': y1,
                    'type': type ? type : 'line',
                    'smooth': true,
                    'label': {
                        'show': true,
                        'position': type ? 'inside' : 'center'
                    },
                    'areaStyle': {},
                    'barGap': 0
                }];

                const _option = {
                    'legend': {
                        'data': ['目标数', '采集数', '完成率'],
                        'selectedMode': false
                    },
                    // toolbox: {
                    //     feature: {
                    //         magicType: {
                    //             type: ['stack']
                    //         },
                    //         dataView: {}
                    //     }
                    // },
                    'tooltip': {
                        'show': true,
                        'formatter': params => {
                            const _obj = JSON.parse(params.name);
                            return _obj.name;
                        }
                    },
                    'xAxis': {
                        'type': 'category',
                        'boundaryGap': true,
                        'data': _x,
                        'axisLabel': {
                            'formatter': function (value, index) {
                                const _text = value ? JSON.parse(value).name : '';
                                return _text.length > 7 ? _text.substring(0, 6) + '...' : _text;
                            },
                            'rotate': 45
                        }
                    },
                    'yAxis': {
                        'type': 'value'
                    },
                    'series': _y
                };

                that.chart.setOption(_option);
            }
        },
        'mounted': function () {
            const that = this;

            that.chart = echarts.init(that.$refs.chart);
            that.chart.on('click', function (event) {
                const _json = event.name;

                if (_json) {
                    const _value = JSON.parse(_json);
                    that.deptIdQueue.push(_value.id);
                    that.query();
                }
            });

            // that.query();
        }
    })

    $('.am-tabs-nav').find('a').on('opened.tabs.amui', function (e) {
        switch ($(this).text()) {
            case '统计':
                statistic.query();
                break;
        }
    });

    $('.tpl-header-switch-button').click(function () {
        setTimeout(() => {
            statistic.redraw();
        }, 400);
    });
}

$(function () {
    init();
});
