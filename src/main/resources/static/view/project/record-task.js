function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/project/record/get', {
            'id': id,
            'brief': true
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function getTask(taskId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/task/get', {
            'id': taskId
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function getTarget(projectId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/project/target/get', {
            'projectId': projectId
        }, {
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function saveTarget(target) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/project/target/save', target, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function read(taskId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/task/read', {
            'id': taskId
        }, {
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function receive(target) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/project/record/task/receive', target, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function submit(taskId, departmentIds, memo) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/task/complete', {
            'id': taskId,
            'departmentIds': departmentIds,
            'memo': memo
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function issue(taskId, departmentIds) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/task/issue', {
            'id': taskId,
            'departmentIds': departmentIds,
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function qualify(taskId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/project/record/task/qualify', {
            'taskId': taskId
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function updateStatus(taskId, status) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/task/update-status', {
            'id': taskId,
            'status': status
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function retract(subjectId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/project/record/task/retract', {
            'subjectId': subjectId,
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function close(taskId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/project/record/task/close', {
            'taskId': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function queryDepartments(deptIds) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/sys/dept/query', {
            'ids': deptIds
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function init() {
    new Vue({
        'el': '#app',
        'data': {
            'layout': {
                'readonly': true,
                'supportSubmit': true,
                // 支持签收
                'supportReceive': false,
                // 支持设置目标值、关闭任务
                'supportTarget': false
            },

            'id': null,
            'title': null,
            'type': null,
            'quota': null,
            'deadline': null,
            'description': null,

            'taskId': window.location.querystring('id'),
            'assignee': null,
            'status': null,
            'originalDepartments': [],
            'departments': [],
            'memo': '',

            'target': {
                'min': null
            }
        },
        'methods': {
            '_choose': function () {
                const that = this;

                Member.get({
                    'entity': 'department',
                    'queryOrganization': deptId => {
                        return new Promise(resolve => {
                            new HttpRequest().ajax('../../sys/dept/organization/restricted', {
                                'id': deptId
                            }, {
                                'msg': {
                                    'success': false
                                }
                            }).then(res => {
                                const _data = res.code === 'OK' ? {
                                    'dept': res.data.dept,
                                    'subordinates': res.data.subordinates,
                                    'users': res.data.members
                                } : {
                                    'dept': {},
                                    'subordinates': [],
                                    'users': []
                                };

                                resolve(_data);
                            });
                        });
                    },
                    'ok': departments => {
                        return new Promise(resolve => {
                            if (departments.length === 0) {
                                ModalUtil.alert('请选择行政网格');

                                resolve(false);
                            }

                            $.each(departments, function (index, val) {
                                const _array = $.grep(that.originalDepartments, function (i) {
                                    return val.id === i.id;
                                });
                                // 未重复
                                if (_array.length === 0) {
                                    that.departments.push(val);
                                }
                            });

                            resolve(true);
                        });
                    }
                });
            },
            '_remove': function (index) {
                this.departments.splice(index, 1);
            },
            '_saveTarget': function () {
                saveTarget(this.target).then(success => {
                    return;
                });
            },
            '_receive': function () {
                const _target = {
                    'projectId': this.id,
                    'taskId': this.taskId
                }

                $.extend(true, _target, this.target);

                receive(_target).then(success => {
                    if (success) {
                        ModalUtil.msg('签收成功，即将刷新', {
                            'time': 2000
                        }, () => {
                            window.location.reload();
                        });
                    }
                });
            },
            '_submit': function () {
                const that = this;

                let _departmentIds = null;
                if (that.assignee === 'GROUP') {
                    _departmentIds = [];
                    $.each(that.departments, function (index, val) {
                        _departmentIds.push(val.id);
                    });

                    if (_departmentIds.length === 0) {
                        ModalUtil.alert('请选择行政网格');
                        return;
                    }
                }

                if (that.memo === null || that.memo === '') {
                    ModalUtil.alert('请填写办理意见');
                    return;
                }

                submit(that.taskId, _departmentIds, that.memo).then(success => {
                    if (success) {
                        that._exit('办理成功，即将关闭');
                    }
                });
            },
            // 组任务增发子任务
            '_issue': function () {
                const that = this;

                const _departmentIds = [];
                $.each(that.departments, function (index, val) {
                    _departmentIds.push(val.id);
                });

                if (_departmentIds.length === 0) {
                    ModalUtil.alert('请选择行政网格');
                    return;
                }

                issue(that.taskId, _departmentIds).then(success => {
                    if (success) {
                        that._exit('增发任务成功，即将关闭');
                    }
                });
            },
            // 用户任务撤回
            '_retract': function () {
                const that = this;

                // 村居管理员支持撤回全村任务
                const _promise = that.layout.supportTarget ? retract(that.id) : updateStatus(that.taskId, 'TODO');
                _promise.then(success => {
                    if (success) {
                        that._exit('撤回成功，即将关闭');
                    }
                });
            },
            // 用户任务关闭
            '_close': function () {
                const that = this;

                qualify(that.taskId).then(dto => {
                    if (dto.three) {
                        return true;
                    }

                    if (dto.one === null) {
                        ModalUtil.alert('您未签收任务，未能关闭任务');
                    } else if (dto.two !== null) {
                        ModalUtil.alert('您已收集' + dto.two + '条记录，还未达标，未能关闭任务');
                    } else {
                        ModalUtil.alert('参数错误，未能关闭任务');
                    }

                    return false;
                }).then(success => {
                    if (!success) {
                        return;
                    }

                    close(that.id).then(success => {
                        if (success) {
                            that._exit('关闭成功，即将关闭');
                        }
                    });
                });
            },
            '_exit': function (msg) {
                const _index = window.parent.layer.getFrameIndex(window.name);

                ModalUtil.msg(msg, {
                    'time': 2000
                }, () => {
                    if (_index) {
                        window.parent.layer.close(_index);
                    } else {
                        window.location.replace(window.location.contextPath + '/');
                    }
                });
            }
        },
        'mounted': function () {
            const that = this;

            if (that.taskId === null) {
                that._exit('参数错误，即将关闭');
            } else {
                // 固定顶部栏
                let _dfd = new Promise(resolve => {
                    resolve();
                });

                const _promise = getTask(that.taskId).then(dto => {
                    if (dto === null) {
                        ModalUtil.msg('系统未检索到任务[' + that.taskId + ']，即将关闭', {
                            'icon': 1,
                            'time': 1000
                        }, function () {
                            window.location.close();
                        });

                        return;
                    }

                    that.id = dto.subjectId;
                    that.assignee = dto.assignee;

                    if (dto.assignee === 'GROUP') {
                        // 未签收则签收
                        if (dto.readTime === null) {
                            read(that.taskId).then(() => {
                                return;
                            });
                        }
                    }
                    // 个人任务
                    else {
                        _dfd = getTarget(dto.subjectId);
                        _dfd.then(tuple => {
                            if (tuple === null) {
                                return;
                            }

                            that.layout.supportSubmit = tuple.two;
                            if (!that.layout.supportSubmit && !tuple.three) {
                                ModalUtil.msg('您的村居管理员还未签收任务，暂时无法办理任务', {
                                    'icon': 0
                                });
                            }

                            // 本村已签收，则其它人员签收
                            if (that.layout.supportSubmit && dto.readTime === null) {
                                read(that.taskId).then(() => {
                                    return;
                                });
                            }

                            // 本村未签收且为村居管理员，可以签收
                            that.layout.supportReceive = !tuple.one && tuple.three;

                            // 本村未签收且为村居管理员，或者本村已签收且为签收人，可以设置目标值
                            that.layout.supportTarget = that.layout.supportReceive || (tuple.one && tuple.two);

                            // 设置目标值
                            if (tuple.four !== null) {
                                that.target = tuple.four;
                            }
                        });
                    }

                    if (dto.transition) {
                        const _deptIds = JSON.parse(dto.transition);
                        queryDepartments(_deptIds).then(dto => {
                            if (dto.status === 'TODO') {
                                $.each(dto, function (index, val) {
                                    that.departments.push(val);
                                });
                            } else {
                                $.each(dto, function (index, val) {
                                    that.originalDepartments.push(val);
                                });
                            }
                        });
                    }

                    that.memo = dto.memo;
                    that.status = dto.status;

                    that.layout.readonly = dto.readonly;

                    return get(that.id);
                });

                if (_promise instanceof Promise) {
                    Promise.all([_dfd, _promise]).then(results => {
                        that.title = results[1].title;
                        that.type = $.grep([results[1].type, results[1].subtype], function (val) {
                            return val !== null
                        }).join('-');

                        that.quota = results[1].quota;
                        that.deadline = results[1].deadline;
                        that.description = results[1].description;

                        that.$nextTick(() => {
                            // 若专项工作已关闭，则只读
                            that.layout.readonly = that.layout.readonly || results[1].status === 'CLOSED';

                            // 非本人任务，跳过
                            if (that.layout.readonly) {
                                return;
                            }

                            // 任务已关闭，跳过
                            if (that.status === 'CLOSED') {
                                return;
                            }

                            $(that.$refs.topbar).sticky({
                                'top': 0
                            });
                        });
                    });
                }
            }
        }
    });
}

$(function () {
    init();
});
