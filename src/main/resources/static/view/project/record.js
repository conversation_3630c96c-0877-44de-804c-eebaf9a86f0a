let table;

function fuzzy(count, index, title, status) {
    const _data = {
        'count': count,
        'index': index,
        'title': title
    };

    if (status) {
        _data.status = status;
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('record/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    let _btns = [];
                    if (val.readonly) {
                        _btns = [2, 3];
                    } else {
                        switch (val.status) {
                            case 'DRAFT':
                                _btns = [0, 1];
                                break;
                            case 'PUBLISHED':
                                _btns = [0, 3, 4];
                                break;
                            default:
                                _btns = [0, 3];
                                break;
                        }
                    }

                    val['_ext_'] = {
                        'btns': _btns
                    };

                    // 设置超时
                    if (val.deadline) {
                        try {
                            const _deadline = new Date(val.deadline);
                            const _now = new Date();
                            if (val.status === 'PUBLISHED' && _now.getTime() > _deadline.getTime()) {
                                val.status = 'OVERTIME';
                            }
                        } catch (e) {

                        }
                    }
                });

                resolve(res.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('record/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function urge(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('record/urge', {
            'id': id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function edit(item) {
    ModalUtil.open({
        'title': item == null ? '新增' : '编辑',
        'type': 2,
        'content': 'record/editor' + (item == null ? '' : ('?id=' + item.id)),
        'end': function () {
            table.query(item == null ? null : table.getIndexCurrent());
        }
    });
}

function init() {
    const searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '标题',
            'icon': 'mdi-archive-outline',
            'control': 'text',
            'ref': 'title'
        }, {
            'title': '状态',
            'icon': 'mdi-state-machine',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': 'DRAFT',
                    'value': '待发布'
                }, {
                    'key': 'PUBLISHED',
                    'value': '进行中'
                }, {
                    'key': 'OVERTIME',
                    'value': '已截止'
                }, {
                    'key': 'CLOSED',
                    'value': '已完成'
                }]
            },
            'ref': 'status',
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                table.query();
            },
            'ref': 'query'
        }]
    });

    table = $('#table').table({
        'columns': [{
            'title': '专项工作名称',
            'field': 'title',
            'control': 'label'
        }, {
            'title': '截止日期',
            'field': 'deadline',
            'control': 'label'
        }, {
            'title': '说明',
            'field': 'description',
            'control': 'label'
        }, {
            'title': '状态',
            'field': 'status',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': 'DRAFT',
                    'value': '待发布'
                }, {
                    'key': 'PUBLISHED',
                    'value': '进行中'
                }, {
                    'key': 'OVERTIME',
                    'value': '已截止'
                }, {
                    'key': 'CLOSED',
                    'value': '已完成'
                }]
            }
        }, {
            'title': '创建人员',
            'field': 'creatorName',
            'control': 'label'
        }, {
            'title': '创建时间',
            'field': 'createTime',
            'control': 'label'
        }],
        'query': (count, index) => {
            return fuzzy(count, index, searchbar.title(), searchbar.status());
        },
        'toolbar': [{
            'name': '新增',
            'icon': 'mdi-plus',
            'callback': () => {
                edit();
            }
        }],
        'btns': [{
            'name': '编辑',
            'icon': 'mdi-square-edit-outline',
            'callback': item => {
                edit(item);
            }
        }, {
            'name': '删除',
            'icon': 'mdi-close',
            'callback': item => {
                remove(item).then(success => {
                    if (success) {
                        table.query();
                    }
                });
            }
        }, {
            'name': '查看',
            'icon': 'mdi-text-search',
            'callback': item => {
                edit(item);
            }
        }, {
            'name': '收集',
            'icon': 'mdi-tray-full',
            'callback': item => {
                window.open('form?projectId=' + item.id);
            }
        }, {
            'name': '催办',
            'icon': 'mdi-email-fast-outline',
            'callback': item => {
                urge(item.id).then();
            }
        }]
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            searchbar.query();
        }
    });
}

$(function () {
    init();
});