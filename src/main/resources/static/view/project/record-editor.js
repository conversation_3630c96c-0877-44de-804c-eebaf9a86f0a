function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('get', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                if (res.data.formDefinition) {
                    res.data.formDefinition = JSON.parse(res.data.formDefinition);
                }

                resolve(res.data);
            } else {
                resolve(null);
            }
        });
    });
}

function getType() {
    return new Promise(resolve => {
        new HttpRequest().ajax('type', null, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? JSON.parse(res.data) : null);
        });
    });
}

function save(item) {
    const _formData = new FormData();

    _formData.append('item', new Blob([JSON.stringify(item)], {
        'type': 'application/json'
    }));

    if (item.legendFiles) {
        item.legendFiles.forEach(file => _formData.append('legendFiles', file));
    }

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'content-type': 'multipart/form-data'
            }
        }).ajax('save', _formData, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function updateDeadline(id, deadline) {
    return new Promise(resolve => {
        new HttpRequest().ajax('update-deadline', {
            'id': id,
            'deadline': deadline
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function launch(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('launch', {
            'id': id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function close(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('close', {
            'id': id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

$.each(FIELDS, function (index, val) {
    if (!$.isPlainObject(val.validator)) {
        val.validator = {};
    }

    if (!$.isPlainObject(val.validator.rule)) {
        val.validator.rule = {}
    }

    if (!val.validator.rule.hasOwnProperty('required')) {
        val.validator.rule.required = false;
    }
})

let app;

function init() {
    app = new Vue({
        'el': '#app',
        'data': {
            'layout': {
                'readonly': false
            },
            'type': {},
            //
            'definition': {
                'groups': ['重点人员', '重点事件', '重点场所', '重点设施', '单位组织', '自定义'],
                'fields': FIELDS
            },
            'form': {
                'id': window.location.querystring('id'),
                'title': null,
                'type': '',
                'subtype': '',
                'deadline': null,
                'quota': null,
                'description': null,
                'guide': null,
                'formDefinition': [],
                'assignees': [],
                'readonly': false,
                'status': 'DRAFT',
                'initialized': false
            },
            'validator': null,
            'legendFiles': null
        },
        'methods': {
            '_selectType': function (key) {
                if (this.layout.readonly) {
                    return;
                }

                this.form.type = key;
            },
            '_selectSubtype': function (key) {
                if (this.layout.readonly) {
                    return;
                }

                this.form.subtype = key;
            },
            '_chooseAssignee': function () {
                const that = this;

                Member.get({
                    'entity': 'department',
                    'queryOrganization': deptId => {
                        return new Promise(resolve => {
                            new HttpRequest().ajax('../../sys/dept/organization/restricted', {
                                'id': deptId
                            }, {
                                'msg': {
                                    'success': false
                                }
                            }).then(res => {
                                const _data = res.code === 'OK' ? {
                                    'dept': res.data.dept,
                                    'subordinates': res.data.subordinates,
                                    'users': res.data.members
                                } : {
                                    'dept': {},
                                    'subordinates': [],
                                    'users': []
                                };

                                resolve(_data);
                            });
                        });
                    },
                    'ok': departments => {
                        return new Promise(resolve => {
                            if (departments.length === 0) {
                                ModalUtil.alert('请选择任务落实单位');

                                resolve(false);
                            }

                            $.each(departments, function (index, val) {
                                that.form.assignees.push(val);
                            });

                            resolve(true);
                        });
                    }
                });
            },
            '_removeAssignee': function (index) {
                this.form.assignees.splice(index, 1);
            },
            '_save': function (redirect) {
                const that = this;

                // 校验
                if (!$(that.$refs.form).valid()) {
                    return;
                }

                if (!that.form.type || !that.form.subtype) {
                    ModalUtil.alert('请选择任务类别');
                    return;
                }

                if (that.form.assignees.length === 0) {
                    ModalUtil.alert('请选择任务落实单位');
                    return;
                }

                if (that.form.formDefinition.length === 0) {
                    ModalUtil.alert('请设置任务内容');
                    return;
                }

                const _data = {
                    ...that.form
                };

                if (_data.id === null) {
                    delete _data.id;
                }

                _data.assigneeIds = [];
                $.each(that.form.assignees, function (index, val) {
                    _data.assigneeIds.push(val.id);
                });

                _data.formDefinition = JSON.parse(JSON.stringify(_data.formDefinition));
                $.each(_data.formDefinition, function (index, val) {
                   if(val.alias) {
                       val.title = val.alias;

                       delete val.alias;
                   }
                });
                _data.formDefinition = JSON.stringify(_data.formDefinition);

                _data.legendFiles = that.legendFiles;

                const _promise = save(_data);
                _promise.then(id => {
                    if (id === null) {
                        return;
                    }

                    if (redirect) {
                        if (!_data.hasOwnProperty('id')) {
                            window.location.replace('editor?id=' + id);
                        } else {
                            window.location.reload();
                        }
                    }
                });

                return _promise;
            },
            '_updateDeadline': function () {
                const that = this;

                updateDeadline(that.form.id, that.form.deadline).then(success => {
                    if (success) {
                        window.location.reload();
                    }
                });
            },
            '_launch': function () {
                const that = this;

                const _promise = that._save(false);
                if (_promise instanceof Promise) {
                    _promise.then(id => {
                        if (!id) {
                            return;
                        }

                        launch(id).then(success => {
                            if (!success) {
                                return;
                            }

                            if (that.form.id === null) {
                                window.location.replace('editor?id=' + id);
                            } else {
                                window.location.reload();
                            }
                        })
                    });
                }
            },
            '_close': function () {
                close(this.form.id).then(success => {
                    if (success) {
                        window.location.reload();
                    }
                });
            },
            '_preField': function (index) {
                const _fields = this.form.formDefinition.splice(index, 1);
                this.form.formDefinition.splice(index - 1, 0, _fields[0]);
            },
            '_postField': function (index) {
                const _fields = this.form.formDefinition.splice(index, 1);
                this.form.formDefinition.splice(index + 1, 0, _fields[0]);
            },
            '_removeField': function (index) {
                this.form.formDefinition.splice(index, 1);
            }
        },
        'mounted': function () {
            const that = this;

            $(that.$refs.topbar).sticky({
                'top': 0
            });

            const _el = $(that.$refs.deadline);
            _el.datetimepicker({
                'autoclose': true,
                'language': 'zh-CN',
                'format': 'yyyy-mm-dd hh:ii:ss',
                'startView': 'month',
                'minView': 'hour',
                'maxView': 'decade'
            }).on('changeDate', function () {
                that.form.deadline = _el.find('input:first').val();

                // 触发验证
                that.validator.element(_el.find('input:first'));
            });

            that.validator = $(that.$refs.form).validate({
                'rules': {
                    'title': {
                        'required': true
                    },
                    'description': {
                        'required': true
                    }
                },
                'errorPlacement': function (label, element) {
                    const _parent = $(element).parents('.am-form-group');
                    _parent.removeClass('am-form-success').addClass('am-form-error');

                    const _msg = $(label).text();

                    if (_parent.hasClass('am-form-icon')) {
                        if ($(element).nextAll().length > 0) {
                            $(element).nextAll().remove();
                        }

                        const _el = '<span class="am-icon-times" title="' + _msg + '"></span>';
                        $(element).after(_el);
                    }
                },
                'success': function (label, element) {
                    const _parent = $(element).parents('.am-form-group');
                    _parent.removeClass('am-form-error').addClass('am-form-success');

                    if (_parent.hasClass('am-form-icon')) {
                        if ($(element).nextAll().length > 0) {
                            $(element).nextAll().remove();
                        }

                        const _el = '<span class="am-icon-check"></span>';
                        $(element).after(_el);
                    }
                }
            });

            $(that.$refs.tabs).tabs({
                'noSwipe': 1
            });

            $(that.$el).find('.am-checkbox-inline input[type=checkbox], input[type=radio]').uCheck();

            getType().then(dto => {
                if (dto === null) {
                    return false;
                }

                // 设置任务类别参数
                that.type = dto;

                // 设置默认重点类别
                for (const prop in dto) {
                    that.form.type = prop;
                    break;
                }

                return that.form.id === null ? true : get(that.form.id);
            }).then(data => {
                if (!$.isPlainObject(data)) {
                    return;
                }

                const _formDefinition = data.formDefinition;
                data.formDefinition = [];

                // 使用待选列表中的对象加入定义数组
                $.each(_formDefinition, function (index, i) {
                    const _fields = $.grep(that.definition.fields, function (j) {
                        return i.field === j.field;
                    });

                    if (_fields.length > 0) {
                        _fields[0].alias = i.title === _fields[0].title ? null : i.title;
                        _fields[0].validator = i.validator;
                        data.formDefinition.push(_fields[0]);
                    }
                });

                if (data.legendIds && data.legendIds.length) {
                    const $fileListDiv = $('#legend-img-list');
                    data.legendIds.forEach((legendId, idx) => {
                        let $div = $(`<div id="legend-img-${idx}" class="legend-img-container"></div>`)
                        let $img = $(`<img class="am-img-thumbnail am-radius" src="/media/read?id=${legendId}" alt="${legendId}" width="140" height="140" />`);
                        let $overlay = $('<div class="legend-img-overlay"></div>');
                        let $a = $('<a href="#" class="am-close am-close-alt am-icon-times legend-img-close-btn"></a>');
                        $a.on('click', function (e) {
                            this.closest('div.legend-img-container').remove();
                            that.form.legendIds = that.form.legendIds.filter(i => i !== legendId);
                        });
                        $overlay.append($a);
                        $div.append($img, $overlay);
                        $fileListDiv.append($div);
                    });
                    $fileListDiv.parent().show();
                }

                that.form = data;

                that.layout.readonly = data.readonly || data.status !== 'DRAFT';
            });

            $('#legend-files').on('change', function () {
                const $fileListDiv = $('#legend-img-list');
                $fileListDiv.html('');
                that.legendFiles = [];
                $.each(this.files, function (idx, file) {
                    if (!/^(?:image\/)/i.test(file.type)) {
                        return;
                    }
                    that.legendFiles.push(file);
                    const fileReader = new FileReader();
                    fileReader.readAsDataURL(file);
                    fileReader.onload = () => {
                        let $div = $(`<div id="legend-img-${idx}" class="legend-img-container"></div>`)
                        let $img = $('<img class="am-img-thumbnail am-radius" width="140" height="140" />');
                        $img.attr('src', fileReader.result);
                        $img.attr('alt', file.name);
                        let $overlay = $('<div class="legend-img-overlay"></div>');
                        let $a = $('<a href="#" class="am-close am-close-alt am-icon-times legend-img-close-btn"></a>');
                        $a.on('click', function (e) {
                            this.closest('div.legend-img-container').remove();
                            that.legendFiles = that.legendFiles.filter((f, i) => f.name !== file.name);
                            if (!that.legendFiles || !that.legendFiles.length) {
                                $fileListDiv.parent().hide();
                            }
                        });
                        $overlay.append($a);
                        $div.append($img, $overlay);
                        $fileListDiv.append($div);
                    }
                });
                if (that.legendFiles && that.legendFiles.length) {
                    $fileListDiv.parent().show();
                } else {
                    $fileListDiv.parent().hide();
                }
            })
        }
    });
}

$(function () {
    init();
});
