const FIELDS = [{
    'title': '姓名',
    'field': 'personName',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '性别',
    'field': 'sex',
    'control': 'radio',
    'group': '重点人员',
    'validator': {
        'options': {
            'true': '男性',
            'false': '女性'
        }
    }
}, {
    'title': '年龄',
    'field': 'age',
    'control': 'number',
    'group': '重点人员',
    'validator': {}
}, {
    'title': '出生年月',
    'field': 'dateOfBirth',
    'control': 'datetime',
    'group': '重点人员',
    'validator': {
        'format': 'yyyy-MM-dd'
    }
}, {
    'title': '证件类型',
    'field': 'credentialType',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '身份证',
            'value': '身份证'
        }, {
            'key': '其它',
            'value': '其它'
        }]
    }
}, {
    'title': '证件号码',
    'field': 'credentialNo',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 64
        }
    }
}, {
    'title': '国籍',
    'field': 'nationality',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 64
        }
    }
}, {
    'title': '民族',
    'field': 'ethnicGroup',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 64
        }
    }
}, {
    'title': '居住类型',
    'field': 'stay',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '常住',
            'value': '常住'
        }, {
            'key': '暂住',
            'value': '暂住'
        }]
    }
}, {
    'title': '居住地址',
    'field': 'residentialAddress',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 255
        }
    }
}, {
    'title': '重点人员状态',
    'field': 'majorPersonState',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '在管',
            'value': '在管'
        }, {
            'key': '失管',
            'value': '失管'
        }, {
            'key': '已故',
            'value': '已故'
        }]
    }
}, {
    'title': '是否低保户',
    'field': 'lowIncomeFamily',
    'control': 'radio',
    'group': '重点人员',
    'validator': {
        'options': {
            'true': '是',
            'false': '否'
        }
    }
}, {
    'title': '监护人姓名',
    'field': 'guardianName',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '与患者关系',
    'field': 'guardianRelationship',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 16
        }
    }
}, {
    'title': '最后一次服药',
    'field': 'lastMedicineTime',
    'control': 'datetime',
    'group': '重点人员',
    'validator': {
        'format': 'yyyy-MM-dd'
    }
}, {
    'title': '监护人联系方式',
    'field': 'guardianContact',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 16
        }
    }
}, {
    'title': '危险性等级',
    'field': 'riskLevel',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '0级',
            'value': '0级'
        }, {
            'key': '1级',
            'value': '1级'
        }, {
            'key': '2级',
            'value': '2级'
        }, {
            'key': '3级',
            'value': '3级'
        }, {
            'key': '4级',
            'value': '4级'
        }, {
            'key': '5级',
            'value': '5级'
        }]
    }
}, {
    'title': '最后一次尿检',
    'field': 'lastUrineTime',
    'control': 'datetime',
    'group': '重点人员',
    'validator': {
        'format': 'yyyy-MM-dd'
    }
}, {
    'title': '释放时间',
    'field': 'releasedTime',
    'control': 'datetime',
    'group': '重点人员',
    'validator': {
        'format': 'yyyy-MM-dd'
    }
}, {
    'title': '上访事由',
    'field': 'petition',
    'control': 'textarea',
    'group': '重点人员',
    'validator': {}
}, {
    'title': '是否签订《息诉罢访协议书》',
    'field': 'achieveAgreement',
    'control': 'radio',
    'group': '重点人员',
    'validator': {
        'options': {
            'true': '是',
            'false': '否'
        }
    }
}, {
    'title': '矫正开始时间',
    'field': 'rectifyFrom',
    'control': 'datetime',
    'group': '重点人员',
    'validator': {
        'format': 'yyyy-MM-dd'
    }
}, {
    'title': '矫正期限',
    'field': 'rectifyTimeLimit',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 16
        }
    }
}, {
    'title': '详细地址',
    'field': 'personDetailedAddress',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 255
        }
    }
}, {
    'title': '户籍地址',
    'field': 'householdRegistrationAddress',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 255
        }
    }
}, {
    'title': '户口所在地',
    'field': 'householdRegistrationLocation',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 255
        }
    }
}, {
    'title': '政治面貌',
    'field': 'politicalStatus',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 8
        }
    }
}, {
    'title': '婚姻情况',
    'field': 'maritalStatus',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 8
        }
    }
}, {
    'title': '联系方式',
    'field': 'contact',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 16
        }
    }
}, {
    'title': '学历',
    'field': 'education',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 16
        }
    }
}, {
    'title': '学位',
    'field': 'degree',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 16
        }
    }
}, {
    'title': '毕业院校',
    'field': 'university',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 64
        }
    }
}, {
    'title': '毕业日期',
    'field': 'dateOfGraduation',
    'control': 'datetime',
    'group': '重点人员',
    'validator': {
        'format': 'yyyy-MM-dd'
    }
}, {
    'title': '工作经历',
    'field': 'workExperience',
    'control': 'textarea',
    'group': '重点人员',
    'validator': {}
}, {
    'title': '工作单位',
    'field': 'workplace',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 255
        }
    }
}, {
    'title': '人员类别',
    'field': 'actor',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '亲属',
            'value': '亲属'
        }, {
            'key': '朋友',
            'value': '朋友'
        }, {
            'key': '同事',
            'value': '同事'
        }, {
            'key': '社区工作人员',
            'value': '社区工作人员',
        }, {
            'key': '网格员',
            'value': '网格员'
        }, {
            'key': '其他',
            'value': '其他'
        }]
    }
}, {
    'title': '人员状态',
    'field': 'personState',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '在册',
            'value': '在册'
        }, {
            'key': '外出',
            'value': '外出'
        }, {
            'key': '失踪',
            'value': '失踪'
        }, {
            'key': '已故',
            'value': '已故'
        }]
    }
}, {
    'title': '严重精神障碍患者人数',
    'field': 'numOfPeopleWithMentalIllness',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '刑释安置帮教人员人数',
    'field': 'numOfFormerPrisoner',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '社区矫正对象人数',
    'field': 'numOfCommunityCorrectionPeople',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '吸毒人员人数',
    'field': 'numOfDrugAddict',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '重点青少年人数',
    'field': 'numOfTargetedYouth',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '易受侵害妇女人数',
    'field': 'numOfVulnerableWoman',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '特困户、低保在册人员人数',
    'field': 'numOfLowIncomeFamily',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '孕产妇人数',
    'field': 'numOfPregnantWoman',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '独居残疾人数',
    'field': 'numOfPeopleWithDisabilities',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '独居老人人数',
    'field': 'numOfOlderAdultsLivingAlone',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '重症人数',
    'field': 'numOfPeopleWithSeriousIllness',
    'control': 'number',
    'default': 0,
    'group': '重点人员',
    'validator': {}
}, {
    'title': '相关人1姓名',
    'field': 'relatedPersonName1',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人1身份证号码',
    'field': 'relatedPersonIdCardNo1',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人1状态',
    'field': 'relatedPersonState1',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '入住',
            'value': '入住'
        }, {
            'key': '离开',
            'value': '离开'
        }]
    }
}, {
    'title': '相关人2姓名',
    'field': 'relatedPersonName2',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人2身份证号码',
    'field': 'relatedPersonIdCardNo2',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人2状态',
    'field': 'relatedPersonState2',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '入住',
            'value': '入住'
        }, {
            'key': '离开',
            'value': '离开'
        }]
    }
}, {
    'title': '相关人3姓名',
    'field': 'relatedPersonName3',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人3身份证号码',
    'field': 'relatedPersonIdCardNo3',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人3状态',
    'field': 'relatedPersonState3',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '入住',
            'value': '入住'
        }, {
            'key': '离开',
            'value': '离开'
        }]
    }
}, {
    'title': '相关人4姓名',
    'field': 'relatedPersonName4',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人4身份证号码',
    'field': 'relatedPersonIdCardNo4',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人4状态',
    'field': 'relatedPersonState4',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '入住',
            'value': '入住'
        }, {
            'key': '离开',
            'value': '离开'
        }]
    }
}, {
    'title': '相关人5姓名',
    'field': 'relatedPersonName5',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人5身份证号码',
    'field': 'relatedPersonIdCardNo5',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人5状态',
    'field': 'relatedPersonState5',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '入住',
            'value': '入住'
        }, {
            'key': '离开',
            'value': '离开'
        }]
    }
}, {
    'title': '相关人6姓名',
    'field': 'relatedPersonName6',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人6身份证号码',
    'field': 'relatedPersonIdCardNo6',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人6状态',
    'field': 'relatedPersonState6',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '入住',
            'value': '入住'
        }, {
            'key': '离开',
            'value': '离开'
        }]
    }
}, {
    'title': '相关人7姓名',
    'field': 'relatedPersonName7',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人7身份证号码',
    'field': 'relatedPersonIdCardNo7',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人7状态',
    'field': 'relatedPersonState7',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '入住',
            'value': '入住'
        }, {
            'key': '离开',
            'value': '离开'
        }]
    }
}, {
    'title': '相关人8姓名',
    'field': 'relatedPersonName8',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人8身份证号码',
    'field': 'relatedPersonIdCardNo8',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人8状态',
    'field': 'relatedPersonState8',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '入住',
            'value': '入住'
        }, {
            'key': '离开',
            'value': '离开'
        }]
    }
}, {
    'title': '相关人9姓名',
    'field': 'relatedPersonName9',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人9身份证号码',
    'field': 'relatedPersonIdCardNo9',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人9状态',
    'field': 'relatedPersonState9',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '入住',
            'value': '入住'
        }, {
            'key': '离开',
            'value': '离开'
        }]
    }
}, {
    'title': '相关人10姓名',
    'field': 'relatedPersonName10',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人10身份证号码',
    'field': 'relatedPersonIdCardNo10',
    'control': 'text',
    'group': '重点人员',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '相关人10状态',
    'field': 'relatedPersonState10',
    'control': 'select',
    'group': '重点人员',
    'validator': {
        'options': [{
            'key': '入住',
            'value': '入住'
        }, {
            'key': '离开',
            'value': '离开'
        }]
    }
}, {
    'title': '事件名称',
    'field': 'eventName',
    'control': 'text',
    'group': '重点事件',
    'validator': {
        'rule': {
            'maxlength': 128
        }
    }
}, {
    'title': '事发地',
    'field': 'eventAddress',
    'control': 'text',
    'group': '重点事件',
    'validator': {
        'rule': {
            'maxlength': 255
        }
    }
}, {
    'title': '事件概述',
    'field': 'eventDescription',
    'control': 'textarea',
    'group': '重点事件',
    'validator': {}
}, {
    'title': '场所名称',
    'field': 'siteName',
    'control': 'text',
    'group': '重点场所',
    'validator': {
        'rule': {
            'maxlength': 128
        }
    }
}, {
    'title': '营业执照',
    'field': 'licenseNo',
    'control': 'text',
    'group': '重点场所',
    'validator': {
        'rule': {
            'maxlength': 64
        }
    }
}, {
    'title': '营业执照有效期',
    'field': 'expiryDateOfLicense',
    'control': 'datetime',
    'group': '重点场所',
    'validator': {
        'format': 'yyyy-MM-dd'
    }
}, {
    'title': '负责人姓名',
    'field': 'personInCharge',
    'control': 'text',
    'group': '重点场所',
    'validator': {
        'rule': {
            'maxlength': 32
        }
    }
}, {
    'title': '身份证号码',
    'field': 'personInChargeCredentialNo',
    'control': 'text',
    'group': '重点场所',
    'validator': {
        'rule': {
            'maxlength': 64
        }
    }
}, {
    'title': '联系电话',
    'field': 'personInChargeContact',
    'control': 'text',
    'group': '重点场所',
    'validator': {
        'rule': {
            'maxlength': 16
        }
    }
}, {
    'title': '详细地址',
    'field': 'detailedAddress',
    'control': 'text',
    'group': '重点场所',
    'validator': {
        'rule': {
            'maxlength': 255
        }
    }
}, {
    'title': '场所状态',
    'field': 'siteState',
    'control': 'text',
    'group': '重点场所',
    'validator': {
        'rule': {
            'maxlength': 16
        }
    }
}, {
    'title': '权属',
    'field': 'ownerType',
    'control': 'select',
    'group': '重点场所',
    'validator': {
        'options': [{
            'key': '公产房',
            'value': '公产房'
        }, {
            'key': '私产',
            'value': '私产'
        }, {
            'key': '侨产',
            'value': '侨产'
        }, {
            'key': '单位产',
            'value': '单位产'
        }]
    }
}, {
    'title': '房间数',
    'field': 'numOfRooms',
    'control': 'number',
    'group': '重点场所',
    'validator': {}
}, {
    'title': '常住人口数',
    'field': 'permanentPopulation',
    'control': 'number',
    'group': '重点场所',
    'validator': {}
}, {
    'title': '外来人口数',
    'field': 'externalPopulation',
    'control': 'number',
    'group': '重点场所',
    'validator': {}
}, {
    'title': '占地面积',
    'field': 'area',
    'control': 'number',
    'group': '重点场所',
    'validator': {}
}, {
    'title': '是否使用煤气',
    'field': 'gas',
    'control': 'radio',
    'group': '重点场所',
    'validator': {
        'options': {
            'true': '是',
            'false': '否'
        }
    }
}, {
    'title': '是否使用瓶装煤气',
    'field': 'bottledGas',
    'control': 'radio',
    'group': '重点场所',
    'validator': {
        'options': {
            'true': '是',
            'false': '否'
        }
    }
}, {
    'title': '是否存在煤气泄漏隐患',
    'field': 'gasLeakRisk',
    'control': 'radio',
    'group': '重点场所',
    'validator': {
        'options': {
            'true': '是',
            'false': '否'
        }
    }
}, {
    'title': '是否存在煤气泄漏问题',
    'field': 'gasLeakProblem',
    'control': 'radio',
    'group': '重点场所',
    'validator': {
        'options': {
            'true': '是',
            'false': '否'
        }
    }
}, {
    'title': '是否已拆除、已倒塌、已封堵、已加固',
    'field': 'dangerousBuildingState',
    'control': 'select',
    'group': '重点场所',
    'validator': {
        'options': [{
            'key': '否',
            'value': '否'
        }, {
            'key': '已拆除',
            'value': '已拆除'
        }, {
            'key': '已倒塌',
            'value': '已倒塌'
        }, {
            'key': '已封堵',
            'value': '已封堵'
        }, {
            'key': '已加固',
            'value': '已加固'
        }]
    }
}, {
    'title': '设施名称',
    'field': 'installationName',
    'control': 'text',
    'group': '重点设施',
    'validator': {
        'rule': {
            'maxlength': 128
        }
    }
}, {
    'title': '设施地址',
    'field': 'installationAddress',
    'control': 'text',
    'group': '重点设施',
    'validator': {
        'rule': {
            'maxlength': 255
        }
    }
}, {
    'title': '单位名称',
    'field': 'unitName',
    'control': 'text',
    'group': '单位组织',
    'validator': {
        'rule': {
            'maxlength': 128
        }
    }
}, {
    'title': '单位/组织地址',
    'field': 'unitAddress',
    'control': 'text',
    'group': '单位组织',
    'validator': {
        'rule': {
            'maxlength': 255
        }
    }
}, {
    'title': '相关问题描述',
    'field': 'description',
    'control': 'textarea',
    'group': '自定义',
    'validator': {}
}, {
    'title': '任务时间',
    'field': 'patrolTime',
    'control': 'datetime',
    'group': '自定义',
    'validator': {
        'format': 'yyyy-MM-dd'
    }
}, {
    'title': '详细地址',
    'field': 'address',
    'control': 'text',
    'group': '自定义',
    'validator': {
        'rule': {
            'maxlength': 255
        }
    }
}, {
    'title': '其他说明',
    'field': 'remarks',
    'control': 'textarea',
    'group': '自定义',
    'validator': {}
}, {
    'title': '备注',
    'field': 'memo',
    'control': 'textarea',
    'group': '自定义',
    'validator': {}
}, {
    'title': '佐证材料',
    'field': 'attachment',
    'control': 'file',
    'group': '自定义',
    'validator': {
        'multiple': true
    }
}, {
    'title': '是否正常登录',
    'field': 'normalLogin',
    'control': 'radio',
    'group': '自定义',
    'validator': {
        'options': {
            'true': '是',
            'false': '否'
        }
    }
}, {
    'title': '能否熟练操作',
    'field': 'knowOperation',
    'control': 'radio',
    'group': '自定义',
    'validator': {
        'options': {
            'true': '是',
            'false': '否'
        }
    }
}, {
    'title': '规格（尺寸）',
    'field': 'size',
    'control': 'text',
    'group': '自定义',
    'validator': {
        'rule': {
            'maxlength': 64
        }
    }
}, {
    'title': '户外LED屏-类型',
    'field': 'ledBoardType',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '彩色',
            'value': '彩色'
        }, {
            'key': '单色',
            'value': '单色'
        }, {
            'key': '纯文字',
            'value': '纯文字'
        }]
    }
}, {
    'title': '户外LED屏-内容更新方式',
    'field': 'ledBoardWayToUpdate',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': 'USB',
            'value': 'USB'
        }, {
            'key': 'SD卡',
            'value': 'SD卡'
        }, {
            'key': '网络远程控制',
            'value': '网络远程控制'
        }, {
            'key': '云端管理平台',
            'value': '云端管理平台'
        }, {
            'key': '电脑单机',
            'value': '电脑单机'
        }, {
            'key': '蓝牙',
            'value': '蓝牙'
        }, {
            'key': 'WIFI',
            'value': 'WIFI'
        }]
    }
}, {
    'title': '户外LED屏-当前设置的内容更新方式',
    'field': 'ledBoardSelectedWayToUpdate',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': 'USB',
            'value': 'USB'
        }, {
            'key': 'SD卡',
            'value': 'SD卡'
        }, {
            'key': '网络远程控制',
            'value': '网络远程控制'
        }, {
            'key': '云端管理平台',
            'value': '云端管理平台'
        }, {
            'key': '电脑单机',
            'value': '电脑单机'
        }, {
            'key': '蓝牙',
            'value': '蓝牙'
        }, {
            'key': 'WIFI',
            'value': 'WIFI'
        }]
    }
}, {
    'title': '户外LED屏-密码策略',
    'field': 'ledBoardPasswordPolicy',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '强密码',
            'value': '强密码'
        }, {
            'key': '弱密码',
            'value': '弱密码'
        }, {
            'key': '初始密码',
            'value': '初始密码'
        }]
    }
}, {
    'title': '户外LED屏-展示内容',
    'field': 'ledBoardContentType',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '标语口号',
            'value': '标语口号'
        }, {
            'key': '公益宣传',
            'value': '公益宣传'
        }, {
            'key': '商业广告',
            'value': '商业广告'
        }, {
            'key': '其他',
            'value': '其他'
        }]
    }
}, {
    'title': '是否1',
    'field': 'yesOrNo1',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '是',
            'value': '是'
        }, {
            'key': '否',
            'value': '否'
        }]
    }
}, {
    'title': '是否2',
    'field': 'yesOrNo2',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '是',
            'value': '是'
        }, {
            'key': '否',
            'value': '否'
        }]
    }
}, {
    'title': '是否3',
    'field': 'yesOrNo3',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '是',
            'value': '是'
        }, {
            'key': '否',
            'value': '否'
        }]
    }
}, {
    'title': '是否4',
    'field': 'yesOrNo4',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '是',
            'value': '是'
        }, {
            'key': '否',
            'value': '否'
        }]
    }
}, {
    'title': '是否5',
    'field': 'yesOrNo5',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '是',
            'value': '是'
        }, {
            'key': '否',
            'value': '否'
        }]
    }
}, {
    'title': '否是1',
    'field': 'noOrYes1',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '否',
            'value': '否'
        }, {
            'key': '是',
            'value': '是'
        }]
    }
}, {
    'title': '否是2',
    'field': 'noOrYes2',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '否',
            'value': '否'
        }, {
            'key': '是',
            'value': '是'
        }]
    }
}, {
    'title': '否是3',
    'field': 'noOrYes3',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '否',
            'value': '否'
        }, {
            'key': '是',
            'value': '是'
        }]
    }
}, {
    'title': '否是4',
    'field': 'noOrYes4',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '否',
            'value': '否'
        }, {
            'key': '是',
            'value': '是'
        }]
    }
}, {
    'title': '否是5',
    'field': 'noOrYes5',
    'control': 'select',
    'group': '自定义',
    'validator': {
        'options': [{
            'key': '否',
            'value': '否'
        }, {
            'key': '是',
            'value': '是'
        }]
    }
}];
