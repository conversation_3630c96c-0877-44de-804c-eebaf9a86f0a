const todo = {
    'table': null,
    'pagination': null
};

const done = {
    'table': null,
    'pagination': null
}

let index, pc;

function query(count, index, todo) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/workbench/todo', {
            'count': count,
            'index': index,
            'todo': todo
        }, {
            'msg': {
                'success': false
            }
        }).then(result => {
            if (result.code === 'OK') {
                $.each(result.data.items, function (index, val) {
                });

                resolve(result.data);
            } else {
                resolve({
                    'total': 0,
                    'items': []
                });
            }
        });
    });
}

function stat() {
    return new Promise(resolve => {
        new HttpRequest().ajax('sys/stat/operation', null, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function statProfile(deptId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('citizen/profile/statistic/tf', {
            'deptId': deptId
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            const _x = [], _y = [];

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    const _array = val.three.split('/');
                    _x.push({
                        'superiorId': val.one,
                        'id': val.two,
                        'name': _array[_array.length - 1]
                    });

                    // 设置根节点id
                    /*if (TF_DEPT_ID_QUEUE.length === 0) {
                        TF_DEPT_ID_QUEUE.push(val.one);
                    }*/

                    _y.push(val.four);
                });

                resolve({
                    'x': _x,
                    'y': _y
                });
            } else {
                resolve({});
            }
        });
    });
}

function statPerson(deptId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('subject/stats/person', {
            'deptId': deptId
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            const _x = [], _y = [];

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    // const _array = val.three.split('/');
                    _x.push({
                        'superiorId': val[0],
                        'id': val[1],
                        'name': val[2]
                    });

                    // 设置根节点id
                    /*if (TF_DEPT_ID_QUEUE.length === 0) {
                        TF_DEPT_ID_QUEUE.push(val.one);
                    }*/

                    _y.push(val[3]);
                });

                resolve({
                    'x': _x,
                    'y': _y
                });
            } else {
                resolve({});
            }
        });
    });
}

function statSite(deptId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('subject/stats/place', {
            'deptId': deptId
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            const _x = [], _y = [];

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    // const _array = val.three.split('/');
                    _x.push({
                        'superiorId': val[0],
                        'id': val[1],
                        'name': val[2]
                    });

                    // 设置根节点id
                    /*if (TF_DEPT_ID_QUEUE.length === 0) {
                        TF_DEPT_ID_QUEUE.push(val.one);
                    }*/

                    _y.push(val[3]);
                });
                
                resolve({
                    'x': _x,
                    'y': _y
                });
            } else {
                resolve({});
            }
        });
    });
}

function statEvent(deptId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('subject/stats/event', {
            'deptId': deptId
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            const _x = [], _y = [];

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    // const _array = val.three.split('/');
                    _x.push({
                        'superiorId': val[0],
                        'id': val[1],
                        'name': val[2]
                    });

                    // 设置根节点id
                    /*if (TF_DEPT_ID_QUEUE.length === 0) {
                        TF_DEPT_ID_QUEUE.push(val.one);
                    }*/

                    _y.push(val[3]);
                });

                resolve({
                    'x': _x,
                    'y': _y
                });
            } else {
                resolve({});
            }
        });
    });
}

function statInstallation(deptId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('subject/stats/property', {
            'deptId': deptId
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            const _x = [], _y = [];

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    // const _array = val.three.split('/');
                    _x.push({
                        'superiorId': val[0],
                        'id': val[1],
                        'name': val[2]
                    });

                    // 设置根节点id
                    /*if (TF_DEPT_ID_QUEUE.length === 0) {
                        TF_DEPT_ID_QUEUE.push(val.one);
                    }*/

                    _y.push(val[3]);
                });
                
                resolve({
                    'x': _x,
                    'y': _y
                });
            } else {
                resolve({});
            }
        });
    });
}

function statUnit(deptId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('subject/stats/unit', {
            'deptId': deptId
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            const _x = [], _y = [];

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    // const _array = val.three.split('/');
                    _x.push({
                        'superiorId': val[0],
                        'id': val[1],
                        'name': val[2]
                    });

                    // 设置根节点id
                    /*if (TF_DEPT_ID_QUEUE.length === 0) {
                        TF_DEPT_ID_QUEUE.push(val.one);
                    }*/

                    _y.push(val[3]);
                });

                resolve({
                    'x': _x,
                    'y': _y
                });
            } else {
                resolve({});
            }
        });
    });
}

function statTaskComplete() {
    return new Promise(resolve => {
        new HttpRequest().ajax('/project/record/stat/task/complete', {
            'topN': 6
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function initPC() {
    pc = echarts.init(document.querySelector('#pc'));
    $('#reloadPC').click(function () {
        pc.showLoading();

        statTaskComplete().then(dto => {
            const _indicator = [], _value = [];
            $.each(dto, function (index, val) {
                _indicator.push({
                    'name': val.one,
                    'max': 100
                });

                _value.push(val.two === 0 ? 0 : Math.round(val.two / val.three * 10000) / 100);
            });

            if (_indicator.length === 0) {
                _indicator.push({
                    'name': '无',
                    'max': 0
                });
            }

            if (_value.length === 0) {
                $.each(_indicator, function (index, val) {
                    _value.push(0);
                });
            }

            if (_indicator.length > 0 && _value.length > 0) {
                pc.setOption({
                    'tooltip': {
                        'trigger': 'item'
                    },
                    'radar': {
                        'indicator': _indicator
                    },
                    'series': [{
                        'type': 'radar',
                        'data': [{
                            'name': '专项工作',
                            'value': _value
                        }]
                    }]
                });
            }

            pc.hideLoading();
        });
    }).trigger('click');


}

function initWorkbench() {
    todo.table = new Vue({
        'el': '#todo',
        'data': {
            'items': []
        },
        'methods': {
            'query': function (count, index) {
                const that = this;

                const _promise = query(count, index, true);
                _promise.then(page => {
                    that.items.splice(0, that.items.length);
                    $.each(page.items, function (index, val) {
                        const _duration = new Date().diff(val.createTime);

                        val.duration = '';
                        if (_duration.days > 0) {
                            val.duration += _duration.days + '天';
                        }
                        if (_duration.hours > 0) {
                            val.duration += _duration.hours + '小时';
                        }
                        if (_duration.minutes > 0) {
                            val.duration += _duration.minutes + '分钟';
                        }

                        that.items.push(val);
                    });
                });

                return _promise;
            },
            '_complete': function (item) {
                const _margin = 112;
                const _maxHeight = $(window.top).height() - _margin;
                const _width = $(window.top).width();

                const _area = [_width * 0.8 + 'px', _maxHeight + 'px'];
                // 小屏，水平铺满
                if (_width < 641) {
                    _area[0] = '100%';
                }

                let _url = '';
                switch (item.subjectType) {
                    case '专项工作':
                        _url = 'project/record/task?id=' + item.id;
                        break;
                    case '设备告警':
                            _url = 'emergency/alarm/task?id=' + item.id;
                        break;
                }

                ModalUtil.open({
                    'type': 2,
                    'title': '办理任务',
                    'area': _area,
                    'content': _url,
                    'end': () => {
                        todo.pagination.query(todo.pagination.getIndexCurrent());
                    }
                });
            },
            '_goto': function (item) {
                window.open('project/form?projectId=' + item.subjectId + '&taskId=' + item.id);
            }
        }
    });

    todo.pagination = $('#todo ~ [name=pagination]').pagination({
        'query': (recordCount, index) => new Promise(resolve => {
            todo.table.query(recordCount, index).then(page => resolve(page.total))
        })
    });

    done.table = new Vue({
        'el': '#done',
        'data': {
            'items': []
        },
        'methods': {
            'query': function (count, index) {
                const that = this;

                const _promise = query(count, index, false);
                _promise.then(page => {
                    that.items.splice(0, that.items.length);
                    $.each(page.items, function (index, val) {
                        that.items.push(val);
                    });
                });

                return _promise;
            },
            '_view': function (item) {
                const _margin = 112;
                const _maxHeight = $(window.top).height() - _margin;
                const _width = $(window.top).width();

                const _area = [_width * 0.8 + 'px', _maxHeight + 'px'];
                // 小屏，水平铺满
                if (_width < 641) {
                    _area[0] = '100%';
                }

                let _url = '';
                switch (item.subjectType) {
                    case '专项工作':
                        _url = 'project/record/task?id=' + item.id;
                        break;
                    case '设备告警':
                        _url = 'emergency/alarm/task?id=' + item.id;
                        break;
                }

                ModalUtil.open({
                    'type': 2,
                    'title': '查阅任务',
                    'area': _area,
                    'content': _url,
                    'end': () => {
                        done.pagination.query(done.pagination.getIndexCurrent());
                    }
                });
            },
            '_goto': function (item) {
                window.open('project/form?projectId=' + item.subjectId + '&taskId=' + item.id);
            }
        }
    });

    done.pagination = $('#done ~ [name=pagination]').pagination({
        'query': (recordCount, index) => new Promise(resolve => {
            done.table.query(recordCount, index).then(page => resolve(page.total))
        }),
        'autoInitialize': false
    });

    $('#reloadTask').click(function () {
        switch ($('.am-tabs-nav .am-active a').text()) {
            case '待办':
                todo.pagination.query();

                break;
            case '已办':
                done.pagination.query();

                break;
        }
    });

    $('.am-tabs-nav').find('a').on('opened.tabs.amui', function (e) {
        $('#reloadTask').trigger('click');
    });
}

function init() {
    // 系统指标
    // new Vue({
    //     'el': '#statistics',
    //     'data': {
    //         'ru': '-',
    //         'cu': '-',
    //         'urr': '-',
    //         'ucr': '-',
    //     },
    //     'mounted': function () {
    //         let that = this;
    //
    //         stat().then(data => {
    //             if (data === null) {
    //                 return;
    //             }
    //
    //             that.ru = data.ru;
    //             that.cu = data.cu;
    //             that.urr = Math.round(data.urr * 10000) / 100.00;
    //             that.ucr = Math.round(data.ucr * 10000) / 100.00;
    //         });
    //     }
    // });

    // 工作台
    initWorkbench();

    // initPC();

    index = new Vue({
        'el': '#index',
        'data': {
            'type': 'profile',
            'chart': null,
            'deptIdQueue': []
        },
        'methods': {
            'query': function () {
                const that = this;

                that.chart.showLoading();

                const _deptId = that.deptIdQueue.length === 0 ? null : that.deptIdQueue[that.deptIdQueue.length - 1];

                let _promise;
                switch (that.type) {
                    case 'profile':
                        _promise = statProfile(_deptId);
                        break;
                    case 'person':
                        _promise = statPerson(_deptId);
                        break;
                    case 'site':
                        _promise = statSite(_deptId);
                        break;
                    case 'event':
                        _promise = statEvent(_deptId);
                        break;
                    case 'installation':
                        _promise = statInstallation(_deptId);
                        break;
                    case 'unit':
                        _promise = statUnit(_deptId);
                        break;
                }

                _promise.then(data => {
                    if (data.x && data.x.length === 0) {
                        ModalUtil.msg('系统不存在下层数据，即将返回上层', null, function () {
                            that._back();
                        });
                    }

                    /* if (TF_DEPT_ID_QUEUE.length === 0) {
                        $('#profile_total').text(data.y.length === 0 ? 0 : data.y.reduce((a, b) => a + b));
                    } */

                    that.__render(data.x, data.y, 'bar');

                    that.chart.hideLoading();
                });
            },
            'redraw': function () {
                this.chart.resize();
            },
            '_back': function () {
                this.deptIdQueue.pop();

                this.query();
            },
            '__render': function (x, y, type) {
                const that = this;

                const _x = [];
                $.each(x, function (index, val) {
                    _x.push({
                        'value': JSON.stringify(val)
                    });
                });

                const _y = [{
                    'name': '',
                    'data': y,
                    'type': type ? type : 'line',
                    'smooth': true,
                    'label': {
                        'show': true,
                        'position': type ? 'inside' : 'center'
                    },
                    'areaStyle': {}
                }];

                const _option = {
                    'tooltip': {
                        'show': true,
                        'formatter': params => {
                            const _obj = JSON.parse(params.name);
                            return _obj.name;
                        }
                    },
                    'xAxis': {
                        'type': 'category',
                        'boundaryGap': true,
                        'data': _x,
                        'axisLabel': {
                            'formatter': function (value, index) {
                                const _text = value ? JSON.parse(value).name : '';
                                return _text.length > 7 ? _text.substring(0, 6) + '...' : _text;
                            },
                            'rotate': 45
                        }
                    },
                    'yAxis': {
                        'type': 'value'
                    },
                    'series': _y
                };

                that.chart.setOption(_option);
            }
        },
        'watch': {
            'type': function () {
                this.query();
            }
        },
        'mounted': function () {
            const that = this;

            that.chart = echarts.init(that.$refs.chart);
            that.chart.on('click', function (event) {
                const _json = event.name;

                if (_json) {
                    const _value = JSON.parse(_json);
                    that.deptIdQueue.push(_value.id);
                    that.query();
                }
            });

            that.query();
        }
    });
}

$(function () {
    init();

    $('.tpl-header-switch-button').click(function () {
        setTimeout(() => {
            index.redraw();
            pc.resize();
        }, 400);
    });
});
