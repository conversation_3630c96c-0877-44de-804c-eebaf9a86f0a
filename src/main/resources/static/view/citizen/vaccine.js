let searchbar, crowds, items;

function fuzzy(count, index, sortBy, name, credentialNo, mp, subdistrictFullName, crowdId, ageMin, ageMax, injectTimes, beginTime, endTime) {
    const _data = {
        'count': count,
        'index': index,
        'sortBy': sortBy,
        'name': name,
        'credentialNo': credentialNo,
        'mp': mp,
        'subdistrictFullName': subdistrictFullName
    };

    if (crowdId) {
        _data.crowdId = crowdId;
    }

    if ($.isNumeric(ageMin)) {
        _data.ageMin = ageMin;
    }

    if ($.isNumeric(ageMax)) {
        _data.ageMax = ageMax;
    }

    if ($.isNumeric(injectTimes)) {
        _data.injectTimes = injectTimes;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('vaccine/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    val.crowdNames = (val.crowdNamesJSON ? JSON.parse(val.crowdNamesJSON) : []).join('，');

                    const _array = val.subdistrictFullName ? val.subdistrictFullName.split('/') : [];
                    val.subdistrict = _array.length > 0 ? [{
                        'id': val.subdistrictId,
                        'name': _array[_array.length - 1],
                        'fullName': val.subdistrictFullName
                    }] : [];
                });
            }

            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function exportTo(name, credentialNo, mp, subdistrictFullName, crowdId, ageMin, ageMax, injectTimes, beginTime, endTime) {
    const _data = {
        'name': name,
        'credentialNo': credentialNo,
        'mp': mp,
        'subdistrictFullName': subdistrictFullName
    };

    if (crowdId) {
        _data.crowdId = crowdId;
    }

    if ($.isNumeric(ageMin)) {
        _data.ageMin = ageMin;
    }

    if ($.isNumeric(ageMax)) {
        _data.ageMax = ageMax;
    }

    if ($.isNumeric(injectTimes)) {
        _data.injectTimes = injectTimes;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    new HttpRequest().ajax('vaccine/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('疫苗接种.xlsx', null, res.data);
        }
    });
}

function initItems() {
    items = $('#table').ediTable({
        'columns': [{
            'title': '姓名',
            'field': 'name',
            'control': 'label'
        }, {
            'title': '居住类型',
            'field': 'stay',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': '常住',
                    'value': '常住'
                }, {
                    'key': '暂住',
                    'value': '暂住'
                }]
            },
            'hiddenInList': true
        }, {
            'title': '证件类型',
            'field': 'credentialType',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': '身份证',
                    'value': '身份证'
                }, {
                    'key': '其它',
                    'value': '其它'
                }]
            },
            'hiddenInList': true
        }, {
            'title': '证件号码',
            'field': 'credentialNo',
            'control': 'label',
            'hiddenInList': true
        }, {
            'title': '手机号码',
            'field': 'mp',
            'control': 'label',
            'hiddenInList': true
        }, {
            'title': '年龄',
            'field': 'age',
            'control': 'label'
        }, {
            'title': '工作单位',
            'field': 'workplace',
            'control': 'label',
            'hiddenInList': true
        }, {
            'title': '居住地行政区划',
            'field': 'subdistrict',
            'control': 'department',
            'validator': {
                'max': 1
            }
        }, {
            'title': '接种疫苗',
            'field': 'vaccine',
            'control': 'label'
        }, {
            'title': '接种针次',
            'field': 'injectTimes',
            'control': 'label'
        }, {
            'title': '接种时间',
            'field': 'injectDate',
            'control': 'label',
            'sortable': true
        }, {
            'title': '特殊人群',
            'field': 'crowdNames',
            'control': 'label',
            'hiddenInList': true
        }, {
            'title': '详细地址',
            'field': 'address',
            'control': 'label',
            'hiddenInList': true
        }, {
            'title': '填报类型',
            'field': 'actor',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': '本人',
                    'value': '本人'
                }, {
                    'key': '亲属',
                    'value': '亲属'
                }, {
                    'key': '朋友',
                    'value': '朋友'
                }, {
                    'key': '同事',
                    'value': '同事'
                }, {
                    'key': '其他',
                    'value': '其他'
                }]
            },
            'hiddenInList': true
        }],
        'query': (count, index, sortBy) => {
            return fuzzy(count, index, sortBy, searchbar.name(), searchbar.credentialNo(), searchbar.mp(), searchbar.subdistrictFullName(), searchbar.crowdId(), searchbar.ageMin(), searchbar.ageMax(), searchbar.injectTimes(), searchbar.beginTime(), searchbar.endTime());
        }
    });
}

function init() {
    crowds = new Promise(resolve => {
        new HttpRequest().ajax('profile/crowds', null, {
            'msg': {
                'success': false
            }
        }).then(res => {
            const _validator = {
                'options': {}
            };

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    _validator.options[val.val + ''] = val.name;
                });
            }

            resolve(_validator);
        });
    });

    searchbar = $('#searchbar').searchbar({
        'layout': {
            'rows': 2,
            'collapsed': false
        },
        'conditions': [{
            'title': '姓名',
            'icon': 'mdi-account',
            'ref': 'name',
            'row': 1
        }, {
            'title': '证件号码',
            'icon': 'mdi-card-account-details-outline',
            'ref': 'credentialNo',
            'row': 1
        }, {
            'title': '手机号码',
            'icon': 'mdi-cellphone',
            'ref': 'mp',
            'row': 1
        }, {
            'title': '居住地行政行政区划',
            'icon': 'mdi-map-marker-outline',
            'ref': 'subdistrictFullName',
            'row': 1
        }, {
            'title': '特殊人群',
            'icon': 'mdi-account-outline',
            'control': 'select',
            'validator': new Promise(resolve => {
                const _validator = {
                    'options': [{
                        'key': '[]',
                        'value': '无'
                    }]
                };

                crowds.then(val => {
                    $.extend(_validator, val);
                    resolve(_validator);
                });
            }),
            'ref': 'crowdId',
            'row': 1
        }, {
            'title': '年龄下限',
            'icon': 'mdi-baby-carriage',
            'control': 'number',
            'ref': 'ageMin',
            'val': 60,
            'row': 2
        }, {
            'title': '年龄上限',
            'icon': 'mdi-human-wheelchair',
            'control': 'number',
            'ref': 'ageMax',
            'row': 2
        }, {
            'title': '接种针次',
            'icon': 'mdi-needle',
            'control': 'number',
            'ref': 'injectTimes',
            'val': 0,
            'row': 2
        }, {
            'title': '开始日期',
            'icon': 'mdi-calendar-start-outline',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd'
            },
            'ref': 'beginTime',
            'row': 2
        }, {
            'title': '结束日期',
            'icon': 'mdi-calendar-end-outline',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd'
            },
            'ref': 'endTime',
            'row': 2
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query',
            'row': 1
        }, {
            'title': '导出',
            'icon': 'mdi-download',
            'callback': () => {
                exportTo(searchbar.name(), searchbar.credentialNo(), searchbar.mp(), searchbar.subdistrictFullName(), searchbar.crowdId(), searchbar.ageMin(), searchbar.ageMax(), searchbar.injectTimes(), searchbar.beginTime(), searchbar.endTime());
            },
            'row': 1
        }]
    });

    initItems();

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});
