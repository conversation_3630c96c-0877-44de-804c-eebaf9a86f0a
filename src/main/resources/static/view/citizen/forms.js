let searchbar, items;

function fuzzy(count, index, sortBy, grid, gridType, name, credentialNo, mp, subdistrictFullName, crowdId, beginTime, endTime) {
    const _data = {
        'count': count,
        'index': index,
        'sortBy': sortBy,
        'grid': grid,
        'name': name,
        'credentialNo': credentialNo,
        'mp': mp,
        'subdistrictFullName': subdistrictFullName
    };

    if (gridType) {
        _data.gridType = gridType;
    }

    if (crowdId) {
        _data.crowdId = crowdId;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('form/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    const _array = val.crowdNamesJSON ? JSON.parse(val.crowdNamesJSON) : [];
                    val.crowdNames = $.isArray(_array) ? _array.join(', ') : '';
                });
            }

            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function importFromFile(file) {
    const _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('form/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('打卡数据.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function exportTo(grid, gridType, name, credentialNo, mp, subdistrictFullName, crowdId, beginTime, endTime) {
    const _data = {
        'grid': grid,
        'name': name,
        'credentialNo': credentialNo,
        'mp': mp,
        'subdistrictFullName': subdistrictFullName
    };

    if (gridType) {
        _data.gridType = gridType
    }

    if (crowdId) {
        _data.crowdId = crowdId;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    new HttpRequest().ajax('form/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('打卡记录.xlsx', null, res.data);
        }
    });
}

function initItems() {
    items = $('#table').table({
        'columns': [{
            'title': '姓名',
            'field': 'name',
            'control': 'label'
        }, {
            'title': '居住地行政区划',
            'field': 'subdistrictFullName',
            'control': 'label'
        }, {
            'title': '采集时间',
            'field': 'createTime',
            'control': 'text',
            'sortable': true
        }],
        'query': (count, index, sortBy) => {
            return fuzzy(count, index, sortBy, searchbar.grid(), searchbar.gridType(), searchbar.name(), searchbar.credentialNo(), searchbar.mp(), searchbar.subdistrictFullName(), searchbar.crowdId(), searchbar.beginTime(), searchbar.endTime());
        },
        'btns': [null, null, {
            'name': '浏览',
            'icon': 'mdi-text-search',
            'callback': (item) => {
                let _width = $(window).width();
                if (_width < 641) {
                    _width = '90%';
                } else if (_width < 1025) {
                    _width = '70%';
                } else {
                    _width = '60%';
                }

                ModalUtil.open({
                    'type': 2,
                    'title': '报备单',
                    'area': [_width, '90%'],
                    'content': 'form?id=' + item.id
                });
            }
        }]
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'layout': {
            'rows': 2,
            'collapsed': false
        },
        'conditions': [{
            'title': '网格名称',
            'icon': 'mdi-grid',
            'ref': 'grid',
            'row': 1
        }, {
            'title': '网格类型',
            'icon': 'mdi-filter',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': false,
                    'value': '行政网格'
                }, {
                    'key': true,
                    'value': '虚拟网格'
                }]
            },
            'ref': 'gridType',
            'row': 1
        }, {
            'title': '姓名',
            'icon': 'mdi-account',
            'ref': 'name',
            'row': 1
        }, {
            'title': '证件号码',
            'icon': 'mdi-card-account-details-outline',
            'ref': 'credentialNo',
            'row': 1
        }, {
            'title': '手机号码',
            'icon': 'mdi-cellphone',
            'ref': 'mp',
            'row': 1
        }, {
            'title': '居住地行政行政区划',
            'icon': 'mdi-map-marker-outline',
            'ref': 'subdistrictFullName',
            'row': 2
        }, {
            'title': '特殊人群',
            'icon': 'mdi-account-outline',
            'control': 'select',
            'validator': new Promise(resolve => {
                new HttpRequest().ajax('form/crowds', {}, {
                    'msg': {
                        'success': false
                    }
                }).then(res => {
                    const validator = {
                        options: [{
                            'key': '[]',
                            'value': '无'
                        }]
                    };

                    if (res.code === 'OK') {
                        $.each(res.data, function (idx, val) {
                            validator.options.push({
                                'key': [val.val + ''],
                                'value': val.name
                            });
                        });
                    }
                    resolve(validator);
                });
            }),
            'ref': 'crowdId',
            'row': 2
        }, {
            'title': '开始时间',
            'icon': 'mdi-clock-start',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'beginTime',
            'row': 2
        }, {
            'title': '结束时间',
            'icon': 'mdi-clock-end',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'endTime',
            'row': 2
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query',
            'row': 1
        }, /*{
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': function () {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            },
            'row': 2
        },*/ {
            'title': '导出',
            'icon': 'mdi-download',
            'callback': () => {
                exportTo(searchbar.grid(), searchbar.gridType(), searchbar.name(), searchbar.credentialNo(), searchbar.mp(), searchbar.subdistrictFullName(), searchbar.crowdId(), searchbar.beginTime(), searchbar.endTime());
            },
            'row': 1
        }]
    });

    initItems();

    $('input[type=file][name=file]').change(function (event) {
        const that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                items.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });

    const $template = $('#importTemplate');
    const href = $template.attr('href');
    $template.attr('href', href + '?' + new Date().getTime());
}

$(function () {
    init();
});