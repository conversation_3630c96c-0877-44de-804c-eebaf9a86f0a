let searchbar, crowds, items;
let columns;

function fuzzy(count, index, sortBy, name, credentialNo, mp, subdistrictFullName, stay, crowdId, status, validation, error, minAge, maxAge, beginTime, endTime) {
    const _data = {
        'count': count,
        'index': index,
        'sortBy': sortBy,
        'name': name,
        'credentialNo': credentialNo,
        'mp': mp,
        'subdistrictFullName': subdistrictFullName
    };

    if (stay) {
        _data.stay = stay;
    }

    if (crowdId) {
        _data.crowdId = crowdId;
    }

    if (status) {
        _data.status = status;
    }

    if (validation) {
        _data.validation = validation;
    }

    if (error) {
        _data.error = error;
    }

    if (minAge !== '') {
        _data.minAge = minAge;
    }

    if (maxAge !== '') {
        _data.maxAge = maxAge;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('profile/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    val.crowdIds = val.crowdIdsJSON ? JSON.parse(val.crowdIdsJSON) : [];

                    const _array = val.subdistrictFullName ? val.subdistrictFullName.split('/') : [];
                    val.subdistrict = _array.length > 0 ? [{
                        'id': val.subdistrictId,
                        'name': _array[_array.length - 1],
                        'fullName': val.subdistrictFullName
                    }] : [];
                    val.addressLocation = {
                        'address': val.address,
                        'lat': val.lat,
                        'lng': val.lng
                    };

                    switch (val.validation) {
                        case '已验证':
                            val['_ext_'] = {
                                'class': 'am-text-success'
                            };
                            break;
                        case '验证失败':
                            val['_ext_'] = {
                                'class': 'am-text-danger'
                            };
                            break;
                    }

                    if (val.validationDescription) {
                        const _array = JSON.parse(val.validationDescription);
                        val.validationDescription = _array.join('\n');
                    }
                });
            }

            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function save(item, raze) {
    item.crowdIdsJSON = JSON.stringify(item.crowdIds);

    if ($.isArray(item.subdistrict) && item.subdistrict.length > 0) {
        item.subdistrictId = item.subdistrict[0].id;
        item.subdistrictFullName = item.subdistrict[0].fullName;
    } else {
        item.subdistrictId = '';
        item.subdistrictFullName = '';
    }

    item.address = item.addressLocation.address;
    item.lat = item.addressLocation.lat;
    item.lng = item.addressLocation.lng;

    return new Promise(resolve => {
        new HttpRequest().ajax(item.id ? (raze ? 'profile/raze-then-update' : 'profile/update') : 'profile/add', item).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('profile/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function importFromFile(file) {
    const _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('profile/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('个人信息.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function exportTo(name, credentialNo, mp, subdistrictFullName, stay, crowdId, status, validation, error, minAge, maxAge, beginTime, endTime) {
    const _data = {
        'name': name,
        'credentialNo': credentialNo,
        'mp': mp,
        'subdistrictFullName': subdistrictFullName
    };

    if (stay) {
        _data.stay = stay;
    }

    if (crowdId) {
        _data.crowdId = crowdId;
    }

    if (status) {
        _data.status = status;
    }

    if (validation) {
        _data.validation = validation;
    }

    if (error) {
        _data.error = error;
    }

    if (minAge !== '') {
        _data.minAge = minAge;
    }

    if (maxAge !== '') {
        _data.maxAge = maxAge;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    new HttpRequest().ajax('profile/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('个人信息.xlsx', null, res.data);
        }
    });
}

function isCredentialNoExist(id, credentialNo) {
    return new Promise(resolve => {
        new HttpRequest().ajax('profile/credential-no/exist', {
            'id': id,
            'credentialNo': credentialNo
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : false);
        });
    });
}

function render(options, data) {
    let _index;

    const _id = Math.random().toString().replace(/\./g, '') + '_';
    $('body').append('<div id="' + _id + '" class="am-margin"></div>');

    const _columns = [];
    $.each(options.columns, function (index, val) {
        if (val.hiddenInEditor) {
            return;
        }

        if (val.hasOwnProperty('classInEditor')) {
            val.class = val.classInEditor;
        }

        _columns.push(val);
    });

    const _form = $('#' + _id).form({
        // 表单定义
        'rows': _columns,
        // 按钮组
        'btns': [{
            'callback': item => {
                // 刷新表格
                options.save(item).then(success => {
                    if (success === true) {
                        ModalUtil.close(_index);

                        if (item.id) {
                            items.query(items.getIndexCurrent());
                        } else {
                            items.query();
                        }
                    }
                });
            }
        }]
    });

    _form.vue.$nextTick(() => {
        _form.setData(data);

        let _width = $(window.top).width();
        if (_width < 641) {
            _width = '90%';
        } else if (_width < 1025) {
            _width = '60%';
        } else {
            _width = '40%';
        }

        _index = ModalUtil.open({
            'type': 1,
            'title': data === null ? '新增' : '编辑',
            'content': $('#' + _id),
            'area': _width,
            'maxHeight': $(window).height() * 0.9,
            'end': function () {
                _form.vue.$destroy();

                $('#' + _id).remove();
            }
        });
    });
}

function initItems() {
    const _rules = App.entityDefaultRules('com.chinamobile.healthcode.model.citizen.Profile', ['name', 'credentialNo', 'mp', 'workplace', 'address']);

    columns = [{
        'title': '姓名',
        'field': 'name',
        'control': 'text',
        'validator': {
            'rule': {
                'required': true,
                'maxlength': 16
            }
        }
    }, {
        'title': '居住类型',
        'field': 'stay',
        'control': 'select',
        'validator': {
            'options': [{
                'key': '常住',
                'value': '常住'
            }, {
                'key': '暂住',
                'value': '暂住'
            }],
            'rule': {
                'required': true
            }
        },
        'hidden': true
    }, {
        'title': '证件类型',
        'field': 'credentialType',
        'control': 'select',
        'validator': {
            'options': [{
                'key': '身份证',
                'value': '身份证'
            }, {
                'key': '其它',
                'value': '其它'
            }],
            'rule': {
                'required': true
            }
        },
        'hidden': true
    }, {
        'title': '证件号码',
        'field': 'credentialNo',
        'control': 'text',
        'validator': new Promise(resolve => {
            _rules.credentialNo.then(data => {
                data.rule.required = true;

                resolve(data);
            });
        }),
        'hidden': true
    }, {
        'title': '手机号码',
        'field': 'mp',
        'control': 'text',
        'validator': {
            'rule': {
                'required': true
            }
        },
        'hidden': true
    }, {
        'title': '特殊人群',
        'field': 'crowdIds',
        'control': 'checkbox',
        'validator': crowds,
        'hidden': true
    }, {
        'title': '工作单位',
        'field': 'workplace',
        'control': 'text',
        'validator': _rules.workplace,
        'hidden': true
    }, {
        'title': '居住地行政区划',
        'field': 'subdistrict',
        'control': 'department',
        'validator': {
            'max': 1,
            'rule': {
                'required': true
            }
        }
    }, {
        'title': '详细地址',
        'field': 'addressLocation',
        'control': 'location',
        'hidden': true,
        'validator': {
            'rule': {
                'required': true
            }
        }
    }, {
        'title': '填报人员类别',
        'field': 'actor',
        'control': 'select',
        'validator': {
            'options': [{
                'key': '本人',
                'value': '本人'
            }, {
                'key': '亲属',
                'value': '亲属'
            }, {
                'key': '朋友',
                'value': '朋友'
            }, {
                'key': '同事',
                'value': '同事'
            }, {
                'key': '社区工作人员',
                'value': '社区工作人员'
            }, {
                'key': '网格员',
                'value': '网格员'
            }, {
                'key': '其他',
                'value': '其他'
            }],
            'rule': {
                'required': true
            }
        },
        'hidden': true
    }, {
        'title': '其他填报人员类别',
        'field': 'actorOther',
        'control': 'text',
        'hidden': true
    }, {
        'title': '人员状态',
        'field': 'status',
        'control': 'select',
        'validator': {
            'options': [{
                'key': '在册',
                'value': '在册'
            }, {
                'key': '外出',
                'value': '外出'
            }, {
                'key': '失踪',
                'value': '失踪'
            }, {
                'key': '已故',
                'value': '已故'
            }],
            'rule': {
                'required': true
            }
        }
    }, {
        'title': '验证状态',
        'field': 'validation',
        'control': 'label',
        'sortable': true
    }, {
        'title': '验证结果',
        'field': 'validationDescription',
        'control': 'textarea',
    }, {
        'title': '采集时间',
        'field': 'createTime',
        'control': 'label',
        'sortable': true
    }, {
        'title': '是否重点人员',
        'field': 'subjectPerson',
        'control': 'label',
        'formatter': function (val) {
            return val ? '是' : '否';
        }
    }, {
        'title': '重点人员类型',
        'field': 'subjectPersonTypes',
        'control': 'label'
    }];

    items = $('#table').table({
        'columns': columns,
        'query': (count, index, sortBy) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, sortBy, searchbar.name(), searchbar.credentialNo(), searchbar.mp(), _regions.length > 0 ? _regions[0].fullName : null, searchbar.stay(), searchbar.crowdId(), searchbar.status(), searchbar.validation(), searchbar.error(), searchbar.minAge(), searchbar.maxAge(), searchbar.beginTime(), searchbar.endTime());
        },
        'toolbar': [{
            'name': '新增',
            'icon': 'mdi-plus',
            'callback': () => {
                columns[0].control = 'text';
                columns[2].control = 'select';
                columns[3].control = 'text';
                columns[4].control = 'text';

                render({
                    'columns': columns,
                    'save': save
                }, null);
            }
        }],
        'btns': [{
            'callback': item => {
                const _errors = item.validationDescription ? item.validationDescription.split('\n') : [];
                columns[0].control = _errors.indexOf('姓名非法，请修改') === -1 && _errors.indexOf('姓名真实性校验失败，请修改') === -1 ? 'label' : 'text';
                columns[2].control = _errors.indexOf('身份证号码非法，请修改') === -1 && _errors.indexOf('姓名真实性校验失败，请修改') === -1 ? 'label' : 'select';
                columns[3].control = _errors.indexOf('身份证号码非法，请修改') === -1 && _errors.indexOf('姓名真实性校验失败，请修改') === -1 ? 'label' : 'text';
                columns[4].control = _errors.indexOf('手机号码非法，请修改') === -1 ? 'label' : 'text';

                render({
                    'columns': columns,
                    'save': item => {
                        if (columns[3].control === 'label' || item.credentialType !== '身份证') {
                            return save(item, false);
                        }

                        return new Promise(resolve => {
                            isCredentialNoExist(item.id, item.credentialNo).then(exist => {
                                if (!exist) {
                                    resolve(save(item, false));

                                    return;
                                }

                                ModalUtil.confirm('该身份证号已被他人使用，是否覆盖？', {
                                    'title': false,
                                    'icon': 3
                                }, function (index) {
                                    resolve(save(item, true));

                                    ModalUtil.close(index);
                                }, function (index) {
                                    ModalUtil.confirm('是否删除当前记录？', {
                                        'title': false,
                                        'icon': 3
                                    }, function (index) {
                                        resolve(remove(item));

                                        ModalUtil.close(index);
                                    }, function (index) {
                                        resolve({
                                            'code': 'OK'
                                        });

                                        ModalUtil.close(index);
                                    });

                                    ModalUtil.close(index);
                                });
                            });
                        });
                    }
                }, item);
            }
        }, {
            'callback': remove
        }]
    });
}

function init() {
    crowds = new Promise(resolve => {
        new HttpRequest().ajax('profile/crowds', null, {
            'msg': {
                'success': false
            }
        }).then(res => {
            const _validator = {
                'options': {}
            };

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    _validator.options[val.val + ''] = val.name;
                });
            }
            resolve(_validator);
        });
    });

    searchbar = $('#searchbar').searchbar({
        'layout': {
            'rows': 3,
            'collapsed': false
        },
        'conditions': [{
            'title': '姓名',
            'icon': 'mdi-account',
            'ref': 'name',
            'row': 1
        }, {
            'title': '证件号码',
            'icon': 'mdi-card-account-details-outline',
            'ref': 'credentialNo',
            'row': 1
        }, {
            'title': '手机号码',
            'icon': 'mdi-cellphone',
            'ref': 'mp',
            'row': 1
        }, {
            'title': '居住地行政行政区划',
            'icon': 'mdi-map-marker-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions',
            'row': 1
        }, {
            'title': '居住类型',
            'icon': 'mdi-home-city-outline',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': '暂住',
                    'value': '暂住'
                }, {
                    'key': '常住',
                    'value': '常住'
                }]
            },
            'ref': 'stay',
            'row': 1
        }, {
            'title': '特殊人群',
            'icon': 'mdi-account-outline',
            'control': 'select',
            'validator': new Promise(resolve => {
                const _validator = {
                    'options': [{
                        'key': '[]',
                        'value': '无'
                    }]
                };

                crowds.then(val => {
                    $.extend(_validator, val);
                    resolve(_validator);
                });
            }),
            'ref': 'crowdId',
            'row': 2
        }, {
            'title': '人员状态',
            'icon': 'mdi-hospital-box',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': '在册',
                    'value': '在册'
                }, {
                    'key': '外出',
                    'value': '外出'
                }, {
                    'key': '失踪',
                    'value': '失踪'
                }, {
                    'key': '已故',
                    'value': '已故'
                }]
            },
            'ref': 'status',
            'row': 2
        }, {
            'title': '验证状态',
            'icon': 'mdi-check-decagram-outline',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': '待验证',
                    'value': '待验证'
                }, {
                    'key': '已验证',
                    'value': '已验证'
                }, {
                    'key': '验证失败',
                    'value': '验证失败'
                }]
            },
            'ref': 'validation',
            'row': 2
        }, {
            'title': '验证结果',
            'icon': 'mdi-alert-decagram-outline',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': '017',
                    'value': '姓名非法'
                }, {
                    'key': '018',
                    'value': '身份证号码非法'
                }, {
                    'key': '019',
                    'value': '手机号码非法'
                }, {
                    'key': '020',
                    'value': '居住地行政区划未详细到网格'
                }, {
                    'key': '021',
                    'value': '姓名真实性校验失败'
                }, {
                    'key': '022',
                    'value': '经纬度无效'
                }, {
                    'key': '023',
                    'value': '人员状态无效'
                }]
            },
            'ref': 'error',
            'row': 2
        }, {
            'title': '最小年龄',
            'icon': 'mdi-calendar-start-outline',
            'control': 'number',
            'ref': 'minAge',
            'row': 2
        }, {
            'title': '最大年龄',
            'icon': 'mdi-calendar-end-outline',
            'control': 'number',
            'ref': 'maxAge',
            'row': 2
        }, {
            'title': '开始时间',
            'icon': 'mdi-clock-start',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'beginTime',
            'row': 3
        }, {
            'title': '结束时间',
            'icon': 'mdi-clock-end',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'endTime',
            'row': 3
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query',
            'row': 1
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            },
            'row': 3
        }, {
            'title': '导出',
            'icon': 'mdi-download',
            'callback': () => {
                const _regions = searchbar.regions();

                exportTo(searchbar.name(), searchbar.credentialNo(), searchbar.mp(), _regions.length > 0 ? _regions[0].fullName : null, searchbar.stay(), searchbar.crowdId(), searchbar.status(), searchbar.validation(), searchbar.error(), searchbar.minAge(), searchbar.maxAge(), searchbar.beginTime(), searchbar.endTime());
            },
            'row': 3
        }]
    });

    initItems();

    $('input[type=file][name=file]').change(function (event) {
        const that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                items.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});
