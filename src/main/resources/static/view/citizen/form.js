function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('form/get', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function init() {
    new Vue({
        'el': '#app',
        'data': {
            'id': window.location.querystring('id'),
            'form': {
                'deptFullName': {
                    'title': '网格',
                    'group': '报备单',
                    'value': ''
                },
                'name': {
                    'title': '姓名',
                    'group': '报备单',
                    'value': ''
                },
                'actor': {
                    'title': '填报类型',
                    'group': '报备单',
                    'value': ''
                },
                'credentialType': {
                    'title': '证件类型',
                    'group': '报备单',
                    'value': ''
                },
                'credentialNo': {
                    'title': '证件号码',
                    'group': '报备单',
                    'value': ''
                },
                'mp': {
                    'title': '手机号码',
                    'group': '报备单',
                    'value': ''
                },
                'crowdNames': {
                    'title': '特殊人群',
                    'group': '报备单',
                    'value': ''
                },
                'workplace': {
                    'title': '工作单位',
                    'group': '报备单',
                    'value': ''
                },
                'stay': {
                    'title': '居住类型',
                    'group': '报备单',
                    'value': ''
                },
                'subdistrictFullName': {
                    'title': '居住地行政区划',
                    'group': '报备单',
                    'value': ''
                },
                'address': {
                    'title': '居住地详细地址',
                    'group': '报备单',
                    'value': ''
                },
                'memo': {
                    'title': '其它说明事项',
                    'group': '报备单',
                    'value': ''
                },
                'createTime': {
                    'title': '采集时间',
                    'group': '报备单',
                    'value': ''
                }
            }
        },
        'mounted': function () {
            const that = this;

            if (!that.id) {
                ModalUtil.msg('表单数据错误，即将为您重定向', {
                    'time': 2000
                }, function () {
                    window.location.replace('form');
                });
            }

            get(that.id).then(data => {
                if (data === null) {
                    return;
                }

                let _array = data['crowdNamesJSON'] ? JSON.parse(data['crowdNamesJSON']) : [];
                data['crowdNames'] = $.isArray(_array) ? _array.join(', ') : '';

                for (const prop in data) {
                    if (that.form.hasOwnProperty(prop)) {
                        if (that.form[prop].hasOwnProperty('value')) {
                            that.form[prop].value = data[prop];
                        } else {
                            that.form[prop] = data[prop];
                        }
                    }
                }
            });
        }
    });
}

$(function () {
    init();
});
