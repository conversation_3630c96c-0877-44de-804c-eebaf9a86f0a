let searchbar, app;

function query(regionId, min, max) {
    const _data = {
        'regionId': regionId
    };

    if (min !== '') {
        _data.min = min;
    }

    if (max !== '') {
        _data.max = max;
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('survey/query', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function exportTo(regionId, min, max) {
    const _data = {
        'regionId': regionId
    };

    if (min !== '') {
        _data.min = min;
    }

    if (max !== '') {
        _data.max = max;
    }

    new HttpRequest().ajax('survey/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('网格.xlsx', null, res.data);
        }
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-marker-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }, {
            'title': '最小人数',
            'icon': 'mdi-account-arrow-down',
            'control': 'number',
            'ref': 'min'
        }, {
            'title': '最大人数',
            'icon': 'mdi-account-arrow-up',
            'control': 'number',
            'ref': 'max'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                let _regions = searchbar.regions();
                if (_regions.length === 0) {
                    ModalUtil.alert('请选择行政区划');
                    return;
                }

                app.query(_regions[0].id, searchbar.min(), searchbar.max());
            },
            'ref': 'query'
        }, {
            'title': '导出',
            'icon': 'mdi-download',
            'callback': () => {
                let _regions = searchbar.regions();
                if (_regions.length === 0) {
                    ModalUtil.alert('请选择行政区划');
                    return;
                }

                exportTo(_regions[0].id, searchbar.min(), searchbar.max());
            }
        }]
    });

    app = new Vue({
        'el': '#app',
        'data': {
            'items': []
        },
        'methods': {
            'query': function (regionId, min, max) {
                let that = this;

                query(regionId, min, max).then(items => {
                    that.items.splice(0, that.items.length);

                    $.each(items, function (index, val) {
                        that.items.push(val);
                    });
                });
            }
        }
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            app.query();
        }
    });
}

$(function () {
    init();
});
