let searchbar, items;

function operate() {
    return new Promise(resolve => {
        new HttpRequest().ajax('grid/operate', null, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' && res.data === true);
        });
    });
}

function queryOrganization(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('../sys/dept/organization', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : {
                'item': null,
                'branches': []
            });
        });
    });
}

function fuzzy(count, index, name, type, member) {
    return new Promise(resolve => {
        new HttpRequest().ajax('grid/fuzzy', {
            'count': count,
            'index': index,
            'name': name,
            'type': type,
            'member': member
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    const _array = JSON.parse(val.memberFullNamesJSON);
                    val.members = _array.join('，');
                });
            }

            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function remove(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('grid/remove', {
            'id': id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function qrCode(content, fullName, name) {
    new HttpRequest({
        'responseType': 'blob'
    }).ajax('grid/qrcode', {
        'content': content,
        'desc': name
    }, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.copyInputStreamToFile(res.data.content, fullName + '.png');
        }
    });
}

function wxQRCode(type, id, fullName, name) {
    new HttpRequest({
        'responseType': 'blob'
    }).ajax('grid/qrcode/wx', {
        'type': type,
        'id': id,
        'desc': name,
        'page': 'pages/tabbar/home/<USER>'
    }, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.copyInputStreamToFile(res.data.content, fullName + '.png');
        }
    });
}

function initItems(permit) {
    const _options = {
        'columns': [{
            'title': '名称',
            'field': 'name',
            'control': 'label'
        }, {
            'title': '专题类型',
            'field': 'type',
            'control': 'label'
        }, {
            'title': '下属行政网格',
            'field': 'members',
            'control': 'label'
        }],
        'query': (count, index) => {
            return fuzzy(count, index, searchbar.name(), searchbar.type(), searchbar.member());
        },
        'btns': [null, null, {
            'name': '二维码',
            'icon': 'mdi-qrcode',
            'callback': item => {
                //qrcode('form/grid/' + item.id + '/exist', item.fullName, '新 ' + item.name + '（虚拟）');
                wxQRCode('grid', item.id, item.fullName, '新 ' + item.name + '（虚拟）');
            }
        }]
    };

    if (permit) {
        _options.toolbar = [{
            'name': '新增',
            'icon': 'mdi-plus',
            'callback': () => {
                let _width = $(window).width();
                if (_width < 641) {
                    _width = '90%';
                } else {
                    _width = '60%';
                }

                ModalUtil.open({
                    'type': 2,
                    'title': '新增',
                    'area': [_width, '90%'],
                    'content': 'grid',
                    'end': function () {
                        items.query();
                    }
                });
            }
        }];

        _options.btns[0] = {
            'callback': function (item) {
                let _width = $(window).width();
                if (_width < 641) {
                    _width = '90%';
                } else {
                    _width = '60%';
                }

                ModalUtil.open({
                    'type': 2,
                    'title': '编辑',
                    'area': [_width, '90%'],
                    'content': 'grid?id=' + item.id,
                    'end': function () {
                        items.query(items.getIndexCurrent());
                    }
                });
            }
        };
        _options.btns[1] = {
            'callback': item => {
                return remove(item.id);
            }
        };
        _options.btns[3] = {
            'name': '任命',
            'icon': 'mdi-shield-account-outline',
            'callback': item => {
                ModalUtil.open({
                    'type': 2,
                    'title': '任命',
                    'content': 'grid/appointment?id=' + item.id,
                    'area': ['90%', '90%']
                });
            }
        };
    }

    items = $('#table').table(_options);
}

function init() {
    new Vue({
        'el': '#departments',
        'data': {
            'dept': {},
            'subordinates': [],
            'deptMapper': {
                'id': 'id',
                'name': 'name',
                'code': 'code',
                'fullName': 'fullName',
                'hasSubordinates': 'hasSubordinates'
            }
        },
        'computed': {
            // 导航栏键
            'deptIdArr': function () {
                return this.dept[this.deptMapper.code] ? this.dept[this.deptMapper.code].split('.') : [];
            },
            // 导航栏
            'deptNameArr': function () {
                return this.dept[this.deptMapper.fullName] ? this.dept[this.deptMapper.fullName].split('/') : [];
            }
        },
        'methods': {
            '_queryOrganization': function (id) {
                const that = this;

                const _deptId = id ? id : null;

                queryOrganization(_deptId).then(data => {
                    // 设置部门
                    if ($.isEmptyObject(data.dept)) {
                        that.dept[that.deptMapper.id] = '';
                        that.dept[that.deptMapper.code] = '';
                        that.dept[that.deptMapper.fullName] = '';
                    } else {
                        that.dept = data.dept;
                    }

                    // 设置下级部门
                    that.subordinates = data.subordinates;

                    deptIdForMembers = id ? id : '';
                });
            },
            '_qrcode': function (item) {
                //qrcode('form/dept/' + item.id + '/exist', item.fullName, '新 ' + item.name);
                wxQRCode('dept', item.id, item.fullName, '新 ' + item.name);
            }
        },
        'mounted': function () {
            this._queryOrganization();
        }
    });

    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '名称',
            'icon': 'mdi-grid',
            'ref': 'name'
        }, {
            'title': '专题类型',
            'icon': 'mdi-archive',
            'ref': 'type'
        }, {
            'title': '成员',
            'icon': 'mdi-sitemap',
            'ref': 'member'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query'
        }]
    });

    operate().then(permit => {
        initItems(permit);
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});