function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('grid/get', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function save(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax(item.id ? 'grid/update' : 'grid/add', {
            'item': item
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function init() {
    new Vue({
        'el': '#app',
        'data': {
            'id': window.location.querystring('id'),
            'name': '',
            'type': '',
            'members': []
        },
        'methods': {
            '_addMembers': function () {
                const that = this;

                Member.get({
                    'entity': 'department',
                    'max': Number.MAX_VALUE,
                    'queryOrganization': deptId => {
                        return new Promise(resolve => {
                            new HttpRequest().ajax('/migrant/visit-form/organization', {
                                'id': deptId
                            }, {
                                'msg': {
                                    'success': false
                                }
                            }).then(res => {
                                const _data = res.code === 'OK' ? {
                                    'dept': res.data.dept,
                                    'subordinates': res.data.subordinates,
                                    'users': res.data.members
                                } : {
                                    'dept': {},
                                    'subordinates': [],
                                    'users': []
                                };

                                resolve(_data);
                            });
                        });
                    },
                    'ok': departments => {
                        return new Promise(resolve => {
                            if (departments.length === 0) {
                                ModalUtil.alert('请选择行政网格');

                                resolve(false);
                            }

                            $.each(departments, function (index, val) {
                                that.members.push(val);
                            });

                            resolve(true);
                        });
                    }
                });
            },
            '_removeMember': function (index) {
                const that = this;

                that.members.splice(index, 1);
            },
            '_save': function () {
                const that = this;

                const _data = {
                    'name': that.name,
                    'type': that.type
                }

                if (that.id) {
                    _data.id = that.id;
                }

                _data.memberIds = [];
                $.each(that.members, function (index, val) {
                    _data.memberIds.push(val.id);
                });

                save(_data).then(success => {
                    if (success) {
                        ModalUtil.msg('操作成功', {
                            'title': false,
                            'icon': 1,
                            'time': 2000
                        }, function () {
                            const _index = window.top.layer.getFrameIndex(window.name);
                            window.top.layer.close(_index);
                        });
                    }
                });
            }
        },
        'mounted': function () {
            const that = this;

            if (that.id) {
                get(that.id).then(data => {
                    if (data === null) {
                        return;
                    }

                    that.id = data.id;
                    that.name = data.name;
                    that.type = data.type;

                    // 设置成员
                    const _json1 = data.memberIdsJSON;
                    const _json2 = data.memberFullNamesJSON;
                    if (_json1 && _json2) {
                        const _array1 = JSON.parse(_json1), _array2 = JSON.parse(_json2);
                        const _members = [];

                        for (let i = 0; i < _array1.length; i++) {
                            _members.push({
                                'id': _array1[i],
                                'fullName': _array2[i]
                            });
                        }

                        $.each(_members, function (index, val) {
                            that.members.push(val);
                        });
                    }
                });
            }
        }
    })
}

$(function () {
    init();
});
