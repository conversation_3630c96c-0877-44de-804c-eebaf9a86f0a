function getUser() {
    return new HttpRequest().ajax('../../app/layout/user', null, {
        'loading': false,
        'msg': {
            'success': false,
            'error': false
        }
    });
}

function getUrl() {
    return new HttpRequest().ajax('board/url', null, {
        'loading': false,
        'msg': {
            'success': false,
            'error': false
        }
    });
}

function init() {
    app = new Vue({
        'el': '#app',
        'data': {
            'src': '',
            'message': '',
        },
        'methods': {
            'reload': function () {
                if (!this.src) {
                    return;
                }

                const _src = this.src;
                this.src = '';
                this.$nextTick(function () {
                    this.src = _src;
                });
            }
        },
        'watch': {
            'src': function (val) {
                const that = this;

                if (val) {
                    that.$nextTick(() => {
                        $(that.$refs.board).on('load', function () {
                            // 读取容器高度
                            const _containerHeight = $(window.document.body).height() - $('.tpl-content-wrapper').offset().top;

                            // 读取画板高度
                            const _boardHeight = $(window.frames[0].window.document.body).height();

                            $(that.$refs.board).height(Math.max(_containerHeight, _boardHeight));
                        });
                    });
                }
            }
        },
        'mounted': function () {
            const that = this;

            Promise.all([getUser(), getUrl()]).then(values => {
                const _user = values[0], _url = values[1];

                if (_user.code !== 'OK') {
                    that.message = _user.message;
                    return;
                }

                if (!_user.data.deptFullName) {
                    that.message = '系统未检索到您的部门信息，请求失败';
                    return;
                }

                const _params = ['area', 'street', 'community', 'grid'];
                const _values = _user.data.deptFullName.split('/');

                let _pairs = [];
                for (let i = 0; i < _values.length && i < _params.length; i++) {
                    _pairs.push(_params[i] + '=' + _values[i]);
                }
                const _querystring = _pairs.length === 0 ? '' : '?' + _pairs.join('&');

                if (_url.code !== 'OK') {
                    that.message = _url.message;
                    return;
                }
                that.src = _url.data + _querystring;
            });
        }
    });

    $('.tpl-header-switch-button').click(function () {
        setTimeout(() => {
            app.reload();
        }, 400);
    });
}

$(function () {
    init();
});