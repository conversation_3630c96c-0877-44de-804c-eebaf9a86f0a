let gridId, users;

function suggest(key) {
    return new Promise(resolve => {
        new HttpRequest().ajax('../../sys/user/suggest', {
            'count': 5,
            'index': 0,
            'key': decodeURIComponent(key)
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data.items : []);
        });
    });
}

function initialize() {
    return new Promise(resolve => {
        new HttpRequest().ajax('user/query', {
            'id': gridId
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function add(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('user/add', {
            'id': gridId,
            'userId': item.id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('user/remove', {
            'id': gridId,
            'userId': item.id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function init() {
    users = $('#users').autoComplete({
        'key': 'name',
        'getText': item => {
            return item.name;
        },
        'getSuggestion': item => {
            return '<p class="am-margin-sm">' + item.name + '/' + (item.mp ? item.mp : '？') + '</p>';
        },
        'query': suggest,
        'add': add,
        'remove': remove
    });

    initialize().then(data => {
        users.setItems(data);
    });
}

$(function () {
    gridId = window.location.querystring('id');
    if (!gridId) {
        window.location.replace('/grid');
    }

    init();
});