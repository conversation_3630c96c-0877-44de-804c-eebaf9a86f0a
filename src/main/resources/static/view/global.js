function HttpRequest(options) {
    const _options = {
        'baseURL': window.location.contextPath,
        'headers': {
            'X-Requested-With': 'XMLHttpRequest'
        },
        'withCredentials': true
    };

    $.extend(true, _options, options);

    this.axios = axios.create(_options);
}

HttpRequest.prototype.ajax = function (url, data, options) {
    const _options = {
        'layer': window,
        'loading': true, // 请求时显示加载提示
        'msg': {
            'success': '请求成功',
            'error': true // 返回错误时显示错误信息
        }
    };

    $.extend(true, _options, options);
    if (_options.msg.success == null) {
        _options.msg.success = '请求成功'
    }
    if (_options.msg.error == null) {
        _options.msg.error = true;
    }

    // 设置忙标记
    if (_options.loading) {
        let _index;

        this.axios.interceptors.request.use(config => {
            _index = _options.layer.ModalUtil.load();
            return config;
        });

        this.axios.interceptors.response.use(response => {
            _options.layer.ModalUtil.close(_index);

            return response;
        }, error => {
            _options.layer.ModalUtil.close(_index);

            return error.response ? error.response : {
                'data': {
                    'code': error.name,
                    'message': error.message
                }
            };
        });
    }

    const that = this;

    function parseError(result) {
        let _code;

        const _array = (result.code + '').split('_');
        if (_array.length === 4) {
            _array.shift(0, 1);
            _code = _array.join('_');
        } else {
            _code = result.code;
        }

        switch (_code) {
            // 身份认证失效
            case '001_B_020':
                _options.layer.ModalUtil.confirm('您的身份验证已失效，是否重新登录？', {
                    'title': false,
                    'icon': 3
                }, function (index) {
                    window.location.reload();

                    _options.layer.ModalUtil.close(index);
                });

                break;
            // 其它
            default:
                if (_options.msg.error) {
                    _options.layer.ModalUtil.alert(result.message == null || result.message === '' ? ('请求失败，错误代码为' + result.code) : result.message);
                }

                break;
        }
    }

    return new Promise(resolve => {
        that.axios.post(url, data).then(response => {
            let _promise;

            switch (response.request.responseType) {
                case 'arraybuffer':
                    _promise = new Promise(resolve => {
                        try {
                            const _decoder = new TextDecoder('utf-8');
                            resolve(JSON.parse(_decoder.decode(new Uint8Array(response.data))));
                        } catch (e) {
                            resolve({
                                'code': 'OK',
                                'data': {
                                    'name': decodeURI(response.headers['content-disposition'].split(';')[1].split('=')[1]),
                                    'content': response.data
                                }
                            });
                        }
                    });

                    break;
                case 'blob':
                    _promise = new Promise(resolve => {
                        const _reader = new FileReader();
                        _reader.onload = function () {
                            try {
                                resolve(JSON.parse(this.result));
                            } catch (e) {
                                resolve({
                                    'code': 'OK',
                                    'data': {
                                        'name': decodeURI(response.headers['content-disposition'].split(';')[1].split('=')[1]),
                                        'content': response.data
                                    }
                                });
                            }
                        };
                        _reader.readAsText(response.data);
                    });

                    break;
                default:
                    _promise = new Promise(resolve => resolve(response.data));
                    break;
            }

            _promise.then(result => {
                if (result.code === 'OK') {
                    if (typeof _options.msg.success === 'string') {
                        _options.layer.ModalUtil.msg(_options.msg.success);
                    }
                } else {
                    parseError(result);
                }

                resolve(result);
            });
        }).catch(error => {
            let _result;

            if (error.response == null) {
                _result = {
                    'code': error.message,
                    'message': error.message
                };
            } else if (error.response.data == null) {
                _result = {
                    'code': error.response.status,
                    'message': error.message
                }
            } else {
                _result = error.response.data.hasOwnProperty('message') ? error.response.data : {
                    'code': error.response.status,
                    'message': error.response.statusText
                };
            }

            parseError(_result);

            resolve(_result);
        });
    });
};

function WebSocketProxy(url, options) {
    const _options = {
        serviceId: null,
        reconnectOnClose: true,
        notifyOnError: true
    }
    $.extend(true, _options, options)

    this.url = url
    this.options = _options
    this.options.serviceId = _options.serviceId || url

    this.ws = null
    this.eventListeners = {
        open: [],
        close: [],
        error: [],
        message: []
    }

    this.connect()
};

WebSocketProxy.prototype.connect = function () {
    this.ws = new WebSocket(this.url)

    const that = this
    this.ws.onopen = function (event) {
        if (!$.isArray(that.eventListeners.open)) {
            return
        }

        that.eventListeners.open.forEach(i => {
            i(event)
        })
    }

    this.ws.onclose = function (event) {
        if (!$.isArray(that.eventListeners.close)) {
            return
        }

        that.eventListeners.close.forEach(i => {
            i(event)
        })

        if (that.options.reconnectOnClose) {
            ModalUtil.confirm(`[${that.options.serviceId}]连接已关闭（${event.reason || 'no reason'}），是否重新连接？`, null, index => {
                that.connect();

                ModalUtil.close(index);
            });
        }
    }

    this.ws.onerror = function (event) {
        if (!$.isArray(that.eventListeners.error)) {
            return
        }

        that.eventListeners.error.forEach(i => {
            i(event)
        })

        if (that.ws.readyState !== WebSocket.CLOSED && that.options.notifyOnError) {
            ModalUtil.msg(`[${that.options.serviceId}]通讯异常`, 'error')
        }
    }

    this.ws.onmessage = function (event) {
        if (!$.isArray(that.eventListeners.message)) {
            return
        }

        const _event = {
            ...event
        }

        // 序列化
        try {
            _event.data = JSON.parse(event.data)
        } catch (e) {
        }

        that.eventListeners.message.forEach(i => {
            i(_event)
        })
    }
};

WebSocketProxy.prototype.send = function (data) {
    if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.send($.isPlainObject(data) ? JSON.stringify(data) : data)
    } else {
        ModalUtil.msg(`[${this.options.serviceId}]通讯失败（状态${this.ws.readyState})`)
    }
};

WebSocketProxy.prototype.close = function () {
    this.ws.close()
};

WebSocketProxy.prototype.on = function (event, callback) {
    if (this.eventListeners.hasOwnProperty(event) && $.isFunction(callback)) {
        this.eventListeners[event].push(callback)
    }
};

WebSocketProxy.prototype.off = function (event, callback) {
    if (this.eventListeners.hasOwnProperty(event)) {
        const _index = this.eventListeners[event].indexOf(callback)
        this.eventListeners[event] = this.eventListeners[event].splice(_index, 1)
    }
};

function App() {
}

// 应用初始化
App.init = function () {
    new HttpRequest().ajax('/app/init', null).then();
};

App.entityDefaultRules = function (className, props) {
    const _promise = new Promise(resolve => {
        new HttpRequest().ajax('/util/validation/jquery', {
            'class': className
        }, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            resolve(result.code === 'OK' ? result.data : {});
        });
    });

    const _promises = {};
    $.each(props, function (index, val) {
        _promises[val] = new Promise(resolve => {
            _promise.then(data => {
                resolve(data[val]);
            });
        });
    });

    return _promises;
};
