let searchbar, items, selectedRegionId;

function operate() {
    return new Promise(resolve => {
        new HttpRequest().ajax('operate', null, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' && res.data === true);
        });
    });
}

function queryOrganization(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('../../sys/dept/organization/restricted', {
            'id': id,
            'roleName': '系统管理员'
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : {
                'item': null,
                'branches': []
            });
        });
    });
}

function fuzzy(count, index, regionId, name) {
    return new Promise(resolve => {
        new HttpRequest().ajax('fuzzy', {
            'count': count,
            'index': index,
            'regionId': selectedRegionId,
            'name': name,
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            // if (res.code === 'OK') {
            //     $.each(res.data.items, function (index, val) {
            //         const _array = JSON.parse(val.memberFullNamesJSON);
            //         val.members = _array.join('，');
            //     });
            // }

            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function remove(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('grid/remove', {
            'id': id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function initItems(permit) {
    const _options = {
        'columns': [{
            'title': '应急单元名称',
            'field': 'name',
            'control': 'label'
        }, {
            'title': '应急单元全称',
            'field': 'fullName',
            'control': 'label'
        }],
        'query': (count, index) => {
            return fuzzy(count, index, selectedRegionId, searchbar.name());
        },
        'btns': [null, null, {
            'name': '负责人',
            'icon': 'mdi-shield-account-outline',
            'callback': item => {
                ModalUtil.open({
                    'type': 2,
                    'title': '负责人',
                    'content': 'managers?regionId=' + item.id,
                    'area': ['90%', '90%']
                });
            }
        }],
        'autoInitialize': false
    };

    if (permit) {
        // _options.toolbar = [{
        //     'name': '新增',
        //     'icon': 'mdi-plus',
        //     'callback': () => {
        //         let _width = $(window).width();
        //         if (_width < 641) {
        //             _width = '90%';
        //         } else {
        //             _width = '60%';
        //         }
        //
        //         ModalUtil.open({
        //             'type': 2,
        //             'title': '新增',
        //             'area': [_width, '90%'],
        //             'content': 'grid',
        //             'end': function () {
        //                 items.query();
        //             }
        //         });
        //     }
        // }];

        // _options.btns[0] = {
        //     'callback': function (item) {
        //         let _width = $(window).width();
        //         if (_width < 641) {
        //             _width = '90%';
        //         } else {
        //             _width = '60%';
        //         }
        //
        //         ModalUtil.open({
        //             'type': 2,
        //             'title': '编辑',
        //             'area': [_width, '90%'],
        //             'content': 'grid?id=' + item.id,
        //             'end': function () {
        //                 items.query(items.getIndexCurrent());
        //             }
        //         });
        //     }
        // };
        // _options.btns[1] = {
        //     'callback': item => {
        //         return remove(item.id);
        //     }
        // };
        _options.btns[2] = {
            'name': '负责人',
            'icon': 'mdi-shield-account-outline',
            'callback': item => {
                console.log('btn');
                console.log(item)
                ModalUtil.open({
                    'type': 2,
                    'title': '负责人',
                    'content': 'unit/managers?regionId=' + item.id,
                    'area': ['90%', '90%']
                });
            }
        };
    }

    items = $('#table').table(_options);
}

function init() {
    new Vue({
        'el': '#departments',
        'data': {
            'dept': {},
            'subordinates': [],
            'deptMapper': {
                'id': 'id',
                'name': 'name',
                'code': 'code',
                'fullName': 'fullName',
                'hasSubordinates': 'hasSubordinates'
            }
        },
        'computed': {
            // 导航栏键
            'deptIdArr': function () {
                return this.dept[this.deptMapper.code] ? this.dept[this.deptMapper.code].split('.') : [];
            },
            // 导航栏
            'deptNameArr': function () {
                return this.dept[this.deptMapper.fullName] ? this.dept[this.deptMapper.fullName].split('/') : [];
            }
        },
        'methods': {
            '_queryOrganization': function (id) {
                const that = this;

                const _deptId = id ? id : null;

                queryOrganization(_deptId).then(data => {
                    // 设置部门
                    if ($.isEmptyObject(data.dept)) {
                        that.dept[that.deptMapper.id] = '';
                        that.dept[that.deptMapper.code] = '';
                        that.dept[that.deptMapper.fullName] = '';
                    } else {
                        that.dept = data.dept;
                    }

                    // 设置下级部门
                    that.subordinates = data.subordinates;

                    deptIdForMembers = id ? id : '';
                    selectedRegionId = id ? id : '';

                    items.query();
                });

            }
        },
        'mounted': function () {
            this._queryOrganization();
        }
    });

    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '名称',
            'icon': 'mdi-grid',
            'ref': 'name'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query'
        }]
    });

    operate().then(permit => {
        initItems(permit);
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});
