function captcha() {
    return new Promise(resolve => {
        new HttpRequest().ajax(window.location.contextPath + '/sec/login/captcha', {
            'width': 105,
            'height': 36,
            'length': 4
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(result => {
            resolve(result.code === 'OK' ? result.data : null);
        });
    });
}

function smsCode(mp) {
    return new Promise(resolve => {
        if (!mp) {
            resolve(null);

            ModalUtil.alert('请输入有效的手机号码');
            return;
        }

        new HttpRequest().ajax(window.location.contextPath + '/sec/login/sms-code', {
            'mp': mp
        }).then(result => {
            resolve(result.code === 'OK' ? result.data : null);
        });
    });
}

function login(auth, username, password, captcha, rememberMe) {
    return new Promise(resolve => {
        let _promise = null;

        switch (auth) {
            case 0:
                _promise = new HttpRequest().ajax(window.location.contextPath + '/sec/login/crypto/rsa/public-key', null, {
                    'msg': {
                        'success': false
                    }
                }).then(result => {
                    return result.code === 'OK'
                        ? new HttpRequest().ajax(window.location.contextPath + '/sec/login/password', {
                            'username': username,
                            'password': CryptoUtil.encryptRsa(password, result.data),
                            'captcha': captcha,
                            'rememberMe': rememberMe ? true : false,
                            'redirect': window.location.querystring('redirect')
                        }, {
                            'msg': {
                                'success': false
                            }
                        })
                        : Promise.resolve({
                            'code': result.code
                        });
                });
                break;
            case 1:
                _promise = new HttpRequest().ajax(window.location.contextPath + '/sec/login/sms', {
                    'mp': username,
                    'smsCode': password,
                    'rememberMe': rememberMe ? true : false,
                    'redirect': window.location.querystring('redirect')
                }, {
                    'msg': {
                        'success': false
                    }
                });
                break;
        }

        if (_promise != null) {
            _promise.then(result => {
                resolve(result.code === 'OK' ? (result.data ? result.data : '') : null);
            });
        }
    });
}

function appId() {
    return new Promise(resolve => {
        new HttpRequest().ajax(window.location.contextPath + '/util/cmpassport/app-id', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            resolve(result);
        });
    });
}

function sign(str) {
    return new Promise(resolve => {
        new HttpRequest().ajax(window.location.contextPath + '/sec/login/cmpassport/sign', {
            'str': str
        }, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            resolve(result);
        });
    });
}

function cmPassport(token, userInformation) {
    return new Promise(resolve => {
        new HttpRequest().ajax(window.location.contextPath + '/sec/login/cmpassport', {
            'token': token,
            'userInformation': userInformation,
            'redirect': window.location.querystring('redirect')
        }, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            resolve(result);
        });
    });
}

function loginByCMPassport() {
    return new Promise(resolve => {
        let _appId = null;
        appId()
            .then(result => {
                if (result.code !== 'OK') {
                    return result;
                } else {
                    _appId = result.data;

                    return {
                        'code': 'OK',
                        'data': YDRZ.getSign(_appId, '1.0')
                    };
                }
            }).then(result => {
            return result.code === 'OK' ? sign(result.data) : result;
        }).then(result => {
            return result.code === 'OK' ? new Promise(resolve1 => {
                YDRZ.getTokenInfo({
                    'data': {
                        'appId': _appId,
                        'version': '1.0',
                        'sign': result.data,
                        'openType': '1', // 0为三网号码，1为移动号码
                        'expandParams': 'phoneNum=13509889129',
                        'isTest': '0'
                    },
                    'success': result => {
                        resolve1(result);
                    },
                    'error': result => {
                        resolve1(result);
                    }
                });
            }) : result;
        }).then(result => {
            if (result.code !== 'OK') {
                resolve(result);
            } else {
                cmPassport(result.data.token, result.data.userInformation).then(result => {
                    resolve(result);
                });
            }
        });
    });
}

function loginByWx() {
    return new Promise(resolve => {
        new HttpRequest().ajax(window.location.contextPath + '/sec/login/wx', {
            'redirect': window.location.querystring('redirect')
        }, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            resolve(result);
        });
    });
}

function loginByYzy() {
    return new Promise(resolve => {
        new HttpRequest().ajax(window.location.contextPath + '/sec/login/yzy', {
            'redirect': window.location.querystring('redirect')
        }, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            resolve(result);
        });
    });
}

function init() {
    app = new Vue({
        'el': '#app',
        'data': {
            'options': {
                'auth': [{
                    'title': '帐号密码',
                    'icon': 'mdi-lock-outline',
                    'username': '',
                    'password': '',
                    'verCode': '',
                    'rememberMe': false,
                    'index': 0,
                    'show': true
                }, {
                    'title': '短信验证码',
                    'icon': 'mdi-email-outline',
                    'mp': '',
                    'smsCode': '',
                    'verCode': '',
                    'seconds': -1,
                    'rememberMe': false,
                    'timer': null,
                    'index': 1,
                    'show': true
                }, {
                    'title': '手机号码',
                    'icon': 'mdi-cellphone',
                    'index': 2,
                    'show': false
                }, {
                    'title': '企业号',
                    'icon': 'mdi-wechat',
                    'index': 3,
                    'show': false
                }, {
                    'title': '粤政易',
                    'icon': 'mdi-wechat',
                    'index': 4,
                    'show': false
                }]
            },

            'auth': 0,

            'captcha': {
                'image': '',
                'token': ''
            },

            'oauth': {
                'icon': '',
                'msg': ''
            },
            'showPassword': false
        },
        'methods': {
            '_switch': function (index) {
                const that = this;

                that.auth = index;
                switch (index) {
                    case 0:
                        that._captcha();
                        break;
                    case 2:
                        ModalUtil.confirm('即将使用您的手机号码一键登录，是否继续？', {
                            'title': false,
                            'icon': 3
                        }, function (index) {
                            ModalUtil.close(index);

                            that.oauth.icon = 'am-icon-circle-o-notch am-icon-spin';
                            that.oauth.msg = '正在为您登录，请稍候';

                            loginByCMPassport().then(result => {
                                if (result.code === 'OK') {
                                    ModalUtil.msg('登录成功，浏览器自动跳转中……', {
                                        'title': false,
                                        'icon': 1,
                                        'time': 2000
                                    }, function () {
                                        window.location.replace(result.data ? result.data : '/');
                                    });
                                } else {
                                    that.oauth.icon = 'am-icon-close am-text-danger';
                                    that.oauth.msg = '手机号码登录失败（' + result.message + '）';
                                }
                            })
                        }, function () {
                            let _array = $.grep(that.options.auth, function (val) {
                                return val.index < 2 && val.show;
                            });
                            if (_array.length > 0) {
                                that._switch(_array[0].index);
                            }
                        });
                        break;
                    case 3:
                        ModalUtil.confirm('即将使用您的微信号一键登录，是否继续？', {
                            'title': false,
                            'icon': 3
                        }, function (index) {
                            ModalUtil.close(index);

                            that.oauth.icon = 'am-icon-circle-o-notch am-icon-spin';
                            that.oauth.msg = '正在为您登录，请稍候';

                            loginByWx().then(result => {
                                if (result.code === 'OK') {
                                    window.location.replace(result.data);
                                } else {
                                    that.oauth.icon = 'am-icon-close am-text-danger';
                                    that.oauth.msg = '微信登录失败（' + result.message + '）';
                                }
                            });
                        }, function () {
                            let _array = $.grep(that.options.auth, function (val) {
                                return val.index < 2 && val.show;
                            });
                            if (_array.length > 0) {
                                that._switch(_array[0].index);
                            }
                        });
                        break;
                    case 4:
                        ModalUtil.confirm('即将使用您的粤政易账号一键登录，是否继续？', {
                            'title': false,
                            'icon': 3
                        }, function (index) {
                            ModalUtil.close(index);

                            that.oauth.icon = 'am-icon-circle-o-notch am-icon-spin';
                            that.oauth.msg = '正在为您登录，请稍候';

                            loginByYzy().then(result => {
                                if (result.code === 'OK') {
                                    window.location.replace(result.data);
                                } else {
                                    that.oauth.icon = 'am-icon-close am-text-danger';
                                    that.oauth.msg = '粤政易登录失败（' + result.message + '）';
                                }
                            });
                        }, function () {
                            let _array = $.grep(that.options.auth, function (val) {
                                return val.index < 2 && val.show;
                            });
                            if (_array.length > 0) {
                                that._switch(_array[0].index);
                            }
                        });
                        break;
                }

                that.$nextTick(() => {
                    $(that.$el).find('input[type=checkbox]').uCheck();
                });
            },
            '_captcha': function () {
                let that = this;

                that.captcha.token = '';
                that.captcha.image = '';

                captcha().then(data => {
                    that.captcha = data;
                });
            },
            '_smsCode': function () {
                const that = this;

                smsCode(that.options.auth[1].mp).then(seconds => {
                    if ($.isNumeric(seconds)) {
                        that.options.auth[1].seconds = seconds;

                        that.options.auth[1].timer = setInterval(() => {
                            if (that.options.auth[1].seconds > 0) {
                                that.options.auth[1].seconds -= 1;
                            }
                            // 结束倒计时
                            else {
                                clearInterval(that.options.auth[1].timer);

                                that.options.auth[1].seconds = -1;
                            }
                        }, 1000);
                    }
                })
            },
            'togglePassword': function () {
                this.showPassword = !this.showPassword;
            },
            'login': function () {
                const that = this;

                let _promise = null;
                switch (that.auth) {
                    case 0:
                        if (!that.options.auth[0].username || !that.options.auth[0].password || !that.options.auth[0].verCode) {
                            ModalUtil.alert('请输入账号、密码和图形验证码');
                            return;
                        }

                        _promise = login(that.auth, that.options.auth[0].username, that.options.auth[0].password, {
                            'token': that.captcha.token,
                            'text': that.options.auth[0].verCode
                        }, that.options.auth[0].rememberMe);
                        break;
                    case 1:
                        if (!that.options.auth[1].mp || !that.options.auth[1].smsCode) {
                            ModalUtil.alert('请输入手机号码和短信验证码');
                            return;
                        }

                        _promise = login(that.auth, that.options.auth[1].mp, that.options.auth[1].smsCode, null, that.options.auth[1].rememberMe);
                        break;
                }

                if (_promise != null) {
                    _promise.then(url => {
                        if (url === null) {
                            // 更新图形验证码
                            that._captcha();

                            return;
                        }

                        ModalUtil.msg('登录成功，浏览器自动跳转中……', {
                            'title': false,
                            'icon': 1,
                            'time': 2000
                        }, function () {
                            window.location.replace(url ? url : '/');
                        });
                    });
                }
            }
        },
        'mounted': function () {
            this._captcha();

            $(this.$el).find('input[type=checkbox]').uCheck();
        }
    });

    $('#app').keyup(function (event) {
        if (event.keyCode === 13) {
            app.login();
        }
    });
}

$(function () {
    init();
});