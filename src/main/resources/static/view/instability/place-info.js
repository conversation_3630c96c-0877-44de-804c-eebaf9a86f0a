let searchbar, table;

function fuzzy(count, index, regionFullName) {
    const _data = {
        'count': count,
        'index': index,
        'regionFullName': regionFullName
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('place-info/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    val.region = [{
                        'id': val.regionId,
                        'fullName': val.regionFullName
                    }];
                });

                resolve(res.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function save(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('place-info/save', item).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('place-info/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function importFromFile(file) {
    let _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('place-info/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('村（社）专项管理重点场所信息.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function initTable() {
    table = $('#table').ediTable({
        'columns': [{
            'title': '行政区域',
            'field': 'region',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                },
                'beforeItemAdd': event => {
                    let department = event.item?.value;
                    if (department && department.level !== 3) {
                        ModalUtil.alert('请选择村、社区一级行政区域');
                        event.cancel = true;
                    }
                },
                'rule': {
                    'required': true
                }
            },
            'width': 150
        }, {
            'title': '党政机关数',
            'field': 'numOfGovernmentAgencies',
            'control': 'number',
            'width': 120
        }, {
            'title': '繁华商圈总数',
            'field': 'numOfBusinessAreas',
            'control': 'number',
            'width': 120
        }, {
            'title': '繁华商圈-步行街数',
            'field': 'numOfPedestrianStreets',
            'control': 'number',
            'width': 120
        }, {
            'title': '繁华商圈-大型商场数',
            'field': 'numOfShoppingMalls',
            'control': 'number',
            'width': 120
        }, {
            'title': '繁华商圈-城市综合体数',
            'field': 'numOfUrbanComplexes',
            'control': 'number',
            'width': 120
        }, {
            'title': '车站数',
            'field': 'numOfStations',
            'control': 'number',
            'width': 120
        }, {
            'title': '码头数',
            'field': 'numOfDocks',
            'control': 'number',
            'width': 120
        }, {
            'title': '广场数',
            'field': 'numOfSquares',
            'control': 'number',
            'width': 120
        }, {
            'title': '标志性建筑物数',
            'field': 'numOfLandmarkBuildings',
            'control': 'number',
            'width': 120
        }, {
            'title': '天桥数',
            'field': 'numOfOverpasses',
            'control': 'number',
            'width': 120
        }, {
            'title': '公共电子显示屏数',
            'field': 'numOfLedDisplays',
            'control': 'number',
            'width': 120
        }, {
            'title': '文体场馆数',
            'field': 'numOfCulturalAndSportsVenues',
            'control': 'number',
            'width': 120
        }, {
            'title': '旅游景区数',
            'field': 'numOfTouristAttractions',
            'control': 'number',
            'width': 120
        }, {
            'title': '学校总数',
            'field': 'numOfSchools',
            'control': 'number',
            'width': 120
        }, {
            'title': '学校-大学（大专）数',
            'field': 'numOfCollegesAndUniversities',
            'control': 'number',
            'width': 120
        }, {
            'title': '学校-中学数',
            'field': 'numOfMiddleSchools',
            'control': 'number',
            'width': 120
        }, {
            'title': '学校-小学数',
            'field': 'numOfPrimarySchools',
            'control': 'number',
            'width': 120
        }, {
            'title': '学校-幼儿园数',
            'field': 'numOfKindergartens',
            'control': 'number',
            'width': 120
        }, {
            'title': '医疗机构总数',
            'field': 'numOfMedicalInstitutions',
            'control': 'number',
            'width': 120
        }, {
            'title': '医疗机构-医院数',
            'field': 'numOfHospitals',
            'control': 'number',
            'width': 120
        }, {
            'title': '医疗机构-卫生站数',
            'field': 'numOfHealthStations',
            'control': 'number',
            'width': 120
        }, {
            'title': '信访接待场所数',
            'field': 'numOfPetitionReceptionPlaces',
            'control': 'number',
            'width': 150
        }, {
            'title': '宗教场所总数',
            'field': 'numOfReligiousPlaces',
            'control': 'number',
            'width': 120
        }, {
            'title': '宗教场所-私人宗教聚集点数',
            'field': 'numOfPrivateReligiousPlaces',
            'control': 'number',
            'width': 150
        }, {
            'title': '休闲娱乐场所总数',
            'field': 'numOfEntertainmentPlaces',
            'control': 'number',
            'width': 120
        }, {
            'title': '休闲娱乐场所-酒店数',
            'field': 'numOfHotels',
            'control': 'number',
            'width': 120
        }, {
            'title': '休闲娱乐场所-KTV数',
            'field': 'numOfKtv',
            'control': 'number',
            'width': 120
        }, {
            'title': '休闲娱乐场所-酒吧数',
            'field': 'numOfBars',
            'control': 'number',
            'width': 120
        }, {
            'title': '休闲娱乐场所-歌舞厅数',
            'field': 'numOfDanceHalls',
            'control': 'number',
            'width': 120
        }, {
            'title': '休闲娱乐场所-沐足按摩数',
            'field': 'numOfMassages',
            'control': 'number',
            'width': 120
        }, {
            'title': '休闲娱乐场所-棋牌室数',
            'field': 'numOfChessAndCardRooms',
            'control': 'number',
            'width': 120
        }, {
            'title': '休闲娱乐场所-茶座数',
            'field': 'numOfTeaHouses',
            'control': 'number',
            'width': 120
        }],
        'query': (count, index) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, _regions.length > 0 ? _regions[0].fullName : null);
        },
        'create': true,
        'save': item => {
            return new Promise(resolve => {
                const _region = item.region && item.region.length > 0 ? item.region[0] : null;
                item.regionId = _region === null ? null : _region.id;
                item.regionFullName = _region === null ? null : _region.fullName;
                
                // 删除region字段，避免传递给后端
                delete item.region;

                save(item).then(success => {
                    resolve({
                        'success': success,
                        'scrollToFirstPage': !item.hasOwnProperty('id')
                    });
                });
            });
        },
        'remove': remove,
        'scrollX': true,
        'scrollY': 'calc(100vh - 200px)'
    });

    // 添加固定列样式
    $('<style>')
        .text(`
            .am-scrollable-horizontal {
                position: relative;
                overflow-x: auto;
            }
            /* 设置表头行的最小宽度 */
            .am-table > thead > tr > th {
                min-width: 150px;
            }
            /* 固定表头和内容的操作列 */
            .am-table .am-hide-sm-only,
            .am-table th:has(> a > .mdi-menu) {
                position: sticky !important;
                right: 0;
                background: #fff !important;
                z-index: 2;
            }
            /* 固定第一列（行政区域） */
            .am-table th:first-child,
            .am-table td:first-child {
                position: sticky !important;
                left: 0;
                background: #fff !important;
                z-index: 2;
                min-width: 230px !important;
            }
            /* 添加右侧阴影效果 */
            .am-table .am-hide-sm-only::before,
            .am-table th:has(> a > .mdi-menu)::before {
                content: '';
                position: absolute;
                top: 0;
                left: -8px;
                height: 100%;
                width: 8px;
                background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
            }
            /* 添加左侧阴影效果 */
            .am-table th:first-child::after,
            .am-table td:first-child::after {
                content: '';
                position: absolute;
                top: 0;
                right: -8px;
                height: 100%;
                width: 8px;
                background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
            }
            /* 确保操作列按钮组样式正确 */
            .am-table .am-hide-sm-only .am-btn-group {
                background: #fff;
            }
            /* 确保表格边框正确显示 */
            .am-table {
                border-collapse: separate;
                border-spacing: 0;
            }
            /* 处理左右固定列的交叉区域 */
            .am-table th:first-child {
                z-index: 3;
            }
        `)
        .appendTo('head');
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                table.query();
            },
            'ref': 'query'
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            }
        }]
    });

    initTable();

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                table.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            table.query();
        }
    });
}

$(function () {
    init();
});
