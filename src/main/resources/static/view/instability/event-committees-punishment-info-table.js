let searchbar, table;

function fuzzy(count, index, regionFullName, category, type, title, overview) {
    const _data = {
        count,
        index,
        regionFullName,
        category,
        type,
        title,
        overview
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('event-committees-punishment-info/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    val.region = [{
                        'id': val.regionId,
                        'fullName': val.regionFullName
                    }];
                });

                resolve(res.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function categories() {
    return new Promise(resolve => {
        new HttpRequest().ajax('event-committees-punishment-info/categories', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('event-committees-punishment-info/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function importFromFile(file) {
    let _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('event-committees-punishment-info/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('村（社）专项管理重点事件信息.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function render(id) {
    ModalUtil.open({
        'title': id === null ? '新增事件' : '编辑事件',
        'type': 2,
        'maxmin': true,
        'area': ['55%', '80%'],
        'content': 'event-committees-punishment-info/form' + (id === null ? '' : ('?id=' + id)),
        'end': function () {
            table.query(id === null ? null : table.getIndexCurrent());
        }
    });
}

function initTable() {
    table = $('#table').table({
        'columns': [{
            'title': '行政区域',
            'field': 'regionFullName',
            'control': 'label',
            'width': 300
        }, {
            'title': '姓名',
            'field': 'name',
            'control': 'label',
            'width': 120
        }, {
            'title': '职务',
            'field': 'position',
            'control': 'label',
            'width': 120
        }, {
            'title': '事件标题',
            'field': 'title',
            'control': 'label',
            'width': 200
        }, {
            'title': '事件分类',
            'field': 'category',
            'control': 'label',
            'width': 120
        }, {
            'title': '事件类型',
            'field': 'type',
            'control': 'label',
            'width': 120
        }, {
            'title': '事件概述',
            'field': 'overview',
            'control': 'label',
            'width': 300
        }, {
            'title': '事发时间',
            'field': 'incidentTime',
            'control': 'label'
        }],
        'query': (count, index) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, _regions.length > 0 ? _regions[0].fullName : null, searchbar.category(), searchbar.type(), searchbar.title(), searchbar.overview());
        },
        'btns': [{
            'callback': item => {
                render(item.id);
            }
        }, {
            'callback': remove
        }],
        'toolbar': [{
            'name': '新增',
            'icon': 'mdi-plus',
            'callback': () => {
                render(null);
            }
        }],
        'scrollX': true,
        'scrollY': 'calc(100vh - 200px)',
        'hideOnSinglePage': false
    });
}

function init() {
    const optionPromise = categories();

    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }, {
            'field': 'category',
            'title': '事件分类',
            'icon': 'mdi-menu',
            'control': 'select',
            'validator': new Promise(resolve => {
                optionPromise.then(categories => {
                    const _params = {
                        'options': []
                    };

                    for (const prop in categories) {
                        _params.options.push({
                            'key': prop,
                            'value': prop
                        });
                    }

                    resolve(_params);
                });
            }),
            'ref': 'category',
            'row': 1
        }, {
            'field': 'type',
            'title': '事件类型',
            'icon': 'mdi-filter-menu',
            'control': 'cascader',
            'validator': new Promise(resolve => {
                optionPromise.then(categories => {
                    resolve({
                        'relativeFieldIndex': 1,
                        'mapper': categories
                    });
                });
            }),
            'ref': 'type',
            'row': 1
        }, {
            'title': '事件标题',
            'icon': 'mdi-archive-outline',
            'ref': 'title',
            'row': 1
        }, {
            'title': '事件概述',
            'icon': 'mdi-checkbook',
            'ref': 'overview',
            'row': 1
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                table.query();
            },
            'ref': 'query'
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            }
        }]
    });

    initTable();

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                table.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            table.query();
        }
    });
}

$(function () {
    init();
});
