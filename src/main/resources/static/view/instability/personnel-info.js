let searchbar, table;

function fuzzy(count, index, regionFullName) {
    const _data = {
        'count': count,
        'index': index,
        'regionFullName': regionFullName
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('personnel-info/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    val.region = [{
                        'id': val.regionId,
                        'fullName': val.regionFullName
                    }];
                });

                resolve(res.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function save(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('personnel-info/save', item).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('personnel-info/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function importFromFile(file) {
    let _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('personnel-info/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('村（社）专项管理重点人员信息.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function initTable() {
    table = $('#table').ediTable({
        'columns': [{
            'title': '行政区域',
            'field': 'region',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                },
                'beforeItemAdd': event => {
                    let department = event.item?.value;
                    if (department && department.level !== 3) {
                        ModalUtil.alert('请选择村、社区一级行政区域');
                        event.cancel = true;
                    }
                },
                'rule': {
                    'required': true
                }
            },
            'width': 1000
        }, {
            'title': '政治安全重点人员数',
            'field': 'numOfPoliticalSecurityKeyPersonnel',
            'control': 'number',
            'width': 150
        }, {
            'title': '律师重点人员数',
            'field': 'numOfLawyerKeyPersonnel',
            'control': 'number',
            'width': 150
        }, {
            'title': '邪教人员总数',
            'field': 'numOfCultMembers',
            'control': 'number',
            'width': 120
        }, {
            'title': '邪教人员-法轮功人员数',
            'field': 'numOfFaLunGongMembers',
            'control': 'number',
            'width': 120
        }, {
            'title': '邪教人员-全能神人员数',
            'field': 'numOfAlmightyGodMembers',
            'control': 'number',
            'width': 120
        }, {
            'title': '邪教人员-其他邪教人员数',
            'field': 'numOfOtherCultMembers',
            'control': 'number',
            'width': 150
        }, {
            'title': '信访重点人员总数',
            'field': 'numOfPetitionKeyPersonnel',
            'control': 'number',
            'width': 120
        }, {
            'title': '信访重点人员-"三跨三分离"人员数',
            'field': 'numOfThreeCrossThreeSeparationPersonnel',
            'control': 'number',
            'width': 150
        }, {
            'title': '网络重点人员数',
            'field': 'numOfOnlineKeyPersonnel',
            'control': 'number',
            'width': 120
        }, {
            'title': '涉众金融投资受损人员数',
            'field': 'numOfFinanciallyAffectedPersons',
            'control': 'number',
            'width': 180
        }, {
            'title': '军队退役人员总数',
            'field': 'numOfMilitaryVeterans',
            'control': 'number',
            'width': 120
        }, {
            'title': '军队退役人员-涉访重点人员数',
            'field': 'numOfPetitionRelatedKeyPersonnel',
            'control': 'number',
            'width': 150
        }, {
            'title': '疫苗"受害"人员数',
            'field': 'numOfVaccineVictims',
            'control': 'number',
            'width': 150
        }, {
            'title': '"三新"从业人员数',
            'field': 'numOfThreeNewPersonnel',
            'control': 'number',
            'width': 150
        }, {
            'title': '严重精神障碍患者总数',
            'field': 'numOfSevereMentalDisorderPatients',
            'control': 'number',
            'width': 150
        }, {
            'title': '严重精神障碍患者-危险性评估三级以上精神障碍患者数',
            'field': 'numOfHighRiskMentalDisorderPatients',
            'control': 'number',
            'width': 220
        }, {
            'title': '刑满释放安置帮教人员总数',
            'field': 'numOfReleasedPrisoners',
            'control': 'number',
            'width': 180
        }, {
            'title': '刑满释放安置帮教人员-重点帮教人员数',
            'field': 'numOfKeyEducationAndHelpPersonnel',
            'control': 'number',
            'width': 150
        }, {
            'title': '社区矫正人员总数',
            'field': 'numOfCommunityCorrectionPersonnel',
            'control': 'number',
            'width': 150
        }, {
            'title': '社区矫正人员-重点社矫人员数',
            'field': 'numOfKeyCommunityCorrectionPersonnel',
            'control': 'number',
            'width': 150
        }, {
            'title': '吸毒人员总数',
            'field': 'numOfDrugAddicts',
            'control': 'number',
            'width': 120
        }, {
            'title': '吸毒人员-社戒社康人员数',
            'field': 'numOfCommunityDrugRehabilitations',
            'control': 'number',
            'width': 150
        }, {
            'title': '"八类"重点未成年人数',
            'field': 'numOfEightCategoriesKeyMinors',
            'control': 'number',
            'width': 170
        }, {
            'title': '"三失一偏"人员数',
            'field': 'numOfThreeLossesOneBiasPersonnel',
            'control': 'number',
            'width': 150
        }, {
            'title': '低保在册人员数',
            'field': 'numOfRegisteredLowIncomePersonnel',
            'control': 'number',
            'width': 150
        }],
        'query': (count, index) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, _regions.length > 0 ? _regions[0].fullName : null);
        },
        'create': true,
        'save': item => {
            return new Promise(resolve => {
                const _region = item.region && item.region.length > 0 ? item.region[0] : null;
                item.regionId = _region === null ? null : _region.id;
                item.regionFullName = _region === null ? null : _region.fullName;
                
                // 删除region字段，避免传递给后端
                delete item.region;

                save(item).then(success => {
                    resolve({
                        'success': success,
                        'scrollToFirstPage': !item.hasOwnProperty('id')
                    });
                });
            });
        },
        'remove': remove,
        'scrollX': true,
        'scrollY': 'calc(100vh - 200px)'
    });

    // 添加固定列样式
    $('<style>')
        .text(`
            .am-scrollable-horizontal {
                position: relative;
                overflow-x: auto;
            }
            /* 设置表头行的最小宽度 */
            .am-table > thead > tr > th {
                min-width: 150px;
            }
            /* 固定表头和内容的操作列 */
            .am-table .am-hide-sm-only,
            .am-table th:has(> a > .mdi-menu) {
                position: sticky !important;
                right: 0;
                background: #fff !important;
                z-index: 2;
            }
            /* 固定第一列（行政区域） */
            .am-table th:first-child,
            .am-table td:first-child {
                position: sticky !important;
                left: 0;
                background: #fff !important;
                z-index: 2;
                min-width: 230px !important;
            }
            /* 添加右侧阴影效果 */
            .am-table .am-hide-sm-only::before,
            .am-table th:has(> a > .mdi-menu)::before {
                content: '';
                position: absolute;
                top: 0;
                left: -8px;
                height: 100%;
                width: 8px;
                background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
            }
            /* 添加左侧阴影效果 */
            .am-table th:first-child::after,
            .am-table td:first-child::after {
                content: '';
                position: absolute;
                top: 0;
                right: -8px;
                height: 100%;
                width: 8px;
                background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
            }
            /* 确保操作列按钮组样式正确 */
            .am-table .am-hide-sm-only .am-btn-group {
                background: #fff;
            }
            /* 确保表格边框正确显示 */
            .am-table {
                border-collapse: separate;
                border-spacing: 0;
            }
            /* 处理左右固定列的交叉区域 */
            .am-table th:first-child {
                z-index: 3;
            }
        `)
        .appendTo('head');
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                table.query();
            },
            'ref': 'query'
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            }
        }]
    });

    initTable();

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                table.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            table.query();
        }
    });
}

$(function () {
    init();
});
