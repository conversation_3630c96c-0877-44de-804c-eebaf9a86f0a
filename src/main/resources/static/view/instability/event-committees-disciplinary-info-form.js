function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('get', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function save(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('save', item).then(res => {
            resolve(res);
        });
    });
}

function initForm() {
    const categories = new Promise(resolve => {
        new HttpRequest().ajax('categories', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });

    // 获取当前用户信息
    const userInfo = new Promise(resolve => {
        new HttpRequest().ajax('../../app/layout/header', {
            'root': false
        }, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });

    const form = $('#form').form({
        'contextPath': '../../',
        'rows': [{
            'title': '行政区域',
            'field': 'region',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                },
                'beforeItemAdd': event => {
                    let department = event.item?.value;
                    if (department && (department.level < 3 || department.level > 4)) {
                        ModalUtil.alert('请选择村、社区或网格一级行政区域');
                        event.cancel = true;
                    }
                },
                'rule': {
                    'required': true
                }
            },
            'width': 300
        }, {
            'title': '姓名',
            'field': 'name',
            'control': 'text',
            'autocomplete': 'off',
            'validator': {
                'rule': {
                    'required': true,
                    'onlyChinese': true,
                    'maxlength': 4
                }
            }
        }, {
            'title': '职务',
            'field': 'position',
            'control': 'text',
            'validator': {
                'rule': {
                    'required': true,
                    'onlyChinese': true
                }
            }
        }, {
            'title': '事件标题',
            'field': 'title',
            'control': 'text',
            'validator': {
                'rule': {
                    'required': true,
                    'containsChinese': true
                }
            }
        }, {
            'title': '事件分类',
            'field': 'category',
            'control': 'select',
            'validator': new Promise(resolve => {
                categories.then(cat => {
                    const validator = {
                        'options': [],
                        'rule': {
                            'required': true
                        }
                    };

                    for (const prop in cat) {
                        validator.options.push({
                            'key': prop,
                            'value': prop
                        });
                    }
                    // $.extend(validator, dto[1]);

                    resolve(validator);
                });
            })
        }, {
            'title': '事件类型',
            'field': 'type',
            'control': 'cascader',
            'validator': new Promise(resolve => {
                categories.then(cat => {
                    const validator = {
                        'rule': {
                            'required': true
                        },
                        'relativeField': 'category',
                        'mapper': cat
                    };

                    // $.extend(validator, dto[1]);

                    resolve(validator);
                });
            })
        }, {
            'title': '事件概述',
            'field': 'overview',
            'control': 'textarea',
            'validator': {
                'rule': {
                    'required': true,
                    'containsChinese': true
                }
            }
        }, {
            'title': '事发时间',
            'field': 'incidentTime',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH:mm:ss',
                'rule': {
                    'required': false
                }
            }
        }],
        'btns': [{
            'callback': item => {
                return new Promise(resolve => {
                    const _region = item.region && item.region.length > 0 ? item.region[0] : null;
                    item.regionId = _region === null ? null : _region.id;
                    item.regionFullName = _region === null ? null : _region.fullName;
                    item.address = '';
                    // item.address = item.location.address;
                    // item.lng = item.location.lng;
                    // item.lat = item.location.lat;

                    // 删除region字段，避免传递给后端
                    delete item.region;

                    save(item).then(res => {
                        if (res && res.code === 'OK') {
                            form.setData({});
                        // } else {
                        //     resolve(false);
                            setTimeout(() => {
                                let index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                                parent.layer.close(index); //再执行关闭
                            }, 500);
                        }
                        resolve({
                            'success': res.code === 'OK',
                            'scrollToFirstPage': !item.hasOwnProperty('id')
                        });
                    });
                });
            }
        }]
    });

    const id = window.location.querystring('id');
    if (id) {
        get(id).then(data => {
            if (data === null) {
                return;
            }

            data.region = [{
                'id': data.regionId,
                'fullName': data.regionFullName
            }];

            // data.location = {
            //     'address': data.address,
            //     'lng': data.lng,
            //     'lat': data.lat
            // };

            form.setData(data);
        });
    } else {
        // 新增时设置默认标题
        Promise.all([userInfo, categories]).then(([user, cat]) => {
            if (user && user.user) {
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const dateStr = `${year}年${month}月${day}日`;
                
                // 获取第一个事件分类作为默认值
                const firstCategory = Object.keys(cat)[0];
                
                // 格式化行政区域名称，只保留到街道一级
                let deptName = '';
                if (user.user.deptFullName && user.user.deptFullName !== '默认') {
                    const parts = user.user.deptFullName.split('/');
                    for (let i = 0; i < Math.min(parts.length, 2); i++) {
                        deptName += parts[i];
                    }
                }
                
                form.setData({
                    'title': `${dateStr}${deptName}${firstCategory}`
                });
            }
        });
    }
}

$(function () {
    initForm();
});
