let app = null;

function getUser() {
    return new HttpRequest().ajax('app/layout/user', null, {
        'loading': false,
        'msg': {
            'success': false,
            'error': false
        }
    });
}

function getUrl() {
    return new HttpRequest().ajax('monitoring-screen/url', null, {
        'loading': false,
        'msg': {
            'success': false,
            'error': false
        }
    });
}

function openAlarm() {
    return new HttpRequest().ajax('monitoring-screen/alarm/open', null, {
        'loading': false,
        'msg': {
            'success': false,
            'error': false
        }
    });
}

function closeAlarm() {
    return new HttpRequest().ajax('monitoring-screen/alarm/close', null, {
        'loading': false,
        'msg': {
            'success': false,
            'error': false
        }
    }).catch(() => {
        // 登录超时等异常静默处理
    });
}

function ws() {
    return new Promise(resolve => {
        new HttpRequest().ajax('app/ws', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            resolve(result.code === 'OK' ? new WebSocketProxy(result.data + '/emergency/alarm/conn', {
                'serviceId': '告警列表'
            }) : null);
        })
    });
}

function init() {
    const modal = new Vue({
        'el': '#window',
        'data': {
            'records': [],
            'focus': 0,
            'timestamp': new Date(),
            'show': false
        },
        'computed': {
            'record': function () {
                if (this.records.length === 0) {
                    return {
                        'id': null,
                        'grid': '-',
                        'unit': '-',
                        'type': '-',
                        'alarmTime': '-'
                    }
                }

                const _record = this.records[this.focus];

                const _region = _record.regionFullName
                const _array = _region.split('/');

                return {
                    'id': _record.id,
                    'grid': _array.length > 3 ? _array[3] : '-',
                    'unit': _array.length > 4 ? _array[4] : '-',
                    'type': _record.type,
                    'alarmTime': _record.alarmTime
                }
            }
        },
        'methods': {
            'getTimestamp': function () {
                return this.timestamp;
            },
            'update': function (records) {
                const that = this

                let _records = $.grep(records, function (val) {
                    return new Date(val.alarmTime) > that.timestamp;
                });

                if (_records.length > 0) {
                    // 打开报警器
                    openAlarm();

                    // 更新时间戳
                    that.timestamp = new Date(records[0].alarmTime);

                    // 已弹窗，则合并告警列表
                    if (that.show) {
                        _records = _records.concat(that.records);
                    }

                    _records = _records.slice(0, 5);

                    that.records = _records;

                    that.focus = 0;
                }

                if (that.records.length > 0 && !that.show) {
                    that.$nextTick(function () {
                        ModalUtil.open({
                            'type': 1,
                            'title': false,
                            //'area': [_width, '90%'],
                            'content': $('#window'),
                            'maxmin': false,
                            'success': function () {
                                that.show = true;
                            },
                            'end': function () {
                                // 关闭报警器
                                closeAlarm();

                                that.show = false;

                                that.records = [];
                            }
                        });
                    });
                }
            },
            '_select': function (index) {
                this.focus = index;
            },
            '_goto': function () {
                // 关闭报警器
                closeAlarm();

                if (this.record.id !== null) {
                    // 打开应急指挥调度中心
                    // window.open('../eoc/#/?alarmId=' + this.record.id)
                    window.open('https://tianshu.fits.com.cn:3024')
                }
            }
        }
    })

    app = new Vue({
        'el': '#app',
        'data': {
            'src': '',
            'message': '',
            'ws': null,
            'region': null,
            'timer': null
        },
        'methods': {
            'reload': function () {
                if (!this.src) {
                    return;
                }

                const _src = this.src;
                this.src = '';
                this.$nextTick(function () {
                    this.src = _src;
                });
            },
            'mock': function () {
                modal.update([{
                    'id': '19890306',
                    'regionFullName': '澄海区/澄华街道/华文/澄华街道华文第四网格/爱德堡幼儿园',
                    'deviceName': '精灵4 pro V2.0',
                    'type': '河道污染',
                    'alarmTime': new Date().format('yyyy-MM-dd HH:mm:ss')
                }]);
            }
        },
        'mounted': function () {
            const that = this;

            Promise.all([getUser(), getUrl()]).then(values => {
                const _user = values[0], _url = values[1];

                if (_user.code !== 'OK') {
                    that.message = _user.message;
                    return;
                }

                if (!_user.data.deptFullName) {
                    that.message = '系统未检索到您的部门信息，请求失败';
                    return;
                }

                that.region = _user.data.deptFullName;

                if (['默认'].indexOf(_user.data.deptFullName) === -1) {
                    that.region = _user.data.deptFullName;

                    _user.data.deptFullName = '/' + _user.data.deptFullName;
                } else {
                    that.region = '';

                    _user.data.deptFullName = '';
                }

                if (_url.code !== 'OK') {
                    that.message = _url.message;
                    return;
                }
                that.src = _url.data + '&belongTo=汕头市' + _user.data.deptFullName;

                // 对村居用户开启告警
                if (that.region.split('/').length === 3) {
                    ws().then(conn => {
                        if (conn === null) {
                            return;
                        }

                        that.ws = conn;

                        that.ws.on('open', function () {
                            // 注册客户端
                            that.ws.send({
                                subject: 'register_screen'
                            })
                        });

                        that.ws.on('message', function (event) {
                            switch (event.data.subject) {
                                case 'warn':
                                    that.ws.send({
                                        subject: 'find',
                                        data: {
                                            count: 10,
                                            index: 0,
                                            regionFullName: that.region,
                                            from: modal.getTimestamp().format('yyyy-MM-dd HH:mm:ss'),
                                            status: 'TODO'
                                        }
                                    });

                                    break;
                                case 'find':
                                    modal.update(event.data.data.items);

                                    break;
                            }
                        });
                    });
                }
            });

            const _time = window.location.querystring('t')
            if ($.isNumeric(_time)) {
                setTimeout(() => {
                    app.mock()
                }, _time * 1000);
            }
        }
    });

    $('.tpl-header-switch-button').click(function () {
        setTimeout(() => {
            app.reload();
        }, 400);
    });

    $(window).on('beforeunload', () => {
        // 关闭报警器
        // closeAlarm();
    })
}

$(function () {
    init();
});
