let searchbar, app, items;

function query(recordDate) {
    const _data = {
        recordDate
    };

    return new Promise(resolve => {
        new HttpRequest().ajax(`routine-inspection/query`, _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function exportTo(recordDate) {
    const _data = {
        recordDate
    };

    new HttpRequest().ajax('routine-inspection/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('日常巡查表.xlsx', null, res.data);
        }
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '统计时间',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd'
            },
            'ref': 'recordDate'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                app.query(searchbar.recordDate());
            },
            'ref': 'query'
        }, {
            'title': '导出',
            'icon': 'mdi-download',
            'callback': () => {
                exportTo(searchbar.recordDate());
            }
        }]
    });

    app = new Vue({
        'el': '#app',
        'data': {
            'headers': [{
                'title': '类型',
                'ref': 'category'
            }, {
                'title': '类别',
                'ref': 'type'
            }, {
                'title': '数量',
                'ref': 'total'
            }, {
                'title': '增量',
                'ref': 'diff'
            }],
            'rows': []
            // 'tabs': [{
            //     'type': 'total',
            //     'name': '系统数据',
            //     'cols': [
            //         '数据'
            //     ],
            //     'active': true
            // }, {
            //     'type': 'diff',
            //     'name': '数据增量',
            //     'cols': [
            //         '数据'
            //     ]
            // }],
            // 'items': {
            //     'total': [],
            //     'diff': []
            // }
        },
        'methods': {
            'query': function (recordDate) {
                let that = this;

                query(recordDate).then(items => {
                    that.rows.splice(0, that.rows.length);

                    if (items && items.length) {
                        // 首行为头部
                        // that.headers = items.shift();

                        $.each(items, function (index, val) {
                            that.rows.push(val);
                        });
                    }
                });
            }
        },
        'mounted': function() {
            this.query(searchbar.recordDate());
        }
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            app.query();
        }
    });
}

$(function () {
    init();
});
