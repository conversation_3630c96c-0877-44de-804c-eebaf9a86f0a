let searchbar, items;
const attachments = {};

function fuzzy(count, index, regionFullName, name, type, address) {
    const _data = {
        'count': count,
        'index': index,
        'regionFullName': regionFullName,
        'name': name,
        'type': type,
        'address': address
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('property/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(result => {
            if (result.code === 'OK') {
                $.each(result.data.items, function (index, val) {
                    val.region = [{
                        'id': val.regionId,
                        'fullName': val.regionFullName
                    }];

                    val.location = {
                        'address': val.address,
                        'lat': val.lat,
                        'lng': val.lng
                    };

                    val['_ext_'] = {
                        'btns': [0, 1]
                    };

                    if (val.platform === 'ANDMU') {
                        val['_ext_'].btns.push(2)
                    }

                    if (val.type === '摄像头') {
                        val['_ext_'].btns.push(3, 4);
                    }

                    if ($.isArray(val.attachments)) {
                        $.each(val.attachments, function (index, value) {
                            value.url = '../media/download?id=' + value.id;
                        });
                    }
                });

                resolve(result.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function save(item) {
    const _attachments = [], _attachmentIds = [];
    if ($.isArray(item.attachments)) {
        $.each(item.attachments, function (index, val) {
            // 新增文件
            if (val.hasOwnProperty('_id')) {
                _attachments.push(attachments[val._id]);
            }

            // 保持文件
            if (val.hasOwnProperty('id')) {
                _attachmentIds.push(val.id);
            }
        });
    }

    item.attachmentIds = _attachmentIds;

    const _data = new FormData();
    _data.append('item', new Blob([JSON.stringify(item)], {
        'type': 'application/json'
    }));

    $.each(_attachments, function (index, val) {
        _data.append('files', val);
    });

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('property/save', _data).then(result => {
            resolve(result.code === 'OK');
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('property/remove', {
            'id': item.id
        }).then(result => {
            resolve(result.code === 'OK');
        });
    });
}

function importFromFile(file) {
    const _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('property/import', _data, {
            'msg': {
                'success': false
            }
        }).then(result => {
            if (result.code === 'OK') {
                FileUtil.downloadFromBase64('重点物品.xlsx', null, result.data);
            }

            resolve(true);
        });
    });
}

function types() {
    return new Promise(resolve => {
        new HttpRequest().ajax('property/types', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            resolve(result.code === 'OK' ? result.data : []);
        });
    });
}

function subscribe(type, platform, id) {
    let _url;
    switch (type) {
        case '摄像头':
            _url = `property/${platform.toLowerCase()}/camera/subscribe`;
            break
        case '烟雾报警器':
            _url = `property/${platform.toLowerCase()}/smoke-detector/subscribe`
    }

    return new Promise(resolve => {
        new HttpRequest().ajax(_url, {
            'id': id
        }).then(result => {
            resolve(result.code === 'OK');
        });
    });
}

function play(cameraId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('../media/preview', {
            'cameraId': cameraId
        }).then(result => {
            resolve(result.code === 'OK' ? result.data : null);
        });
    });
}

function playback(cameraId, from, to) {
    return new Promise(resolve => {
        new HttpRequest().ajax('../media/playback', {
            'cameraId': cameraId,
            'from': from instanceof Date ? from.format('yyyy-MM-dd HH:mm:ss') : from,
            'to': to instanceof Date ? to.format('yyyy-MM-dd HH:mm:ss') : to
        }).then(result => {
            resolve(result.code === 'OK' ? result.data : null);
        });
    });
}

function initItems(promise) {
    const _rules = App.entityDefaultRules('com.chinamobile.healthcode.model.subject.PropertyDescription', ['name', 'type', 'credentialNo', 'contact', 'address', 'type', 'status']);

    items = $('#table').ediTable({
        'columns': [{
            'title': '物品名称',
            'field': 'name',
            'control': 'text',
            'validator': _rules.name
        }, {
            'title': '类别',
            'field': 'type',
            'control': 'select',
            'validator': new Promise(resolve => {
                promise.then(items => {
                    const _options = [];

                    $.each(items, function (index, val) {
                        _options.push({
                            'key': val.val,
                            'value': val.val
                        });
                    });

                    resolve({
                        'options': _options,
                        'rule': {
                            'required': true
                        }
                    });
                });
            })
        }, {
            'title': '归属网格',
            'field': 'region',
            'control': 'department',
            'validator': {
                'max': 1,
                'rule': {
                    'required': true
                }
            }
        }, {
            'title': '位置',
            'field': 'location',
            'control': 'location',
            'validator': {
                'rule': {
                    'required': true
                }
            }
        }, {
            'title': '附件',
            'field': 'attachments',
            'control': 'file',
            'validator': {
                'multiple': true
            },
            'hidden': true
        }],
        'query': (count, index) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, _regions.length > 0 ? _regions[0].fullName : null, searchbar.name(), searchbar.type(), searchbar.address());
        },
        'create': true,
        'save': item => {
            return new Promise(resolve => {
                const _region = item.region.length > 0 ? item.region[0] : null;
                item.regionId = _region === null ? null : _region.id;
                item.regionFullName = _region === null ? null : _region.fullName;
                item.address = item.location.address;
                item.lng = item.location.lng;
                item.lat = item.location.lat;

                save(item).then(success => {
                    resolve({
                        'success': success,
                        'scrollToFirstPage': !item.hasOwnProperty('id')
                    });
                });
            });
        },
        'remove': remove,
        'tableBtns': [{
            'name': '订阅',
            'icon': 'mdi-star-outline',
            'callback': item => {
                subscribe(item.type, item.platform, item.deviceId);
            }
        }, {
            'name': '预览',
            'icon': 'mdi-play-box-outline',
            'callback': item => {
                play(item.deviceId).then(url => {
                    if (url === null) {
                        return
                    }

                    switch (item.platform) {
                        case 'ACS':
                            const _video = document.querySelectorAll('video')[0];

                            const _hls = new Hls();
                            _hls.loadSource(url);
                            _hls.attachMedia(_video);
                            _hls.on(Hls.Events.MANIFEST_PARSED, () => {
                                _video.play(); // 调用播放 API
                            });

                            ModalUtil.open({
                                'type': 1,
                                'title': '预览',
                                'content': $('video'),
                                'area': ['90%', '90%'],
                                'maxmin': true,
                                'end': function () {
                                    _video.pause();
                                }
                            });

                            break;
                        case 'ANDMU':
                            ModalUtil.open({
                                'type': 2,
                                'title': '预览',
                                'content': url,
                                'area': ['90%', '90%'],
                                'maxmin': true
                            });

                            break;
                    }
                });
            }
        }, {
            'name': '回放',
            'icon': 'mdi-replay',
            'callback': item => {
                const _from = new Date().addMinutes(-10);
                const _to = new Date().addMinutes(-5);

                playback(item.deviceId, _from, _to).then(url => {
                    if (url === null) {
                        return
                    }

                    const _video = document.querySelectorAll('video')[0];

                    const _hls = new Hls();
                    _hls.loadSource(url);
                    _hls.attachMedia(_video);
                    _hls.on(Hls.Events.MANIFEST_PARSED, () => {
                        _video.play(); // 调用播放 API
                    });

                    ModalUtil.open({
                        'type': 1,
                        'title': '预览',
                        'content': $('video'),
                        'area': ['90%', '90%'],
                        'maxmin': true,
                        'end': function () {
                            _video.pause();
                        }
                    });
                });
            }
        }],
        'fileSelected': (row, name, files) => {
            const _files = [];
            $.each(files, function (index, val) {
                // 随机生成id
                const _id = Math.random().toString().replace(/\./g, '');
                attachments[_id] = val;

                _files.push({
                    '_id': _id,
                    'name': val.name
                });
            });

            return new Promise(resolve => {
                resolve(_files);
            });
        },
        'fileDeleted': (row, index, file) => {
            delete attachments[file._id];

            return new Promise(resolve => {
                resolve(true);
            });
        }
    });
}

function init() {
    const _promise = types();

    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }, {
            'title': '名称',
            'icon': 'mdi-archive-outline',
            'ref': 'name',
        }, {
            'title': '类别',
            'icon': 'mdi-menu',
            'control': 'select',
            'validator': new Promise(resolve => {
                _promise.then(dto => {
                    const _options = [];

                    $.each(dto, function (index, val) {
                        _options.push({
                            'key': val.val,
                            'value': val.val
                        });
                    });

                    resolve({
                        'options': _options
                    });
                });
            }),
            'ref': 'type'
        }, {
            'title': '位置',
            'icon': 'mdi-map-marker-outline',
            'ref': 'address'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query'
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            }
        }]
    });

    initItems(_promise);

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                items.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});