let searchbar, app;

function query(regionId, recordDate) {
    const _data = {
        regionId,
        recordDate
    };

    return new Promise(resolve => {
        new HttpRequest().ajax(`daily-report/query`, _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function exportTo(regionId, recordDate) {
    const _data = {
        regionId,
        recordDate
    };

    new HttpRequest().ajax('daily-report/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('数据日报表.xlsx', null, res.data);
        }
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-marker-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }, {
            'title': '统计时间',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd'
            },
            'ref': 'recordDate'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                let _regions = searchbar.regions();
                let _regionId = _regions[0] ? _regions[0].id : null;
                app.query(_regionId, searchbar.recordDate());
            },
            'ref': 'query'
        }, {
            'title': '导出',
            'icon': 'mdi-download',
            'callback': () => {
                let _regions = searchbar.regions();
                let _regionId = _regions[0] ? _regions[0].id : null;
                // if (_regions.length === 0) {
                //     ModalUtil.alert('请选择行政区划');
                //     return;
                // }

                exportTo(_regionId, searchbar.recordDate());
            }
        }]
    });

    app = new Vue({
        'el': '#app',
        'data': {
            'headers': [{
                'title': '行政区域',
                'ref': 'regionFullName'
            }, {
                'title': '当日登录数',
                'ref': 'numOfLoginUsers'
            }, {
                'title': '登录占比率',
                'ref': 'percentageOfLoginUsers'
            }, {
                'title': '重点人员-总数',
                'ref': 'personInTotal'
            }, {
                'title': '重点人员-当日增量',
                'ref': 'incrementOfPerson'
            }, {
                'title': '重点场所-总数',
                'ref': 'placeInTotal'
            }, {
                'title': '重点场所-当日增量',
                'ref': 'incrementOfPlace'
            }, {
                'title': '重点事件-总数',
                'ref': 'eventInTotal'
            }, {
                'title': '重点事件-当日增量',
                'ref': 'incrementOfEvent'
            }, {
                'title': '重点设施-总数',
                'ref': 'propertyInTotal'
            }, {
                'title': '重点设施-当日增量',
                'ref': 'incrementOfProperty'
            }, {
                'title': '重点单位-总数',
                'ref': 'unitInTotal'
            }, {
                'title': '重点单位-当日增量',
                'ref': 'incrementOfUnit'
            }],
            'rows': []
        },
        'methods': {
            'query': function (regionId, recordDate) {
                const that = this;
                query(regionId, recordDate).then(items => {
                    that.rows.splice(0, that.rows.length);

                    if (items && items.length) {
                        // 首行为头部
                        // that.headers = items.shift();

                        $.each(items, function (index, val) {
                            that.rows.push(val);
                        });
                    }
                });
            }
        },
        'mounted': function() {
            let _regions = searchbar.regions();
            let _regionId = _regions[0] ? _regions[0].id : null;
            this.query(_regionId, searchbar.recordDate());
        }
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            app.query();
        }
    });
}

$(function () {
    init();
});
