function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('get', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function save(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('save', item).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function queryPerson(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('persons', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function saveParticipant(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('participant/save', item).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function init() {
    const _id = window.location.querystring('id');

    const table = new Vue({
        'el': '#table',
        'data': {
            'id': window.location.querystring('id'),
            'persons': [],
        },
        'methods': {
            '_query': function () {
                const that = this;

                queryPerson(that.id).then(data => {
                    that.persons.splice(0, that.persons.length);

                    for (let i = 0; i < data.length; i++) {
                        data[i].subjectCategoryValueArray = data[i].subjectCategoryValue.split(',');
                        data[i].subjectCategory = data[i].subjectCategoryValueArray.map(v => _subjectCategoryOptions[v]).join(',');

                        const _array = _occupancyStateOptions.filter(o => o.key === data[i].occupancyStateValue)
                        data[i].occupancyState = _array.length > 0 ? _array[0].value : null;
                        that.persons.push(data[i]);
                    }
                });
            },
            '_add': function (item) {
                let _region = item.region.length > 0 ? item.region[0] : null;
                item.regionId = _region === null ? null : _region.id;
                item.regionFullName = _region === null ? null : _region.fullName;
                item.occupancyState = _occupancyStateOptions[item.occupancyStateValue];

                item.subjectCategoryValue = item.subjectCategoryValueArray.join(',');
                item.subjectCategory = item.subjectCategoryValueArray.map(v => _subjectCategoryOptions[v]).join(',');

                if (_currentPersonIndex > -1) {
                    Vue.set(this.persons, _currentPersonIndex, item);
                    this.persons[_currentPersonIndex] = item;
                    _currentPersonIndex = -1;
                } else {
                    this.persons.push(item);
                }
            },
            '_save': function (index) {
                const that = this;

                saveParticipant(that.participants[index]).then(success => {
                    if (success) {
                        that._query();
                    }
                });
            },
            '_edit': function (index) {
                const that = this;
                _currentPersonIndex = index;
                let _person = that.persons[index]
                _person.region = [];
                _person.region.push({
                    'id': _person.regionId,
                    'fullName': _person.regionFullName
                });
                personsForm.setData(that.persons[index]);
            },
            '_remove': function (index) {
                const that = this;
                that.persons.splice(index, 1);
                if (_currentPersonIndex) {
                    personsForm.setData();
                    _currentPersonIndex = -1;
                }
            },
        },
        'mounted': function () {
            const that = this;
            //
            // if (that.id !== null) {
            //     that._query();
            // }

            if (_id !== null) {
                get(_id).then(data => {
                    if (data === null) {
                        return;
                    }

                    data.region = [{
                        'id': data.regionId,
                        'fullName': data.regionFullName
                    }];

                    data.location = {
                        'address': data.address,
                        'lng': data.lng,
                        'lat': data.lat
                    };

                    form.setData(data);

                    if (data.attachmentIds) {
                        let $_gallery = $('<ul name="attachments" data-am-widget="gallery" class="am-gallery am-avg-sm-2 am-avg-md-3 am-avg-lg-4 am-gallery-imgbordered" data-am-gallery="{ pureview: {target: "a"} }" ></ul>');
                        $.each(data.attachmentIds, (idx, val) => {
                            $_gallery.append(`<li>
                                <div class="am-gallery-item">
                                    <a href="/media/read?id=${val}" target="_blank" title="${val}">
                                    <img src="/media/read?id=${val}" alt="${val}"/>
                                    <h3 class="am-gallery-title">${val}</h3>
                                </div>
                            </li>`)
                        });
                        $('input[name="attachments"]').parent().html($_gallery);

                        // DOM ready之后需重新渲染组件
                        $.each(['gallery'], function(i, m) {
                            let module = $.AMUI[m];
                            module && module.init && module.init();
                        })
                    } else {
                        $('input[name="attachments"]').hide()
                    }

                    if (data.persons) {
                        that.persons.splice(0, that.persons.length);

                        for (const element of data.persons) {
                            element.subjectCategoryValueArray = element.subjectCategoryValue.split(',');
                            element.subjectCategory = element.subjectCategoryValueArray.map(v => _subjectCategoryOptions[v]).join(',');

                            const _array = _occupancyStateOptions.filter(o => o.key === element.occupancyStateValue)
                            element.occupancyState = _array.length > 0 ? _array[0].value : null;
                            that.persons.push(element);
                        }
                    }
                });
            }
        }
    });

    const _rules = App.entityDefaultRules('com.chinamobile.healthcode.model.subject.PlaceDescription', ['regionId', 'placeTypeValue', 'placeName', 'personInCharge', 'credentialNo', 'contact', 'address']);
    const _personRules = App.entityDefaultRules('com.chinamobile.healthcode.model.subject.PlacePersonDescription', ['regionId', 'name', 'credentialNo', 'contact', 'hkLocation', 'address', 'careerSituation', 'workplace', 'subjectCategoryValue', 'migrantSituation', 'occupancyStateValue']);

    const _placeTypes = new Promise(resolve => {
        new HttpRequest().ajax('options/place-type', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });

    let _subjectCategoryOptions;
    const _subjectCategories = new Promise(resolve => {
        new HttpRequest().ajax('options/subject-category', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });

    let _occupancyStateOptions;
    const _occupancyStates = new Promise(resolve => {
        new HttpRequest().ajax('options/occupancy-state', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });

    // Promise.all(_placeTypes, _subjectCategories, _occupancyStates);

    let _currentPersonIndex = -1;

    const form = $('#form').form({
        'contextPath': '../../',
        // 表单定义
        'rows': [{
            'title': '归属网格',
            'field': 'region',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                },
                'rule': {
                    'required': true
                }
            }
        }, {
            'field': 'placeTypeValue',
            'title': '场所类别',
            'control': 'select',
            'validator': new Promise(resolve => {
                _placeTypes.then(data => {
                    const _params = {
                        'options': [],
                        'rule': {
                            'required': true
                        }
                    };

                    for (const i in data) {
                        _params.options.push({
                            'key': data[i].val,
                            'value': data[i].name
                        });
                    }

                    // $.extend(_params, dto[1]);

                    resolve(_params);
                });
            })
        }, {
            'field': 'placeName',
            'title': '场所名称',
            'validator': _rules.placeName
        }, {
            'field': 'personInCharge',
            'title': '负责人姓名',
            'validator': _rules.personInCharge
        }, {
            'field': 'credentialNo',
            'title': '身份证号码',
            'control': _id ? 'label' : 'text',
            'validator': {
                'rule': {
                    'required': false,
                    'checkCredentialNo': _id ? false : true
                }
            }
        }, {
            'field': 'contact',
            'title': '联系电话',
            'validator': {
                'rule': {
                    'required': false,
                    'maxlength': 11
                }
            }
        }, {
            'field': 'permanentPopulation',
            'title': '常住人口数',
            'validator': _rules.permanentPopulation
        }, {
            'field': 'externalPopulation',
            'title': '外来人口数',
            'validator': _rules.externalPopulation
        }, {
            'field': 'location',
            'title': '详细地址',
            'control': 'location',
            'validator': _rules.address
        }, {
            'field': 'area',
            'title': '占地面积',
            'validator': _rules.area
        }, {
            'field': 'remarks',
            'title': '备注',
            'validator': _rules.remarks
        }, {
            'field': 'attachments',
            'title': '房屋外观照片'
        }],
        // 按钮组
        'btns': [{
            'callback': item => {
                const _region = item.region.length > 0 ? item.region[0] : null;
                item.regionId = _region === null ? null : _region.id;
                item.regionFullName = _region === null ? null : _region.fullName;
                item.address = item.location.address;
                item.lng = item.location.lng;
                item.lat = item.location.lat;
                item.persons = table.persons;

                return new Promise(resolve => {
                    save(item).then(id => {
                        if (id !== null) {
                            resolve(true);

                            item.id = id;
                            table.id = id;

                            // 关闭窗口
                            if (window.name) {
                                const _index = window.parent.layer.getFrameIndex(window.name);
                                window.parent.layer.close(_index);
                            }
                        } else {
                            resolve(false);
                        }
                    });
                });
            }
        }]
    });

    const personsForm = $('#personsForm').form({
        'contextPath': '../../',
        // 表单定义
        'rows': [{
            'title': '所属区域',
            'field': 'region',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../../sys/dept/organization', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                },
                'rule': {
                    'required': true
                }
            }
        }, {
            'field': 'name',
            'title': '姓名',
            'validator': _personRules.name
        }, {
            'field': 'credentialNo',
            'title': '身份证号码',
            'control': _currentPersonIndex == -1 ? 'text' : 'label',
            'validator': {
                'rule': {
                    'required': _currentPersonIndex == -1,
                    'checkCredentialNo': _currentPersonIndex == -1
                }
            }
        }, {
            'field': 'contact',
            'title': '联系电话',
            'validator': {
                'rule': {
                    'required': true,
                    'maxlength': 11
                }
            }
        }, {
            'field': 'hkLocation',
            'title': '户籍地址',
            'validator': _personRules.hkLocation
        }, {
            'field': 'address',
            'title': '现居住地址',
            'validator': _personRules.address
        // }, {
        //     'field': 'location',
        //     'title': '现居住地址',
        //     'control': 'location',
        //     'validator': _rules.address
        }, {
            'field': 'careerSituation',
            'title': '从业/学习情况',
            'validator': _personRules.careerSituation
        }, {
            'field': 'workplace',
            'title': '现工作/学习地点',
            'validator': _personRules.workplace
        }, {
            'field': 'subjectCategoryValueArray',
            'title': '重点人群',
            'control': 'checkbox',
            'validator': new Promise(resolve => {
                _subjectCategories.then(data => {
                    const _rule = {
                        'options': {}
                    };

                    $.each(data, function (index, val) {
                        _rule.options[val.val + ''] = val.name;
                    });
                    _subjectCategoryOptions = _rule.options;
                    resolve(_rule);
                });
            }),
            'hidden': true
        }, {
            'field': 'migrantSituation',
            'title': '外出情况',
            'placeholder': '进入本辖区的时间-离开本辖区的时间:去向',
            'validator': _personRules.migrantSituation
        }, {
            'field': 'occupancyStateValue',
            'title': '人员状态',
            'control': 'select',
            'validator': new Promise(resolve => {
                _occupancyStates.then(data => {
                    const _params = {
                        'options': [],
                        'rule': {
                            'required': true
                        }
                    };

                    for (const i in data) {
                        _params.options.push({
                            'key': data[i].val,
                            'value': data[i].name
                        });
                    }
                    _occupancyStateOptions = _params.options;

                    resolve(_params);
                });
            })
        }, {
            'field': 'remarks',
            'title': '备注',
            'validator': _personRules.remarks
        }],
        // 按钮组
        'btns': [{
            'name': '新增',
            'icon': 'mdi-plus',
            'callback': item => {
                // let _region = item.region.length > 0 ? item.region[0] : null;
                // item.regionId = _region === null ? null : _region.id;
                // item.regionFullName = _region === null ? null : _region.fullName;
                // // item.address = item.location.address;
                // // item.lng = item.location.lng;
                // // item.lat = item.location.lat;
                // item.subjectCategory = _subjectCategoryOptions[item.subjectCategoryValue];
                // item.occupancyState = _occupancyStateOptions[item.occupancyStateValue];
                table._add(item);

                personsForm.setData();
            }
        }]
    });
}

$(function () {
    init();
});
