let searchbar, items;

function fuzzy(count, index, sortBy, regionFullName, name) {
    const _data = {
        'count': count,
        'index': index,
        'sortBy': sortBy,
        'regionFullName': regionFullName,
        'name': name
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('building/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function update(id, name) {
    return new Promise(resolve => {
        new HttpRequest().ajax('building/update', {
            'id': id,
            'name': name
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('building/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function initItems() {
    items = $('#table').ediTable({
        'columns': [{
            'title': '建筑名称',
            'field': 'name',
            'control': 'text',
            'validator': {
                'rule': {
                    'required': true
                }
            },
            'sortable': true
        }, {
            'title': '归属行政区划',
            'field': 'regionFullName',
            'control': 'label',
            'sortable': true
        }, {
            'title': '经度',
            'field': 'lng',
            'control': 'label'
        }, {
            'title': '纬度',
            'field': 'lat',
            'control': 'label'
        }],
        'query': (count, index, sortBy) => {
            const _regions = searchbar.regions();
            return fuzzy(count, index, sortBy, _regions.length > 0 ? _regions[0].fullName : null, searchbar.name());
        },
        'save': item => {
            return new Promise(resolve => {
                update(item.id, item.name).then(success => {
                    resolve({
                        'success': success,
                        'scrollToFirstPage': false
                    });
                });
            });
        },
        'remove': remove
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }, {
            'title': '名称',
            'icon': 'mdi-office-building-outline',
            'control': 'text',
            'ref': 'name'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query',
        }]
    });

    initItems();

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});
