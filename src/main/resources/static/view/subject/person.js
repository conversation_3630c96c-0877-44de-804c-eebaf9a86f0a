function options() {
    return new Promise(resolve => {
        new HttpRequest().ajax('options', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            resolve(result.code === 'OK' ? result.data : {});
        });
    });
}

function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('get', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function save(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('save', item).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function constructOptions(arr) {
    const options = []
    $.each(arr, function (index, val) {
        options.push({
            'key': val,
            'value': val
        });
    });
    return options;
}

function selectOptions($selector, valArr) {
    $.each(valArr, (index, val) => {
        $selector.find(`option[value="${val}"]`).attr('selected', true);
    });
    $selector.trigger('changed.selected.amui');
}

function init() {
    const _id = window.location.querystring('id');

    const _ruleProperties = [
        'name',
        'credentialType',
        'credentialNo',
        'contact',
        'address',
        'type',
        'status'];
    const _rules = App.entityDefaultRules('com.chinamobile.healthcode.model.subject.PersonDescription', _ruleProperties);
    Promise.all([...Object.values(_rules), options()]).then(ress => {
        const fieldOptions = ress[_ruleProperties.length];
        app = new Vue({
            'el': '#app',
            'data': {
                'layout': {
                    'controlsNotSupportFormFeedback': ['checkbox', 'datetime', 'department', 'radio'],
                },

                'rows': {
                    'basic': [{
                        'title': '姓名',
                        'field': 'name',
                        'validator': _rules.name,
                        'validatorValue': {}
                    }, {
                        'title': '性别',
                        'field': 'isMale',
                        'control': 'radio',
                        'validator': {
                            'options': [{
                                'key': true,
                                'value': '男性'
                            }, {
                                'key': false,
                                'value': '女性'
                            }],
                            'rule': {
                                'required': true
                            }
                        },
                        'validatorValue': {}
                    }, {
                        'title': '人员类型',
                        'field': 'populationType',
                        'control': 'select',
                        'validator': new Promise(resolve => {
                            resolve({
                                'options': constructOptions(fieldOptions['人员类型']),
                                'rule': {
                                    'required': true
                                }
                            });
                        }),
                        'validatorValue': {}
                    }, {
                        'title': '证件类型',
                        'field': 'credentialType',
                        'control': function(data) {
                            return data.credentialInfoEditable ? 'select' : 'label';
                        },
                        'validator': function(data) {
                            return data.credentialInfoEditable ? new Promise(resolve => {
                                _rules.credentialType.then(dto => {
                                    dto.options = constructOptions(fieldOptions['证件类型']);
                                    resolve(dto);
                                });
                            }) : null;
                        },
                        'validatorValue': {}
                    }, {
                        'title': '证件号码',
                        'field': 'credentialNo',
                        'control': function(data) {
                            return data.credentialInfoEditable ? 'text' : 'label';
                        },
                        'validator': function(data) {
                            console.log(data)
                            return data.credentialInfoEditable ? {
                                'rule': {
                                    'required': false,
                                    'checkCredentialNo': true
                                }} : null;
                        },
                        'validatorValue': {}
                    }, {
                        'title': '联系方式',
                        'field': 'contact',
                        'validator': _rules.contact,
                        'validatorValue': {}
                    }, {
                        'title': '归属网格',
                        'field': 'region',
                        'control': 'department',
                        'validator': {
                            'max': 1,
                            'rule': {
                                'required': true
                            }
                        },
                        'validatorValue': {}
                    }, {
                        'title': '现居住地',
                        'field': 'address',
                        'validator': _rules.address,
                        'validatorValue': {},
                        'hidden': function (data) {
                            return data.type === '重点未成年人';
                        }
                    }, {
                        'title': '人员类别',
                        'field': 'type',
                        'control': 'select',
                        'validator': _rules.type,
                        'validatorValue': {}
                    }, {
                        'title': '人员分类',
                        'field': 'subtype',
                        'control': 'select',
                        'validator': new Promise(resolve => {
                            resolve({
                                'options': [],
                                'rule': {}
                            });
                        }),
                        'validatorValue': {},
                        'hidden': function (data) {
                            return !data.type || !this.validatorValue.options || Object.keys(this.validatorValue.options).length === 0;
                        }
                    }, {
                        'title': '人员状态',
                        'field': 'status',
                        'control': 'select',
                        'validator': _rules.status,
                        'validatorValue': {}
                    }],
                    'drug': [{
                        'title': '最近一次尿检时间',
                        'field': 'lastUrineTime',
                        'control': 'datetime',
                        'validator': {
                            'format': 'yyyy-MM-dd'
                        },
                        'validatorValue': {}
                    }],
                    'rectification': [{
                        'title': '矫正开始时间',
                        'field': 'rectifyFrom',
                        'control': 'datetime',
                        'validator': {
                            'format': 'yyyy-MM-dd'
                        },
                        'validatorValue': {}
                    }, {
                        'title': '矫正期限',
                        'field': 'rectifyTimeLimit',
                        'validatorValue': {}
                    }],
                    'petition': [{
                        'title': '上访事由',
                        'field': 'petition',
                        'control': 'textarea',
                        'validatorValue': {}
                    }, {
                        'title': '是否签订《息诉罢访协议书》',
                        'field': 'archiveAgreement',
                        'control': 'radio',
                        'validator': {
                            'options': [{
                                'key': true,
                                'value': '是'
                            }, {
                                'key': false,
                                'value': '否'
                            }]
                        },
                        'validatorValue': {}
                    }],
                    'psycho': [{
                        'title': '是否低保户',
                        'field': 'isLowIncomeFamily',
                        'control': 'radio',
                        'validator': {
                            'options': [{
                                'key': true,
                                'value': '是'
                            }, {
                                'key': false,
                                'value': '否'
                            }]
                        },
                        'validatorValue': {}
                    }, {
                        'title': '监护人姓名',
                        'field': 'guardianName',
                        'validatorValue': {}
                    }, {
                        'title': '与患者关系',
                        'field': 'guardianRelationship',
                        'validatorValue': {}
                    }, {
                        'title': '联系方式',
                        'field': 'guardianContact',
                        'validatorValue': {}
                    }, {
                        'title': '危险性等级',
                        'field': 'riskLevel',
                        'control': 'select',
                        'validatorValue': {
                            'options': [{
                                'key': '0级',
                                'value': '0级'
                            }, {
                                'key': '1级',
                                'value': '1级'
                            }, {
                                'key': '2级',
                                'value': '2级'
                            }, {
                                'key': '3级',
                                'value': '3级'
                            }, {
                                'key': '4级',
                                'value': '4级'
                            }, {
                                'key': '5级',
                                'value': '5级'
                            }]
                        }
                    }],
                    'minors': [{
                        'title': '人员等级',
                        'field': 'levelOfConcern',
                        'control': 'select',
                        'validator': new Promise(resolve => {
                            resolve({
                                'options': constructOptions(fieldOptions['重点未成年人']['人员等级']),
                                'rule': {
                                    'required': true
                                }
                            });
                        }),
                        'validatorValue': {}
                    }, {
                        'title': '人员分类',
                        'field': 'categoryOfMinorArr',
                        'control': 'select',
                        'multiple': true,
                        'validator': new Promise(resolve => {
                            resolve({
                                'options': constructOptions(fieldOptions['重点未成年人']['人员分类']),
                                'rule': {
                                    'required': true
                                }
                            });
                        }),
                        'validatorValue': {}
                    }, {
                        'title': '学校',
                        'field': 'schoolName',
                        'validatorValue': {}
                    }, {
                        'title': '家庭情况',
                        'field': 'familyStatusArr',
                        'control': 'select',
                        'multiple': true,
                        'validator': new Promise(resolve => {
                            resolve({
                                'options': constructOptions(fieldOptions['重点未成年人']['家庭情况']),
                                'rule': {
                                    'required': true
                                }
                            });
                        }),
                        'validatorValue': {}
                    }, {
                        'title': '监护人姓名',
                        'field': 'guardianName',
                        'validator': {
                            'rule': {
                                'required': true
                            }
                        },
                        'validatorValue': {}
                    }, {
                        'title': '监护人联系方式',
                        'field': 'guardianContact',
                        'validator': {
                            'rule': {
                                'required': true
                            }
                        },
                        'validatorValue': {}
                    }, {
                        'title': '监护人身份证号码',
                        'field': 'guardianCredentialNo',
                        'validatorValue': {}
                    }, {
                        'title': '监护人居住详址',
                        'field': 'address',
                        'validator': {
                            'rule': {
                                'required': true
                            }
                        },
                        'validatorValue': {}
                    }, {
                        'title': '与未成年人关系',
                        'field': 'guardianRelationship',
                        'control': 'select',
                        'validator': new Promise(resolve => {
                            resolve({
                                'options': constructOptions(fieldOptions['重点未成年人']['与未成年人关系']),
                                'rule': {
                                    'required': true
                                }
                            });
                        }),
                        'validatorValue': {}
                    }, {
                        'title': '帮扶手段',
                        'field': 'wayToHelpArr',
                        'control': 'select',
                        'multiple': true,
                        'validator': new Promise(resolve => {
                            resolve({
                                'options': constructOptions(fieldOptions['重点未成年人']['帮扶手段'])
                            });
                        }),
                        'validatorValue': {}
                    }, {
                        'title': '帮扶人姓名',
                        'field': 'helperName',
                        'validatorValue': {}
                    }, {
                        'title': '帮扶人联系方式',
                        'field': 'helperContact',
                        'validatorValue': {}
                    }, {
                        'title': '帮扶情况',
                        'field': 'helpSituation',
                        'validatorValue': {}
                    }, {
                        'title': '主责部门',
                        'field': 'responsibleDepartment',
                        'placeholder': '教育局、检察院、法院、团委、妇联……',
                        'validatorValue': {}
                    }, {
                        'title': '主责人',
                        'field': 'responsiblePersonName',
                        'validatorValue': {}
                    }, {
                        'title': '主责人联系电话',
                        'field': 'responsiblePersonContact',
                        'validatorValue': {}
                    }, {
                        'title': '备注',
                        'field': 'memo',
                        'validatorValue': {}
                    }]
                },

                'data': {
                    'id': null,
                    'name': null,
                    'credentialType': null,
                    'credentialNo': null,
                    'contact': null,
                    'region': [],
                    'regionId': null,
                    'regionFullName': null,
                    'address': null,
                    'lng': null,
                    'lat': null,
                    'type': null,
                    'subtype': null,
                    'status': null,
                    'isMale': true,
                    'lastUrineTime': null,
                    'rectifyFrom': null,
                    'rectifyTimeLimit': null,
                    'petition': null,
                    'archiveAgreement': null,
                    'guardianName': null,
                    'guardianRelationship': null,
                    'guardianContact': null,
                    'isLowIncomeFamily': null,
                    'riskLevel': null,
                    'populationType': null,
                    'levelOfConcern': null,
                    'categoryOfMinorArr': [],
                    'categoryOfMinor': null,
                    'schoolName': null,
                    'familyStatusArr': [],
                    'familyStatus': null,
                    'guardianCredentialNo': null,
                    'wayToHelpArr': [],
                    'wayToHelp': null,
                    'helperName': null,
                    'helperContact': null,
                    'helpSituation': null,
                    'responsibleDepartment': null,
                    'responsiblePersonName': null,
                    'responsiblePersonContact': null,
                    'memo': null,
                    'credentialInfoEditable': false,
                    'originalCredentialNo': null
                },

                'contextPath': '../..',

                'map': {
                    'field': null,
                    'jsKey': null,
                    'modal': null
                },

                'validator': null
            },
            'methods': {
                '_initControl': function () {
                    const that = this;

                    const _promises = [];
                    for (const prop in that.rows) {
                        $.each(that.rows[prop], function (index, val) {
                            let _promise;
                            
                            // 处理动态 validator 函数
                            let validator = val.validator;
                            if ($.isFunction(validator)) {
                                validator = validator(that.data);
                            }
                            
                            if (validator instanceof Promise) {
                                _promise = validator;

                                // 处理动态 control 函数
                                let control = val.control;
                                if ($.isFunction(control)) {
                                    control = control(that.data);
                                }

                                // 设置默认值，避免影响表单的渲染
                                switch (control) {
                                    case 'radio':
                                    case 'select':
                                        val.validatorValue.options = {};
                                        break;
                                    case 'datetime':
                                        val.validatorValue.format = 'yyyy-MM-dd';
                                        break;
                                }
                            } else {
                                _promise = new Promise(resolve => {
                                    resolve(validator);
                                });
                            }

                            _promises.push(new Promise(resolve => {
                                _promise.then(data => {
                                    // 合并
                                    $.extend(true, val.validatorValue, data);

                                    resolve(val);
                                });
                            }));
                        });
                    }

                    Promise.all(_promises).then(rows => {
                        const _rules = {};

                        // 重新渲染
                        that.$forceUpdate();

                        // 渲染完成后设置组件
                        that.$nextTick(() => {
                            $.each(rows, function (index, val) {
                                // 处理动态 control 函数
                                let control = val.control;
                                if ($.isFunction(control)) {
                                    control = control(that.data);
                                }
                                
                                // 设置控件
                                switch (control) {
                                    // 设置日期控件
                                    case 'datetime':
                                    {
                                        const _options = {
                                            'autoclose': true,
                                            'language': 'zh-CN',
                                            'format': 'yyyy-mm-dd hh:ii:ss',
                                            'startView': 'month',
                                            'minView': 'hour',
                                            'maxView': 'decade'
                                        };

                                        switch (val.validatorValue.format) {
                                            case 'yyyy':
                                                _options.format = 'yyyy';
                                                _options.startView = 'decade';
                                                _options.minView = 'decade';
                                                break;
                                            case 'yyyy-MM':
                                                _options.format = 'yyyy-mm';
                                                _options.startView = 'year';
                                                _options.minView = 'year';
                                                break;
                                            case 'yyyy-MM-dd':
                                                _options.format = 'yyyy-mm-dd';
                                                _options.minView = 'month';
                                                break;
                                            case 'yyyy-MM-dd HH':
                                                _options.format = 'yyyy-mm-dd hh';
                                                _options.minView = 'day';
                                                break;
                                            case 'yyyy-MM-dd HH:mm':
                                                _options.format = 'yyyy-mm-dd hh:ii';
                                                _options.minView = 'hour';
                                                break;
                                            default:
                                                break;
                                        }

                                        const _el = $(that.$refs['datetime-' + val.field]);
                                        _el.datetimepicker(_options).on('changeDate', function () {
                                            let _val = _el.find('input:first').val();
                                            if (_val) {
                                                switch (_options.format) {
                                                    case 'yyyy':
                                                        _val += '-01-01';
                                                        break;
                                                    case 'yyyy-mm':
                                                        _val += '-01';
                                                        break;
                                                }
                                            }

                                            Vue.set(that.data, val.field, _val);

                                            // 触发验证
                                            that.validator.element(_el.find('input:first'));
                                        });

                                        break;
                                    }
                                    case 'select':
                                    {
                                        if (val.multiple) {
                                            const _el = $(that.$refs[val.field]);
                                            _el.selected({
                                                'btnWidth': '100%'
                                            }).on('change', function () {
                                                const _array = [];

                                                // 获取选中项
                                                _el.next().find('.am-selected-list > li.am-checked').each(function () {
                                                    _array.push($(this).attr('data-value'));
                                                });
                                                that.data[val.field] = _array;
                                            });
                                            // 统一样式
                                            _el.next().css('border', '1px solid #ccc')
                                                .css('padding', '0.5em')
                                                .css('background', '#fff url(\'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMTJweCIgeT0iMHB4IiB3aWR0aD0iMjRweCIgaGVpZ2h0PSIzcHgiIHZpZXdCb3g9IjAgMCA2IDMiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDYgMyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHBvbHlnb24gcG9pbnRzPSI1Ljk5MiwwIDIuOTkyLDMgLTAuMDA4LDAgIi8+PC9zdmc+\') no-repeat 100% center');
                                            _el.next().find('button.am-selected-btn.am-btn.am-dropdown-toggle.am-btn-default')
                                                .css('border', 'none')
                                                .css('padding', '0')
                                                .css('min-height', '20px');
                                            _el.next().find('i.am-selected-icon.am-icon-caret-down').remove();
                                            _el.next().find('ul.am-selected-list').find('i.am-icon-check').css('top', '50%');
                                        } else {
                                            const _el = $(that.$refs[val.field]);

                                            if (val.field === 'credentialType') {
                                                that.rows.basic.find(r => r.field === val.field).control = control;
                                                that.$forceUpdate();
                                            }

                                            _el.on('change', function() {
                                                if (val.field === 'type') {
                                                    // 当主类型改变时，更新子类型选项
                                                    const _type = that.data.type;
                                                    if (_type) {
                                                        const _subtypeOptions = fieldOptions['人员分类'][_type] || [];
                                                        that.rows.basic.find(r => r.field === 'subtype').validatorValue.options = constructOptions(_subtypeOptions);
                                                        that.data.subtype = null;
                                                        that.$forceUpdate();
                                                    }
                                                }
                                            });
                                        }
                                        break;
                                    }
                                    default:
                                    {
                                        if (val.field === 'credentialType') {
                                            that.rows.basic.find(r => r.field === val.field).control = control;
                                            that.$forceUpdate();
                                        } else if (val.field === 'credentialNo') {
                                            that.rows.basic.find(r => r.field === val.field).control = control;
                                            that.$forceUpdate();
                                            
                                            // 为身份证号字段添加修改监听
                                            if (control === 'text') {
                                                const _el = $(that.$refs[val.field]);
                                                _el.on('input', function() {
                                                    // 重新设置验证规则
                                                    that.rows.basic.find(r => r.field === val.field).validatorValue.rule.checkCredentialNo = true;
                                                    // 触发验证
                                                    if (that.validator) {
                                                        that.validator.element(_el);
                                                    }
                                                });
                                            }
                                        }
                                    }
                                }

                                if (!val.validatorValue || $.isEmptyObject(val.validatorValue.rule)) {
                                    return;
                                }

                                _rules[val.field] = val.validatorValue.rule;
                            });

                            // 设置验证器
                            that.validator = $(that.$el).validate({
                                'rules': _rules,
                                'errorPlacement': function (label, element) {
                                    const _parent = $(element).parents('.am-form-group');
                                    _parent.removeClass('am-form-success').addClass('am-form-error');

                                    const _msg = $(label).text();

                                    if (_parent.hasClass('am-form-icon')) {
                                        if ($(element).nextAll().length > 0) {
                                            $(element).nextAll().remove();
                                        }

                                        const _el = '<span class="am-icon-times" title="' + _msg + '"></span>';
                                        $(element).after(_el);
                                    }
                                },
                                'success': function (label, element) {
                                    const _parent = $(element).parents('.am-form-group');
                                    _parent.removeClass('am-form-error').addClass('am-form-success');

                                    if (_parent.hasClass('am-form-icon')) {
                                        if ($(element).nextAll().length > 0) {
                                            $(element).nextAll().remove();
                                        }

                                        const _el = '<span class="am-icon-check"></span>';
                                        $(element).after(_el);
                                    }
                                }
                            });

                            // 设置uCheck控件
                            $(that.$el).find('input[type=checkbox], input[type=radio]').each(function () {
                                $(this).uCheck();
                            });
                        });
                    });
                },
                '_getText': function (row) {
                    let _text = '', _array;

                    switch (row.control) {
                        // 部门/人员选择控件
                        case 'department':
                            _array = [];
                            $.each(this.data[row.field], function (index, val) {
                                _array.push(val.fullName);
                            });

                            _text = _array.join(',');
                            break;
                        // 定位控件
                        case 'location':
                            _text = this.data[row.field].address;
                            break;
                        // 单选/下拉控件
                        case 'radio':
                        case 'select':
                            _text = row.validatorValue.options[this.data[row.field]];
                            break;
                        default:
                            _text = this.data[row.field];
                            break;
                    }

                    return _text;
                },
                '_getMember': function (row, entity) {
                    const that = this;

                    const _options = {
                        'entity': entity
                    };

                    if ($.isNumeric(row.validatorValue.max)) {
                        _options.max = row.validatorValue.max
                    }

                    if (row.validatorValue.hasOwnProperty('userMapper')) {
                        _options.userMapper = row.validatorValue.userMapper;
                    }

                    if (row.validatorValue.hasOwnProperty('deptMapper')) {
                        _options.deptMapper = row.validatorValue.deptMapper;
                    }

                    if ($.isFunction(row.validatorValue.queryOrganization)) {
                        _options.queryOrganization = row.validatorValue.queryOrganization;
                    }

                    if ($.isFunction(row.validatorValue.queryUsers)) {
                        _options.queryUsers = row.validatorValue.queryUsers;
                    }

                    switch (entity) {
                        case 'department':
                            _options.ok = (departments, users) => {
                                that.data[row.field].splice(0, that.data[row.field].length);
                                $.each(departments, function (index, val) {
                                    that.data[row.field].push(val);
                                });

                                return true;
                            };
                            break;
                        case 'user':
                            _options.ok = (departments, users) => {
                                that.data[row.field].splice(0, that.data[row.field].length);
                                $.each(users, function (index, val) {
                                    that.data[row.field].push(val);
                                });

                                return true;
                            };
                            break;
                    }

                    Member.get(_options);
                },
                '_locate': function (row) {
                    const that = this;

                    if (that.map.jsKey === null) {
                        return;
                    }

                    that.map.modal = ModalUtil.open({
                        'title': '选址',
                        'type': 2,
                        'content': 'https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=' + that.map.jsKey + '&referer=myapp',
                        'area': ['100%', '100%']
                    });

                    that.map.field = row.field;
                },
                '_save': function () {
                    const that = this;

                    if (!$(that.$el).valid()) {
                        const _arr = [];
                        for (const prop in that.validator.invalid) {
                            _arr.push(prop);
                        }

                        const _rows = [];
                        $.each(that.rows, function (index, val) {
                            if ($.inArray(val.field, _arr) !== -1) {
                                _rows.push(val);
                            }
                        });

                        ModalUtil.alert('请修改' + _rows[0].title + '，' + that.validator.invalid[_rows[0].field]);

                        return;
                    }

                    if (that.data.region.length > 0) {
                        that.data.regionId = that.data.region[0].id;
                        that.data.regionFullName = that.data.region[0].fullName;
                    }

                    that.data.categoryOfMinor = JSON.stringify(that.data.categoryOfMinorArr);
                    that.data.familyStatus = JSON.stringify(that.data.familyStatusArr);
                    that.data.wayToHelp = JSON.stringify(that.data.wayToHelpArr);

                    save(JSON.parse(JSON.stringify(that.data))).then(id => {
                        if (id === null) {
                            return;
                        }

                        if (that.data.id === null) {
                            that.data.id = id;
                        }

                        // 关闭窗口
                        if (window.name) {
                            const _index = window.parent.layer.getFrameIndex(window.name);
                            window.parent.layer.close(_index);
                        }
                    });
                }
            },
            'mounted': function () {
                const that = this;

                // that._initControl();

                new HttpRequest().ajax(that.contextPath + '/util/qqmap/js-key', null, {
                    'loading': false,
                    'msg': {
                        'success': false,
                        'error': false
                    }
                }).then(res => {
                    if (res.code === 'OK') {
                        that.map.jsKey = res.data;
                    }
                });

                window.addEventListener('message', function (event) {
                    ModalUtil.close(that.map.modal);

                    if (!event.data || event.data.module !== 'locationPicker') {
                        return;
                    }

                    const _location = event.data.poiname === '我的位置' ? event.data.poiaddress : event.data.poiname;

                    if (that.map.field !== null) {
                        that.data[that.map.field].address = event.data.cityname + '·' + _location;
                        that.data[that.map.field].lat = event.data.latlng.lat;
                        that.data[that.map.field].lng = event.data.latlng.lng;

                        that.map.field = null;
                    }
                }, false);

                if (_id) {
                    get(_id).then(item => {
                        if (item === null) {
                            return;
                        }

                        for (const prop in that.data) {
                            that.data[prop] = item[prop];
                        }

                        that.data.categoryOfMinorArr = JSON.parse(item.categoryOfMinor);
                        that.data.familyStatusArr = JSON.parse(item.familyStatus);
                        that.data.wayToHelpArr = JSON.parse(item.wayToHelp);
                        selectOptions($(that.$refs['categoryOfMinorArr']), that.data.categoryOfMinorArr);
                        selectOptions($(that.$refs['familyStatusArr']), that.data.familyStatusArr);
                        selectOptions($(that.$refs['wayToHelpArr']), that.data.wayToHelpArr);

                        Vue.set(that.data, 'region', [{
                            'id': that.data.regionId,
                            'fullName': that.data.regionFullName
                        }]);

                        // 初始化人员分类选项
                        if (that.data.type) {
                            const _subtypeOptions = fieldOptions['人员分类'][that.data.type] || [];
                            that.rows.basic.find(r => r.field === 'subtype').validatorValue.options = constructOptions(_subtypeOptions);
                            that.$forceUpdate();
                        }

                        // 重新初始化控件以处理动态 control 和 validator
                        that._initControl();
                    });
                }
            }
        })
    })
}

$(function () {
    init();
});
