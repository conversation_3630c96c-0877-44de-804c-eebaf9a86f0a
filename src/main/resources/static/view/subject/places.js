let searchbar, items, placeTypes;
let $instabilityDifferenceDiv = null;

function fuzzy(count, index, sortBy, regionFullName, placeName, placeTypeValue) {
    const _data = {
        'count': count,
        'index': index,
        'sortBy': sortBy,
        'regionFullName': regionFullName,
        'placeName': placeName,
        'placeTypeValue': placeTypeValue
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('place/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('place/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function importFromFile(file) {
    let _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('place/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('重点场所.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function render(id) {
    ModalUtil.open({
        'title': id === null ? '新增场所' : '编辑场所',
        'type': 2,
        'maxmin': true,
        'area': ['90%', '90%'],
        'content': 'place/form' + (id === null ? '' : ('?id=' + id)),
        'end': function () {
            items.query(id === null ? null : items.getIndexCurrent());
        }
    });
}

function initItems() {
    items = $('#table').table({
        'columns': [{
            'title': '场所类别',
            'field': 'placeTypeName',
            'control': 'label'
        }, {
            'title': '场所名称',
            'field': 'placeName',
            'control': 'label'
        }, {
            'title': '负责人姓名',
            'field': 'personInCharge',
            'control': 'label'
        }, {
            'title': '身份证号码',
            'field': 'credentialNo',
            'control': 'label'
        }, {
            'title': '联系电话',
            'field': 'contact',
            'control': 'label'
        }, {
            'title': '归属网格',
            'field': 'regionFullName',
            'control': 'label'
        }, {
            'title': '常住人口数',
            'field': 'permanentPopulation',
            'control': 'label',
            'hidden': true
        }, {
            'title': '外来人口数',
            'field': 'externalPopulation',
            'control': 'label',
            'hidden': true
        }, {
            'title': '详细地址',
            'field': 'address',
            'control': 'label'
        }, {
            'title': '占地面积',
            'field': 'area',
            'control': 'label',
            'hidden': true
        }, {
            'title': '备注',
            'field': 'remarks',
            'control': 'label',
            'hidden': true
        }],
        'query': (count, index, sortBy) => {
            const _regions = searchbar.regions();
            const _type = searchbar.placeTypeValue();

            updateStabilityDifference(_type);

            return fuzzy(count, index, sortBy, _regions.length > 0 ? _regions[0].fullName : null, searchbar.placeName(), searchbar.placeTypeValue());
        },
        'btns': [{
            'callback': item => {
                render(item.id);
            }
        }, {
            'callback': remove
        }],
        'toolbar': [{
            'name': '新增',
            'icon': 'mdi-plus',
            'callback': () => {
                render(null);
            }
        }]
    });
}

function getStatsDifference(regionFullName, type) {
    return new Promise(resolve => {
        const data = { regionFullName, type };
        new HttpRequest().ajax('place/instability/difference', data, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : 0);
        });
    });
}

// 统计差异显示
function updateStabilityDifference(type) {
    const regionFullName = searchbar.regions().length > 0 ? searchbar.regions()[0].fullName : null;
    getStatsDifference(regionFullName, type).then(data => {
        if ($instabilityDifferenceDiv) {
            $instabilityDifferenceDiv.remove();
            $instabilityDifferenceDiv = null;
        }

        if (data) {
            // 明细折叠面板逻辑，参考重点人员
            let typeTextPromise = type ? getTypeText(type) : Promise.resolve(null);
            typeTextPromise.then(typeTextValue => {
                let alertClass = data.three > 0 ? 'am-alert-danger' : (data.three < 0 ? 'am-alert-warning' : 'am-alert-secondary');
                let showCollapse = (type == null && Array.isArray(data.four) && data.four.length > 0);
                const typeText = typeTextValue ? `（${typeTextValue}）` : '';
                $instabilityDifferenceDiv = $(`
                    <div class="am-alert ${alertClass}" data-am-alert style="margin-top: 10px;${showCollapse ? ' cursor:pointer;' : ''}" id="instability-diff-alert">
                        <div style="position: relative;">
                            <p style="margin-bottom:0; padding-right: ${showCollapse ? '30px' : '0'};"><i class="mdi-alert-circle"></i> 上报重点场所${typeText}${data.one}个，已建档${data.two}个，差异${data.three}个</p>
                            ${showCollapse ? '<span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%);"><i class="mdi mdi-chevron-down"></i></span>' : ''}
                        </div>
                        <div id="instabilityTypeCollapse" class="panel-collapse collapse" style="margin-top:10px; display:none;"></div>
                    </div>
                `);
                $('#searchbar').after($instabilityDifferenceDiv);
                if (showCollapse) {
                    let detailsHtml = `<ul style='margin-bottom:0; color:inherit; background:transparent;'>${data.four.map(txt => `<li>${txt}</li>`).join('')}</ul>`;
                    let $collapse = $instabilityDifferenceDiv.find('#instabilityTypeCollapse');
                    $collapse.html(detailsHtml);
                    $instabilityDifferenceDiv.click(function (e) {
                        if ($(e.target).closest('#instabilityTypeCollapse').length === 0) {
                            if ($collapse.is(':visible')) {
                                $collapse.slideUp(200);
                                $instabilityDifferenceDiv.find('.mdi-chevron-down, .mdi-chevron-up').removeClass('mdi-chevron-up').addClass('mdi-chevron-down');
                            } else {
                                $collapse.slideDown(200);
                                $instabilityDifferenceDiv.find('.mdi-chevron-down, .mdi-chevron-up').removeClass('mdi-chevron-down').addClass('mdi-chevron-up');
                            }
                        }
                    });
                }
            });
        }
    });
}

// 获取类别名称显示文本（可选，若type为value时需转为name）
function getTypeText(typeValue) {
    return new Promise(resolve => {
        // 这里假设placeTypes是一个Promise，resolve后为{options: {key: value, ...}}
        if (!placeTypes || !typeValue) {
            resolve(null);
            return;
        }

        placeTypes.then(types => {
            if (types && types.options) {
                resolve(types.options[typeValue] || null);
            } else {
                resolve(null);
            }
        });
    });
}

function init() {
    const _rules = App.entityDefaultRules('com.chinamobile.healthcode.model.subject.PlaceDescription', ['placeName', 'placeTypeValue']);
    const _regionFullName = window.location.querystring('regionFullName');

    placeTypes = new Promise(resolve => {
        new HttpRequest().ajax('place/options/place-type', null, {
            'msg': {
                'success': false
            }
        }).then(res => {
            const _validator = {
                'options': {}
            };

            if (res.code === 'OK') {
                $.each(res.data, function (index, val) {
                    _validator.options[val.val + ''] = val.name;
                });
            }

            resolve(_validator);
        });
    });

    // 准备 regions 条件配置
    const regionsCondition = {
        'title': '行政区域',
        'icon': 'mdi-map-outline',
        'control': 'department',
        'validator': {
            'max': 1,
            'queryOrganization': deptId => {
                return new Promise(resolve => {
                    new HttpRequest().ajax('../sys/dept/organization/restricted', {
                        'id': deptId
                    }, {
                        'msg': {
                            'success': false
                        }
                    }).then(res => {
                        const _data = res.code === 'OK' ? {
                            'dept': res.data.dept,
                            'subordinates': res.data.subordinates,
                            'users': res.data.members
                        } : {
                            'dept': {},
                            'subordinates': [],
                            'users': []
                        };

                        resolve(_data);
                    });
                });
            }
        },
        'ref': 'regions'
    };

    // 如果有 regionFullName 参数，先设置初始值
    if (_regionFullName) {
        console.log('Processing regionFullName for places:', decodeURIComponent(_regionFullName));
        regionsCondition.val = [];
    }

    searchbar = $('#searchbar').searchbar({
        'conditions': [regionsCondition, {
            'title': '场所名称',
            'icon': 'mdi-account',
            'ref': 'placeName'
        }, {
            'title': '场所类别',
            'icon': 'mdi-filter-menu',
            'control': 'select',
            'validator': new Promise(resolve => {
                _rules.placeTypeValue.then(dto => {
                    const _validator = {
                        'options': [{
                            'key': '[]',
                            'value': '无'
                        }]
                    };

                    placeTypes.then(val => {
                        $.extend(_validator, val);
                        resolve(_validator);
                    });
                });
            }),
            'ref': 'placeTypeValue'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query',
            'row': 1
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            },
            'row': 1
        }]
    });

    // 处理regionFullName参数 - 在searchbar初始化后立即执行
    if (_regionFullName) {
        const decodedRegionFullName = decodeURIComponent(_regionFullName);

        new HttpRequest().ajax('../sys/dept/get-by-full-name', {
            'fullName': decodedRegionFullName
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK' && res.data) {
                // 直接操作 Vue 实例的数据来设置部门信息
                const searchbarElement = $('#searchbar')[0];
                if (searchbarElement && searchbarElement.__vue__) {
                    const vueInstance = searchbarElement.__vue__;
                    const regionsCondition = vueInstance.conditions.find(c => c.ref === 'regions');
                    if (regionsCondition) {
                        // 设置部门数据
                        regionsCondition.val.splice(0, regionsCondition.val.length);
                        regionsCondition.val.push(res.data);
                    }
                }
            }
        }).catch(err => {
            console.error('Error calling department API for places:', err);
        });
    }

    initItems();

    // 如果有regionFullName参数，在initItems后触发查询
    if (_regionFullName) {
        // 延迟一点时间确保部门数据已经设置
        setTimeout(() => {
            items.query();
        }, 200);
    }

    // 监听类型选择变化 - 使用定时器检查searchbar.placeTypeValue()的值变化
    let lastType = null;
    setInterval(() => {
        const currentType = searchbar.placeTypeValue();
        if (currentType !== lastType) {
            lastType = currentType;
            updateStabilityDifference(currentType);
        }
    }, 500); // 每500毫秒检查一次

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                items.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});
