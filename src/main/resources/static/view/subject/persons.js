let searchbar, items;
let $instabilityDifferenceDiv = null;

function fuzzy(count, index, sortBy, regionFullName, name, type, status) {
    const _data = {
        'count': count,
        'index': index,
        'sortBy': sortBy,
        'regionFullName': regionFullName,
        'name': name,
        'type': type,
        'status': status
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('person/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function getStatsDifference(regionFullName, type) {
    return new Promise(resolve => {
        const data = {
            regionFullName,
            type
        };
        new HttpRequest().ajax('person/instability/difference', data, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : 0);
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('person/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function importFromFile(file) {
    let _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('person/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('重点人员.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function render(id) {
    ModalUtil.open({
        'title': id === null ? '新增人员' : '编辑人员',
        'type': 2,
        'maxmin': true,
        'area': ['90%', '90%'],
        'content': 'person/form' + (id === null ? '' : ('?id=' + id)),
        'end': function () {
            items.query(id === null ? null : items.getIndexCurrent());
        }
    });
}

function initItems() {
    const _rules = App.entityDefaultRules('com.chinamobile.healthcode.model.subject.PersonDescription', ['type']);
    items = $('#table').table({
        'columns': [{
            'title': '姓名',
            'field': 'name',
            'control': 'label'
        }, {
            'title': '性别',
            'field': 'isMale',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': true,
                    'value': '男性'
                }, {
                    'key': false,
                    'value': '女性'
                }]
            }
        }, {
            'title': '证件类型',
            'field': 'credentialType',
            'control': 'label'
        }, {
            'title': '证件号码',
            'field': 'credentialNo',
            'control': 'label'
        }, {
            'title': '归属网格',
            'field': 'regionFullName',
            'control': 'label'
        }, {
            'title': '类型',
            'field': 'type',
            'control': 'select',
            'validator': _rules.type
        }, {
            'title': '分类',
            'field': 'subtype',
            'control': 'label'
        }, {
            'title': '状态',
            'field': 'status',
            'control': 'label'
        }],
        'query': (count, index, sortBy) => {
            const _regions = searchbar.regions();
            const _type = searchbar.type();

            updateStabilityDifference(_type);

            return fuzzy(count, index, sortBy, _regions.length > 0 ? _regions[0].fullName : null, searchbar.name(), searchbar.type(), searchbar.status());
        },
        'btns': [{
            'callback': item => {
                render(item.id);
            }
        }, {
            'callback': remove
        }],
        'toolbar': [{
            'name': '新增',
            'icon': 'mdi-plus',
            'callback': () => {
                render(null);
            }
        }]
    });
}

function init() {
    const _rules = App.entityDefaultRules('com.chinamobile.healthcode.model.subject.PersonDescription', ['name', 'credentialType', 'credentialNo', 'address', 'type', 'status']);
    const _regionFullName = window.location.querystring('regionFullName');

    // 准备 regions 条件配置
    const regionsCondition = {
        'title': '行政区域',
        'icon': 'mdi-map-outline',
        'control': 'department',
        'validator': {
            'max': 1,
            'queryOrganization': deptId => {
                return new Promise(resolve => {
                    new HttpRequest().ajax('../sys/dept/organization/restricted', {
                        'id': deptId
                    }, {
                        'msg': {
                            'success': false
                        }
                    }).then(res => {
                        const _data = res.code === 'OK' ? {
                            'dept': res.data.dept,
                            'subordinates': res.data.subordinates,
                            'users': res.data.members
                        } : {
                            'dept': {},
                            'subordinates': [],
                            'users': []
                        };

                        resolve(_data);
                    });
                });
            }
        },
        'ref': 'regions'
    };

    // 如果有 regionFullName 参数，先获取部门信息并设置初始值
    if (_regionFullName) {
        console.log('Processing regionFullName:', decodeURIComponent(_regionFullName));
        // 设置一个临时的初始值，稍后会被真实的部门数据替换
        regionsCondition.val = [];
    }

    searchbar = $('#searchbar').searchbar({
        'conditions': [regionsCondition, {
            'title': '姓名',
            'icon': 'mdi-account',
            'ref': 'name'
        }, {
            'title': '类型',
            'icon': 'mdi-filter-menu',
            'control': 'select',
            'validator': new Promise(resolve => {
                _rules.type.then(dto => {
                    resolve({
                        'options': dto.options
                    });
                });
            }),
            'ref': 'type'
        }, {
            'title': '状态',
            'icon': 'mdi-state-machine',
            'control': 'select',
            'validator': new Promise(resolve => {
                _rules.status.then(dto => {
                    resolve({
                        'options': dto.options
                    });
                });
            }),
            'ref': 'status'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query',
            'row': 1
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            },
            'row': 1
        }]
    });

    // 处理regionFullName参数 - 在searchbar初始化后立即执行
    if (_regionFullName) {
        const decodedRegionFullName = decodeURIComponent(_regionFullName);

        new HttpRequest().ajax('../sys/dept/get-by-full-name', {
            'fullName': decodedRegionFullName
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK' && res.data) {
                // 直接操作 Vue 实例的数据来设置部门信息
                const searchbarElement = $('#searchbar')[0];
                if (searchbarElement && searchbarElement.__vue__) {
                    const vueInstance = searchbarElement.__vue__;
                    const regionsCondition = vueInstance.conditions.find(c => c.ref === 'regions');
                    if (regionsCondition) {
                        // 设置部门数据
                        regionsCondition.val.splice(0, regionsCondition.val.length);
                        regionsCondition.val.push(res.data);
                    }
                }
            }
        }).catch(err => {
            console.error('Error calling department API:', err);
        });
    }

    // 监听类型选择变化 - 使用定时器检查searchbar.type()的值变化
    let lastType = null;
    setInterval(() => {
        const currentType = searchbar.type();
        if (currentType !== lastType) {
            lastType = currentType;
            updateStabilityDifference(currentType);
        }
    }, 500); // 每500毫秒检查一次

    initItems();

    // 如果有regionFullName参数，在initItems后触发查询
    if (_regionFullName) {
        // 延迟一点时间确保部门数据已经设置
        setTimeout(() => {
            items.query();
        }, 200);
    }

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                items.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

// 获取并显示统计差异
function updateStabilityDifference(type) {
    const regionFullName = searchbar.regions().length > 0 ? searchbar.regions()[0].fullName : null;
    getStatsDifference(regionFullName, type).then(data => {
        // 移除旧的统计显示
        if ($instabilityDifferenceDiv) {
            $instabilityDifferenceDiv.remove();
            $instabilityDifferenceDiv = null;
        }

        if (data) {
            let typeText = type ? `（${type}）` : '';
            let alertClass = data.three > 0 ? 'am-alert-danger' : (data.three < 0 ? 'am-alert-warning' : 'am-alert-secondary');
            let showCollapse = (type == null && Array.isArray(data.four) && data.four.length > 0);
            $instabilityDifferenceDiv = $(`
                    <div class="am-alert ${alertClass}" data-am-alert style="margin-top: 10px;${showCollapse ? ' cursor:pointer;' : ''}" id="instability-diff-alert">
                        <div style="position: relative;">
                            <p style="margin-bottom:0; padding-right: ${showCollapse ? '30px' : '0'};"><i class="mdi-alert-circle"></i> 上报重点人员${typeText}${data.one}位，已建档${data.two}位，差异${data.three}位</p>
                            ${showCollapse ? '<span style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%);"><i class="mdi mdi-chevron-down"></i></span>' : ''}
                        </div>
                        <div id="instabilityTypeCollapse" class="panel-collapse collapse" style="margin-top:10px; display:none;"></div>
                    </div>
                `);
            $('#searchbar').after($instabilityDifferenceDiv);
            if (showCollapse) {
                let detailsHtml = `<ul style='margin-bottom:0; color:inherit; background:transparent;'>${data.four.map(txt => `<li>${txt}</li>`).join('')}</ul>`;
                let $collapse = $instabilityDifferenceDiv.find('#instabilityTypeCollapse');
                $collapse.html(detailsHtml);
                $instabilityDifferenceDiv.click(function (e) {
                    if ($(e.target).closest('#instabilityTypeCollapse').length === 0) {
                        if ($collapse.is(':visible')) {
                            $collapse.slideUp(200);
                            $instabilityDifferenceDiv.find('.mdi-chevron-down, .mdi-chevron-up').removeClass('mdi-chevron-up').addClass('mdi-chevron-down');
                        } else {
                            $collapse.slideDown(200);
                            $instabilityDifferenceDiv.find('.mdi-chevron-down, .mdi-chevron-up').removeClass('mdi-chevron-down').addClass('mdi-chevron-up');
                        }
                    }
                });
            }
        }
    });
}

$(function () {
    init();
});
