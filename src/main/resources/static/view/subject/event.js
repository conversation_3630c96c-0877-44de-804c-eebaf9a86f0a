function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('get', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function save(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('save', item).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function queryParticipant(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('participant/query', {
            'eventId': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function saveParticipant(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('participant/save', item).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function removeParticipant(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('participant/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function getProfile(credentialNo) {
    return new Promise(resolve => {
        new HttpRequest().ajax('../../../citizen/profile/get-by-credential', {
            'credentialNo': credentialNo
        }, {
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function init() {
    const table = new Vue({
        'el': '#table',
        'data': {
            'eventId': window.location.querystring('id'),
            'participants': [],

            'profileMapping': {
                'name': '姓名',
                'mp': '联系方式',
                'credentialType': '证件类型',
                'credentialNo': '证件号码',
                'stay': '居住类型',
                'subdistrictFullName': '居住地',
                'address': '住址',
                'status': '人员状态'
            }
        },
        'methods': {
            '_query': function () {
                const that = this;

                queryParticipant(that.eventId).then(data => {
                    that.participants.splice(0, that.participants.length);

                    for (let i = 0; i < data.length; i++) {
                        data[i].collapsed = true;
                        data[i].profile = {};
                        that.participants.push(data[i]);
                    }
                });
            },
            '_add': function () {
                this.participants.push({
                    'id': null,
                    'eventId': this.eventId,
                    'name': '',
                    'credentialNo': '',
                    'collapsed': true,
                    'profile': {}
                });
            },
            '_save': function (index) {
                const that = this;

                saveParticipant(that.participants[index]).then(success => {
                    if (success && that.eventId) {
                        that._query();
                    }
                });
            },
            '_remove': function (index) {
                const that = this;

                if (that.participants[index].id === null) {
                    that.participants.splice(index, 1);
                } else {
                    removeParticipant(that.participants[index]).then(success => {
                        if (success) {
                            that._query();
                        }
                    });
                }
            },
            '_collapseProfile': function (item) {
                const that = this;

                item.collapsed = !item.collapsed;

                if ($.isEmptyObject(item.profile)) {
                    getProfile(item.credentialNo).then(data => {
                        if (data === null) {
                            item.profile.id = '';
                        } else {
                            for (const prop in data) {
                                item.profile[prop] = data[prop];
                            }

                            that.$forceUpdate();
                        }
                    });
                }
            }
        },
        'mounted': function () {
            const that = this;

            if (that.eventId !== null) {
                that._query();
            }
        }
    });

    const _rules = App.entityDefaultRules('com.chinamobile.healthcode.model.subject.EventDescription', ['title', 'type', 'subtype', 'description', 'address', 'beginTime']);

    const _types = new Promise(resolve => {
        new HttpRequest().ajax('types', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : null);
        });
    });

    const form = $('#form').form({
        'contextPath': '../../',
        // 表单定义
        'rows': [{
            'field': 'title',
            'title': '标题',
            'validator': _rules.title
        }, {
            'title': '归属网格',
            'field': 'region',
            'control': 'department',
            'validator': {
                'max': 1,
                'rule': {
                    'required': true
                }
            }
        }, {
            'field': 'type',
            'title': '大类',
            'control': 'select',
            'validator': new Promise(resolve => {
                Promise.all([_types, _rules.type]).then(dto => {
                    const _params = {
                        'options': []
                    };

                    for (const prop in dto[0]) {
                        _params.options.push({
                            'key': prop,
                            'value': prop
                        });
                    }

                    $.extend(_params, dto[1]);

                    resolve(_params);
                });
            })
        }, {
            'field': 'subtype',
            'title': '小类',
            'control': 'cascader',
            'validator': new Promise(resolve => {
                Promise.all([_types, _rules.subtype]).then(dto => {
                    const _rule = {
                        'relativeField': 'type',
                        'mapper': dto[0]
                    };

                    $.extend(_rule, dto[1]);

                    resolve(_rule);
                });
            })
        }, {
            'field': 'description',
            'title': '事件概述',
            'validator': _rules.description
        }, {
            'field': 'location',
            'title': '事发地',
            'control': 'location',
            'validator': _rules.address
        }, {
            'field': 'beginTime',
            'title': '事发时间',
            'control': 'datetime',
            'validator': new Promise(resolve => {
                _rules.beginTime.then(dto => {
                    const _rule = {
                        'format': 'yyyy-MM-dd HH:mm:ss'
                    };

                    $.extend(_rule, dto);

                    resolve(_rule);
                });
            })
        }],
        // 按钮组
        'btns': [{
            'callback': item => {
                const _region = item.region.length > 0 ? item.region[0] : null;
                item.regionId = _region === null ? null : _region.id;
                item.regionFullName = _region === null ? null : _region.fullName;
                item.address = item.location.address;
                item.lng = item.location.lng;
                item.lat = item.location.lat;

                return new Promise(resolve => {
                    save(item).then(id => {
                        if (id !== null && !item.id) {
                            form.setData({});
                        } else {
                            resolve(false);
                        }
                    });
                });
            }
        }]
    });

    const _id = window.location.querystring('id');
    if (_id !== null) {
        get(_id).then(data => {
            if (data === null) {
                return;
            }

            data.region = [{
                'id': data.regionId,
                'fullName': data.regionFullName
            }];

            data.location = {
                'address': data.address,
                'lng': data.lng,
                'lat': data.lat
            };

            form.setData(data);
        });
    }
}

$(function () {
    init();
});
