let searchbar, items;

function fuzzy(count, index, regionFullName, name, type, address) {
    const _data = {
        'count': count,
        'index': index,
        'regionFullName': regionFullName,
        'name': name,
        'type': type,
        'address': address
    };

    return new Promise(resolve => {
        new HttpRequest().ajax('unit/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    val.region = [{
                        'id': val.regionId,
                        'fullName': val.regionFullName
                    }];

                    val.location = {
                        'address': val.address,
                        'lat': val.lat,
                        'lng': val.lng
                    };
                });

                resolve(res.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function save(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('unit/save', item).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('unit/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function types() {
    return new Promise(resolve => {
        new HttpRequest().ajax('unit/types', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function importFromFile(file) {
    let _data = new FormData();
    _data.append('file', file);

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('unit/import', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                FileUtil.downloadFromBase64('重点单位.xlsx', null, res.data);
            }

            resolve(true);
        });
    });
}

function initItems(promise) {
    const _rules = App.entityDefaultRules('com.chinamobile.healthcode.model.subject.UnitDescription', ['name', 'type', 'credentialNo', 'contact', 'address', 'type', 'status']);

    items = $('#table').ediTable({
        'columns': [{
            'title': '单位名称',
            'field': 'name',
            'control': 'text',
            'validator': _rules.name
        }, {
            'title': '单位类别',
            'field': 'type',
            'control': 'select',
            'validator': new Promise(resolve => {
                promise.then(items => {
                    const _options = [];

                    $.each(items, function (index, val) {
                        _options.push({
                            'key': val.val,
                            'value': val.val
                        });
                    });

                    resolve({
                        'options': _options,
                        'rule': {
                            'required': true
                        }
                    });
                });
            })
        }, {
            'title': '归属网格',
            'field': 'region',
            'control': 'department',
            'validator': {
                'max': 1,
                'rule': {
                    'required': true
                }
            }
        }, {
            'title': '地点',
            'field': 'location',
            'control': 'location',
            'validator': {
                'rule': {
                    'required': true
                }
            }
        }],
        'query': (count, index) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, _regions.length > 0 ? _regions[0].fullName : null, searchbar.name(), searchbar.type(), searchbar.address());
        },
        'create': true,
        'save': item => {
            return new Promise(resolve => {
                const _region = item.region.length > 0 ? item.region[0] : null;
                item.regionId = _region === null ? null : _region.id;
                item.regionFullName = _region === null ? null : _region.fullName;
                item.address = item.location.address;
                item.lng = item.location.lng;
                item.lat = item.location.lat;

                save(item).then(success => {
                    resolve({
                        'success': success,
                        'scrollToFirstPage': !item.hasOwnProperty('id')
                    });
                });
            });
        },
        'remove': remove
    });
}

function init() {
    const _promise = types();

    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }, {
            'title': '名称',
            'icon': 'mdi-archive-outline',
            'ref': 'name',
        }, {
            'title': '分类',
            'icon': 'mdi-menu',
            'control': 'select',
            'validator': new Promise(resolve => {
                _promise.then(dto => {
                    const _options = [];

                    $.each(dto, function (index, val) {
                        _options.push({
                            'key': val.val,
                            'value': val.val
                        });
                    });

                    resolve({
                        'options': _options
                    });
                });
            }),
            'ref': 'type'
        }, {
            'title': '地址',
            'icon': 'mdi-map-marker-outline',
            'ref': 'address'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query'
        }, {
            'title': '导入',
            'icon': 'mdi-upload',
            'callback': () => {
                ModalUtil.open({
                    'type': 1,
                    'title': '导入',
                    'content': $('#upload'),
                    'area': '300px'
                });
            }
        }]
    });

    initItems(_promise);

    $('input[type=file][name=file]').change(function (event) {
        let that = this;

        importFromFile(event.target.files[0]).then(success => {
            $(that).val('');

            if (success) {
                items.query();
            }
        });
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});
