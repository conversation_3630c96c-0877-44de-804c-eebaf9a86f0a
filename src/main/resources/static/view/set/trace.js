function query(id, type) {
    return new Promise(resolve => {
        new HttpRequest().ajax('trace', {
            'id': id,
            'type': type
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                });
            }

            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function init() {
    new Vue({
        'el': '#app',
        'data': {
            'id': window.location.querystring('id'),
            'type': window.location.querystring('type'),
            'items': []
        },
        'mounted': function () {
            const that = this;

            if (that.id === null || that.type === null) {
                ModalUtil.msg('参数错误，即将关闭', {
                    'icon': 0,
                    'time': 1000
                }, function () {
                    window.location.close();
                });
            }

            query(that.id, that.type).then(items => {
                that.items.splice(0, that.items.length);

                $.each(items, function (index, val) {
                    switch (val.event) {
                        case '任务指派':
                            val.eventDesc = val.event;
                            if (val.address) {
                                val.eventDesc += '给' + val.address;
                            }
                            break;
                        case '扫码':
                        case '现场确认':
                            val.eventDesc = val.event;
                            if (val.address) {
                                val.eventDesc += '于' + val.address;
                            }
                            break
                            break;
                        default:
                            val.eventDesc = val.event;
                    }

                    that.items.push(val);
                });
            });
        }
    });
}

$(function () {
    init();
});