function get(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('ticket/get/detail', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                res.data.driverGreenHealthCode = res.data.driverGreenHealthCode === true ? '是' : '否';
                res.data.driverNucleicAcidNegative = res.data.driverNucleicAcidNegative === true ? '是' : '否';
            }

            resolve(res.code === 'OK' ? res.data : null);
        });
    });
}

function trace(id, type) {
    return new Promise(resolve => {
        new HttpRequest().ajax('../form/trace', {
            'id': id,
            'type': type
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                });
            }

            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function init() {
    new Vue({
        'el': '#app',
        'data': {
            'id': window.location.querystring('id'),
            'form': {
                'originRegionId': {
                    'title': '',
                    'group': '',
                    'value': ''
                },
                'originCompanyName': {
                    'title': '公司名称',
                    'group': '行程信息',
                    'value': ''
                },
                'originContactName': {
                    'title': '联系人姓名',
                    'group': '行程信息',
                    'value': ''
                },
                'originContactMobileNumber': {
                    'title': '联系人手机号码',
                    'group': '行程信息',
                    'value': ''
                },
                'licensePlateNumber': {
                    'title': '车牌号',
                    'group': '行程信息',
                    'value': ''
                },
                'fromLocation': {
                    'title': '出发地',
                    'group': '行程信息',
                    'value': ''
                },
                'originRegionFullName': {
                    'title': '到访行政区及街道',
                    'group': '行程信息',
                    'value': ''
                },
                'originCompanyName': {
                    'title': '到访公司',
                    'group': '行程信息',
                    'value': ''
                },
                'eta': {
                    'title': '计划到达时间',
                    'group': '行程信息',
                    'value': ''
                },
                'etd': {
                    'title': '计划离场时间',
                    'group': '行程信息',
                    'value': ''
                },
                'driverName': {
                    'title': '司机姓名',
                    'group': '司机信息',
                    'value': ''
                },
                'driverIdentityCardNumber': {
                    'title': '司机身份证号',
                    'group': '司机信息',
                    'value': ''
                },
                'driverMobileNumber': {
                    'title': '司机手机号码',
                    'group': '司机信息',
                    'value': ''
                },
                'driverGreenHealthCode': {
                    'title': '司机健康码绿码',
                    'group': '司机信息',
                    'value': ''
                },
                'driverNucleicAcidNegative': {
                    'title': '司机48小时内核酸是否阴性',
                    'group': '司机信息',
                    'value': ''
                },
                'passengerList': []
            },
            'trace': []
        },
        'mounted': function () {
            const that = this;

            if (!that.id) {
                ModalUtil.msg('表单数据错误，即将为您重定向', {
                    'time': 2000
                }, function () {
                    window.location.replace('ticket');
                });
            }

            get(that.id).then(data => {
                if (data === null) {
                    return;
                }

                for (const prop in data) {
                    if (that.form.hasOwnProperty(prop)) {
                        if (that.form[prop].hasOwnProperty('value')) {
                            that.form[prop].value = data[prop];
                        } else {
                            that.form[prop] = data[prop];
                        }
                    }
                }
            });

            trace(that.id, '货运司机报备').then(items => {
                that.trace.splice(0, that.trace.length);

                $.each(items, function (index, val) {
                    switch (val.event) {
                        case '任务指派':
                            val.eventDesc = val.event;
                            if (val.address) {
                                val.eventDesc += '给' + val.address;
                            }
                            break;
                        case '扫码':
                        case '现场确认':
                            val.eventDesc = val.event;
                            if (val.address) {
                                val.eventDesc += '于' + val.address;
                            }
                            break
                            break;
                        default:
                            val.eventDesc = val.event;
                    }

                    that.trace.push(val);
                });
            });
        }
    });
}

$(function () {
    init();
});