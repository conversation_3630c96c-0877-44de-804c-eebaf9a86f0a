const STATUS = {
    'options': [{
        'key': 'DRAFT',
        'value': '未审核'
    }, {
        'key': 'ZONE',
        'value': '园区确认'
    }, {
        'key': 'DENIED',
        'value': '确认退回'
    }, {
        'key': 'DISPATCHED',
        'value': '任务指派'
    }, {
        'key': 'REJECTED',
        'value': '任务退回'
    }, {
        'key': 'STREET',
        'value': '街道确认'
    }, {
        'key': 'PASSED',
        'value': '现场确认'
    }]
};

let searchbar, items;

function operate() {
    return new Promise(resolve => {
        new HttpRequest().ajax('ticket/operate', null, {
            'loading': false,
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function fuzzy(count, index, sortBy, driverName, driverMp, licensePlateNumber, departure, originRegionFullName, originCompanyName, originContactName, state, departureBeginTime, departureEndTime, beginTime, endTime) {
    const _data = {
        'count': count,
        'index': index,
        'sortBy': sortBy,
        'driverName': driverName,
        'driverMp': driverMp,
        'licensePlateNumber': licensePlateNumber,
        'departure': departure,
        'originRegionFullName': originRegionFullName,
        'originCompanyName': originCompanyName,
        'originContactName': originContactName
    };

    if (state) {
        _data.state = state;
    }

    if (departureBeginTime) {
        _data.departureBeginTime = departureBeginTime;
    }

    if (departureEndTime) {
        _data.departureEndTime = departureEndTime;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('ticket/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    switch (val.state) {
                        // 草稿状态允许货主确认/退回、街道工作人员确认/退回
                        case 'DRAFT':
                            val['_ext_'] = {
                                'btns': [2, 3, 4, 5]
                            };
                            break;
                        // 货主确认状态允许街道工作人员确认/退回
                        case 'ZONE':
                            val['_ext_'] = {
                                'btns': [3, 5]
                            };
                            break;
                        // 任务指派状态允许街道工作人员确认、社区工作人员确认/退回
                        case 'DISPATCHED':
                            val['_ext_'] = {
                                'btns': [3, 6]
                            };
                            break;
                        // 任务退回状态允许街道工作人员确认/退回
                        case 'REJECTED':
                            val['_ext_'] = {
                                'btns': [3, 5]
                            };
                            break;
                        default:
                            val['_ext_'] = {
                                'btns': []
                            };
                    }

                    const _btns = $.grep(val['_ext_'].btns, function (val, index) {
                        switch (val) {
                            case 2:
                                return $.inArray('approve', operates) >= 0;
                            case 3:
                                return $.inArray('close', operates) >= 0;
                            case 4:
                                return $.inArray('refuse-by-zone', operates) >= 0;
                            case 5:
                                return $.inArray('refuse-by-subdistrict', operates) >= 0;
                            case 6:
                                return $.inArray('reject', operates) >= 0;
                            default:
                                return false;
                        }
                    });

                    _btns.push(0);
                    val['_ext_'].btns = _btns;
                });
            }

            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function exportTo(driverName, driverMp, licensePlateNumber, departure, originRegionFullName, originCompanyName, originContactName, state, departureBeginTime, departureEndTime, beginTime, endTime) {
    const _data = {
        'driverName': driverName,
        'driverMp': driverMp,
        'licensePlateNumber': licensePlateNumber,
        'departure': departure,
        'originRegionFullName': originRegionFullName,
        'originCompanyName': originCompanyName,
        'originContactName': originContactName
    };

    if (state) {
        _data.state = state;
    }

    if (departureBeginTime) {
        _data.departureBeginTime = departureBeginTime;
    }

    if (departureEndTime) {
        _data.departureEndTime = departureEndTime;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    new HttpRequest().ajax('ticket/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('报备单.xlsx', null, res.data);
        }
    });
}

// 货主确认
function approve(id, opinion) {
    return new Promise(resolve => {
        new HttpRequest().ajax('ticket/approve', {
            'id': id,
            'opinion': opinion
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

// 货主、街道工作人员拒收
function refuse(id, opinion) {
    return new Promise(resolve => {
        new HttpRequest().ajax('ticket/refuse', {
            'id': id,
            'opinion': opinion
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

// 任务指派
function dispatch(ids, deptId, deptFullName) {
    return new Promise(resolve => {
        new HttpRequest().ajax('ticket/dispatch', {
            'ids': ids,
            'deptId': deptId,
            'deptFullName': deptFullName
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

// 任务退回
function reject(id, opinion) {
    return new Promise(resolve => {
        new HttpRequest().ajax('ticket/reject', {
            'id': id,
            'opinion': opinion
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

// 任务关闭
function close(id, opinion) {
    return new Promise(resolve => {
        new HttpRequest().ajax('ticket/close', {
            'id': id,
            'opinion': opinion
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function initItems() {
    operate().then(data => {
        operates = data;

        const _toolbar = [];
        $.each(data, function (index, val) {
            switch (val) {
                case 'dispatch':
                    _toolbar.push({
                        'name': '派单',
                        'icon': 'mdi-target',
                        'requireSelection': true,
                        'callback': () => {
                            let _array = items.getSelected();
                            const _ids = [];
                            $.each(_array, function (index, val) {
                                _ids.push(val.id);
                            });

                            const _status = ['DENIED', 'STREET', 'PASSED'];
                            _array = $.grep(_array, function (val) {
                                return $.inArray(val.state, _status) !== -1;
                            });
                            if (_array.length > 0) {
                                ModalUtil.alert('[已退回/街道确认/现场确认]状态的报备单不支持指派，请修改');
                                return;
                            }

                            Member.get({
                                'entity': 'department',
                                'max': 1,
                                'queryOrganization': deptId => {
                                    return new Promise(resolve => {
                                        new HttpRequest().ajax('/migrant/visit-form/organization', {
                                            'id': deptId
                                        }, {
                                            'msg': {
                                                'success': false
                                            }
                                        }).then(res => {
                                            const _data = res.code === 'OK' ? {
                                                'dept': res.data.dept,
                                                'subordinates': res.data.subordinates,
                                                'users': res.data.members
                                            } : {
                                                'dept': {},
                                                'subordinates': [],
                                                'users': []
                                            };

                                            resolve(_data);
                                        });
                                    });
                                },
                                'ok': departments => {
                                    return new Promise(resolve => {
                                        if (departments.length === 0) {
                                            ModalUtil.alert('请派单到社区');

                                            resolve(false);
                                        }

                                        dispatch(_ids, departments[0].id, departments[0].fullName).then(success => {
                                            if (success) {
                                                ModalUtil.msg('已成功指派任务给' + departments[0].name, {
                                                    'title': false,
                                                    'icon': 1,
                                                    'time': 1000
                                                }, function () {
                                                    items.query(items.getIndexCurrent());

                                                    resolve(success);
                                                });
                                            }
                                        });
                                    });
                                }
                            });
                        }
                    });
                    break;
            }
        });

        items = $('#table').table({
            'columns': [{
                'title': '司机姓名',
                'field': 'driverName',
                'control': 'label'
            }, {
                'title': '车牌号',
                'field': 'licensePlateNumber',
                'control': 'label'
            }, {
                'title': '到访行政区划',
                'field': 'originRegionFullName',
                'control': 'label'
            }, {
                'title': '货主注册地',
                'field': 'destinationRegionFullName',
                'control': 'label'
            }, {
                'title': '指派社区/村居',
                'field': 'dispatchedCommunityFullName',
                'control': 'label'
            }, {
                'title': '状态',
                'field': 'state',
                'control': 'select',
                'validator': STATUS
            }, {
                'title': '创建时间',
                'field': 'createTime',
                'control': 'label',
                'sortable': true
            }],
            'multiple': true,
            'query': (count, index, sortBy) => {
                return fuzzy(count, index, sortBy, searchbar.driverName(), searchbar.driverMp(), searchbar.licensePlateNumber(), searchbar.departure(), searchbar.originRegionFullName(), searchbar.originCompanyName(), searchbar.originContactName(), searchbar.state(), searchbar.departureBeginTime(), searchbar.departureEndTime(), searchbar.beginTime(), searchbar.endTime());
            },
            'btns': [{
                'callback': (item) => {
                    let _width = $(window).width();
                    if (_width < 641) {
                        _width = '90%';
                    } else if (_width < 1025) {
                        _width = '70%';
                    } else {
                        _width = '60%';
                    }

                    ModalUtil.open({
                        'type': 2,
                        'title': '报备单',
                        'area': [_width, '90%'],
                        'content': 'ticket?id=' + item.id
                    });
                }
            }, null, {
                'name': '确认',
                'icon': 'mdi-stamper',
                'callback': item => {
                    ModalUtil.prompt({
                        'title': '请填写办理意见',
                        'formType': 2
                    }, function (val, index) {
                        approve(item.id, val).then(success => {
                            if (success) {
                                items.query(items.getIndexCurrent());

                                ModalUtil.close(index);
                            }
                        });
                    });
                }
            }, {
                'name': '确认',
                'icon': 'mdi-stamper',
                'callback': item => {
                    ModalUtil.prompt({
                        'title': '请填写办理意见',
                        'formType': 2
                    }, function (val, index) {
                        close(item.id, val).then(success => {
                            if (success) {
                                items.query(items.getIndexCurrent());

                                ModalUtil.close(index);
                            }
                        });
                    });
                }
            }, {
                'name': '退回',
                'icon': 'mdi-arrow-u-left-top',
                'callback': item => {
                    ModalUtil.prompt({
                        'title': '请填写办理意见',
                        'formType': 2
                    }, function (val, index) {
                        refuse(item.id, val).then(success => {
                            if (success) {
                                items.query(items.getIndexCurrent());

                                ModalUtil.close(index);
                            }
                        });
                    });
                }
            }, {
                'name': '退回',
                'icon': 'mdi-arrow-u-left-top',
                'callback': item => {
                    ModalUtil.prompt({
                        'title': '请填写办理意见',
                        'formType': 2
                    }, function (val, index) {
                        refuse(item.id, val).then(success => {
                            if (success) {
                                items.query(items.getIndexCurrent());

                                ModalUtil.close(index);
                            }
                        });
                    });
                }
            }, {
                'name': '退回',
                'icon': 'mdi-close',
                'callback': item => {
                    ModalUtil.prompt({
                        'title': '请填写确认意见',
                        'formType': 2
                    }, function (val, index) {
                        reject(item.id, val).then(success => {
                            if (success) {
                                items.query(items.getIndexCurrent());

                                ModalUtil.close(index);
                            }
                        });
                    });
                }
            }],
            'toolbar': _toolbar
        });
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'layout': {
            'rows': 3
        },
        'conditions': [{
            'title': '司机名称',
            'icon': 'mdi-account',
            'ref': 'driverName',
            'row': 1
        }, {
            'title': '司机手机号码',
            'icon': 'mdi-cellphone',
            'ref': 'driverMp',
            'row': 1
        }, {
            'title': '车牌号',
            'icon': 'mdi-car',
            'ref': 'licensePlateNumber',
            'row': 1
        }, {
            'title': '出发地',
            'icon': 'mdi-map-marker-right',
            'ref': 'departure',
            'row': 1
        }, {
            'title': '到访行政区及街道',
            'icon': 'mdi-map-marker-left',
            'ref': 'originRegionFullName',
            'row': 1
        }, {
            'title': '公司名称',
            'icon': 'mdi-domain',
            'ref': 'originCompanyName',
            'row': 2
        }, {
            'title': '联系人姓名',
            'icon': 'mdi-contacts-outline',
            'ref': 'originContactName',
            'row': 2
        }, {
            'title': '状态',
            'icon': 'mdi-state-machine',
            'control': 'select',
            'validator': STATUS,
            'ref': 'state',
            'row': 2
        }, {
            'title': '抵汕开始时间',
            'icon': 'mdi-clock-start',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'departureBeginTime',
            'row': 2
        }, {
            'title': '抵汕结束时间',
            'icon': 'mdi-clock-end',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'departureEndTime',
            'row': 2
        }, {
            'title': '开始时间',
            'icon': 'mdi-clock-start',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'beginTime',
            'row': 3
        }, {
            'title': '结束时间',
            'icon': 'mdi-clock-end',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'endTime',
            'row': 3
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query',
            'row': 1
        }, {
            'title': '导出',
            'icon': 'mdi-download',
            'callback': () => {
                exportTo(searchbar.driverName(), searchbar.driverMp(), searchbar.licensePlateNumber(), searchbar.departure(), searchbar.originRegionFullName(), searchbar.originCompanyName(), searchbar.originContactName(), searchbar.state(), searchbar.departureBeginTime(), searchbar.departureEndTime(), searchbar.beginTime(), searchbar.endTime());
            },
            'ref': 'export',
            'row': 1
        }]
    });

    initItems();

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});
