let searchbar, items;

function fuzzy(count, index, sortBy, driverName, driverMp, licensePlateNumber, departure, originRegionFullName, originCompanyName, originContactName, examinerAddress, departureBeginTime, departureEndTime, beginTime, endTime) {
    const _data = {
        'count': count,
        'index': index,
        'sortBy': sortBy,
        'driverName': driverName,
        'driverMp': driverMp,
        'licensePlateNumber': licensePlateNumber,
        'departure': departure,
        'originRegionFullName': originRegionFullName,
        'originCompanyName': originCompanyName,
        'originContactName': originContactName,
        'examinerAddress': examinerAddress,
        'state': 'PASSED'
    };

    if (departureBeginTime) {
        _data.departureBeginTime = departureBeginTime;
    }

    if (departureEndTime) {
        _data.departureEndTime = departureEndTime;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('../ticket/fuzzy', _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function exportTo(driverName, driverMp, licensePlateNumber, departure, originRegionFullName, originCompanyName, originContactName, examinerAddress, departureBeginTime, departureEndTime, beginTime, endTime) {
    const _data = {
        'driverName': driverName,
        'driverMp': driverMp,
        'licensePlateNumber': licensePlateNumber,
        'departure': departure,
        'originRegionFullName': originRegionFullName,
        'originCompanyName': originCompanyName,
        'originContactName': originContactName,
        'examinerAddress': examinerAddress,
        'state': 'PASSED'
    };

    if (departureBeginTime) {
        _data.departureBeginTime = departureBeginTime;
    }

    if (departureEndTime) {
        _data.departureEndTime = departureEndTime;
    }

    if (beginTime) {
        _data.beginTime = beginTime;
    }

    if (endTime) {
        _data.endTime = endTime;
    }

    new HttpRequest().ajax('../ticket/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('报备单.xlsx', null, res.data);
        }
    });
}

function initItems() {
    items = $('#table').table({
        'columns': [{
            'title': '司机姓名',
            'field': 'driverName',
            'control': 'label'
        }, {
            'title': '车牌号',
            'field': 'licensePlateNumber',
            'control': 'label'
        }, {
            'title': '到访行政区及街道',
            'field': 'originRegionFullName',
            'control': 'label'
        }, {
            'title': '爱心站',
            'field': 'examinerAddress',
            'control': 'label'
        }, {
            'title': '现场确认人员',
            'field': 'examinerName',
            'control': 'label'
        }, {
            'title': '现场确认时间',
            'field': 'examineTime',
            'control': 'label',
            'sortable': true
        }, {
            'title': '创建时间',
            'field': 'createTime',
            'control': 'label',
            'sortable': true
        }],
        'query': (count, index, sortBy) => {
            return fuzzy(count, index, sortBy, searchbar.driverName(), searchbar.driverMp(), searchbar.licensePlateNumber(), searchbar.departure(), searchbar.originRegionFullName(), searchbar.originCompanyName(), searchbar.originContactName(), searchbar.examinerAddress(), searchbar.departureBeginTime(), searchbar.departureEndTime(), searchbar.beginTime(), searchbar.endTime());
        },
        'btns': [null, null, {
            'name': '浏览',
            'icon': 'mdi-text-search',
            'callback': item => {
                let _width = $(window).width();
                if (_width < 641) {
                    _width = '90%';
                } else if (_width < 1025) {
                    _width = '70%';
                } else {
                    _width = '60%';
                }

                ModalUtil.open({
                    'type': 2,
                    'title': '报备单',
                    'area': [_width, '90%'],
                    'content': '../ticket?id=' + item.id
                });
            }
        }]
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'layout': {
            'rows': 3
        },
        'conditions': [{
            'title': '司机名称',
            'icon': 'mdi-account',
            'ref': 'driverName',
            'row': 1
        }, {
            'title': '司机手机号码',
            'icon': 'mdi-cellphone',
            'ref': 'driverMp',
            'row': 1
        }, {
            'title': '车牌号',
            'icon': 'mdi-car',
            'ref': 'licensePlateNumber',
            'row': 1
        }, {
            'title': '出发地',
            'icon': 'mdi-map-marker-right',
            'ref': 'departure',
            'row': 1
        }, {
            'title': '到访行政区及街道',
            'icon': 'mdi-map-marker-left',
            'ref': 'originRegionFullName',
            'row': 1
        }, {
            'title': '公司名称',
            'icon': 'mdi-domain',
            'ref': 'originCompanyName',
            'row': 2
        }, {
            'title': '联系人姓名',
            'icon': 'mdi-contacts-outline',
            'ref': 'originContactName',
            'row': 2
        }, {
            'title': '爱心站地址',
            'icon': 'mdi-state-machine',
            'ref': 'examinerAddress',
            'row': 2
        }, {
            'title': '抵汕开始时间',
            'icon': 'mdi-clock-start',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'departureBeginTime',
            'row': 2
        }, {
            'title': '抵汕结束时间',
            'icon': 'mdi-clock-end',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'departureEndTime',
            'row': 2
        }, {
            'title': '开始时间',
            'icon': 'mdi-clock-start',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'beginTime',
            'row': 3
        }, {
            'title': '结束时间',
            'icon': 'mdi-clock-end',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd HH'
            },
            'ref': 'endTime',
            'row': 3
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query',
            'row': 1
        }, {
            'title': '导出',
            'icon': 'mdi-download',
            'callback': () => {
                exportTo(searchbar.driverName(), searchbar.driverMp(), searchbar.licensePlateNumber(), searchbar.departure(), searchbar.originRegionFullName(), searchbar.originCompanyName(), searchbar.originContactName(), searchbar.examinerAddress(), searchbar.departureBeginTime(), searchbar.departureEndTime(), searchbar.beginTime(), searchbar.endTime());
            },
            'ref': 'export',
            'row': 1
        }]
    });

    initItems();

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});