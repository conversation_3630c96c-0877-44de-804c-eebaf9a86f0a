let searchbar, items;
const attachments = {};

function fuzzy(count, index, title, isEnabled) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/sys/bulletin/fuzzy', {
            'count': count,
            'index': index,
            'title': title,
            'isEnabled': isEnabled
        }, {
            'msg': {
                'success': false
            }
        }).then(res => {
            if (res.code === 'OK') {
                $.each(res.data.items, function (index, val) {
                    if ($.isArray(val.attachments)) {
                        $.each(val.attachments, function (index, value) {
                            value.url = '../media/download?id=' + value.id;
                        });
                    }
                });

                resolve(res.data);
            } else {
                resolve({
                    total: 0,
                    items: []
                });
            }
        });
    });
}

function save(item) {
    const _attachments = [], _attachmentIds = [];
    if ($.isArray(item.attachments)) {
        $.each(item.attachments, function (index, val) {
            // 新增文件
            if (val.hasOwnProperty('_id')) {
                _attachments.push(attachments[val._id]);
            }

            // 保持文件
            if (val.hasOwnProperty('id')) {
                _attachmentIds.push(val.id);
            }
        });
    }

    item.attachmentIds = _attachmentIds;

    const _data = new FormData();
    _data.append('item', new Blob([JSON.stringify(item)], {
        'type': 'application/json'
    }));

    $.each(_attachments, function (index, val) {
        _data.append('files', val);
    });

    return new Promise(resolve => {
        new HttpRequest({
            'headers': {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': ''
            }
        }).ajax('/sys/bulletin/save', _data).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/sys/bulletin/remove', {
            'id': item.id
        }).then(res => {
            resolve(res.code === 'OK');
        });
    });
}

function initItems() {
    const _rules = App.entityDefaultRules('com.chinamobile.healthcode.model.sys.Bulletin', ['title', 'description']);

    items = $('#table').ediTable({
        'columns': [{
            'title': '标题',
            'field': 'title',
            'control': 'text',
            'validator': _rules.title,
            'sortable': true
        }, {
            'title': '描述',
            'field': 'description',
            'control': 'textarea',
            'validator': _rules.description
        }, {
            'title': '序号',
            'field': 'seq',
            'control': 'number',
            'sortable': true
        }, {
            'title': '是否生效',
            'field': 'isEnabled',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': true,
                    'value': '生效'
                }, {
                    'key': false,
                    'value': '失效'
                }],
                'rule': {
                    'required': true
                }
            }
        }, {
            'title': '附件',
            'field': 'attachments',
            'control': 'file',
            'validator': {
                'multiple': true
            },
            'hidden': true
        }, {
            'title': '创建人',
            'field': 'creatorName',
            'control': 'label'
        }, {
            'title': '创建时间',
            'field': 'createTime',
            'control': 'label',
            'sortable': true
        }, ],
        'query': (count, index) => {
            return fuzzy(count, index, searchbar.title(), searchbar.isEnabled());
        },
        'create': true,
        'save': item => {
            return new Promise(resolve => {
                save(item).then(success => {
                    resolve({
                        'success': success,
                        'scrollToFirstPage': !item.hasOwnProperty('id')
                    });
                });
            });
        },
        'remove': remove,
        'fileSelected': (row, name, files) => {
            const _files = [];
            $.each(files, function (index, val) {
                // 随机生成id
                const _id = Math.random().toString().replace(/\./g, '');
                attachments[_id] = val;

                _files.push({
                    '_id': _id,
                    'name': val.name
                });
            });

            return new Promise(resolve => {
                resolve(_files);
            });
        },
        'fileDeleted': (row, index, file) => {
            delete attachments[file._id];

            return new Promise(resolve => {
                resolve(true);
            });
        }
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '标题',
            'icon': 'mdi-archive-outline',
            'control': 'text',
            'ref': 'title',
        }, {
            'title': '是否生效',
            'icon': 'mdi-state-machine',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': true,
                    'value': '生效'
                }, {
                    'key': false,
                    'value': '失效'
                }],
            },
            'ref': 'isEnabled',
            'val': true
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                items.query();
            },
            'ref': 'query'
        }]
    });

    initItems();

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            items.query();
        }
    });
}

$(function () {
    init();
});
