Vue.component('profile', {
    'template': '<form class="am-form am-form-horizontal">\
                    <div class="am-form-group am-text-center">\
                        <img :src="user.profilePictureURL.val ? user.profilePictureURL.val : avatarURL" @click="_selectPicture" class="am-radius" height="100px" width="100px" style="cursor: pointer"/>\
                        <input @change="_readPicture($event);" ref="picture" type="file" style="display: none" accept="image/*">\
                    </div>\
                    <div class="am-form-group">\
                        <div class="am-input-group">\
                            <span class="am-input-group-label"><i class="mdi-account-outline"></i></span>\
                            <input v-model="user.name.val" :readonly="!user.name.enableEdit" :placeholder="user.name.title" type="text" name="name" class="am-form-field">\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <div class="am-input-group">\
                            <span class="am-input-group-label"><i class="mdi-card-account-details-outline"></i></span>\
                            <input v-model="user.account.val" :readonly="!user.account.enableEdit" :placeholder="user.account.title" type="text" name="account" class="am-form-field"/>\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <div class="am-input-group">\
                            <span class="am-input-group-label"><i class="mdi-alphabetical"></i></span>\
                            <input v-model="user.alphabet.val" :readonly="!user.alphabet.enableEdit" :placeholder="user.alphabet.title" type="text" name="alphabet" class="am-form-field"/>\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <div class="am-input-group">\
                            <span class="am-input-group-label"><i class="mdi-sitemap"></i></span>\
                            <input v-model="user.deptFullName.val" :placeholder="user.deptFullName.title" name="deptFullName" class="am-form-field" readonly/>\
                            <span class="am-input-group-btn">\
                                <button :disabled="!user.deptId.enableEdit" @click="_dept" class="am-btn am-btn-default" type="button">\
                                    <i class="mdi-plus"></i>\
                                </button>\
                            </span>\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <div class="am-input-group">\
                            <span class="am-input-group-label"><i class="mdi-cellphone"></i></span>\
                            <input v-model="user.mp.val" :placeholder="user.mp.title" type="number" name="mp" class="am-form-field"/>\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <div class="am-input-group">\
                            <span class="am-input-group-label"><i class="mdi-email-outline"></i></span>\
                            <input v-model="user.email.val" :placeholder="user.email.title" type="text" name="email" class="am-form-field"/>\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <div class="am-input-group">\
                            <span class="am-input-group-label"><i class="mdi-wechat"></i></span>\
                            <input v-model="user.maOpenId.val" :placeholder="user.maOpenId.title" type="text" class="am-form-field" readonly/>\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <div ref="birthday" class="am-input-group date form_datetime-3">\
                            <span class="add-on am-input-group-label"><i class="icon-th mdi-calendar-account-outline"></i></span>\
                            <input v-model="user.birthday.val" :placeholder="user.birthday.title" type="text" name="birthday" class="am-form-field" readonly/>\
                            <span class="add-on am-input-group-label"><i class="icon-remove mdi-close"></i></span>\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <div class="am-input-group">\
                            <span class="am-input-group-label"><i class="mdi-map-marker-outline"></i></span>\
                            <input v-model="user.address.val" :placeholder="user.address.title" name="address" class="am-form-field"/>\
                            <span class="am-input-group-btn">\
                                <button @click="_locate" class="am-btn am-btn-default" type="button">\
                                    <i class="mdi-map-marker-radius-outline"></i>\
                                </button>\
                            </span>\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <div class="am-input-group">\
                            <span class="am-input-group-label"><i class="mdi-gender-male"></i></span>\
                            <select v-model="user.isMale.val" :placeholder="user.isMale.title" name="isMale" class="am-form-field">\
                                <optgroup label="性别">\
                                    <option value="true">男性</option>\
                                    <option value="false">女性</option>\
                                </optgroup>\
                            </select>\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <div class="am-input-group">\
                            <span class="am-input-group-label"><i class="mdi-order-numeric-ascending"></i></span>\
                            <input v-model="user.seq.val" :placeholder="user.seq.title" type="number" name="seq" class="am-form-field"/>\
                        </div>\
                    </div>\
                    <div class="am-form-group">\
                        <button @click="_save" type="button" class="am-btn am-btn-block am-btn-primary am-center">\
                            <span class="mdi-check"></span>\
                        </button>\
                    </div>\
                </form>',
    'props': ['contextPath', 'user', 'save'],
    'data': function () {
        return {
            'avatarURL': this.contextPath + '/img/avatar.png',
            'jsKey': '',
            'mapIndex': '',
            'validator': null
        }
    },
    'methods': {
        '_uploadPicture': function (fileName, base64) {
            const that = this;

            return new Promise(resolve => {
                new HttpRequest().ajax(that.contextPath + '/sys/media/stage/base64', {
                    'bucket': 'profile-picture',
                    'items': [{
                        'name': fileName,
                        'base64': base64
                    }]
                }, {
                    'msg': {
                        'success': false
                    }
                }).then(result => {
                    resolve(result.code === 'OK' && result.data[0].success ? result.data[0].id : null);
                });
            });
        },
        '_selectPicture': function () {
            $(this.$refs.picture).trigger('click');
        },
        '_readPicture': function (event) {
            const that = this;

            if (!window.FileReader) {
                ModalUtil.alert('您的浏览器不支持文件上传');
                return;
            }

            if (event.target.files.length === 0) {
                return;
            }

            const _file = event.target.files[0];

            // 缩放
            FileUtil.zoomImage(_file, {
                'max': 100
            }).then(base64 => {
                that._uploadPicture(_file.name, base64).then(mediaId => {
                    if (mediaId === null) {
                        ModalUtil.alert('头像上传失败');
                    } else {
                        that.user.profilePictureId.val = mediaId;
                        that.user.profilePictureURL.val = base64;
                    }
                });
            });
        },
        '_dept': function () {
            const that = this;

            Member.contextPath = that.contextPath;
            Member.get({
                'entity': 'department',
                'max': 1,
                'ok': (departments, users) => {
                    if (departments.length === 0) {
                        return;
                    }

                    that.user.deptId.val = departments[0].id;
                    that.user.deptFullName.val = departments[0].fullName;

                    return true;
                }
            });
        },
        '_locate': function () {
            const that = this;

            if (that.jsKey === '') {
                return;
            }

            that.mapIndex = ModalUtil.open({
                'type': 2,
                'title': '请选择',
                'content': 'https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=' + that.jsKey + '&referer=myapp',
                'area': ['100%', '100%']
            });
        },
        '_save': function () {
            const that = this;

            // 校验
            if (!$(that.$el).valid()) {
                const _arr = [];
                for (const prop in that.validator.invalid) {
                    _arr.push(prop);
                }

                ModalUtil.alert('请修改' + that.user[_arr[0]].title + '，' + that.validator.invalid[_arr[0]]);

                return;
            }

            const _item = {};
            for (const prop in that.user) {
                if (that.user[prop].val !== null) {
                    _item[prop] = that.user[prop].val === '' ? null : that.user[prop].val;
                }
            }

            that.save(_item).then(success => {
            });
        }
    },
    'mounted': function () {
        const that = this;

        $(that.$refs.birthday).datetimepicker({
            'language': 'zh-CN',
            'format': 'yyyy-mm-dd',
            'startView': 'month',
            'minView': 'month',
            'maxView': 'month',
            'autoclose': true,
        }).on('changeDate', function (event) {
            that.user.birthday.val = event.date === null ? '' : event.date.format('yyyy-MM-dd');
        });

        new HttpRequest().ajax(that.contextPath + '/util/qqmap/js-key', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            if (result.code === 'OK') {
                that.jsKey = result.data;
            }
        });

        window.addEventListener('message', function (event) {
            ModalUtil.close(that.mapIndex);

            if (!event.data || event.data.module != 'locationPicker') {
                return;
            }

            const _location = event.data.poiname === '我的位置' ? event.data.poiaddress : event.data.poiname;
            that.user.address.val = event.data.cityname + '·' + _location;
        }, false);

        that.$emit('get');

        const _props = ['name', 'account', 'alphabet', 'deptName', 'mp', 'email', 'birthday', 'address', 'isMale'];
        const _rules = App.entityDefaultRules('com.chinamobile.sparrow.domain.model.sys.User', _props);
        const _arr = [];
        $.each(_props, function (index, val) {
            _arr.push(_rules[val]);
        });
        Promise.all(_arr).then(rules => {
            const _rules = {};
            for (let i = 0; i < _props.length; i++) {
                _rules[_props[i]] = rules[i].rule;
            }
            _rules.deptFullName = {
                'required': true
            };
            _rules.mp.minlength = 11;
            _rules.email.email = true;

            that.validator = $(that.$el).validate({
                'rules': _rules,
                'errorPlacement': function (label, element) {
                    $(element).parents('.am-form-group').removeClass('am-form-success').addClass('am-form-error');
                },
                'success': function (label, element) {
                    $(element).parents('.am-form-group').removeClass('am-form-error').addClass('am-form-success');
                }
            });
        });
    }
});

$.fn.extend({
    'profile': function (options) {
        const _options = {
            'contextPath': '',
            'user': {
                'id': {
                    'val': ''
                },
                'name': {
                    'title': '名称',
                    'val': '',
                    'enableEdit': false
                },
                'account': {
                    'title': '登录账号',
                    'val': '',
                    'enableEdit': false
                },
                'alphabet': {
                    'title': '拼音',
                    'val': '',
                    'enableEdit': false
                },
                'profilePictureId': {
                    'val': ''
                },
                'profilePictureURL': {
                    'title': '部门',
                    'val': ''
                },
                'deptId': {
                    'val': '',
                    'enableEdit': false
                },
                'deptFullName': {
                    'title': '部门',
                    'val': '',
                    'enableEdit': false
                },
                'mp': {
                    'title': '手机号码',
                    'val': ''
                },
                'email': {
                    'title': '电子邮箱',
                    'val': ''
                },
                'maOpenId': {
                    'title': '小程序OpenId',
                    'val': ''
                },
                'birthday': {
                    'title': '生日',
                    'val': ''
                },
                'address': {
                    'title': '住址',
                    'val': ''
                },
                'isMale': {
                    'title': '性别',
                    'val': ''
                },
                'seq': {
                    'title': '排序',
                    'val': null
                }
            },
            'get': () => {
                return new Promise(resolve => resolve(null));
            },
            'save': user => {
                return new Promise(resolve => resolve(true));
            }
        };

        $.extend(true, _options, options);

        const _html = '<profile :context-path="contextPath" :user="user" :save="save" @get="get"></profile>';
        $(this).append(_html);

        const _component = new Vue({
            'el': this[0],
            'data': {
                'contextPath': _options.contextPath,
                'user': _options.user
            },
            'methods': {
                'get': function () {
                    const that = this;

                    _options.get().then(user => {
                        if (user === null) {
                            return;
                        }

                        if (user.birthday) {
                            user.birthday = new Date(user.birthday).format('yyyy-MM-dd');
                        }

                        for (const prop in user) {
                            if (that.user.hasOwnProperty(prop)) {
                                that.user[prop].val = user[prop];
                            } else {
                                that.user[prop] = {
                                    'val': user[prop]
                                };
                            }
                        }

                        if (that.user.profilePictureId.val) {
                            that.user.profilePictureURL.val = that.contextPath + '/media/read?id=' + that.user.profilePictureId.val;
                        }
                    });
                },
                'save': function (user) {
                    return _options.save(user);
                }
            }
        });

        return {
            'vue': _component
        }
    }
});