let departments, users;
let deptIdForMembers;

function rsaPublicKey() {
    return new Promise(resolve => {
        new HttpRequest().ajax('../sec/login/crypto/rsa/public-key', null, {
            'msg': {
                'success': false
            }
        }).then(result => {
            resolve(result.code === 'OK' ? result.data : null);
        });
    });
}

function queryOrganization(id) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/sys/dept/organization', {
            'id': id
        }, {
            'msg': {
                'success': false
            }
        }).then(result => {
            resolve(result.code === 'OK' ? result.data : {
                'item': null,
                'branches': [],
                'users': []
            });
        });
    });
}

function queryUsers(count, index, sortBy, id, account, name, mp, isEnabled) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/sys/dept/user/fuzzy', {
            'count': count,
            'index': index,
            'sortBy': sortBy,
            'id': id,
            'account': account,
            'username': name,
            'mp': mp,
            'isEnabled': isEnabled
        }, {
            'msg': {
                'success': false
            }
        }).then(result => {
            if (result.code !== 'OK') {
                resolve({
                    'total': 0,
                    'items': []
                });
            }

            $.each(result.data.items, function (index, val) {
                val.dept = {
                    'id': val.deptId,
                    'name': val.deptName,
                    'code': val.deptCode,
                    'fullName': val.deptFullName
                };

                val['_ext_'] = val.isEnabled ? {
                    'btns': [0, 1, 3]
                } : {
                    'btns': [0, 2]
                };
            });

            resolve(result.data);
        });
    });
}

function saveDept(dept) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/sys/dept/save', dept).then(result => {
            resolve(result.code === 'OK');
        });
    });
}

function moveDept(deptId, superiorId) {
    if (deptId === superiorId) {
        return new Promise(resolve => {
            ModalUtil.alert('不允许原地移动，请修改');

            resolve(false);
        });
    }

    return new Promise(resolve => {
        new HttpRequest().ajax('/sys/dept/move', {
            'id': deptId,
            'superiorId': superiorId
        }).then(result => {
            resolve(result.code === 'OK');
        });
    });
}

function removeDept(deptId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/sys/dept/remove', {
            'id': deptId
        }).then(result => {
            resolve(result.code === 'OK');
        });
    });
}

function saveUser(user) {
    return new Promise(resolve => {
        new HttpRequest().ajax('/sys/user/save', user).then(result => {
            resolve(result.code === 'OK');
        });
    });
}

function resetPassword(userId, password) {
    return new Promise(resolve => {
        rsaPublicKey()
            .then(key => {
                return key === null ? Promise.resolve({
                    'code': 'ERROR'
                }) : new HttpRequest().ajax('/sys/user/password/reset', {
                    'id': userId,
                    'password': CryptoUtil.encryptRsa(password, key)
                });
            })
            .then(result => {
                resolve(result.code === 'OK');
            });
    });
}

function enableUser(userId, enabled) {
    return new Promise(resolve => {
        new HttpRequest().ajax(enabled ? '/sys/user/enable' : '/sys/user/disable', {
            'id': userId
        }).then(result => {
            resolve(result.code === 'OK');
        });
    });
}

function editDept(item, superior) {
    let _index;

    const _id = Math.random().toString().replace(/\./g, '') + '_';
    $('body').append('<div id="' + _id + '" class="am-margin"></div>');

    const _form = $('#' + _id).form({
        'rows': [{
            'title': '名称',
            'field': 'name',
            'validator': {
                'rule': {
                    'required': true
                }
            }
        }, {
            'title': '排序',
            'field': 'seq',
            'control': 'number'
        }],
        // 按钮组
        'btns': [{
            'callback': data => {
                if (data.seq === '') {
                    data.seq = null
                }

                saveDept(data).then(success => {
                    if (success) {
                        ModalUtil.close(_index);

                        if (superior !== null && superior.id) {
                            deptIdForMembers = superior.id;
                        }

                        departments.queryOrganization(deptIdForMembers);
                    }
                });
            }
        }]
    });

    _form.vue.$nextTick(() => {
        _form.setData(item === null ? {
            'superiorId': superior.id
        } : item);

        _index = ModalUtil.open({
            'type': 1,
            'title': item === null ? (superior === null || !superior.name ? '在根节点下新建部门' : ('在“' + superior.name + '”下新建子部门')) : '修改部门',
            'content': $('#' + _id),
            'end': function () {
                _form.vue.$destroy();

                $('#' + _id).remove();
            }
        });
    });
}

function render(user) {
    const _el = $('<div id="user" class="am-padding" style="display: none"></div>');
    $('body').append(_el);

    const _profile = _el.profile({
        'contextPath': window.location.contextPath,
        'user': {
            'name': {
                'enableEdit': true
            },
            'account': {
                'enableEdit': true
            },
            'alphabet': {
                'enableEdit': true
            },
            'deptId': {
                'val': '',
                'enableEdit': true
            }
        },
        'get': () => {
            return new Promise(resolve => resolve(user));
        },
        'save': user => {
            return saveUser(user);
        }
    });

    ModalUtil.open({
        'title': user === null ? '新增' : '编辑',
        'type': 1,
        'content': $('#user'),
        'area': ['90%', '90%'],
        'maxmin': true,
        'end': function () {
            _profile.vue.$destroy();

            $('#user').remove();

            if (user === null) {
                users.query();
            } else {
                users.query(users.getIndexCurrent());
            }
        }
    });
}

function init() {
    const searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '登录帐号',
            'icon': 'mdi-card-account-details-outline',
            'ref': 'account'
        }, {
            'title': '名称',
            'icon': 'mdi-account-outline',
            'ref': 'name'
        }, {
            'title': '手机号码',
            'icon': 'mdi-cellphone',
            'control': 'number',
            'ref': 'mp'
        }, {
            'title': '是否生效',
            'icon': 'mdi-state-machine',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': true,
                    'value': '生效'
                }, {
                    'key': false,
                    'value': '失效'
                }]
            },
            'ref': 'isEnabled'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                users.query();
            },
            'ref': 'query'
        }]
    });

    users = $('#users').table({
        'columns': [{
            'title': '登录帐号',
            'field': 'account',
            'control': 'label',
            'sortable': true
        }, {
            'title': '名称',
            'field': 'name',
            'control': 'label',
            'sortable': true
        }, {
            'title': '部门',
            'field': 'deptFullName',
            'control': 'label',
        }, {
            'title': '手机号码',
            'field': 'mp',
            'control': 'label',
            'sortable': true
        }, {
            'title': '性别',
            'field': 'isMale',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': true,
                    'value': '男'
                }, {
                    'key': false,
                    'value': '女'
                }]
            }
        }, {
            'title': '排序',
            'field': 'seq',
            'control': 'label',
            'sortable': true
        }, {
            'title': '创建人员',
            'field': 'creatorName',
            'control': 'label'
        }, {
            'title': '创建时间',
            'field': 'createTime',
            'control': 'label',
            'sortable': true
        }],
        'query': (count, index, sortBy) => {
            return queryUsers(count, index, sortBy, deptIdForMembers, searchbar.account(), searchbar.name(), searchbar.mp(), searchbar.isEnabled());
        },
        'btns': [{
            'name': '编辑',
            'icon': 'mdi-square-edit-outline',
            'callback': item => {
                render(item);
            }
        }, {
            'name': '删除',
            'icon': 'mdi-close',
            'callback': item => {
                return enableUser(item.id, false);
            }
        }, {
            'name': '恢复',
            'icon': 'mdi-reload',
            'callback': item => {
                enableUser(item.id, true).then(success => {
                    if (success) {
                        users.query();
                    }
                });
            }
        }, {
            'name': '重置密码',
            'icon': 'mdi-lock-outline',
            'callback': item => {
                ModalUtil.prompt({
                    'title': '重置密码',
                    'formType': 0,
                    'value': ''
                }, function (val, index) {
                    resetPassword(item.id, val).then(success => {
                        if (success) {
                            ModalUtil.close(index);
                        }
                    });
                });
            }
        }],
        'toolbar': [{
            'name': '新增',
            'icon': 'mdi-plus',
            'callback': () => {
                render(null);
            }
        }],
        'autoInitialize': false
    });

    departments = new Vue({
        'el': '#departments',
        'data': {
            'dept': {},
            'subordinates': [],
            'deptMapper': {
                'id': 'id',
                'name': 'name',
                'code': 'code',
                'fullName': 'fullName',
                'hasSubordinates': 'hasSubordinates'
            }
        },
        'computed': {
            // 导航栏键
            'deptIdArr': function () {
                return this.dept[this.deptMapper.code] ? this.dept[this.deptMapper.code].split('.') : [];
            },
            // 导航栏
            'deptNameArr': function () {
                return this.dept[this.deptMapper.fullName] ? this.dept[this.deptMapper.fullName].split('/') : [];
            }
        },
        'methods': {
            'queryOrganization': function (id) {
                const that = this;

                const _deptId = id ? id : null;

                queryOrganization(_deptId).then(data => {
                    // 设置部门
                    if ($.isEmptyObject(data.dept)) {
                        that.dept[that.deptMapper.id] = '';
                        that.dept[that.deptMapper.code] = '';
                        that.dept[that.deptMapper.fullName] = '';
                    } else {
                        that.dept = data.dept;
                    }

                    // 设置下级部门
                    that.subordinates = data.subordinates;

                    deptIdForMembers = id ? id : '';

                    users.query();
                });
            },
            '_queryUsers': function (dept) {
                deptIdForMembers = dept ? dept[this.deptMapper.id] : '';

                users.query();
            },
            '_addDept': function (dept) {
                editDept(null, dept === null ? null : dept);
            },
            '_editDept': function (dept) {
                editDept(dept, null);
            },
            '_moveDept': function (dept) {
                Member.get({
                    'entity': 'department',
                    'max': 1,
                    'ok': superiors => {
                        return new Promise(resolve => {
                            if (superiors.length === 0) {
                                ModalUtil.alert('请选择移入部门');

                                resolve(false);
                            }

                            moveDept(dept.id, superiors[0].id).then(success => {
                                if (success) {
                                    departments.queryOrganization(deptIdForMembers);
                                }

                                resolve(success);
                            });
                        });
                    }
                });
            },
            '_removeDept': function (dept) {
                ModalUtil.confirm('删除操作不可恢复，请点击“确认”按钮以继续', {
                    'title': false,
                    'icon': 3
                }, function (index) {
                    removeDept(dept.id).then(success => {
                        if (success) {
                            departments.queryOrganization(deptIdForMembers);
                        }
                    });

                    ModalUtil.close(index);
                });
            }
        },
        'mounted': function () {
            this.queryOrganization();
        }
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            searchbar.query();
        }
    });
}

$(function () {
    init();
});