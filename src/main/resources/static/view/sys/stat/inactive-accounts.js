let searchbar, app;

function query(count, index, sortBy, regionId, startDate, endDate) {
    const _data = {
        count,
        index,
        sortBy,
        regionId,
        startDate,
        endDate
    };

    return new Promise(resolve => {
        new HttpRequest().ajax(`mpu/details`, _data, {
            'msg': {
                'success': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : {
                total: 0,
                items: []
            });
        });
    });
}

function exportTo(regionId, startDate, endDate) {
    const _data = {
        regionId,
        startDate,
        endDate
    };

    new HttpRequest().ajax('mpu/details/export/base64', _data, {
        'msg': {
            'success': false
        }
    }).then(res => {
        if (res.code === 'OK') {
            FileUtil.downloadFromBase64('未登录账号表.xlsx', null, res.data);
        }
    });
}

function init() {
    searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-marker-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }, {
            'id': 'startDate',
            'title': '统计开始时间',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd'
            },
            'ref': 'startDate'
        }, {
            'id': 'endDate',
            'title': '统计结束时间',
            'control': 'datetime',
            'validator': {
                'format': 'yyyy-MM-dd'
            },
            'ref': 'endDate'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                app.query();
            },
            'ref': 'query'
        }, {
            'title': '导出',
            'icon': 'mdi-download',
            'callback': () => {
                let _regions = searchbar.regions();
                let _regionId = _regions[0] ? _regions[0].id : null;
                // if (_regions.length === 0) {
                //     ModalUtil.alert('请选择行政区划');
                //     return;
                // }

                exportTo(_regionId, searchbar.startDate(), searchbar.endDate());
            }
        }]
    });

    app = $('#table').table({
        'columns': [{
            'title': '行政区域',
            'field': 'three',
            'control': 'label'
        }, {
            'title': '账号名',
            'field': 'one',
            'control': 'label'
        }, {
            'title': '姓名',
            'field': 'two',
            'control': 'label'
        }],
        'query': (count, index, sortBy) => {
            let _regions = searchbar.regions();
            let _regionId = _regions[0] ? _regions[0].id : null;
            return query(count, index, sortBy, _regionId, searchbar.startDate(), searchbar.endDate());
        }
    });

    $('#startDate').on('changeDate', function (event) {
        if (event.date) {
            let endDateRangeFrom = new Date(event.date);
            let endDateRangeTo = new Date(event.date);
            endDateRangeTo.setMonth(endDateRangeFrom.getMonth() + 1);
            endDateRangeTo.setDate(endDateRangeTo.getDate() - 1);
            $('#endDate').datetimepicker('setStartDate', endDateRangeFrom);
            $('#endDate').datetimepicker('setEndDate', endDateRangeTo);
        } else {
            $('#endDate').datetimepicker('setStartDate', null);
            $('#endDate').datetimepicker('setEndDate', null);
        }
    });
    $('#endDate').on('changeDate', function (event) {
        if (event.date) {
            let startDateRangeTo = new Date(event.date);
            let startDateRangeFrom = new Date(event.date);
            startDateRangeFrom.setMonth(startDateRangeTo.getMonth() - 1);
            startDateRangeFrom.setDate(startDateRangeFrom.getDate() + 1);
            $('#startDate').datetimepicker('setStartDate', startDateRangeFrom);
            $('#startDate').datetimepicker('setEndDate', startDateRangeTo);
        } else {
            $('#startDate').datetimepicker('setStartDate', null);
            $('#startDate').datetimepicker('setEndDate', null);
        }
    });

    // let _regions = searchbar.regions();
    // let _regionId = _regions[0] ? _regions[0].id : null;
    // this.query(_regionId, searchbar.startDate(), searchbar.endDate());

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            app.query();
        }
    });
}

$(function () {
    init();
});
