function fuzzy(count, index, regionFullName, unitId) {
    return new Promise(resolve => {
        new HttpRequest().ajax('counseling/fuzzy', {
            'count': count,
            'index': index,
            'regionFullName': regionFullName,
            'unitId': unitId
        }, {
            'msg': {
                'success': false
            }
        }).then(result => {
            resolve(result.code === 'OK' ? result.data : {
                total: 0,
                items: []
            });
        });
    });
}

function remove(item) {
    return new Promise(resolve => {
        new HttpRequest().ajax('counseling/remove', {
            'id': item.id
        }).then(result => {
            resolve(result.code === 'OK');
        });
    });
}

function centers() {
    return new Promise(resolve => {
        new HttpRequest().ajax('../subject/unit/center/query', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(res => {
            resolve(res.code === 'OK' ? res.data : []);
        });
    });
}

function init() {
    const searchbar = $('#searchbar').searchbar({
        'conditions': [{
            'title': '行政区域',
            'icon': 'mdi-map-outline',
            'control': 'department',
            'validator': {
                'max': 1,
                'queryOrganization': deptId => {
                    return new Promise(resolve => {
                        new HttpRequest().ajax('../sys/dept/organization/restricted', {
                            'id': deptId
                        }, {
                            'msg': {
                                'success': false
                            }
                        }).then(res => {
                            const _data = res.code === 'OK' ? {
                                'dept': res.data.dept,
                                'subordinates': res.data.subordinates,
                                'users': res.data.members
                            } : {
                                'dept': {},
                                'subordinates': [],
                                'users': []
                            };

                            resolve(_data);
                        });
                    });
                }
            },
            'ref': 'regions'
        }, {
            'title': '综治中心',
            'icon': 'mdi-office-building',
            'control': 'select',
            'validator': new Promise(resolve => {
                centers().then(data => {
                    resolve({
                        'options': data.map(i => {
                            return {
                                'key': i.id,
                                'value': i.name
                            }
                        })
                    });
                })
            }),
            'ref': 'unitId'
        }],
        'btns': [{
            'title': '搜索',
            'icon': 'mdi-magnify',
            'callback': () => {
                table.query();
            },
            'ref': 'query'
        }]
    });

    const table = $('#table').ediTable({
        'columns': [{
            'title': '所属综治中心',
            'field': 'unitName',
            'control': 'text'
        }, {
            'title': '咨询者',
            'field': 'visitorName',
            'control': 'text'
        }, {
            'title': '性别',
            'field': 'isVisitorFemale',
            'control': 'select',
            'validator': {
                'options': [{
                    'key': true,
                    'value': '男'
                }, {
                    'key': false,
                    'value': '女'
                }]
            }
        }, {
            'title': '年龄',
            'field': 'visitorAge',
            'control': 'number'
        }, {
            'title': '主要问题',
            'field': 'problem',
            'control': 'textarea'
        }, {
            'title': '咨询地点',
            'field': 'address',
            'control': 'textarea'
        }, {
            'title': '辅导记录',
            'field': 'log',
            'control': 'textarea'
        }, {
            'title': '辅导教师',
            'field': 'teacher',
            'control': 'text'
        }, {
            'title': '辅导结果',
            'field': 'result',
            'control': 'textarea'
        }, {
            'title': '备注',
            'field': 'memo',
            'control': 'textarea'
        }, {
            'title': '时间',
            'field': 'recordingTime',
            'control': 'text'
        }],
        'query': (count, index) => {
            const _regions = searchbar.regions();

            return fuzzy(count, index, _regions.length > 0 ? _regions[0].fullName : null, searchbar.unitId());
        },
        'remove': remove
    });

    $('#searchbar').keyup(function (event) {
        if (event.keyCode === 13) {
            searchbar.query();
        }
    });
}

$(function () {
    init();
});