Vue.component('component-form', {
    'template': '<form :class="formClass">\
					<template v-if="readonly">\
						<div v-for="(row, index) in rows" class="am-form-group">\
							<label v-text="row.title" :class="elementClass.label" class="am-padding-top-0"></label>\
							<div :class="elementClass.field">\
                                <template v-if="row.control === \'file\'">\
                                    <!-- 图片预览 -->\
                                    <template v-if="row.validatorValue.type && row.validatorValue.type == \'image\'" v-show="$.isArray(data[row.field]) && data[row.field].length > 0" class="am-u-sm-12 am-u-md-8 am-u-lg-6 am-margin-left-0">\
                                        <div :name="row.field" class="am-slider am-slider-default" data-am-flexslider style="margin-bottom:0!important">\
                                            <ul class="am-slides">\
                                                <li></li>\
                                            </ul>\
                                        </div>\
                                    </template>\
                                    <!-- 附件列表 -->\
                                    <template v-else-if="$.isArray(data[row.field]) && data[row.field].length > 0">\
                                        <ul class="am-list am-margin-bottom-0 am-padding-left-0">\
                                            <li v-for="i in data[row.field]" class="am-text-truncate">\
                                                <a v-text="i.name" :href="i.url ? i.url : \'javascript:void(0)\'" :target="i.url ? \'_blank\' : \'_self\'" style="font-size:1.2rem"></a>\
                                            </li>\
                                        </ul>\
                                    </template>\
                                </template>\
                                <template v-else-if="row.control === \'ranking\'" :class="elementClass.field">\
                                    <a role="button">\
                                        <span v-for="i in row.validatorValue.options" :class="($.isNumeric(data[row.field]) && data[row.field] >= i) ? \'mdi-star\' : \'mdi-star-outline\'" class="am-icon-sm"></span>\
                                    </a>\
                                </template>\
                                <input v-else :value="_getText(row)" type="text" disabled/>\
                            </div>\
						</div>\
					</template>\
					<template v-else>\
                        <template v-for="(row, index) in rows" >\
                            <div :class="$.inArray(row.control, controlsSupportFormFeedback) === -1 ? \'\' : \'am-form-icon am-form-feedback\'" class="am-form-group" :style="breakpoint === \'sm\' ? \'\' : \'display: flex;align-items: center\'">\
                                <!-- 标签 -->\
                                <template>\
                                    <label v-if="row.validatorValue.rule && row.validatorValue.rule.required === true" :class="elementClass.label" class="am-text-warning am-padding-top-0">* {{row.title}}</label>\
                                    <label v-else :class="elementClass.label" class="am-padding-top-0">{{row.title}}</label>\
                                </template>\
                                <!-- 字段 -->\
                                <template>\
                                    <!-- 多选按钮 -->\
                                    <div v-if="row.control === \'checkbox\'" :class="elementClass.field">\
                                        <template v-if="$.isArray(row.validatorValue.options)">\
                                            <label v-for="i in row.validatorValue.options" class="am-checkbox">\
                                                <input v-model="data[row.field]" :name="row.field" :value="i.key" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" type="checkbox">\
                                                <span v-text="i.value" style="top:0;position:absolute"></span>\
                                            </label>\
                                        </template>\
                                        <template v-else>\
                                            <label v-for="(value, key) in row.validatorValue.options" class="am-checkbox">\
                                                <input v-model="data[row.field]" :name="row.field" :value="key" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" type="checkbox">\
                                                <span v-text="value" style="top:0;position:absolute"></span>\
                                            </label>\
                                        </template>\
                                    </div>\
                                    <!-- 文件上传 -->\
                                    <div v-else-if="row.control === \'file\'" :class="elementClass.field">\
                                        <!-- 上传 -->\
                                        <div class="am-form-group am-form-file">\
                                            <button type="button" class="am-btn am-btn-sm am-btn-secondary">\
                                                <span></span> 上传\
                                            </button>\
                                            <input :name="row.field" @click="_fileBeforeSelect(row);" @change="_fileSelected(row);" type="file">\
                                        </div>\
                                    </div>\
                                    <!-- 单选按钮 -->\
                                    <div v-else-if="row.control === \'radio\'" :class="elementClass.field">\
                                        <template v-if="$.isArray(row.validatorValue.options)">\
                                            <label v-for="i in row.validatorValue.options" class="am-radio">\
                                                <input v-model="data[row.field]" :name="row.field" :value="i.key" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" type="radio">\
                                                <span v-text="i.value" style="top:0;position:absolute"></span>\
                                            </label>\
                                        </template>\
                                        <template v-else>\
                                            <label v-for="(value, key) in row.validatorValue.options" class="am-radio">\
                                                <input v-model="data[row.field]" :name="row.field" :value="key" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" type="radio">\
                                                <span v-text="value" style="top:0;position:absolute"></span>\
                                            </label>\
                                        </template>\
                                    </div>\
                                    <!-- 打分 -->\
                                    <div v-else-if="row.control === \'ranking\'" :class="elementClass.field" class="am-padding-top-sm">\
                                        <a>\
                                            <span v-for="s in row.validatorValue.options" :class="($.isNumeric(data[row.field]) && data[row.field] >= s) ? \'mdi-star\' : \'mdi-star-outline\'" @click="Vue.set(data, row.field, s);" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" class="am-icon-sm"></span>\
                                        </a>\
                                    </div>\
                                    <div v-else :class="elementClass.field">\
                                        <!-- 级联选择器 -->\
                                        <select v-if="row.control === \'cascader\'" :name="row.field" v-model="data[row.field]" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" class="am-form-field">\
                                            <optgroup :label="\'请选择\' + row.title">\
                                                <template v-if="data[row.validatorValue.relativeField] != null">\
                                                    <template v-if="$.isArray(row.validatorValue.mapper[data[row.validatorValue.relativeField]])">\
                                                        <option v-for="i in row.validatorValue.mapper[data[row.validatorValue.relativeField]]" :value="i.key" v-text="i.value"></option>\
                                                    </template>\
                                                    <template v-else>\
                                                        <option v-for="(value, key) in row.validatorValue.mapper[data[row.validatorValue.relativeField]]" :value="key" v-text="value"></option>\
                                                    </template>\
                                                </template>\
                                            </optgroup>\
                                        </select>\
                                        <!-- 时间 -->\
                                        <div v-else-if="row.control === \'datetime\'" :ref="\'datetime-\' + row.field" class="am-input-group date form_datetime-3">\
                                            <span class="add-on am-input-group-label"><i class="icon-th mdi-calendar-outline"></i></span>\
                                            <input :name="row.field" v-model.trim="data[row.field]" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" type="text" class="am-form-field" readonly/>\
                                            <span class="add-on am-input-group-label"><i class="icon-remove mdi-close"></i></span>\
                                        </div>\
                                        <!-- 部门选择 -->\
                                        <div v-else-if="row.control === \'department\'" class="am-input-group">\
                                            <input :name="row.field" :value="_getText(row)" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" type="text" class="am-form-field" readonly/>\
                                            <span class="am-input-group-btn">\
                                                <button @click="_getMember(row, \'department\');" class="am-btn am-btn-secondary" type="button">\
                                                    <span class="mdi-sitemap-outline"></span>\
                                                </button>\
                                            </span>\
                                        </div>\
                                        <!-- 标签 -->\
                                        <input v-else-if="row.control === \'label\'" :value="$.isFunction(row.formatter) ? row.formatter(data[row.field]) : data[row.field]" type="text" class="am-form-field" disabled/>\
                                        <!-- 定位 -->\
                                        <div v-else-if="row.control === \'location\'" class="am-input-group">\
                                            <input v-model.trim="data[row.field].address" :name="row.field" type="text" class="am-form-field"/>\
                                            <span class="am-input-group-btn">\
                                                <button @click="_locate(row)" class="am-btn am-btn-secondary" type="button">\
                                                    <span class="mdi-map-marker-radius-outline"></span>\
                                                </button>\
                                            </span>\
                                        </div>\
                                        <!-- 下拉 -->\
                                        <select v-else-if="row.control === \'select\'" :name="row.field" v-model="data[row.field]" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" class="am-form-field">\
                                            <optgroup :label="\'请选择\' + row.title">\
                                                <template v-if="$.isArray(row.validatorValue.options)">\
                                                    <option v-for="i in row.validatorValue.options" :value="i.key" v-text="i.value"></option>\
                                                </template>\
                                                <template v-else>\
                                                    <option v-for="(value, key) in row.validatorValue.options" :value="key" v-text="value"></option>\
                                                </template>\
                                            </optgroup>\
                                        </select>\
                                        <!-- 多行文本 -->\
                                        <textarea v-else-if="row.control == \'textarea\'" :name="row.field" v-model.trim="data[row.field]" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" rows="3" class="am-form-field"></textarea>\
                                        <!-- 人员选择 -->\
                                        <div v-else-if="row.control === \'user\'" class="am-input-group">\
                                            <input :name="row.field" :value="_getText(row)" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" type="text" class="am-form-field" readonly/>\
                                            <span class="am-input-group-btn">\
                                                <button @click="_getMember(row, \'user\');" class="am-btn am-btn-secondary" type="button">\
                                                    <span class="mdi-account-box-outline"></span>\
                                                </button>\
                                            </span>\
                                        </div>\
                                        <!-- 单行文本，默认 -->\
                                        <input v-else :name="row.field" :type="row.control" :autocomplete="row.autocomplete" v-model.trim="data[row.field]" @change="$.isFunction(row.change) && row.change(row.field, data[row.field])" class="am-form-field"/>\
                                    </div>\
                                </template>\
                            </div>\
                            <!-- 文件列表 -->\
                            <div v-if="row.control === \'file\' && $.isArray(data[row.field]) && data[row.field].length > 0" class="am-form-group">\
                                <div :class="(elementClass.field ? (elementClass.field.substring(0, elementClass.field.lastIndexOf(\'-\')) + \'-offset-\' + (12 - elementClass.field.split(\'-\')[3]) + \' \') : \'\') + elementClass.field">\
                                    <!-- 图片预览 -->\
                                    <template v-if="row.validatorValue.type && row.validatorValue.type === \'image\'" class="am-u-sm-12 am-u-md-8 am-u-lg-6 am-u-md-offset-2 am-u-lg-offset-3">\
                                        <div :ref="\'slider-\' + row.field" class="am-slider am-slider-default" data-am-flexslider>\
                                            <ul class="am-slides">\
                                                <li></li>\
                                            </ul>\
                                        </div>\
                                    </template>\
                                    <!-- 附件列表 -->\
                                    <template v-else>\
                                        <ul class="am-list">\
                                            <li v-for="(i, index) in data[row.field]" class="am-text-truncate">\
                                                <a :href="i.url ? i.url : \'javascript:void(0)\'" :target="i.url ? \'_blank\' : \'_self\'" style="font-size:1.2rem">\
                                                    {{i.name ? i.name : (\'文件\' + (index + 1)) }}\
                                                    <button @click="_fileDeleted($event, row, index, i);" type="button" class="am-close am-fr">\
                                                        <span class="mdi-close"></span>\
                                                    </button>\
                                                </a>\
                                            </li>\
                                        </ul>\
                                    </template>\
                                </div>\
                            </div>\
                        </template>\
                    </template>\
                    <hr v-if="btns.length > 0"/>\
                    <div class="am-form-group am-g">\
                        <div :class="elementClass.field === \'\' ? \'\' : \'am-u-sm-12\'" style="text-align: right">\
                            <button v-if="btns.length > 0 && btns[0] != null && $.isFunction(btns[0].callback)" @click="_ok();" type="button" class="am-btn am-btn-sm am-btn-primary am-margin-left-sm">\
                                <span :class="readonly ? \'mdi-square-edit-outline\' : \'mdi-content-save-outline\'"></span> {{readonly? \'编辑\' : \'保存\'}}\
                            </button>\
                            <button v-if="btns.length > 1 && btns[1] != null && $.isFunction(btns[1].callback) && !readonly" @click="_cancel();" type="button" class="am-btn am-btn-sm am-btn-primary am-margin-left-sm">\
                                <span class="mdi-close"></span> 取消\
                            </button>\
                            <button v-if="btns.length > 2 && btns[2] != null && $.isFunction(btns[2].callback) && !readonly" @click="_remove();" type="button" class="am-btn am-btn-sm am-btn-primary am-margin-left-sm">\
                                <span class="mdi-trash-can"></span> 删除\
                            </button>\
                            <button v-for="(btn, index) in btns" v-if="index > 3 && btn != null && $.isFunction(btn.callback) && !readonly" @click="btn.callback(row);" class="am-btn am-btn-sm am-btn-primary am-margin-left-sm">\
                                <span class="mdi-menu"></span> {{btn.name}}\
                            </button>\
                        </div>\
                    </div>\
                </form>',
    'props': ['contextPath', 'layout', 'rows', 'data', 'copy', 'readonly', 'fileSelected', 'fileDeleted', 'btns', 'message'],
    'data': function () {
        let _breakpoint;

        const _width = window.innerWidth;
        if (_width < 641) {
            _breakpoint = 'sm';
        } else if (_width < 1025) {
            _breakpoint = 'md'
        } else {
            _breakpoint = 'lg';
        }

        return {
            'breakpoint': _breakpoint,
            'controlsSupportValidate': ['cascader', 'checkbox', 'datetime', 'department', 'location', 'number', 'password', 'radio', 'ranking', 'select', 'tel', 'text', 'textarea', 'user'],
            'controlsSupportFormFeedback': ['number', 'password', 'select', 'tel', 'text', 'textarea'],
            'validateErrorMsg': {},
            'validator': null,
            'map': {
                'jsKey': null,
                'field': null,
                'modal': null
            }
        };
    },
    'computed': {
        'formClass': function () {
            let _class = 'am-form';
            if (this.breakpoint !== 'sm') {
                _class += ' am-form-horizontal';
            }

            return _class;
        },
        'elementClass': function () {
            // 默认布局
            const _layout = {
                'sm': {
                    'label': '',
                    'field': ''
                },
                'md': {
                    'label': 'am-u-md-3',
                    'field': 'am-u-md-9'
                },
                'lg': {
                    'label': 'am-u-lg-2',
                    'field': 'am-u-lg-10'
                }
            };

            // 合并布局
            if (this.layout !== null) {
                $.extend(_layout, this.layout);
            }

            let _class;
            switch (this.breakpoint) {
                case 'sm':
                    _class = _layout.sm;
                    break;
                case 'md':
                    _class = _layout.md;
                    break;
                default:
                    _class = _layout.lg;
                    break;
            }

            _class.label = 'am-form-label ' + _class.label;
            return _class;
        }
    },
    'methods': {
        'validate': function () {
            // 校验
            if ($(this.$el).valid()) {
                return true;
            }

            const _array = [];
            for (const prop in this.validator.invalid) {
                _array.push(prop);
            }

            const _rows = [];
            $.each(this.rows, function (index, val) {
                if ($.inArray(val.field, _array) !== -1) {
                    _rows.push(val);
                }
            });

            this.$emit('invalidated', _rows, this.validator.invalid);

            return false;
        },
        '_initControl': function () {
            const that = this;

            const _promises = [];
            $.each(that.rows, function (index, val) {
                let _promise;

                if (val.validator instanceof Promise) {
                    _promise = val.validator;

                    // 设置默认值，避免影响表单的渲染
                    switch (val.control) {
                        case 'cascader':
                            val.validatorValue = {
                                'relativeField': '',
                                'mapper': {}
                            };
                            break;
                        case 'checkbox':
                        case 'radio':
                        case 'ranking':
                        case 'select':
                            val.validatorValue.options = [];
                            break;
                        case 'datetime':
                            val.validatorValue.format = 'yyyy-MM-dd';
                            break;
                        case 'file':
                            val.validatorValue.type = 'file';
                            val.validatorValue.multiple = false;
                            val.validatorValue.filter = null;
                            break;
                    }
                } else {
                    _promise = new Promise(resolve => {
                        resolve(val.validator);
                    });
                }

                _promises.push(new Promise(resolve => {
                    _promise.then(data => {
                        // 合并
                        $.extend(true, val.validatorValue, data);

                        resolve(val);
                    });
                }));
            });

            Promise.all(_promises).then(rows => {
                const _rules = {};

                $.each(rows, function (index, val) {
                    // 设置控件
                    switch (val.control) {
                        // 设置日期控件
                        case 'datetime':
                            const _options = {
                                'autoclose': true,
                                'language': 'zh-CN',
                                'format': 'yyyy-mm-dd hh:ii:ss',
                                'startView': 'month',
                                'minView': 'hour',
                                'maxView': 'decade'
                            };

                            switch (val.validatorValue.format) {
                                case 'yyyy':
                                    _options.format = 'yyyy';
                                    _options.startView = 'decade';
                                    _options.minView = 'decade';
                                    break;
                                case 'yyyy-MM':
                                    _options.format = 'yyyy-mm';
                                    _options.startView = 'year';
                                    _options.minView = 'year';
                                    break;
                                case 'yyyy-MM-dd':
                                    _options.format = 'yyyy-mm-dd';
                                    _options.minView = 'month';
                                    break;
                                case 'yyyy-MM-dd HH':
                                    _options.format = 'yyyy-mm-dd hh';
                                    _options.minView = 'day';
                                    break;
                                case 'yyyy-MM-dd HH:mm':
                                    _options.format = 'yyyy-mm-dd hh:ii';
                                    _options.minView = 'hour';
                                    break;
                                default:
                                    break;
                            }

                            const _el = $(that.$refs['datetime-' + val.field]);
                            _el.datetimepicker(_options).on('changeDate', function () {
                                let _val = _el.find('input:first').val();
                                if (_val) {
                                    switch (_options.format) {
                                        case 'yyyy':
                                            _val += '-01-01';
                                            break;
                                        case 'yyyy-mm':
                                            _val += '-01';
                                            break;
                                    }
                                }

                                Vue.set(that.data, val.field, _val);

                                // 触发验证
                                that.validator.element(_el.find('input:first'));
                            });

                            break;
                        case 'file':
                            // 设置默认媒体类型
                            if (val.validatorValue == null) {
                                val.validatorValue = {};
                            }
                            if (val.validatorValue.type) {
                                val.validatorValue.type = 'file';
                            }

                            const _el2 = $(that.$el).find('[name=' + val.field + ']');
                            let _icon = 'mdi-file-document-plus-outline';

                            switch (val.validatorValue.type) {
                                // 音频
                                case 'audio':
                                    // 默认过滤器
                                    _el2.attr('accept', 'audio/mp4,audio/mpeg');
                                    _icon = 'mdi-music-note-plus';
                                    break;
                                // 图片
                                case 'image':
                                    _el2.attr('accept', 'image/gif,image/jpeg,image/png');
                                    _icon = 'mdi-image-plus-outline';
                                    break;
                                // 视频
                                case 'video':
                                    _el2.attr('accept', 'video/mp4,video/mpeg');
                                    _icon = 'mdi-movie-plus-outline';
                                    break;
                                // 其它
                                default:
                                    break;
                            }

                            // 设置图标
                            _el2.prev().children('span').addClass(_icon);

                            // 设置多选
                            if (val.validatorValue && val.validatorValue.multiple) {
                                _el2.prop('multiple', true);
                            }

                            // 设置过滤器
                            if (val.validatorValue && val.validatorValue.filter) {
                                _el2.attr('accept', this.rows[i].validatorValue.filter);
                            }

                            break;
                    }

                    if ($.inArray(val.control, that.controlsSupportValidate) === -1) {
                        return;
                    }

                    if ($.isEmptyObject(val.validatorValue.rule)) {
                        return;
                    }

                    _rules[val.field] = val.validatorValue.rule;
                });

                // 设置验证器
                that.validator = $(that.$el).validate({
                    'rules': _rules,
                    'errorPlacement': function (label, element) {
                        const _parent = $(element).parents('.am-form-group');
                        _parent.removeClass('am-form-success').addClass('am-form-error');

                        const _msg = $(label).text();

                        if (_parent.hasClass('am-form-icon')) {
                            if ($(element).nextAll().length > 0) {
                                $(element).nextAll().remove();
                            }

                            const _el = '<span class="am-icon-times" title="' + _msg + '"></span>';
                            $(element).after(_el);
                        }
                    },
                    'success': function (label, element) {
                        const _parent = $(element).parents('.am-form-group');
                        _parent.removeClass('am-form-error').addClass('am-form-success');

                        if (_parent.hasClass('am-form-icon')) {
                            if ($(element).nextAll().length > 0) {
                                $(element).nextAll().remove();
                            }

                            const _el = '<span class="am-icon-check"></span>';
                            $(element).after(_el);
                        }
                    }
                });

                // 设置uCheck控件
                $(that.$el).find('input[type=checkbox], input[type=radio]').each(function () {
                    $(this).uCheck();
                });

                // 设置flexslider控件
                for (const prop in that.$refs) {
                    if (prop.indexOf('slider-') !== 0) {
                        continue;
                    }

                    $(that.$refs[prop]).flexslider();
                }

                that.$forceUpdate();
            });
        },
        '_getText': function (row) {
            const that = this;

            let _text = '', _array;

            switch (row.control) {
                // 级联
                case 'cascader':
                    const _relativeValue = that.data[row.validatorValue.relativeField];
                    if (_relativeValue === null) {
                        _text = null;
                    } else {
                        if ($.isArray(row.validatorValue.mapper[_relativeValue])) {
                            const _array = $.grep(row.validatorValue.mapper[_relativeValue], function (index, val) {
                                return that.data[row.field] === val.key;
                            });
                            _text = _array.length > 0 ? _array[0].value : null;
                        } else {
                            _text = row.validatorValue.mapper[_relativeValue][that.data[row.field]];
                        }
                    }
                    break;
                // 多选控件
                case 'checkbox':
                    if ($.isArray(that.data[row.field])) {
                        _array = [];

                        if ($.isArray(row.validatorValue.options)) {
                            for (let i = 0; i < that.data[row.field].length; i++) {
                                const _temp = $.grep(row.validatorValue.options, function (val) {
                                    return val.key === that.data[row.field][i];
                                });
                                if (_temp.length > 0) {
                                    _array.push(_temp[0].value);
                                }
                            }
                        } else {
                            for (let i = 0; i < that.data[row.field].length; i++) {
                                _array.push(row.validatorValue.options[that.data[row.field][i]]);
                            }
                        }

                        _text = _array.join(',');
                    } else {
                        _text = '';
                    }

                    break;
                // 部门/人员选择控件
                case 'department':
                    _array = [];
                    $.each(that.data[row.field], function (index, val) {
                        _array.push(val.fullName);
                    });

                    _text = _array.join('，');
                    break;
                // 定位控件
                case 'location':
                    _text = that.data[row.field].address;
                    break;
                // 单选/下拉控件
                case 'radio':
                case 'select':
                    if ($.isArray(row.validatorValue.options)) {
                        const _temp = $.grep(row.validatorValue.options, function (val) {
                            return val.key === that.data[row.field];
                        });
                        if (_temp.length > 0) {
                            _text = (_temp[0].value);
                        }
                    } else {
                        _text = row.validatorValue.options[that.data[row.field]];
                    }
                    break;
                case 'user':
                    _array = [];
                    $.each(that.data[row.field], function (index, val) {
                        _array.push(val.name);
                    });

                    _text = _array.join('，');
                    break;
                case 'label':
                    if ($.isFunction(row.formatter)) {
                        return row.formatter(that.data[row.field]);
                    } else {
                        return that.data[row.field];
                    }
                default:
                    _text = that.data[row.field];
                    break;
            }

            return _text;
        },
        '_fileBeforeSelect': function (row) {
            // 选中文件前先清理
            $(this.$el).find('[name=' + row.field + ']').val('');
        },
        '_fileSelected': function (row) {
            const _name = row.field;
            const _el = $(this.$el).find('[name=' + _name + ']');

            if (!$.isFunction(this.fileSelected)) {
                return;
            }

            const that = this;

            const _val = this.fileSelected(row, _name, _el[0].files);
            if (_val instanceof Promise) {
                _val.then(data => {
                    if ($.isArray(data)) {
                        $.each(data, function (index, val) {
                            that.data[row.field].push(val);
                        });
                        //Vue.set(that.data, row.field, data);
                    }
                });
            } else if ($.isArray(_val)) {
                $.each(data, function (index, val) {
                    that.data[row.field].push(val);
                });
                //Vue.set(that.data, row.field, _val);
            }

            that.validator.element(_el);
        },
        '_fileDeleted': function (event, row, index, val) {
            event.stopPropagation();
            event.preventDefault();

            if (!$.isFunction(this.fileDeleted)) {
                return;
            }

            const that = this;

            const _val = this.fileDeleted(row, index, val);
            if (_val instanceof Promise) {
                _val.then(success => {
                    if (success) {
                        that.data[row.field].splice(index, 1);
                    }
                    //Vue.set(that.data, row.field, data);
                });
            } else if (_val) {
                that.data[row.field].splice(index, 1);
                //Vue.set(that.data, row.field, _val);
            }
        },
        '_getMember': function (row, entity) {
            const that = this;

            const _options = {
                'entity': entity
            };

            if ($.isNumeric(row.validatorValue.max)) {
                _options.max = row.validatorValue.max
            }

            if (row.validatorValue.hasOwnProperty('userMapper')) {
                _options.userMapper = row.validatorValue.userMapper;
            }

            if (row.validatorValue.hasOwnProperty('deptMapper')) {
                _options.deptMapper = row.validatorValue.deptMapper;
            }

            if ($.isFunction(row.validatorValue.queryOrganization)) {
                _options.queryOrganization = row.validatorValue.queryOrganization;
            }

            if ($.isFunction(row.validatorValue.queryUsers)) {
                _options.queryUsers = row.validatorValue.queryUsers;
            }

            if ($.isFunction(row.validatorValue.beforeItemAdd)) {
                _options.beforeItemAdd = row.validatorValue.beforeItemAdd;
            }

            switch (entity) {
                case 'department':
                    _options.ok = (departments, users) => {
                        that.data[row.field].splice(0, that.data[row.field].length);
                        $.each(departments, function (index, val) {
                            that.data[row.field].push(val);
                        });

                        return true;
                    };
                    break;
                case 'user':
                    _options.ok = (departments, users) => {
                        that.data[row.field].splice(0, that.data[row.field].length);
                        $.each(users, function (index, val) {
                            that.data[row.field].push(val);
                        });

                        return true;
                    };
                    break;
            }

            Member.get(_options);
        },
        '_locate': function (row) {
            const that = this;

            if (that.map.jsKey === null) {
                return;
            }

            that.map.modal = ModalUtil.open({
                'type': 2,
                'title': '请选择',
                'content': 'https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=' + that.map.jsKey + '&referer=myapp',
                'area': ['100%', '100%']
            });

            that.map.field = row.field;
        },
        '_ok': function () {
            const that = this;

            // 激活
            if (that.readonly) {
                Vue.set(that, 'readonly', false);
                return;
            }

            // 校验
            if (!that.validate()) {
                return;
            }

            const _val = that.btns[0].callback(JSON.parse(JSON.stringify(that.data)));
            if (_val instanceof Promise) {
                _val.then(success => {
                    if (success === true) {
                        that.__ok(success);
                    }
                });
            } else if (_val === true) {
                that.__ok(true);
            }
        },
        '__ok': function (success) {
            if (success) {
                // 锁定
                Vue.set(this, 'readonly', true);

                // 赋值
                Vue.set(this, 'copy', JSON.parse(JSON.stringify(this.data)));
            }
        },
        '_cancel': function () {
            // 还原数据
            Vue.set(this, 'data', JSON.parse(JSON.stringify(this.copy)));

            this.btns[1].callback(JSON.parse(JSON.stringify(this.data)));
        },
        '_remove': function () {
            const that = this;

            ModalUtil.confirm(this.message.deleteConfirmMsg, {
                'icon': 3
            }, function (index) {
                const _val = this.btns[2].callback(JSON.parse(JSON.stringify(that.data)));
                if (_val instanceof Promise) {
                    _val.then(success => {
                        if (success === true) {
                            ModalUtil.close(index);
                        }
                    });
                } else if (_val === true) {
                    ModalUtil.close(index);
                }
            });
        }
    },
    'watch': {
        'readonly': function (newVal) {
            const that = this;

            if (!newVal) {
                that.$nextTick(() => {
                    this._initControl();
                });
            }
        }
    },
    'mounted': function () {
        const that = this;

        // 制作拷贝
        Vue.set(that, 'copy', JSON.parse(JSON.stringify(that.data)));

        // 使用了member组件
        if (typeof Member === 'function') {
            Member.contextPath = that.contextPath;
        }

        that._initControl();

        new HttpRequest({
            'baseURL': that.contextPath
        }).ajax('/util/qqmap/js-key', null, {
            'loading': false,
            'msg': {
                'success': false,
                'error': false
            }
        }).then(result => {
            if (result.code === 'OK') {
                that.map.jsKey = result.data;
            }
        });

        window.addEventListener('message', function (event) {
            ModalUtil.close(that.map.modal);

            if (!event.data || event.data.module != 'locationPicker') {
                return;
            }

            const _location = event.data.poiname === '我的位置' ? event.data.poiaddress : event.data.poiname;

            if (that.map.field !== null) {
                that.data[that.map.field].address = event.data.cityname + '·' + _location;
                that.data[that.map.field].lat = event.data.latlng.lat;
                that.data[that.map.field].lng = event.data.latlng.lng;

                that.map.field = null;
            }
        }, false);

        $.validator.addMethod('checkCredentialNo', function (value, element, params) {
            let checkName = /^(\d{15}|\d{18}|\d{17}[Xx])$/g;
            return this.optional(element) || (checkName.test(value));
        }, '长度为15或18位');

        $.validator.addMethod('containsChinese', function (value, element, params) {
            let regex = /[\u4e00-\u9fa5]/;
            return this.optional(element) || regex.test(value);
        }, '填写内容须包含中文');

        $.validator.addMethod("onlyChinese", function(value, element, params) {
            let regex = /^[\u4e00-\u9fa5]+$/;
            return this.optional(element) || regex.test(value);
        }, "只支持填写中文");
    }
});

$.fn.extend({
    // 表单组件
    'form': function (options) {
        const _options = {
            'contextPath': window.location.contextPath,
            // 表单定义
            'rows': [],
            'invalidated': (rows, error) => {
                ModalUtil.alert('请修改' + rows[0].title + '，' + error[rows[0].field]);
            },
            'fileSelected': null,
            'fileDeleted': null,
            // 按钮组
            'btns': [],
            'message': {
                'deleteConfirmMsg': '删除操作不可恢复，请点击“确认”按钮以继续'
            }
        };

        $.extend(true, _options, options);

        for (let i = 0; i < _options.rows.length; i++) {
            // 设置默认控件
            if (!_options.rows[i].hasOwnProperty('control')) {
                _options.rows[i].control = 'text';
            }

            _options.rows[i].validatorValue = ['checkbox', 'radio', 'select', 'ranking'].indexOf(_options.rows[i].control) === -1 ? {} : [];
        }

        const _initData = data => {
            // 归一化数据
            const _data = $.isPlainObject(data) ? data : {};

            for (let i = 0; i < _options.rows.length; i++) {
                if (_data[_options.rows[i].field] != null) {
                    continue;
                }

                if (['checkbox', 'department', 'file', 'user'].indexOf(_options.rows[i].control) !== -1) {
                    if (!_data[_options.rows[i].field] || !$.isArray(_data[_options.rows[i].field])) {
                        _data[_options.rows[i].field] = [];
                    }
                } else if (['location'].indexOf(_options.rows[i].control) !== -1) {
                    if (!_data[_options.rows[i].field] || $.isEmptyObject(_data[_options.rows[i].field])) {
                        _data[_options.rows[i].field] = {
                            'address': null,
                            'lat': null,
                            'lng': null
                        }
                    }
                }
            }

            return _data;
        };

        const _html = '<component-form :context-path="contextPath" :rows="rows" :data="data" :copy="copy" :readonly="readonly" :btns="btns" :message="message" :file-selected="fileSelected" :file-deleted="fileDeleted" @invalidated="invalidated"></component-form>';
        $(this).append(_html);

        const _form = new Vue({
            'el': this[0],
            'data': {
                'contextPath': _options.contextPath,
                'rows': _options.rows,
                'data': _initData(),
                'copy': _initData(),
                'readonly': false,
                'btns': _options.btns,
                'message': _options.message,
                'invalidated': _options.invalidated,
                'fileSelected': _options.fileSelected,
                'fileDeleted': _options.fileDeleted
            },
            'methods': {
                'getData': function () {
                    return JSON.parse(JSON.stringify(this.data));
                },
                'setData': function (data) {
                    Vue.set(this, 'data', _initData(data == null ? null : JSON.parse(JSON.stringify(data))));
                    Vue.set(this, 'copy', _initData(data == null ? null : JSON.parse(JSON.stringify(data))));

                    // 删除验证样式
                    $(this.$el).find('.am-form-success, .am-form-error').each(function () {
                        $(this).removeClass('am-form-success').removeClass('am-form-error');
                    });
                    $(this.$el).find('.am-form-feedback input').nextAll().remove();
                },
                'setReadonly': function (val) {
                    Vue.set(this, 'readonly', val);
                }
            }
        });

        // 未提供确定回调，使表格只读
        if (_form.btns && _form.btns[0] && $.isFunction(_form.btns[0].callback)) {
            _form.setReadonly(false);
        }

        return {
            'vue': _form,
            'validate': _form.$children[0].validate,
            'getData': _form.getData,
            'setData': _form.setData,
            'setReadonly': _form.setReadonly
        }
    }
});
