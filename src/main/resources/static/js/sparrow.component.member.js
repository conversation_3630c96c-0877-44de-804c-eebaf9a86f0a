Vue.component('v-members', {
    'template': '<div class="am-panel-group">\
                    <div class="am-panel am-panel-default">\
                        <div ref="tags" class="am-panel-bd">\
                        </div>\
                    </div>\
                    <div class="am-panel am-panel-default">\
                        <div class="am-panel-bd" style="display: flex;align-items: center;justify-content: space-between">\
                            <ol class="am-breadcrumb am-breadcrumb-slash am-margin-0 am-padding-0">\
                                <li>\
                                    <a @click="_queryOrganization(null);" style="cursor:pointer">\
                                        <span class="mdi-home-outline"></span>\
                                    </a>\
                                </li>\
                                <li v-for="(i, index) in deptIdArr" :class="{ \'am-active\': index === deptIdArr.length - 1 }">\
                                    <a @click="_queryOrganization(i);" style="cursor:pointer">{{deptNameArr[index]}}</a>\
                                </li>\
                            </ol>\
                            <!-- 未限制数量，支持全选 -->\
                            <template v-if="!$.isNumeric(max)">\
                                <a @click="_addAll" style="cursor:pointer">\
                                    <span class="mdi-tray-plus" style="font-size:18px"></span>\
                                </a>\
                            </template>\
                        </div>\
                        <ul class="am-list">\
                            <li v-for="i in subordinates" style="display: flex;align-items: center;justify-content: space-between">\
                                <a v-if="entity !== \'department\' || i[deptMapper.hasSubordinates]" @click="_queryOrganization(i[deptMapper.id]);" class="am-text-truncate" style="cursor:pointer">\
                                    <span class="mdi-bank-outline"></span> {{i[deptMapper.name]}}\
                                </a>\
                                <label v-else class="am-margin-bottom-0 am-padding-left-sm">\
                                    <span class="mdi-bank-outline"></span> {{i[deptMapper.name]}}\
                                </label>\
                                <a v-if="entity === \'all\' || entity === \'department\'" @click="_addDept(i);" style="cursor:pointer">\
                                    <span class="mdi-plus" style="font-size:18px"></span>\
                                </a>\
                            </li>\
                            <li v-if="entity !== \'department\'" v-for="i in users" style="display: flex;align-items: center;justify-content: space-between">\
                                <label class="am-margin-bottom-0 am-padding-sm">\
                                    <span class="mdi-account"></span> {{i[userMapper.name]}}\
                                </label>\
                                <a @click="_addUser(i);" style="cursor:pointer">\
                                    <span class="mdi-plus" style="font-size:18px"></span>\
                                </a>\
                            </li>\
                            <li v-if="subordinates.length === 0 && users.length === 0" class="am-text-center">\
                                <label class="am-padding-sm am-margin-bottom-0">暂无</label>\
                            </li>\
                        </ul>\
                        <div class="am-panel-footer">\
                            <button @click="_ok();" type="button" class="am-btn am-btn-sm am-btn-primary am-center">\
                                <span class="mdi-check"></span> 确定\
                            </button>\
                        </div>\
                    </div>\
                </div>',
    'props': ['dept', 'subordinates', 'users', 'entity', 'max', 'deptMapper', 'userMapper', 'queryOrganization', 'queryUsers', 'beforeItemAdd'],
    'computed': {
        // 导航栏键
        'deptIdArr': function () {
            return this.dept[this.deptMapper.code] ? this.dept[this.deptMapper.code].split('.') : [];
        },
        // 导航栏
        'deptNameArr': function () {
            return this.dept[this.deptMapper.fullName] ? this.dept[this.deptMapper.fullName].split('/') : [];
        }
    },
    'methods': {
        '_addDept': function (department) {
            // 如果是单选，则直接替换
            const _items = $(this.$refs.tags).tagsinput('items');
            if (this.max === 1 && _items.length > 0) {
                $(this.$refs.tags).tagsinput('remove', _items[0]);
            }

            $(this.$refs.tags).tagsinput('add', {
                'type': 'department',
                'value': department
            });
        },
        '_addUser': function (user) {
            // 如果是单选，则直接替换
            const _items = $(this.$refs.tags).tagsinput('items');
            if (this.max === 1 && _items.length > 0) {
                $(this.$refs.tags).tagsinput('remove', _items[0]);
            }

            $(this.$refs.tags).tagsinput('add', {
                'type': 'user',
                'value': user
            });
        },
        '_addAll': function () {
            const that = this;

            if ($.inArray(that.entity, ['all', 'department']) !== -1) {
                $.each(this.subordinates, function (index, val) {
                    that._addDept(val);
                });
            }

            if ($.inArray(that.entity, ['all', 'user']) !== -1) {
                $.each(this.users, function (index, val) {
                    that._addUser(val);
                });
            }
        },
        '_queryOrganization': function (deptId) {
            const that = this;

            const _val = this.queryOrganization(deptId ? deptId : null);
            if (_val instanceof Promise) {
                _val.then(data => {
                    that.__queryOrganization(data);
                });
            } else {
                this.__queryOrganization(_val);
            }
        },
        '__queryOrganization': function (data) {
            // 设置部门
            if ($.isEmptyObject(data.dept)) {
                Vue.set(this.dept, this.deptMapper.code, '');
                Vue.set(this.dept, this.deptMapper.fullName, '');
            } else {
                Vue.set(this, 'dept', data.dept);
            }

            // 设置下级部门
            Vue.set(this, 'subordinates', data.subordinates);

            // 设置成员
            Vue.set(this, 'users', data.users);
        },
        '_ok': function () {
            const _departments = [], _users = [];

            const _items = $(this.$refs.tags).tagsinput('items');
            $.each(_items, function (index, val) {
                switch (val.type) {
                    case 'department':
                        _departments.push(val.value);
                        break;
                    case 'user':
                        _users.push(val.value);
                        break;
                }
            });

            this.$emit('ok', _departments, _users);
        }
    },
    'mounted': function () {
        const that = this;

        const _bloodhound = new Bloodhound({
            'datumTokenizer': Bloodhound.tokenizers.obj.whitespace(that.deptMapper.name),
            'queryTokenizer': Bloodhound.tokenizers.whitespace,
            'remote': {
                'url': '%QUERY',
                'wildcard': '%QUERY',
                'rateLimitBy': 10000,
                'transport': function (url, options, onSuccess, onError) {
                    if (that.entity === 'department' || !$.isFunction(that.queryUsers)) {
                        return;
                    }

                    const _val = that.queryUsers(url);
                    if (_val instanceof Promise) {
                        _val.then(data => {
                            for (let i = 0; i < data.length; i++) {
                                data[i] = {
                                    'type': 'user',
                                    'value': data[i]
                                };
                            }

                            onSuccess(data);
                        });
                    } else if ($.isArray(_val)) {
                        for (let i = 0; i < _val.length; i++) {
                            _val[i] = {
                                'type': 'user',
                                'value': _val[i]
                            };
                        }

                        onSuccess(_val);
                    } else {
                        onSuccess([]);
                    }
                }
            }
        });
        _bloodhound.initialize();

        // 挂载标签
        $(that.$refs.tags).tagsinput({
            'itemText': function (item) {
                return item.value[that.userMapper.name];
            },
            'itemValue': function (item) {
                return item;
            },
            'typeaheadjs': {
                'source': _bloodhound.ttAdapter(),
                'displayKey': function (item) {
                    return JSON.stringify(item);
                },
                'templates': {
                    'suggestion': function (item) {
                        return '<p class="am-margin-sm">' + item.value[that.userMapper.name] + '/' + item.value[that.userMapper.id] + '</p>';
                    }
                }
            },
            'trimValue': true,
            'maxTags': $.isNumeric(that.max) ? that.max : undefined,
            'allowDuplicates': false
        });

        // 选择部门时禁用自动完成
        if (that.entity === 'department') {
            $(that.$el).find('.tt-input').prop('readonly', true);
        }

        $(that.$refs.tags).on('beforeItemAdd', function (event) {
            const _items = $(that.$refs.tags).tagsinput('items');

            // 去重
            switch (event.item.type) {
                case 'department':
                    for (let i = 0; i < _items.length; i++) {
                        if (_items[i].type === 'department' && _items[i].value[that.deptMapper.id] === event.item.value[that.deptMapper.id]) {
                            event.cancel = true;
                            return;
                        }
                    }
                    break;
                case 'user':
                    for (let i = 0; i < _items.length; i++) {
                        if (_items[i].type === 'user' && _items[i].value[that.userMapper.id] === event.item.value[that.userMapper.id]) {
                            event.cancel = true;
                            return;
                        }
                    }
                    break;
            }
            that.beforeItemAdd(event);
        });

        $(that.$refs.tags).next().css('width', '100%').css('margin-bottom', '0px');

        // 初始化组织架构
        that._queryOrganization(null);
    }
});

function Member() {
}

Member.contextPath = '';

Member.get = options => {
    const _options = {
        // 选择目标，可选项为all|department|user
        'entity': 'user',

        'max': undefined,

        // 人员属性映射器
        'userMapper': {
            'id': 'id',
            'name': 'name',
            'deptId': 'deptId',
            'deptName': 'deptName',
            'deptCode': 'deptCode',
            'deptFullName': 'deptFullName'
        },
        // 部门属性映射器
        'deptMapper': {
            'id': 'id',
            'name': 'name',
            'code': 'code',
            'fullName': 'fullName',
            'hasSubordinates': 'hasSubordinates'
        },
        // 读取组织架构信息，包括当前部门、下级部门、人员等数据
        'queryOrganization': deptId => {
            return new Promise(resolve => {
                new HttpRequest({
                    'baseURL': Member.contextPath
                }).ajax('/sys/dept/organization', {
                    'id': deptId
                }, {
                    'msg': {
                        'success': false
                    }
                }).then(result => {
                    let data;

                    if (result.code === 'OK') {
                        data = {
                            'dept': result.data.dept,
                            'subordinates': result.data.subordinates,
                            'users': result.data.members
                        };
                    } else {
                        data = {
                            'dept': {},
                            'subordinates': [],
                            'users': []
                        };
                    }

                    resolve(data);
                });
            });
        },
        // 读取人员
        'queryUsers': key => {
            return new Promise(resolve => {
                new HttpRequest({
                    'baseURL': Member.contextPath
                }).ajax('/sys/user/suggest', {
                    'count': 5,
                    'key': key
                }, {
                    'msg': {
                        'success': false
                    }
                }).then(result => {
                    resolve(result.code === 'OK' ? result.data : []);
                });
            });
        },
        // 确认回调，返回true则关闭当前窗口
        'ok': (departments, users) => {
            return new Promise(resolve => {
                console.log(departments);
                console.log(users);

                resolve(true);
            });
        },
        'beforeItemAdd': event => {
            event.cancel = false;
        }
    };

    $.extend(true, _options, options);

    const _obj = $('<v-members :dept="dept" :subordinates="subordinates" :users="users" :entity="entity" :max="max" :dept-mapper="deptMapper" :user-mapper="userMapper" :query-organization="queryOrganization" :query-users="queryUsers" :before-item-add="beforeItemAdd" @ok="ok" class="am-margin"></v-members>');
    $('body').append(_obj);

    let _width = $(window.top).width();
    if (_width < 641) {
        _width = '90%';
    } else if (_width < 1025) {
        _width = '60%';
    } else {
        _width = '30%';
    }

    // 设置窗口索引
    const _modal = ModalUtil.open({
        'type': 1,
        'title': false,
        'area': [_width, '90%'],
        'content': _obj,
        'end': function () {
            _member.$destroy();

            $(_member.$el).remove();
        }
    });

    const _dept = {};
    _dept[_options.deptMapper.code] = '';
    _dept[_options.deptMapper.fullName] = '';

    const _member = new Vue({
        'el': _obj[0],
        'data': {
            'dept': _dept,
            'subordinates': [],
            'users': [],
            'entity': _options.entity,
            'max': _options.max,
            'deptMapper': _options.deptMapper,
            'userMapper': _options.userMapper,
            'queryOrganization': _options.queryOrganization,
            'queryUsers': _options.queryUsers,
            'beforeItemAdd': _options.beforeItemAdd
        },
        'methods': {
            'ok': function (departments, users) {
                if (!$.isFunction(_options.ok)) {
                    return;
                }

                const _val = _options.ok(departments, users);
                if (_val instanceof Promise) {
                    _val.then(success => {
                        if (success === true) {
                            ModalUtil.close(_modal);
                        }
                    });
                } else if (_val === true) {
                    ModalUtil.close(_modal);
                }
            }
        }
    });
};