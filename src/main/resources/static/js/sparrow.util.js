// 时间 ----------------------------------------------------------------------------------------------------------------
Date.today = function () {
    const _date = new Date();
    return new Date(_date.getFullYear(), _date.getMonth(), _date.getDate());
};

Date.lastMonday = function (date) {
    const _date = date instanceof Date ? new Date(date.getFullYear(), date.getMonth(), date.getDate()) : new Date();
    _date.setDate(_date.getDate() - _date.getDay() + 1);
    return new Date(_date.getFullYear(), _date.getMonth(), _date.getDate());
};

Date.lastWeekdays = function () {
    return Date.lastNDays(7);
}

Date.thisWeekdays = function () {
    let _date = new Date();
    const _weekday = _date.getDay() - 1;
    _date = _date.addDays(_weekday * -1);

    const _array = [];
    for (let i = 0; i < 7; i++) {
        _date = i === 0 ? _date : _date.addDays(1);
        _array.push(_date.format('yyyy-MM-dd'));
    }
    return _array;
}

Date.lastNDays = function (num) {
    const _array = [];
    for (let i = 0, date = new Date().addDays(-num); i < num; i++) {
        date = date.addDays(1);
        _array.push(date.format('yyyy-MM-dd'));
    }

    return _array;
}

Date.prototype.format = function (str) {
    const _pattens = {
        'M+': this.getMonth() + 1, //月份
        'd+': this.getDate(), //日
        'h+': this.getHours() % 12 === 0 ? 12 : this.getHours() % 12, //小时
        'H+': this.getHours(), //小时
        'm+': this.getMinutes(), //分
        's+': this.getSeconds(), //秒
        'q+': Math.floor((this.getMonth() + 3) / 3), //季度
        'S': this.getMilliseconds() //毫秒
    };

    if (/(y+)/.test(str)) {
        str = str.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
    }

    for (const i in _pattens) {
        if (new RegExp("(" + i + ")").test(str)) {
            str = str.replace(RegExp.$1, (RegExp.$1.length === 1) ? (_pattens[i]) : (('00' + _pattens[i]).substr(('' + _pattens[i]).length)));
        }
    }

    return str;
};

Date.prototype.addYears = function (num) {
    const _date = new Date();
    _date.setTime(this.getTime());
    _date.setFullYear(_date.getFullYear() + num);
    return _date;
};

Date.prototype.addDays = function (num) {
    const _date = new Date();
    _date.setTime(this.getTime());
    _date.setDate(_date.getDate() + num);
    return _date;
};

Date.prototype.addHours = function (num) {
    const _date = new Date();
    _date.setTime(this.getTime());
    _date.setHours(_date.getHours() + num);
    return _date;
};

Date.prototype.addMinutes = function (num) {
    const _date = new Date();
    _date.setTime(this.getTime());
    _date.setMinutes(_date.getMinutes() + num);
    return _date;
};

Date.prototype.addSeconds = function (num) {
    const _date = new Date();
    _date.setTime(this.getTime());
    _date.setSeconds(_date.getSeconds() + num);
    return _date;
};

Date.prototype.addMilliseconds = function (num) {
    const _date = new Date();
    _date.setTime(this.getTime());
    _date.setMilliseconds(_date.getMilliseconds() + num);
    return _date;
};

Date.prototype.diff = function (time) {
    const _diff = this - new Date(time).getTime();

    return {
        'days': parseInt(_diff / (24 * 60 * 60 * 1000)),
        'hours': parseInt(_diff % (24 * 60 * 60 * 1000) / (60 * 60 * 1000)),
        'minutes': parseInt(_diff % (60 * 60 * 1000) / (60 * 1000))
    };
};

// 数学计算 ------------------------------------------------------------------------------------------------------------
Math.add = function (num1, num2) {
    let _r1, _r2, _m;

    let _array = num1.toString().split('.');
    _r1 = _array.length > 1 ? _array[1].length : 0;

    _array = num2.toString().split('.');
    _r2 = _array.length > 1 ? _array[1].length : 0;

    _m = Math.pow(10, Math.max(_r1, _r2));
    return Math.round(num1 * _m + num2 * _m) / _m;
};

Math.multiple = function (num1, num2) {
    const _s1 = num1.toString(), _s2 = num2.toString();
    let _m = 0;

    let _array = _s1.split('.');
    if (_array.length > 1) {
        _m += _array[1].length;
    }

    _array = _s2.split('.');
    if (_array.length > 1) {
        _m += _array[1].length;
    }

    return Number(_s1.replace('.', '')) * Number(_s2.replace('.', '')) / Math.pow(10, _m)
};

// ---------------------------------------------------------------------------------------------------------------------
Location.prototype.querystring = function (key) {
    const _reg = new RegExp('(^|&)' + key + '=([^&]*)(&|$)', 'i');
    const _match = this.search.substr(1).match(_reg);
    return _match == null ? null : decodeURIComponent(_match[2]);
};

// 编码器 --------------------------------------------------------------------------------------------------------------
function EncodeUtil() {
}

EncodeUtil.characterEntity = function (str) {
    const _mapper = {
        '<': '&lt;',
        '>': '&gt;',
        '&': '&amp;',
        '"': '&quot;'
    };

    return str == null ? null : str.replace(/[<>&"]/g, function (val) {
        return _mapper[val];
    });
};

// 解码器 --------------------------------------------------------------------------------------------------------------
function DecodeUtil() {
}

DecodeUtil.characterEntity = function (str) {
    const _mapper = {
        'lt': '<',
        'gt': '>',
        'nbsp': ' ',
        'amp': '&',
        'quot': '"'
    };

    return str == null ? null : str.replace(/&(lt|gt|nbsp|amp|quot);/ig, function (val, i) {
        return _mapper[i];
    });
};

// 加解密 --------------------------------------------------------------------------------------------------------------
function CryptoUtil() {
}

// 省eip使用的加密
CryptoUtil.eip = function (str) {
    setMaxDigits(131);
    const _key = new RSAKeyPair('10001', '', 'a15667bb6466b8f0b2f15443e8da36d06060c7c705f00212514b3b4611cea268dba0f9b65d34ad2f51920c5ed6e29670a6354266b3622475435b50346366e1f41b08f82c7f2e25e35c9854fb69324c8b62ae8c416d625b42175798bff5c6ae78a722eb4e021130bf579bdbccb7bd832e925c072f0ea6485943a54ea723d41afd');
    return encryptedString(_key, encodeURIComponent(str));
};

// RSA
CryptoUtil.encryptRsa = function (str, publicKeyCredential) {
    const _encrypt = new JSEncrypt();
    _encrypt.setPublicKey(publicKeyCredential);

    return _encrypt.encrypt(str);
};

CryptoUtil.md5 = function (str) {
    return $.md5(str);
};

// 对话框 --------------------------------------------------------------------------------------------------------------
function ModalUtil() {
}

ModalUtil.open = function (options) {
    const _margin = 112;
    const _maxHeight = $(window.top).height() - _margin;
    const _width = $(window.top).width();

    const _area = [_width * 0.6 + 'px', _maxHeight + 'px'];
    // 小屏，水平铺满
    if (_width < 641) {
        _area[0] = '100%';
    }
    // 非iframe层，自适应高度
    else if (options.type !== 2) {
        _area[1] = 'auto';
    }

    const _options = {
        'anim': _width < 641 ? 'slideUp' : 0,
        'area': _area,
        'maxHeight': _maxHeight,
        'maxmin': true,
        'offset': _width < 641 ? _margin + 'px' : 'auto',
        'shadeClose': _width < 641,
        'scrollbar': false
    };
    $.extend(_options, options);

    return layer.open(_options);
}

ModalUtil.alert = function (msg, options, callback) {
    if (typeof msg == 'string') {
        const reg = new RegExp('\n', 'g');
        msg = msg.replace(reg, '<br/>');
    }

    if (msg == null || msg === '') {
        msg = 'message missing';
    }

    if ($.isEmptyObject(options)) {
        options = {
            'title': false,
            'icon': 2
        }
    }

    return layer.alert(msg, options, callback);
};

ModalUtil.confirm = layer.confirm;

ModalUtil.prompt = layer.prompt;

ModalUtil.msg = function (msg, options, callback) {
    if (typeof msg == 'string') {
        const reg = new RegExp('\n', "g");
        msg = msg.replace(reg, '<br/>');
    }

    if (!msg) {
        msg = 'message missing';
    }

    if ($.isEmptyObject(options)) {
        options = {
            'title': false,
            'time': 3000
        }
    }

    return layer.msg(msg, options, callback);
};

ModalUtil.load = function () {
    return layer.load(2, {
        'shade': 0.3
    });
};

ModalUtil.popover = layer.tips;

ModalUtil.close = function (index) {
    layer.close(index);
};

// 文件 ----------------------------------------------------------------------------------------------------------------
function FileUtil() {
}

// 文件转base64
FileUtil.readFilesToBase64String = function (files) {
    return new Promise(resolve => {
        const _items = [];
        let _count = 0;

        $.each(files, function (i, file) {
            const _reader = new FileReader();
            _reader.readAsDataURL(file);
            _reader.onload = function (e) {
                _items.push({
                    'name': file.name,
                    'base64': this.result
                });

                _count++;

                if (_count === files.length) {
                    resolve(_items);
                }
            }
        });
    });
};

FileUtil.downloadFromBase64 = function (name, mime, base64) {
    const _atob = atob(base64);
    let _length = _atob.length;
    const _array = new Uint8Array(_length);
    while (_length--) {
        _array[_length] = _atob.charCodeAt(_length);
    }

    const _blob = new Blob([_array], {
        'type': mime
    });
    const _url = URL.createObjectURL(_blob);

    const _el = document.createElement("a");
    _el.setAttribute("href", _url);
    _el.setAttribute("download", name);
    _el.setAttribute("target", "_blank");

    const _event = document.createEvent("MouseEvents");
    _event.initEvent('click', true, true);

    _el.dispatchEvent(_event);
};

FileUtil.copyUrlToFile = function (url, name) {
    const _xhr = new XMLHttpRequest();
    _xhr.responseType = 'blob';
    _xhr.onload = function (e) {
        if (this.status === 200) {
            const _blob = this.response;
            _blob.type = 'application/octet-stream';

            FileUtil.downloadBlob(_blob, name);
        }
    };

    _xhr.open('GET', url, true);
    _xhr.send();
};

FileUtil.copyInputStreamToFile = function (stream, name) {
    const _blob = new Blob([stream], {
        'type': 'application/octet-stream'
    });

    FileUtil.downloadBlob(_blob, name);
};

FileUtil.downloadBlob = function (blob, name) {
    const _a = document.createElement('a');
    _a.href = URL.createObjectURL(blob);
    _a.download = name;
    _a.click();
};

// 判断文件后缀是否为图片
FileUtil.isImage = function (fileName) {
    if (!fileName) {
        return false;
    }

    const _index = fileName.lastIndexOf(".");
    const _ext = fileName.substr(_index + 1).toLowerCase();
    return ['bmp', 'jpg', 'png', 'tif', 'gif', 'pcx', 'tga', 'exif', 'fpx', 'svg', 'psd', 'cdr', 'pcd', 'dxf'].indexOf(_ext) !== -1;
};

// 缩放图片
FileUtil.zoomImage = function (file, options) {
    return new Promise(resolve => {
        const _options = {
            'max': 1080,
            'format': 'image/jpeg'
        };
        $.extend(true, _options, options);

        const _canvas = document.createElement('canvas');
        const _ctx = _canvas.getContext('2d');

        const _img = new Image();
        _img.onload = function () {
            let w = this.naturalWidth, h = this.naturalHeight;

            // 缩放图片
            if ($.isNumeric(_options.max)) {
                if (w !== h * 2 && h !== w * 2 && (w > _options.max || h > _options.max)) { // 非全景图，并且超高
                    if (w > h) {
                        const _zoom = w / _options.max;
                        w = _options.max;
                        h = h / _zoom;
                    } else {
                        const _zoom = h / _options.max;
                        h = _options.max;
                        w = w / _zoom;
                    }
                }
            }

            _canvas.width = w;
            _canvas.height = h;

            // 摆正图片
            let _orientation = null;
            EXIF.getData(file, function () {
                _orientation = EXIF.getTag(this, 'Orientation');
            });

            let x = 0, y = 0;
            if (_orientation !== '' && _orientation !== 1) {
                let _rotateTimes;
                switch (_orientation) {
                    case 3: // 旋转180
                        _rotateTimes = 2;
                        break;
                    case 6: // 顺时针旋转90
                        _rotateTimes = 1;
                        break;
                    case 8: // 逆时针旋转90
                        _rotateTimes = 3;
                        break;
                }

                w = _canvas.width;
                h = _canvas.height;
                switch (_rotateTimes) {
                    case 1:
                        _canvas.width = h;
                        _canvas.height = w;
                        y = -h;
                        break;
                    case 2:
                        x = -w;
                        y = -h;
                        break;
                    case 3:
                        _canvas.width = h;
                        _canvas.height = w;
                        x = -w;
                        break;
                }
                const _degree = Math.PI / 180 * 90 * _rotateTimes;
                _ctx.rotate(_degree);
            }

            _ctx.clearRect(0, 0, _canvas.width, _canvas.height);
            _ctx.drawImage(_img, x, y, _canvas.width, _canvas.height);

            resolve(_canvas.toDataURL(_options.format, 100));
        };

        const _reader = new FileReader();
        _reader.readAsDataURL(file);
        _reader.onload = function (e) {
            _img.src = this.result;
        };
    });
};