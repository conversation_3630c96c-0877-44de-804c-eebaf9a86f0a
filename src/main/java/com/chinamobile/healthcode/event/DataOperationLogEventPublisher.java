package com.chinamobile.healthcode.event;

import com.chinamobile.healthcode.model.DataOperationLogEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 数据操作日志事件发布器
 * 用于发布数据操作日志事件
 */
@Component
public class DataOperationLogEventPublisher {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 发布单个操作日志事件
     */
    public void publishLogEvent(Object source, DataOperationLogEntity logEntity) {
        DataOperationLogEvent event = new DataOperationLogEvent(source, logEntity);
        eventPublisher.publishEvent(event);
    }

    /**
     * 发布批量操作日志事件
     */
    public void publishBatchLogEvent(Object source, DataOperationLogEntity batchLogEntity) {
        DataOperationLogEvent event = new DataOperationLogEvent(source, batchLogEntity);
        eventPublisher.publishEvent(event);
    }

    /**
     * 发布错误操作日志事件
     */
    public void publishErrorLogEvent(Object source, DataOperationLogEntity errorLogEntity) {
        DataOperationLogEvent event = new DataOperationLogEvent(source, errorLogEntity);
        eventPublisher.publishEvent(event);
    }
}
