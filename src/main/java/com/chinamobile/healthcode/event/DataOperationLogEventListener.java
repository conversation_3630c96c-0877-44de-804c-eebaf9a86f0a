package com.chinamobile.healthcode.event;

import com.chinamobile.healthcode.model.DataOperationLogEntity;
import com.chinamobile.healthcode.repository.DataOperationLogRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 数据操作日志事件监听器
 * 异步处理数据操作日志事件
 */
@Component
public class DataOperationLogEventListener {

    private static final Logger logger = LoggerFactory.getLogger(DataOperationLogEventListener.class);

    @Autowired
    private DataOperationLogRepository dataOperationLogRepository;

    /**
     * 处理数据操作日志事件
     */
    @EventListener
    @Async
    public void handleDataOperationLogEvent(DataOperationLogEvent event) {
        try {
            DataOperationLogEntity logEntity = event.getLogEntity();
            logger.debug("处理数据操作日志事件: {}", logEntity.getDescription());
            
            dataOperationLogRepository.saveLogAsync(logEntity);
            
            logger.debug("数据操作日志保存成功: {}", logEntity.getId());
        } catch (Exception e) {
            logger.error("保存数据操作日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理批量操作日志事件
     */
    @EventListener
    @Async
    public void handleBatchLogEvent(DataOperationLogEvent event) {
        try {
            DataOperationLogEntity logEntity = event.getLogEntity();
            logger.debug("处理批量操作日志事件: {}", logEntity.getDescription());
            
            dataOperationLogRepository.saveLogAsync(logEntity);
            
            logger.debug("批量操作日志保存成功: {}", logEntity.getId());
        } catch (Exception e) {
            logger.error("保存批量操作日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理错误操作日志事件
     */
    @EventListener
    @Async
    public void handleErrorLogEvent(DataOperationLogEvent event) {
        try {
            DataOperationLogEntity logEntity = event.getLogEntity();
            logger.debug("处理错误操作日志事件: {}", logEntity.getDescription());
            
            dataOperationLogRepository.saveLogAsync(logEntity);
            
            logger.debug("错误操作日志保存成功: {}", logEntity.getId());
        } catch (Exception e) {
            logger.error("保存错误操作日志失败: {}", e.getMessage(), e);
        }
    }
}
