package com.chinamobile.healthcode.event;

import com.chinamobile.healthcode.model.DataOperationLogEntity;
import org.springframework.context.ApplicationEvent;

/**
 * 数据操作日志事件
 * 用于事件驱动方式记录日志
 */
public class DataOperationLogEvent extends ApplicationEvent {
    
    private final DataOperationLogEntity logEntity;
    
    public DataOperationLogEvent(Object source, DataOperationLogEntity logEntity) {
        super(source);
        this.logEntity = logEntity;
    }
    
    public DataOperationLogEntity getLogEntity() {
        return logEntity;
    }
    
    @Override
    public String toString() {
        return "DataOperationLogEvent{" +
                "logEntity=" + logEntity +
                ", source=" + source +
                '}';
    }
}
