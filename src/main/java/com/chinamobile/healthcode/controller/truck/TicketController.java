package com.chinamobile.healthcode.controller.truck;

import com.chinamobile.healthcode.model.FormTrace;
import com.chinamobile.healthcode.model.truck.TruckEnterRegistrationTicket;
import com.chinamobile.healthcode.repository.FormTraceRepository;
import com.chinamobile.healthcode.repository.truck.TicketRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

import static com.chinamobile.healthcode.repository.truck.TicketRepository.ADMIN_SENIOR_ROLE;

@Controller
@RequestMapping(value = "truck/ticket")
public class TicketController {

    final TicketRepository ticketRepository;
    final FormTraceRepository formTraceRepository;
    final DefaultRoleRepository roleRepository;
    final LoginUtil loginUtil;

    public TicketController(TicketRepository ticketRepository, FormTraceRepository formTraceRepository, DefaultRoleRepository roleRepository, LoginUtil loginUtil) {
        this.ticketRepository = ticketRepository;
        this.formTraceRepository = formTraceRepository;
        this.roleRepository = roleRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "truck:ticket:index")
    public String index(@RequestParam(value = "id", required = false) String id) {
        return StringUtils.isEmpty(id) ? "truck/tickets" : "truck/ticket";
    }

    @GetMapping(value = "/passed")
    @RequiresPermissions(value = "truck:ticket:passed")
    public String index() {
        return "truck/tickets-passed";
    }

    @PostMapping(value = "/get/detail")
    @ResponseBody
    public Result<TruckEnterRegistrationTicket> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<TruckEnterRegistrationTicket> _item = ticketRepository.getWithPassengersAndEditable(_id, loginUtil.getUser());
        if (!_item.isOK()) {
            return _item;
        }

        // 脱敏
        if (!roleRepository.isUserInRoleCached(loginUtil.getUserId(), ADMIN_SENIOR_ROLE) && !Objects.equals(loginUtil.getUserId(), _item.data.getCreatorId())) {
            if (_item.data.getDriverIdentityCardNumber() != null && _item.data.getDriverIdentityCardNumber().length() > 16) {
                _item.data.setDriverIdentityCardNumber(_item.data.getDriverIdentityCardNumber().substring(0, 2) + "**************" + _item.data.getDriverIdentityCardNumber().substring(16));
            }
        }

        return _item;
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    public Result<PagingItems<TruckEnterRegistrationTicket>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<SortField> _sortFields = (List<SortField>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new com.google.gson.reflect.TypeToken<List<SortField>>() {
                }.getType())).orElse(null);
        String _driverName = Optional.ofNullable(data.get("driverName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _driverMp = Optional.ofNullable(data.get("driverMp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _licensePlateNumber = Optional.ofNullable(data.get("licensePlateNumber"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _departure = Optional.ofNullable(data.get("departure"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _originRegionFullName = Optional.ofNullable(data.get("originRegionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _originCompanyName = Optional.ofNullable(data.get("originCompanyName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _originContactName = Optional.ofNullable(data.get("originContactName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _examinerAddress = Optional.ofNullable(data.get("examinerAddress"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        TruckEnterRegistrationTicket.ENUM_STATUS _state = Optional.ofNullable(data.get("state"))
                .map(i -> i.isJsonNull() ? null : TruckEnterRegistrationTicket.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);
        Date _departureBeginTime = Optional.ofNullable(data.get("departureBeginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _departureEndTime = Optional.ofNullable(data.get("departureEndTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);

        Result<PagingItems<TruckEnterRegistrationTicket>> _page = new Result<>();
        _page.data = ticketRepository.fuzzy(_count, _index, _sortFields, _driverName, _driverMp, _licensePlateNumber, _departure, _originRegionFullName, _originCompanyName, _originContactName, _examinerAddress, _state, _departureBeginTime, _departureEndTime, _beginTime, _endTime, loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/fuzzy/my")
    @ResponseBody
    public Result<PagingItems<TruckEnterRegistrationTicket>> queryByUser(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();

        Result<PagingItems<TruckEnterRegistrationTicket>> _page = new Result<>();
        _page.data = ticketRepository.queryByUser(_count, _index, loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/fuzzy/relative")
    @ResponseBody
    public Result<PagingItems<TruckEnterRegistrationTicket>> fuzzyRelative(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();

        Result<PagingItems<TruckEnterRegistrationTicket>> _page = new Result<>();
        _page.data = ticketRepository.fuzzyRelative(_count, _index, loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/export/base64")
    @ResponseBody
    public Result<String> export(@RequestBody JsonObject data) throws IOException {
        String _driverName = Optional.ofNullable(data.get("driverName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _driverMp = Optional.ofNullable(data.get("driverMp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _licensePlateNumber = Optional.ofNullable(data.get("licensePlateNumber"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _departure = Optional.ofNullable(data.get("departure"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _originRegionFullName = Optional.ofNullable(data.get("originRegionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _originCompanyName = Optional.ofNullable(data.get("originCompanyName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _originContactName = Optional.ofNullable(data.get("originContactName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _examinerAddress = Optional.ofNullable(data.get("examinerAddress"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        TruckEnterRegistrationTicket.ENUM_STATUS _state = Optional.ofNullable(data.get("state"))
                .map(i -> i.isJsonNull() ? null : TruckEnterRegistrationTicket.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);
        Date _departureBeginTime = Optional.ofNullable(data.get("departureBeginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _departureEndTime = Optional.ofNullable(data.get("departureEndTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);

        Result<String> _base64 = new Result<>();
        _base64.data = ticketRepository.export(_driverName, _driverMp, _licensePlateNumber, _departure, _originRegionFullName, _originCompanyName, _originContactName, _examinerAddress, _state, _departureBeginTime, _departureEndTime, _beginTime, _endTime, loginUtil.getUser());
        return _base64;
    }

    @PostMapping(value = "/operate")
    @ResponseBody
    public Result<List<String>> operate() {
        Result<List<String>> _operates = new Result<>();
        _operates.data = new ArrayList<>();

        if (roleRepository.isUserInRoleCached(loginUtil.getUserId(), TicketRepository.ZONE_DIRECTOR_ROLE)) {
            _operates.data.add("approve");
            _operates.data.add("refuse-by-zone");
        }

        if (roleRepository.isUserInRoleCached(loginUtil.getUserId(), TicketRepository.SUBDISTRICT_STAFF_ROLE)) {
            _operates.data.add("dispatch");
            _operates.data.add("refuse-by-subdistrict");
            _operates.data.add("close");
        }

        if (roleRepository.isUserInRoleCached(loginUtil.getUserId(), TicketRepository.COMMUNITY_STAFF_ROLE)) {
            _operates.data.add("reject");
            _operates.data.add("close");
        }

        return _operates;
    }

    @PostMapping("/submit")
    @ResponseBody
    public Result submit(@RequestBody TicketRepository.TruckEnterRegistrationTicketDto dto) {
        return ticketRepository.submit(dto, loginUtil.getUser());
    }

    @PostMapping("/update")
    @ResponseBody
    public Result update(@RequestBody TicketRepository.TruckEnterRegistrationTicketDto dto) {
        return ticketRepository.update(dto, loginUtil.getUser());
    }

    /**
     * 园区确认
     *
     * @param data
     * @return
     */
    @PostMapping(value = "/approve")
    @ResponseBody
    @RequiresPermissions(value = "truck:ticket:approve")
    public Result approve(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _opinion = Optional.ofNullable(data.get("opinion"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        return ticketRepository.approve(_id, _opinion, loginUtil.getUser());
    }

    /**
     * 园区+街道退回
     *
     * @param data
     * @return
     */
    @PostMapping(value = "/refuse")
    @ResponseBody
    @RequiresPermissions(value = "truck:ticket:refuse")
    public Result refuse(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _opinion = data.get("opinion").getAsString();

        return ticketRepository.refuse(_id, _opinion, loginUtil.getUser());
    }

    /**
     * 街道指派
     *
     * @param data
     * @return
     */
    @PostMapping(value = "/dispatch")
    @ResponseBody
    @RequiresPermissions(value = "truck:ticket:dispatch")
    public Result dispatch(@RequestBody JsonObject data) {
        List<String> _ids = ConverterUtil.json2Object(data.get("ids").toString(), new TypeToken<List<String>>() {
        }.getType());
        String _deptId = data.get("deptId").getAsString();
        String _deptFullName = data.get("deptFullName").getAsString();
        String _opinion = Optional.ofNullable(data.get("opinion"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        return ticketRepository.dispatch(_ids, _deptId, _deptFullName, _opinion, loginUtil.getUser());
    }

    /**
     * 社区退回
     *
     * @param data
     * @return
     */
    @PostMapping(value = "/reject")
    @ResponseBody
    @RequiresPermissions(value = "truck:ticket:reject")
    public Result reject(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _opinion = data.get("opinion").getAsString();

        return ticketRepository.reject(_id, _opinion, loginUtil.getUser());
    }

    @PostMapping(value = "/close")
    @ResponseBody
    @RequiresPermissions(value = "truck:ticket:close")
    public Result close(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _opinion = data.get("opinion").getAsString();

        return ticketRepository.close(_id, _opinion, loginUtil.getUser());
    }

    @PostMapping("/force-pass")
    @ResponseBody
    @RequiresPermissions(value = "truck:ticket:force-pass")
    public Result forcePass(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return ticketRepository.forcePass(_id, loginUtil.getUser());
    }

    @PostMapping("/scan")
    @ResponseBody
    @RequiresPermissions(value = "truck:ticket:scan")
    public Result<TruckEnterRegistrationTicket> scan(@RequestParam String id) {
        User _user = loginUtil.getUser();
        formTraceRepository.add(id, FormTrace.ENUM_FORM_TYPE.货运司机报备, "扫码", null, _user.getAddress(), _user.getId());

        return ticketRepository.getWithPassengersAndScanable(id, loginUtil.getUser());
    }

}