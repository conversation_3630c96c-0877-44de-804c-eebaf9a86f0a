package com.chinamobile.healthcode.controller.migrant;

import com.chinamobile.healthcode.repository.migrant.StatisticService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import org.jinq.tuples.Pair;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Controller(value = "migrantStatisticController")
@RequestMapping(value = "migrant/statistic")
public class StatisticController {

    final StatisticService statisticService;
    final LoginUtil loginUtil;

    public StatisticController(StatisticService statisticService, LoginUtil loginUtil) {
        this.statisticService = statisticService;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/dnf/all")
    @ResponseBody
    public Result<List<Pair<String, Long>>> dnfAll(@RequestBody JsonObject data) {
        Result<List<Pair<String, Long>>> _counts = new Result<>();
        _counts.data = dnf(data, null);
        return _counts;
    }

    @PostMapping(value = "/dnf/permit")
    @ResponseBody
    public Result<List<Pair<String, Long>>> dnf(@RequestBody JsonObject data) {
        Result<List<Pair<String, Long>>> _counts = new Result<>();
        _counts.data = dnf(data, loginUtil.getUser());
        return _counts;
    }

    @PostMapping(value = "/process")
    @ResponseBody
    public Result<List<Pair<String, List<Pair<String, Long>>>>> process(@RequestBody JsonObject data) {
        Date _begin = Optional.ofNullable(data.get("begin"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        _begin = _begin == null ? DateUtil.addDays(DateUtil.getDate(new Date()), -6) : DateUtil.getDate(_begin);

        Date _end = Optional.ofNullable(data.get("end"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        if (_end == null) {
            _end = DateUtil.addDays(new Date(), 1);
        }
        _end = DateUtil.getDate(_end);

        Result<List<Pair<String, List<Pair<String, Long>>>>> _counts = new Result<>();
        _counts.data = statisticService.process(_begin, _end, loginUtil.getUser());
        return _counts;
    }

    List<Pair<String, Long>> dnf(JsonObject data, User user) {
        Date _begin = Optional.ofNullable(data.get("begin"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        _begin = _begin == null ? DateUtil.addDays(DateUtil.getDate(new Date()), -6) : DateUtil.getDate(_begin);

        Date _end = Optional.ofNullable(data.get("end"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        if (_end == null) {
            _end = DateUtil.addDays(new Date(), 1);
        }
        _end = DateUtil.getDate(_end);

        return statisticService.dnf(_begin, _end, user);
    }

}