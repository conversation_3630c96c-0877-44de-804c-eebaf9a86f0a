package com.chinamobile.healthcode.controller.migrant;

import com.chinamobile.healthcode.model.migrant.MigrantForm;
import com.chinamobile.healthcode.repository.migrant.FormRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.NotLog;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

@Controller(value = "migrantFormController")
@RequestMapping(value = "migrant/visit-form")
public class FormController {

    final FormRepository formRepository;
    final DepartmentRepository departmentRepository;
    final DefaultRoleRepository roleRepository;
    final LoginUtil loginUtil;

    public FormController(FormRepository formRepository, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository, LoginUtil loginUtil) {
        this.formRepository = formRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "migrant:visit-form:index")
    public String index(@RequestParam(value = "id", required = false) String id) {
        return StringUtils.isEmpty(id) ? "migrant/forms" : "migrant/form";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    public Result<MigrantForm> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<MigrantForm> _item = formRepository.get(_id);
        if (!_item.isOK()) {
            return _item;
        }

        // 脱敏
        if (!roleRepository.isUserInRoleCached(loginUtil.getUserId(), FormRepository.ADMIN_ROLE) && !Objects.equals(loginUtil.getUserId(), _item.data.getCreatorId())) {
            if (_item.data.getCredentialNo() != null && _item.data.getCredentialNo().length() > 16) {
                _item.data.setCredentialNo(_item.data.getCredentialNo().substring(0, 2) + "**************" + _item.data.getCredentialNo().substring(16));
            }
        }

        return _item;
    }

    @PostMapping(value = "/add")
    @ResponseBody
    public Result<String> add(@RequestBody JsonObject data) {
        MigrantForm _item = ConverterUtil.json2Object(data.get("item").toString(), MigrantForm.class);

        return formRepository.add(_item, loginUtil.getUserId());
    }

    @PostMapping(value = "/update")
    @ResponseBody
    public Result<String> update(@RequestBody JsonObject data) {
        MigrantForm _item = ConverterUtil.json2Object(data.get("item").toString(), MigrantForm.class);

        return formRepository.update(_item, loginUtil.getUserId());
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    public Result<PagingItems<MigrantForm>> mime(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);

        Result<PagingItems<MigrantForm>> _page = new Result<>();
        _page.data = formRepository.queryByUserId(_count, _index, _mp, _beginTime, _endTime, loginUtil.getUserId());
        return _page;
    }

    @PostMapping(value = "/fuzzy/all")
    @ResponseBody
    public Result<PagingItems<MigrantForm>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<SortField> _sortFields = (List<SortField>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new com.google.gson.reflect.TypeToken<List<SortField>>() {
                }.getType())).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _credentialNo = Optional.ofNullable(data.get("credentialNo"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _destinationRegionFullName = Optional.ofNullable(data.get("destinationRegionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        MigrantForm.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : MigrantForm.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);
        Date _departureBeginTime = Optional.ofNullable(data.get("departureBeginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _departureEndTime = Optional.ofNullable(data.get("departureEndTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);

        Result<PagingItems<MigrantForm>> _page = new Result<>();
        _page.data = formRepository.fuzzy(_count, _index, _sortFields, _name, _credentialNo, _mp, _destinationRegionFullName, _status, _beginTime, _endTime, _departureBeginTime, _departureEndTime, loginUtil.getUser());

        // 脱敏
        if (!roleRepository.isUserInRoleCached(loginUtil.getUserId(), FormRepository.ADMIN_ROLE)) {
            for (MigrantForm i : _page.data.items) {
                if (i.getCredentialNo() != null && i.getCredentialNo().length() > 16) {
                    i.setCredentialNo(i.getCredentialNo().substring(0, 2) + "**************" + i.getCredentialNo().substring(16));
                }
            }
        }

        return _page;
    }

    @PostMapping(value = "/export/base64")
    @ResponseBody
    public Result<String> export(@RequestBody JsonObject data) throws IOException {
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _credentialNo = Optional.ofNullable(data.get("credentialNo"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _destinationRegionFullName = Optional.ofNullable(data.get("destinationRegionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        MigrantForm.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : MigrantForm.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);
        Date _departureBeginTime = Optional.ofNullable(data.get("departureBeginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _departureEndTime = Optional.ofNullable(data.get("departureEndTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);

        Result<String> _base64 = new Result<>();
        _base64.data = formRepository.export(_name, _credentialNo, _mp, _destinationRegionFullName, _status, _departureBeginTime, _departureEndTime, _beginTime, _endTime, loginUtil.getUser());
        return _base64;
    }

    @PostMapping(value = "/operate")
    @ResponseBody
    public Result<List<String>> operate() {
        Result<List<String>> _operates = new Result<>();
        _operates.data = new ArrayList<>();

        if (roleRepository.isUserInRoleCached(loginUtil.getUserId(), FormRepository.SUBDISTRICT_STAFF_ROLE)) {
            _operates.data.add("dispatch");
            _operates.data.add("refuse");
            _operates.data.add("close-by-subdistrict");
        }

        if (roleRepository.isUserInRoleCached(loginUtil.getUserId(), FormRepository.COMMUNITY_STAFF_ROLE)) {
            _operates.data.add("reject");
            //_operates.data.add("receive");
            _operates.data.add("close-by-community");
        }

        return _operates;
    }

    @PostMapping(value = "/dispatch")
    @ResponseBody
    @RequiresPermissions(value = "migrant:visit-form:dispatch")
    public Result dispatch(@RequestBody JsonObject data) {
        List<String> _ids = ConverterUtil.json2Object(data.get("ids").toString(), new TypeToken<List<String>>() {
        }.getType());
        String _deptId = data.get("deptId").getAsString();
        String _deptFullName = data.get("deptFullName").getAsString();
        String _opinion = Optional.ofNullable(data.get("opinion"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        return formRepository.dispatch(_ids, _deptId, _deptFullName, _opinion, loginUtil.getUserId());
    }

    @PostMapping(value = "/refuse")
    @ResponseBody
    @RequiresPermissions(value = "migrant:visit-form:refuse")
    public Result refuse(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _opinion = data.get("opinion").getAsString();

        return formRepository.refuse(_id, _opinion, loginUtil.getUser());
    }

    /*@PostMapping(value = "/receive")
    @ResponseBody
    @RequiresPermissions(value = "migrant:visit-form:receive")
    public Result receive(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return visitFormRepository.receive(_id, loginUtil.getUser());
    }*/

    @PostMapping(value = "/reject")
    @ResponseBody
    @RequiresPermissions(value = "migrant:visit-form:reject")
    public Result reject(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _opinion = data.get("opinion").getAsString();

        return formRepository.reject(_id, _opinion, loginUtil.getUser());
    }

    @PostMapping(value = "/close")
    @ResponseBody
    @RequiresPermissions(value = "migrant:visit-form:close")
    public Result close(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _opinion = data.get("opinion").getAsString();

        return formRepository.close(_id, _opinion, loginUtil.getUser());
    }

    @PostMapping(value = "/organization")
    @ResponseBody
    public Result<DepartmentRepository.Organization> organization(@RequestBody JsonObject data) throws InstantiationException, IllegalAccessException {
        String _id = Optional.ofNullable(data.get("id"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<DepartmentRepository.Organization> _organization = departmentRepository.getAsOrganization(_id);
        if (!_organization.isOK()) {
            return _organization;
        }

        List<String> _names = Arrays.asList("金平区", "龙湖区", "潮阳区", "潮南区", "澄海区", "濠江区", "南澳县");

        // 过滤父级节点
        boolean _ok = false;
        if (_organization.data.dept != null && _organization.data.dept.getLevel() > 0) {
            for (String i : _names) {
                if (_organization.data.dept.getFullName().equals(i) || _organization.data.dept.getFullName().startsWith(i + "/")) {
                    _ok = true;

                    break;
                }
            }

            if (!_ok) {
                _organization.setCode(Result.DATA_ACCESS_DENY);
                return _organization;
            }
        }

        List<Department> _subordinates = new ArrayList<>();
        for (Object i : _organization.data.subordinates) {
            Department _dept = (Department) i;

            _ok = false;
            for (String j : _names) {
                if (_dept.getFullName().equals(j) || _dept.getFullName().startsWith(j + "/")) {
                    _ok = true;

                    break;
                }
            }

            if (_ok) {
                _subordinates.add(_dept);
            }
        }
        _organization.data.subordinates = _subordinates;

        return _organization;
    }

    @PostMapping(value = "/tip")
    @ResponseBody
    @NotLog
    public Result<String> tip() {
        Result<String> _msg = new Result<>();
        _msg.data = formRepository.tip();
        return _msg;
    }

}