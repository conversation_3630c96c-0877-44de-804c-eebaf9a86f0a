package com.chinamobile.healthcode.controller;

import com.chinamobile.healthcode.model.Task;
import com.chinamobile.healthcode.repository.TaskRepository;
import com.chinamobile.healthcode.repository.project.RecordRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

@Controller
@RequestMapping(value = "")
public class TaskController {

    final RecordRepository recordRepository;
    final TaskRepository taskRepository;
    final LoginUtil loginUtil;

    public TaskController(RecordRepository recordRepository, TaskRepository taskRepository, LoginUtil loginUtil) {
        this.recordRepository = recordRepository;
        this.taskRepository = taskRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "/workbench")
    public String index() throws IOException {
        return "workbench";
    }

    @PostMapping(value = "/workbench/todo")
    @ResponseBody
    public Result<PagingItems<Task>> todo(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        boolean _todo = data.get("todo").getAsBoolean();
        Task.ENUM_TYPE _subjectType = Optional.ofNullable(data.get("subjectType"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsInt)
                .map(Task.ENUM_TYPE::valueOf)
                .orElse(null);

        Result<PagingItems<Task>> _page = new Result<>();
        _page.data = taskRepository.fuzzy(_count, _index, _subjectType, _todo ? Collections.singletonList(Task.ENUM_STATUS.TODO) : Arrays.asList(Task.ENUM_STATUS.DONE, Task.ENUM_STATUS.CLOSED), loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/task/count/unread")
    @ResponseBody
    public Result<Long> countUnread() {
        Result<Long> _count = new Result<>();
        _count.data = taskRepository.countUnread(loginUtil.getUser());
        return _count;
    }

    @PostMapping(value = "/task/get")
    @ResponseBody
    public Result<Task> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return taskRepository.get(_id, loginUtil.getUser());
    }

    @PostMapping(value = "/task/read")
    @ResponseBody
    public Result receive(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return taskRepository.read(_id, loginUtil.getUser());
    }

    @PostMapping(value = "/task/complete")
    @ResponseBody
    public Result complete(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        List<String> _departmentIds = (List<String>) Optional.ofNullable(data.get("departmentIds"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<String>>() {
                }.getType())).orElse(null);
        String _memo = data.get("memo").getAsString();

        Result _success = new Result();

        Result<Task> _item = taskRepository.getBrief(_id, loginUtil.getUser());
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getReadonly()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 新增任务
        if (!CollectionUtils.isEmpty(_departmentIds)) {
            recordRepository.addTasks(_item.data.getSubjectId(), _item.data.getId(), _departmentIds, loginUtil.getUserId());

            _item.data.setTransition(ConverterUtil.toJson(_departmentIds));
        }

        // 办理任务
        _item.data.setMemo(_memo);
        _item.data.setStatus(Task.ENUM_STATUS.DONE);

        if (_item.data.getReadTime() == null) {
            _item.data.setReadTime(new Date());
        }
        _item.data.setCompleteTime(new Date());

        _success = taskRepository.update(_item.data, loginUtil.getUserId());
        if (!_success.isOK()) {
            return _success;
        }

        // 设备告警任务，同时关闭其他用户的任务
        if (Task.ENUM_TYPE.设备告警 == _item.data.getSubjectType()) {
            taskRepository.closeBySubjectId(_item.data.getSubjectId(), loginUtil.getUserId());
        }

        return _success;
    }

    @PostMapping(value = "/task/issue")
    @ResponseBody
    public Result issue(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        List<String> _departmentIds = ConverterUtil.json2Object(data.get("departmentIds").toString(), new TypeToken<List<String>>() {
        }.getType());

        Result _success = new Result();

        if (CollectionUtils.isEmpty(_departmentIds)) {
            return _success;
        }

        Result<Task> _item = taskRepository.getBrief(_id, loginUtil.getUser());
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getReadonly()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 新增任务
        recordRepository.addTasks(_item.data.getSubjectId(), _item.data.getId(), _departmentIds, loginUtil.getUserId());

        // 获取原部门
        List<String> _temp = StringUtils.hasLength(_item.data.getTransition()) ? ConverterUtil.json2Object(_item.data.getTransition(), new TypeToken<List<String>>() {
        }.getType()) : new ArrayList<>();
        // 设置增发
        _temp.addAll(_departmentIds);

        _item.data.setTransition(ConverterUtil.toJson(_temp));

        return taskRepository.update(_item.data, loginUtil.getUserId());
    }

    @PostMapping(value = "/task/update-status")
    @ResponseBody
    public Result updateStatus(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        Task.ENUM_STATUS _status = Task.ENUM_STATUS.valueOf(data.get("status").getAsString());
        String _memo = Optional.ofNullable(data.get("memo"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        return taskRepository.updateStatus(_id, _memo, _status, loginUtil.getUser());
    }

}
