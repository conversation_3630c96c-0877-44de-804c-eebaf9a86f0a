package com.chinamobile.healthcode.controller.iot;

import com.chinamobile.healthcode.model.iot.Drone;
import com.chinamobile.healthcode.model.iot.DroneRoute;
import com.chinamobile.healthcode.repository.iot.DroneRepository;
import com.chinamobile.healthcode.repository.iot.InterphoneRepository;
import com.chinamobile.healthcode.repository.subject.PropertyRepository;
import com.chinamobile.healthcode.service.UAVService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.service.uav.infra.TokenStore;
import com.chinamobile.sparrow.domain.service.uav.lang.Route;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.jinq.tuples.Pair;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "iot/device")
public class DeviceController {

    final DroneRepository droneRepository;
    final PropertyRepository propertyRepository;
    final InterphoneRepository interphoneRepository;
    final UAVService uavService;
    final LoginUtil loginUtil;

    public DeviceController(
            DroneRepository droneRepository,
            PropertyRepository propertyRepository,
            InterphoneRepository interphoneRepository,
            UAVService uavService,
            LoginUtil loginUtil
    ) {
        this.droneRepository = droneRepository;
        this.propertyRepository = propertyRepository;
        this.interphoneRepository = interphoneRepository;
        this.uavService = uavService;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/property/find")
    public Result<List<JsonObject>> properties(@RequestBody JsonObject data) {
        List<String> _allTypes = Arrays.asList("摄像头", "烟雾报警器");

        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        List<String> _types = Optional.ofNullable(data.get("types"))
                .map(i -> i.isJsonNull() ? _allTypes : ConverterUtil.json2Object(i.toString(), new TypeToken<List<String>>() {
                }.getType())).orElse(_allTypes);

        Result<List<JsonObject>> _devices = new Result<>();
        _devices.data = new ArrayList<>();

        if (_types.contains("摄像头") || _types.contains("烟雾报警器")) {
            List<JsonObject> _temp = propertyRepository.fuzzy(-1, -1, _regionFullName, null, null, null, loginUtil.getUser()).items.stream()
                    .filter(i -> _types.contains(i.getType()))
                    .map(i -> (JsonObject) ConverterUtil.gson.toJsonTree(i))
                    .collect(Collectors.toList());

            _devices.data.addAll(_temp);
        }

        return _devices;
    }

    @PostMapping(value = "/drone/get")
    public Result<Drone> drone(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return droneRepository.get(_id);
    }

    @PostMapping(value = "/drone/find")
    public Result<List<Drone>> drones(@RequestBody JsonObject data) {
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _from = Optional.ofNullable(data.get("from"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Date _to = Optional.ofNullable(data.get("to"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);

        Result<List<Drone>> _devices = new Result<>();
        _devices.data = droneRepository.find(-1, -1, _regionFullName, _from, _to, loginUtil.getUser()).items;
        return _devices;
    }

    @PostMapping(value = "/drone/token")
    public Result<String> token(@RequestBody JsonObject data) throws IOException {
        Result<String> _token = new Result<>();

        String _id = data.get("id").getAsString();

        Result<Drone> _drone = droneRepository.get(_id);
        if (!_drone.isOK()) {
            return _token.pack(_drone);
        }

        TokenStore _tokenStore = uavService.tokenStore(_drone.data.getClientId());
        if (_tokenStore != null) {
            _tokenStore.clear();
            _token.data = _tokenStore.getToken();
        }

        return _token;
    }

    @PostMapping(value = "/drone/live")
    public Result<String> live(@RequestBody JsonObject data) throws IOException {
        Result<String> _url = new Result<>();

        String _id = data.get("id").getAsString();

        Result<Drone> _item = droneRepository.get(_id);
        if (!_item.isOK()) {
            return _url.pack(_item);
        }

        return uavService.droneFacade(_item.data.getClientId()).live(_id);
    }

    @PostMapping(value = "/drone/route/get")
    public Result<List<Route.Coordinate>> route(@RequestBody JsonObject data) throws IOException {
        String _routeId = data.get("routeId").getAsString();

        return droneRepository.getRawRoute(_routeId);
    }

    @PostMapping(value = "/drone/route/find")
    public Result<PagingItems<DroneRoute>> routes(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _droneId = Optional.ofNullable(data.get("droneId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _from = Optional.ofNullable(data.get("from"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Date _to = Optional.ofNullable(data.get("to"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);

        Result<PagingItems<DroneRoute>> _devices = new Result<>();
        _devices.data = droneRepository.findRoutes(_count, _index, _droneId, _regionFullName, _from, _to, loginUtil.getUser());
        return _devices;
    }

    @PostMapping(value = "/drone/route/vod")
    public Result<String> vod(@RequestBody JsonObject data) throws IOException {
        Result<String> _url = new Result<>();

        String _routeId = data.get("routeId").getAsString();

        Result<DroneRoute> _route = droneRepository.getRoute(_routeId);
        if (!_route.isOK()) {
            return _url.pack(_route);
        }

        Result<Drone> _item = droneRepository.get(_route.data.getDroneId());
        if (!_item.isOK()) {
            return _url.pack(_item);
        }

        return uavService.droneFacade(_item.data.getClientId()).vod(_routeId);
    }

    @PostMapping(value = "/statistics")
    @Transactional(readOnly = true)
    public Result<List<Pair<String, Long>>> statistics(@RequestBody JsonObject data) {
        List<String> _allTypes = Arrays.asList("摄像头", "对讲机", "烟雾报警器");

        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        List<String> _types = Optional.ofNullable(data.get("types"))
                .map(i -> i.isJsonNull() ? _allTypes : ConverterUtil.json2Object(i.toString(), new TypeToken<List<String>>() {
                }.getType())).orElse(_allTypes);

        Result<List<Pair<String, Long>>> _statistics = new Result<>();
        _statistics.data = new ArrayList<>();

        if (_types.contains("摄像头") || _types.contains("烟雾报警器")) {
            List<Pair<String, Long>> _temp = propertyRepository.stream(_regionFullName, null, null, null, loginUtil.getUser())
                    .where(i -> _types.contains(i.getType()) && "1".equals(i.getStatus()))
                    .group(i -> i.getType(), (type, stream) -> stream.count())
                    .toList();

            _statistics.data.addAll(_temp);
        }

        if (_types.contains("对讲机")) {
            _statistics.data.add(new Pair<>("对讲机", interphoneRepository.stream(_regionFullName, null, loginUtil.getUser())
                    .where(i -> i.getIsOnline())
                    .count()));
        }

        return _statistics;
    }

}