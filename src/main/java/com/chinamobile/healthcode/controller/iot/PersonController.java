package com.chinamobile.healthcode.controller.iot;

import com.chinamobile.healthcode.model.iot.Person;
import com.chinamobile.healthcode.repository.iot.monitoring.PersonRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller(value = "iotMonitoringPersonController")
@RequestMapping(value = "/iot/monitoring/person")
public class PersonController {

    final PersonRepository personRepository;
    final LoginUtil loginUtil;

    public PersonController(
            PersonRepository personRepository,
            LoginUtil loginUtil
    ) {
        this.personRepository = personRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/find")
    @ResponseBody
    public Result<List<Person>> find(@RequestBody JsonObject data) {
        List<String> _alarmIds = ConverterUtil.json2Object(data.get("alarmIds").toString(), new TypeToken<List<String>>() {
        }.getType());

        Result<List<Person>> _records = new Result<>();
        _records.data = personRepository.findByAlarmIds(_alarmIds);
        return _records;
    }

}