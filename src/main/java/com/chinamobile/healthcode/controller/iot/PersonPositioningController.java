package com.chinamobile.healthcode.controller.iot;

import com.chinamobile.healthcode.model.iot.PersonPositioning;
import com.chinamobile.healthcode.repository.iot.monitoring.PersonPositioningRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "/iot/monitoring/person-positioning")
public class PersonPositioningController {

    final PersonPositioningRepository personPositioningRepository;
    final LoginUtil loginUtil;

    public PersonPositioningController(
            PersonPositioningRepository personPositioningRepository,
            LoginUtil loginUtil
    ) {
        this.personPositioningRepository = personPositioningRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/track")
    @ResponseBody
    public Result<List<PersonPositioning>> track(@RequestBody JsonObject data) {
        String _alarmId = data.get("alarmId").getAsString();
        String _regionFullName = data.get("regionFullName").getAsString();
        Date _from = Optional.ofNullable(data.get("from"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH:mm:ss")).orElse(null);
        Date _to = Optional.ofNullable(data.get("to"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH:mm:ss")).orElse(null);

        Result<List<PersonPositioning>> _records = new Result<>();
        _records.data = personPositioningRepository.track(_alarmId, _regionFullName, _from, _to, loginUtil.getUser());
        return _records;
    }

}