package com.chinamobile.healthcode.controller.iot;

import com.chinamobile.healthcode.model.iot.Interphone;
import com.chinamobile.healthcode.repository.iot.InterphoneRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.springboot.web.ShiroHandshakeInterceptorImpl;
import com.google.gson.JsonObject;
import org.apache.shiro.web.subject.support.WebDelegatingSubject;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.util.List;
import java.util.Optional;

@Component
public class InterphoneWebSocket<PERSON>and<PERSON> extends AbstractWebSocketHandler {

    final InterphoneRepository interphoneRepository;

    public InterphoneWebSocketHandler(InterphoneRepository interphoneRepository) {
        this.interphoneRepository = interphoneRepository;
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        JsonObject _data = ConverterUtil.json2Object(message.getPayload(), JsonObject.class);
        String _regionFullName = Optional.ofNullable(_data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Interphone>> _interphones = new Result<>();
        _interphones.data = interphoneRepository.search(-1, -1, _regionFullName, null, parse(session)).items;

        session.sendMessage(new TextMessage(ConverterUtil.toJson(_interphones)));
    }

    DefaultUser parse(WebSocketSession session) {
        return (DefaultUser) Optional.ofNullable(session.getAttributes().get(ShiroHandshakeInterceptorImpl.SUBJECT_KEY))
                .map(i -> ((WebDelegatingSubject) i).getPrincipal())
                .filter(i -> i instanceof DefaultUser)
                .orElse(null);
    }

}