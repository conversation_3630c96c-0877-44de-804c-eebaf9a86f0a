package com.chinamobile.healthcode.controller.cmc;

import com.chinamobile.healthcode.model.cmc.Counseling;
import com.chinamobile.healthcode.repository.cmc.CounselingRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Tuple4;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "cmc/counseling")
public class CounselingController {

    final CounselingRepository counselingRepository;
    final LoginUtil loginUtil;

    public CounselingController(CounselingRepository counselingRepository, LoginUtil loginUtil) {
        this.counselingRepository = counselingRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "cmc:counseling:index")
    public String index() {
        return "cmc/counseling";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    public Result<Counseling> get(@RequestBody JsonObject data) throws IOException, InstantiationException, IllegalAccessException {
        String _id = data.get("id").getAsString();

        return counselingRepository.get(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/save")
    @ResponseBody
    public Result<String> save(@Validated @RequestBody Counseling item) throws IOException, InstantiationException, IllegalAccessException {
        return counselingRepository.save(item, loginUtil.getUser());
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    public Result<PagingItems<Counseling>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _unitId = Optional.ofNullable(data.get("unitId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        boolean _includeSubordinates = Optional.ofNullable(data.get("includeSubordinates"))
                .map(i -> i.isJsonNull() ? true : i.getAsBoolean()).orElse(true);

        Result<PagingItems<Counseling>> _page = new Result<>();
        _page.data = counselingRepository.fuzzy(_count, _index, _regionFullName, _unitId, _includeSubordinates, loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/me")
    @ResponseBody
    public Result<PagingItems<Counseling>> me(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();

        Result<PagingItems<Counseling>> _page = new Result<>();
        _page.data = counselingRepository.me(_count, _index, loginUtil.getUserId());
        return _page;
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return counselingRepository.remove(_id, loginUtil.getUser());
    }

    @PostMapping(value = "/statistics")
    @ResponseBody
    public Result<List<Tuple4<String, String, String, Long>>> statistics(@RequestBody JsonObject data) {
        String _regionId = Optional.ofNullable(data.get("regionId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Tuple4<String, String, String, Long>>> _dto = new Result<>();
        _dto.data = counselingRepository.statistics(_regionId, loginUtil.getUser());
        return _dto;
    }

}