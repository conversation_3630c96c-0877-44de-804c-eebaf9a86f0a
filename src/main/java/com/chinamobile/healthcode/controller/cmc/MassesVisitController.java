package com.chinamobile.healthcode.controller.cmc;

import com.chinamobile.healthcode.model.cmc.MassesVisit;
import com.chinamobile.healthcode.repository.cmc.MassesVisitRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Tuple4;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "cmc/masses-visit")
public class MassesVisitController {

    final MassesVisitRepository massesVisitRepository;
    final LoginUtil loginUtil;

    public MassesVisitController(MassesVisitRepository massesVisitRepository, LoginUtil loginUtil) {
        this.massesVisitRepository = massesVisitRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "cmc:masses-visit:index")
    public String index() {
        return "cmc/masses-visit";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    public Result<MassesVisit> get(@RequestBody JsonObject data) throws IOException, InstantiationException, IllegalAccessException {
        String _id = data.get("id").getAsString();

        return massesVisitRepository.get(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/save")
    @ResponseBody
    public Result<String> save(@Validated @RequestBody MassesVisit item) throws IOException, InstantiationException, IllegalAccessException {
        return massesVisitRepository.save(item, loginUtil.getUser());
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    public Result<PagingItems<MassesVisit>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _unitId = Optional.ofNullable(data.get("unitId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        boolean _includeSubordinates = Optional.ofNullable(data.get("includeSubordinates"))
                .map(i -> i.isJsonNull() ? true : i.getAsBoolean()).orElse(true);

        Result<PagingItems<MassesVisit>> _page = new Result<>();
        _page.data = massesVisitRepository.fuzzy(_count, _index, _regionFullName, _unitId, _includeSubordinates, loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/me")
    @ResponseBody
    public Result<PagingItems<MassesVisit>> me(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();

        Result<PagingItems<MassesVisit>> _page = new Result<>();
        _page.data = massesVisitRepository.me(_count, _index, loginUtil.getUserId());
        return _page;
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return massesVisitRepository.remove(_id, loginUtil.getUser());
    }

    @PostMapping(value = "/statistics")
    @ResponseBody
    public Result<List<Tuple4<String, String, String, Long>>> statistics(@RequestBody JsonObject data) {
        String _regionId = Optional.ofNullable(data.get("regionId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Tuple4<String, String, String, Long>>> _dto = new Result<>();
        _dto.data = massesVisitRepository.statistics(_regionId, loginUtil.getUser());
        return _dto;
    }

}