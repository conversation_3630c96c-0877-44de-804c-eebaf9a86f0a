package com.chinamobile.healthcode.controller;

import com.chinamobile.healthcode.model.OperationLog;
import com.chinamobile.healthcode.model.OperationLogQueryDto;
import com.chinamobile.healthcode.service.OperationLogService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 操作日志控制器
 */
@RestController
@RequestMapping("/api/operation-logs")
public class OperationLogController {

    private final OperationLogService operationLogService;

    public OperationLogController(OperationLogService operationLogService) {
        this.operationLogService = operationLogService;
    }

    /**
     * 分页查询操作日志
     */
    @PostMapping("/query")
    public Result<Page<OperationLog>> queryLogs(@RequestBody OperationLogQueryDto queryDto) {
        try {
            Page<OperationLog> logs = operationLogService.queryLogs(queryDto);
            Result<Page<OperationLog>> result = new Result<>();
            result.data = logs;
            return result;
        } catch (Exception e) {
            Result<Page<OperationLog>> result = new Result<>();
            result.setCode(Result.ENUM_ERROR.P, 1, new Object[]{e.getMessage()});
            return result;
        }
    }

    /**
     * 根据实体类型和ID查询操作日志
     */
    @GetMapping("/entity/{entityType}/{entityId}")
    public Result<List<OperationLog>> getLogsByEntity(
            @PathVariable String entityType,
            @PathVariable String entityId
    ) {
        try {
            List<OperationLog> logs = operationLogService.getLogsByEntity(entityType, entityId);
            Result<List<OperationLog>> result = new Result<>();
            result.data = logs;
            return result;
        } catch (Exception e) {
            Result<List<OperationLog>> result = new Result<>();
            result.setCode(Result.ENUM_ERROR.P, 1, new Object[]{e.getMessage()});
            return result;
        }
    }

    /**
     * 根据操作人ID查询操作日志
     */
    @GetMapping("/operator/{operatorId}")
    public Result<List<OperationLog>> getLogsByOperator(@PathVariable String operatorId) {
        try {
            List<OperationLog> logs = operationLogService.getLogsByOperator(operatorId);
            Result<List<OperationLog>> result = new Result<>();
            result.data = logs;
            return result;
        } catch (Exception e) {
            Result<List<OperationLog>> result = new Result<>();
            result.setCode(Result.ENUM_ERROR.P, 1, new Object[]{e.getMessage()});
            return result;
        }
    }

    /**
     * 根据部门ID查询操作日志
     */
    @GetMapping("/department/{deptId}")
    public Result<List<OperationLog>> getLogsByDepartment(@PathVariable String deptId) {
        try {
            List<OperationLog> logs = operationLogService.getLogsByDepartment(deptId);
            Result<List<OperationLog>> result = new Result<>();
            result.data = logs;
            return result;
        } catch (Exception e) {
            Result<List<OperationLog>> result = new Result<>();
            result.setCode(Result.ENUM_ERROR.P, 1, new Object[]{e.getMessage()});
            return result;
        }
    }

    /**
     * 根据时间范围查询操作日志
     */
    @GetMapping("/time-range")
    public Result<List<OperationLog>> getLogsByTimeRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime
    ) {
        try {
            List<OperationLog> logs = operationLogService.getLogsByTimeRange(startTime, endTime);
            Result<List<OperationLog>> result = new Result<>();
            result.data = logs;
            return result;
        } catch (Exception e) {
            Result<List<OperationLog>> result = new Result<>();
            result.setCode(Result.ENUM_ERROR.P, 1, new Object[]{e.getMessage()});
            return result;
        }
    }

    /**
     * 清理指定时间之前的日志
     */
    @DeleteMapping("/clean")
    public Result<Integer> cleanLogsBefore(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date
    ) {
        try {
            int count = operationLogService.cleanLogsBefore(date);
            Result<Integer> result = new Result<>();
            result.data = count;
            return result;
        } catch (Exception e) {
            Result<Integer> result = new Result<>();
            result.setCode(Result.ENUM_ERROR.P, 1, new Object[]{e.getMessage()});
            return result;
        }
    }
}
