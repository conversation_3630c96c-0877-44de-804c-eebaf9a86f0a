package com.chinamobile.healthcode.controller.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.subject.EventDescription;
import com.chinamobile.healthcode.repository.subject.EventRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Controller
@RequestMapping(value = "subject/event")
public class EventController {

    final EventRepository eventRepository;
    final DictionaryRepository dictionaryRepository;
    final LoginUtil loginUtil;

    public EventController(EventRepository eventRepository, DictionaryRepository dictionaryRepository, LoginUtil loginUtil) {
        this.eventRepository = eventRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "subject:event:index")
    public String index() {
        return "subject/events";
    }

    @GetMapping(value = "/form")
    @RequiresPermissions(value = "subject:event:index")
    public String form() {
        return "subject/event";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "subject:event:get")
    public Result<EventDescription> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return eventRepository.get(_id);
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "subject:event:save")
    public Result<String> save(@Validated({ValidationGroup.Insert.class, ValidationGroup.Update.class}) @RequestBody EventDescription item) {
        return eventRepository.save(item, loginUtil.getUser());
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    @RequiresPermissions(value = "subject:event:fuzzy")
    public Result<PagingItems<EventDescription>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<SortField> _sortFields = (List<SortField>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new com.google.gson.reflect.TypeToken<List<SortField>>() {
                }.getType())).orElse(null);
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _subtype = Optional.ofNullable(data.get("subtype"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _title = Optional.ofNullable(data.get("title"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _description = Optional.ofNullable(data.get("description"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _credentialNo = Optional.ofNullable(data.get("credentialNo"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Boolean _finished = Optional.ofNullable(data.get("finished"))
                .map(i -> i.isJsonNull() ? null : i.getAsBoolean()).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        String _address = Optional.ofNullable(data.get("address"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PagingItems<EventDescription>> _page = new Result<>();
        _page.data = eventRepository.fuzzy(_count,
                _index,
                _sortFields,
                _regionFullName,
                _type,
                _subtype,
                _title,
                _credentialNo,
                _name,
                _description,
                _finished,
                _beginTime,
                _endTime,
                _address,
                loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "subject:event:remove")
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return eventRepository.remove(_id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @ResponseBody
    @RequiresPermissions(value = "subject:event:import")
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile file) throws Exception {
        File _file = null;

        try {
            _file = new File(FileUtils.getTempDirectory() + File.separator + UUID.randomUUID() + "." + FilenameUtils.getExtension(file.getOriginalFilename()));
            FileUtils.copyInputStreamToFile(file.getInputStream(), _file);
            return eventRepository.importFromExcel(_file, loginUtil.getUser());
        } finally {
            if (_file != null) {
                _file.delete();
            }
        }
    }

    @PostMapping(value = "/types")
    @ResponseBody
    public Result<JsonObject> types() {
        Result<JsonObject> _types = new Result<>();
        _types.data = eventRepository.types();
        return _types;
    }

    @PostMapping("/stats/my")
    @ResponseBody
    public Result<List<Pair<String, Long>>> myStats() {
        return eventRepository.myStats(loginUtil.getUser());
    }

    @PostMapping("/instability/difference")
    @ResponseBody
    @RequiresPermissions(value = "subject:event:fuzzy")
    public Result<Tuple4<Long, Long, Long, List<String>>> getInstabilityDifference(@RequestBody JsonObject data) {
        Result<Tuple4<Long, Long, Long, List<String>>> res = new Result<>();
        String regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String type = Optional.ofNullable(data.get("type"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        res.data = eventRepository.getInstabilityDifference(regionFullName, type, loginUtil.getUser());
        return res;
    }

}
