package com.chinamobile.healthcode.controller.subject;

import com.chinamobile.healthcode.model.subject.Participant;
import com.chinamobile.healthcode.repository.subject.EventParticipantRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping(value = "subject/event/participant")
public class EventParticipantController {

    final EventParticipantRepository eventParticipantRepository;
    final LoginUtil loginUtil;

    public EventParticipantController(EventParticipantRepository eventParticipantRepository, LoginUtil loginUtil) {
        this.eventParticipantRepository = eventParticipantRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "subject:event:participant:save")
    public Result<String> save(@Validated @RequestBody Participant item) {
        return eventParticipantRepository.save(item, loginUtil.getUserId());
    }

    @PostMapping(value = "/query")
    @ResponseBody
    @RequiresPermissions(value = "subject:event:participant:query")
    public Result<List<Participant>> query(@RequestBody JsonObject data) {
        String _eventId = data.get("eventId").getAsString();

        Result<List<Participant>> _page = new Result<>();
        _page.data = eventParticipantRepository.query(_eventId);
        return _page;
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "subject:event:participant:remove")
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return eventParticipantRepository.remove(_id);
    }

}