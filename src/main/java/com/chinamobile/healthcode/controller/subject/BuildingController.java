package com.chinamobile.healthcode.controller.subject;

import com.chinamobile.healthcode.model.subject.BuildingDescription;
import com.chinamobile.healthcode.model.subject.PropertyDescription;
import com.chinamobile.healthcode.model.subject.UnitDescription;
import com.chinamobile.healthcode.repository.subject.BuildingRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "subject/building")
public class BuildingController {

    final BuildingRepository buildingRepository;
    final LoginUtil loginUtil;

    public BuildingController(BuildingRepository buildingRepository, LoginUtil loginUtil) {
        this.buildingRepository = buildingRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "subject:building:index")
    public String index() {
        return "subject/buildings";
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "subject:building:save")
    public Result<String> save(@Validated @RequestBody BuildingDescription item) {
        return buildingRepository.save(item, loginUtil.getUser());
    }

    @PostMapping(value = "/update")
    @ResponseBody
    @RequiresPermissions(value = "subject:building:update")
    public Result<String> update(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _name = data.get("name").getAsString();

        return buildingRepository.update(_id, _name);
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    @RequiresPermissions(value = "subject:building:fuzzy")
    public Result<PagingItems<BuildingDescription>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<SortField> _sortFields = (List<SortField>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new com.google.gson.reflect.TypeToken<List<SortField>>() {
                }.getType())).orElse(null);
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PagingItems<BuildingDescription>> _page = new Result<>();
        _page.data = buildingRepository.fuzzy(_count, _index, _sortFields, _regionFullName, _name, loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "subject:building:remove")
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return buildingRepository.remove(_id, loginUtil.getUser());
    }

}
