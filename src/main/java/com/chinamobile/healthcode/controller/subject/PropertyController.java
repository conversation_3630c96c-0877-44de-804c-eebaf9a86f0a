package com.chinamobile.healthcode.controller.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.subject.PropertyDescription;
import com.chinamobile.healthcode.repository.iot.CameraRepository;
import com.chinamobile.healthcode.repository.iot.SmokeDetectorRepository;
import com.chinamobile.healthcode.repository.subject.PropertyRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Pair;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;

@Controller
@RequestMapping(value = "subject/property")
public class PropertyController {

    final PropertyRepository propertyRepository;
    final DictionaryRepository dictionaryRepository;
    final AbstractMediaRepository mediaRepository;
    final CameraRepository cameraRepository;
    final SmokeDetectorRepository smokeDetectorRepository;
    final LoginUtil loginUtil;

    public PropertyController(
            PropertyRepository propertyRepository,
            DictionaryRepository dictionaryRepository,
            AbstractMediaRepository mediaRepository,
            CameraRepository cameraRepository,
            SmokeDetectorRepository smokeDetectorRepository,
            LoginUtil loginUtil
    ) {
        this.propertyRepository = propertyRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.mediaRepository = mediaRepository;
        this.cameraRepository = cameraRepository;
        this.smokeDetectorRepository = smokeDetectorRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "subject:property:index")
    public String index() {
        return "subject/property";
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "subject:property:save")
    public Result<String> save(@RequestPart(value = "files", required = false) MultipartFile[] files, @Validated({ValidationGroup.Insert.class, ValidationGroup.Update.class}) @RequestPart(value = "item") PropertyDescription item) throws IOException {
        List<File> _files = new ArrayList<>();
        if (files != null) {
            for (MultipartFile i : files) {
                File _file = new File(FileUtils.getTempDirectory() + File.separator + i.getOriginalFilename());
                FileUtils.copyInputStreamToFile(i.getInputStream(), _file);

                _files.add(_file);
            }
        }

        List<String> _attachmentIds = new ArrayList<>();
        for (File i : _files) {
            Result<Media> _media = mediaRepository.add(null, i, true, loginUtil.getUserId());
            if (_media.isOK()) {
                _attachmentIds.add(_media.data.getId());
            }
        }

        if (CollectionUtils.isEmpty(item.getAttachmentIds())) {
            item.setAttachmentIds(_attachmentIds);
        } else {
            item.getAttachmentIds().addAll(_attachmentIds);
        }

        return propertyRepository.save(item, loginUtil.getUser());
    }

    @PostMapping(value = "/save/form")
    @ResponseBody
    @RequiresPermissions(value = "subject:property:save")
    public Result<String> save(@Validated({ValidationGroup.Insert.class, ValidationGroup.Update.class}) @RequestBody PropertyDescription item) throws IOException {
        return propertyRepository.save(item, loginUtil.getUser());
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    @RequiresPermissions(value = "subject:property:fuzzy")
    public Result<PagingItems<PropertyDescription>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _address = Optional.ofNullable(data.get("address"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PagingItems<PropertyDescription>> _page = new Result<>();
        _page.data = propertyRepository.fuzzy(_count, _index, _regionFullName, _name, _type, _address, loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "subject:property:remove")
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return propertyRepository.remove(_id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @ResponseBody
    @RequiresPermissions(value = "subject:property:import")
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile file) throws Exception {
        File _file = null;

        try {
            _file = new File(FileUtils.getTempDirectory() + File.separator + UUID.randomUUID() + "." + FilenameUtils.getExtension(file.getOriginalFilename()));
            FileUtils.copyInputStreamToFile(file.getInputStream(), _file);

            return propertyRepository.importFromExcel(_file, loginUtil.getUser());
        } finally {
            if (_file != null) {
                _file.delete();
            }
        }
    }

    @PostMapping(value = "/types")
    @ResponseBody
    public Result<List<Dictionary>> types() {
        Result<List<Dictionary>> _vals = new Result<>();
        _vals.data = propertyRepository.types();
        return _vals;
    }

    @PostMapping("/stats/my")
    @ResponseBody
    public Result<List<Pair<String, Long>>> myStats() {
        return propertyRepository.myStats(loginUtil.getUser());
    }

    @PostMapping(value = "/andmu/camera/subscribe")
    @ResponseBody
    public Result<Void> cameraSubscribe(@RequestBody JsonObject data) throws IOException {
        String _id = data.get("id").getAsString();

        cameraRepository.subscribe(Collections.singletonList(_id));

        return new Result<>();
    }

    @PostMapping(value = "/andmu/smoke-detector/subscribe")
    @ResponseBody
    public Result<Void> smokeDetectorSubscribe(@RequestBody JsonObject data) throws IOException {
        String _id = data.get("id").getAsString();

        smokeDetectorRepository.subscribe(Collections.singletonList(_id));

        return new Result<>();
    }

}