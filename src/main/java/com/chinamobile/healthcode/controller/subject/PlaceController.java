package com.chinamobile.healthcode.controller.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.subject.PlaceDescription;
import com.chinamobile.healthcode.model.subject.PlacePersonDescription;
import com.chinamobile.healthcode.repository.subject.PlaceRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.collect.ImmutableList;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 5/22/2023 10:12
 */
@Controller
@RequestMapping(value = "subject/place")
public class PlaceController {

    private final PlaceRepository placeRepository;

    private final DefaultRoleRepository roleRepository;

    private final DictionaryRepository dictionaryRepository;

    private final LoginUtil loginUtil;

    public PlaceController(PlaceRepository placeRepository,
                           DefaultRoleRepository roleRepository,
                           DictionaryRepository dictionaryRepository,
                           LoginUtil loginUtil) {
        this.placeRepository = placeRepository;
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "subject:place:index")
    public String places() {
        return "subject/places";
    }

    @GetMapping(value = "/form")
    @RequiresPermissions(value = "subject:place:index")
    public String place() {
        return "subject/place";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "subject:place:get")
    public Result<PlaceDescription> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<PlaceDescription> _item = placeRepository.getWithPersons(_id);
        if (_item.isOK()) {
            encryptSensitiveData(_item.data);
        }

        return _item;
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    @RequiresPermissions(value = "subject:place:fuzzy")
    public Result<PagingItems<PlaceDescription>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<SortField> _sortFields = (List<SortField>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new com.google.gson.reflect.TypeToken<List<SortField>>() {
                }.getType())).orElse(null);
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _placeName = Optional.ofNullable(data.get("placeName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _placeTypeValue = Optional.ofNullable(data.get("placeTypeValue"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PagingItems<PlaceDescription>> _page = new Result<>();
        _page.data = placeRepository.fuzzy(_count, _index, _sortFields, _regionFullName, _placeName, _placeTypeValue, loginUtil.getUser());

        // 脱敏
        _page.data.items.forEach(this::encryptSensitiveData);

        return _page;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "subject:place:save")
    public Result<String> save(@Validated({ValidationGroup.Insert.class, ValidationGroup.Update.class}) @RequestBody PlaceDescription item) {
        return placeRepository.saveOrUpdate(item, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "subject:place:remove")
    public Result<PlaceDescription> remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return placeRepository.remove(_id, loginUtil.getUser());
    }

    @PostMapping("/persons")
    @ResponseBody
    @RequiresPermissions(value = "subject:place:fuzzy")
    public Result<List<PlacePersonDescription>> queryPersons(@RequestBody JsonObject data) {
        Result<List<PlacePersonDescription>> _result = new Result<>();

        String _id = data.get("id").getAsString();

        Result<PlaceDescription> _p = placeRepository.getWithPersons(_id);

        if (_p.isOK()) {
            _result.data = _p.data.getPersons();
        }

        return _result;
    }

    @PostMapping(value = "/import")
    @ResponseBody
    @RequiresPermissions(value = "subject:place:import")
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile file) throws Exception {
        File _file = null;

        try {
            _file = new File(FileUtils.getTempDirectory() + File.separator + UUID.randomUUID() + "." + FilenameUtils.getExtension(file.getOriginalFilename()));
            FileUtils.copyInputStreamToFile(file.getInputStream(), _file);

            return placeRepository.importFromExcel(_file, loginUtil.getUser());
        } finally {
            if (_file != null) {
                _file.delete();
            }
        }
    }

    @PostMapping("/options/{type}")
    @ResponseBody
    public Result<List<Dictionary>> options(@PathVariable String type) {
        Result<List<Dictionary>> _options = new Result<>();
        String _groupId;
        List<SortField> _sortFieldList = null;

        switch (type) {
            case "place-type":
                _groupId = "重点场所-类别";
                _sortFieldList = ImmutableList.of(new SortField("createTime", false));
                break;
            case "subject-category":
                _groupId = "金凤码-特殊人群";
                break;
            case "occupancy-state":
                _groupId = "重点场所-人员状态";
                break;
            default:
                _groupId = "";
        }

        if (StringUtils.isNotBlank(_groupId)) {
            _options.data = dictionaryRepository.fuzzy(_groupId, null, true, _sortFieldList);
        }
        return _options;
    }

    @PostMapping("/stats/my")
    @ResponseBody
    public Result<List<Pair<String, Long>>> myStats() {
        return placeRepository.myStats(loginUtil.getUser());
    }

    @PostMapping("/instability/difference")
    @ResponseBody
    @RequiresPermissions(value = "subject:place:fuzzy")
    public Result<Tuple4<Long, Long, Long, List<String>>> getInstabilityDifference(@RequestBody JsonObject data) {
        Result<Tuple4<Long, Long, Long, List<String>>> res = new Result<>();
        String regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String type = Optional.ofNullable(data.get("type"))
                .filter(d -> !d.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        res.data = placeRepository.getInstabilityDifference(regionFullName, type, loginUtil.getUser());
        return res;
    }

    private void encryptSensitiveData(PlaceDescription placeDescription) {
        if (!roleRepository.isUserInRoleCached(loginUtil.getUserId(), PlaceRepository.ADMIN_ROLE)) {
            placeDescription.setCredentialNo(encryptCredentialNo(placeDescription.getCredentialNo()));
            if (placeDescription.getPersons() != null) {
                for (PlacePersonDescription placePersonDescription : placeDescription.getPersons()) {
                    placePersonDescription.setCredentialNo(encryptCredentialNo(placePersonDescription.getCredentialNo()));
                }
            }
        }
    }

    private String encryptCredentialNo(String credentialNo) {
        if (credentialNo != null && credentialNo.length() > 4) {
            int _length = credentialNo.length();
            String _credentialNo = credentialNo.substring(0, 2);
            _credentialNo += StringUtils.repeat("*", _length - 4);
            _credentialNo += credentialNo.substring(_length - 2);

            return _credentialNo;
        }
        return credentialNo;
    }

}
