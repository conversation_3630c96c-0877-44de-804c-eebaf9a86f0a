package com.chinamobile.healthcode.controller.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.subject.UnitDescription;
import com.chinamobile.healthcode.repository.subject.UnitRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Pair;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Controller
@RequestMapping(value = "subject/unit")
public class UnitController {

    final UnitRepository unitRepository;
    final DictionaryRepository dictionaryRepository;
    final LoginUtil loginUtil;

    public UnitController(UnitRepository unitRepository, DictionaryRepository dictionaryRepository, LoginUtil loginUtil) {
        this.unitRepository = unitRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "subject:unit:index")
    public String index() {
        return "subject/units";
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "subject:unit:save")
    public Result<String> save(@Validated({ValidationGroup.Insert.class, ValidationGroup.Update.class}) @RequestBody UnitDescription item) {
        return unitRepository.save(item, loginUtil.getUser());
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    @RequiresPermissions(value = "subject:unit:fuzzy")
    public Result<PagingItems<UnitDescription>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _address = Optional.ofNullable(data.get("address"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PagingItems<UnitDescription>> _page = new Result<>();
        _page.data = unitRepository.fuzzy(_count, _index, _regionFullName, _name, _type, _address, loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/center/default")
    @ResponseBody
    public Result<UnitDescription> getDefaultCenter() {
        Result<UnitDescription> _unit = new Result<>();
        _unit.data = unitRepository.getDefaultCenter(loginUtil.getUser());
        return _unit;
    }

    @PostMapping(value = "/center/query")
    @ResponseBody
    public Result<List<UnitDescription>> queryCenters() {
        Result<List<UnitDescription>> _units = new Result<>();
        _units.data = unitRepository.queryCenters(null);
        return _units;
    }

    @PostMapping(value = "/suggest/center")
    @ResponseBody
    public Result<List<UnitDescription>> suggestCenters(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        String _keyword = data.get("keyword").getAsString();

        Result<List<UnitDescription>> _units = new Result<>();
        _units.data = unitRepository.suggestCenters(_count, _keyword);
        return _units;
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "subject:unit:remove")
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return unitRepository.remove(_id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @ResponseBody
    @RequiresPermissions(value = "subject:unit:import")
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile file) throws Exception {
        File _file = null;

        try {
            _file = new File(FileUtils.getTempDirectory() + File.separator + UUID.randomUUID() + "." + FilenameUtils.getExtension(file.getOriginalFilename()));
            FileUtils.copyInputStreamToFile(file.getInputStream(), _file);

            return unitRepository.importFromExcel(_file, loginUtil.getUser());
        } finally {
            if (_file != null) {
                _file.delete();
            }
        }
    }

    @PostMapping(value = "/types")
    @ResponseBody
    public Result<List<Dictionary>> types() {
        Result<List<Dictionary>> _vals = new Result<>();
        _vals.data = unitRepository.types();
        return _vals;
    }

    @PostMapping("/stats/my")
    @ResponseBody
    public Result<List<Pair<String, Long>>> myStats() {
        return unitRepository.myStats(loginUtil.getUser());
    }

}