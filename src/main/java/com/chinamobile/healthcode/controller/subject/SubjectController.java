package com.chinamobile.healthcode.controller.subject;

import com.chinamobile.healthcode.model.stats.DailyStatsReport;
import com.chinamobile.healthcode.model.stats.RoutineInspectionStats;
import com.chinamobile.healthcode.repository.stats.DailyStatsReportRepository;
import com.chinamobile.healthcode.repository.stats.RoutineInspectionStatsRepository;
import com.chinamobile.healthcode.repository.subject.SubjectRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 6/9/2023 10:36
 */
@Controller
@RequestMapping("subject")
public class SubjectController {
    private final LoginUtil loginUtil;

    private final SubjectRepository subjectRepository;
    private final DictionaryRepository dictionaryRepository;
    private final RoutineInspectionStatsRepository routineInspectionStatsRepository;
    private final DailyStatsReportRepository dailyStatsReportRepository;

    public SubjectController(LoginUtil loginUtil,
                             SubjectRepository subjectRepository,
                             DictionaryRepository dictionaryRepository,
                             RoutineInspectionStatsRepository routineInspectionStatsRepository,
                             DailyStatsReportRepository dailyStatsReportRepository) {
        this.loginUtil = loginUtil;
        this.subjectRepository = subjectRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.routineInspectionStatsRepository = routineInspectionStatsRepository;
        this.dailyStatsReportRepository = dailyStatsReportRepository;
    }

    @GetMapping("/survey")
    @RequiresPermissions("subject:survey:index")
    public String surveyIndex() {
        return "subject/survey";
    }

    @PostMapping("/survey/query")
    @ResponseBody
    @RequiresPermissions("subject:survey:fuzzy")
    public Result<List<Object>> querySurvey(@RequestBody JsonObject data) {
        String _regionId = Optional.ofNullable(data.get("regionId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(loginUtil.getUser().getDeptId());
        String _subjectType = Optional.ofNullable(data.get("subjectType"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).get();

        Result<List<Object>> _survey = new Result<>();
        _survey.data = subjectRepository.querySurvey(_subjectType, _regionId, loginUtil.getUser());
        return _survey;
    }

    @PostMapping(value = "/stats/{subjectType}")
    @ResponseBody
    public Result<List<Object[]>> queryStats(@RequestBody JsonObject data, @PathVariable String subjectType) {
        String _deptId = Optional.ofNullable(data.get("deptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Object[]>> _counts = new Result<>();
        _counts.data = subjectRepository.queryStats(subjectType, _deptId, loginUtil.getUser());
        return _counts;
    }

    @PostMapping(value = "/stats/types/{subjectType}")
    @ResponseBody
    public Result<List<Object[]>> queryStatsWithTypes(@RequestBody JsonObject data, @PathVariable String subjectType) {
        String _deptId = Optional.ofNullable(data.get("deptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Object[]>> _counts = new Result<>();
        _counts.data = subjectRepository.queryStatsWithTypes(subjectType, _deptId, loginUtil.getUser());
        return _counts;
    }

    @PostMapping("/dicts")
    @ResponseBody
    public Result<List<Dictionary>> queryDicts() {
        Result<List<Dictionary>> _dicts = new Result<>();
        _dicts.data = dictionaryRepository.fuzzy("三防专题", null, true, null);
        return _dicts;
    }

    @GetMapping("/survey/daily-report")
    @RequiresPermissions("subject:survey:daily-report:index")
    public String dailyReport() {
        return "subject/daily-report";
    }

    @PostMapping("/survey/daily-report/query")
    @ResponseBody
    @RequiresPermissions("subject:survey:daily-report:fuzzy")
    public Result<List<DailyStatsReport>> queryDailyReport(@RequestBody JsonObject data) {
        String _regionId = Optional.ofNullable(data.get("regionId"))
                .filter(json -> !json.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        Date _recordDate = Optional.ofNullable(data.get("recordDate"))
                .filter(json -> !json.isJsonNull())
                .map(i -> DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(DateUtil.getDate(DateUtil.addDays(new Date(), -1)));

        Result<List<DailyStatsReport>> _survey = new Result<>();
        _survey.data = dailyStatsReportRepository.fuzzy(_regionId, _recordDate, loginUtil.getUser());
        return _survey;
    }

    @PostMapping("/survey/daily-report/export/base64")
    @ResponseBody
    @RequiresPermissions("subject:survey:daily-report:export")
    public Result<String> exportDailyReport(@RequestBody JsonObject data) throws IOException {
        String _regionId = Optional.ofNullable(data.get("regionId"))
                .filter(json -> !json.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        Date _recordDate = Optional.ofNullable(data.get("recordDate"))
                .filter(json -> !json.isJsonNull())
                .map(i -> DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(DateUtil.getDate(DateUtil.addDays(new Date(), -1)));

        Result<String> _base64 = new Result<>();
        _base64.data = dailyStatsReportRepository.export(_regionId, _recordDate, loginUtil.getUser());
        return _base64;
    }

    @GetMapping("/survey/routine-inspection")
    @RequiresPermissions("subject:survey:routine-inspection:index")
    public String routineInspection() {
        return "subject/routine-inspection";
    }

    @PostMapping("/survey/routine-inspection/query")
    @ResponseBody
    @RequiresPermissions("subject:survey:routine-inspection:fuzzy")
    public Result<List<RoutineInspectionStats>> queryRoutineInspection(@RequestBody JsonObject data) {
        Date _recordDate = Optional.ofNullable(data.get("recordDate"))
                .filter(json -> !json.isJsonNull())
                .map(i -> DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(DateUtil.getDate(DateUtil.addDays(new Date(), -1)));

        Result<List<RoutineInspectionStats>> _survey = new Result<>();
        _survey.data = routineInspectionStatsRepository.fuzzy(_recordDate);
        return _survey;
    }

    @PostMapping("/survey/routine-inspection/export/base64")
    @ResponseBody
    @RequiresPermissions("subject:survey:routine-inspection:export")
    public Result<String> exportRoutineInspection(@RequestBody JsonObject data) throws IOException {
        Date _recordDate = Optional.ofNullable(data.get("recordDate"))
                .filter(json -> !json.isJsonNull())
                .map(i -> DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(DateUtil.getDate(DateUtil.addDays(new Date(), -1)));

        Result<String> _base64 = new Result<>();
        _base64.data = routineInspectionStatsRepository.export(_recordDate);
        return _base64;
    }
}

