package com.chinamobile.healthcode.controller;

import com.chinamobile.healthcode.model.subject.PersonDescription;
import com.chinamobile.healthcode.repository.MonitoringScreenRepository;
import com.chinamobile.healthcode.service.event.PublishService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.google.gson.JsonObject;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "monitoring-screen")
public class MonitoringScreenController {

    final String alarmRingTopic;
    final MonitoringScreenRepository monitoringScreenRepository;
    final DictionaryRepository dictionaryRepository;
    final PublishService publishService;
    final LoginUtil loginUtil;

    public MonitoringScreenController(
            @Value(value = "${event.topic.alarm-ring}") String alarmRingTopic,
            MonitoringScreenRepository monitoringScreenRepository,
            DictionaryRepository dictionaryRepository,
            PublishService publishService,
            LoginUtil loginUtil
    ) {
        this.alarmRingTopic = alarmRingTopic;
        this.monitoringScreenRepository = monitoringScreenRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.publishService = publishService;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    public String index(Model model, @Value(value = "${monitoring-screen.title:社情地图}") String title) {
        model.addAttribute("title", title);
        return "monitoring-screen";
    }

    @PostMapping(value = "/url")
    @ResponseBody
    public Result<String> url(@Value(value = "${monitoring-screen.url}") String url) {
        Result<String> _url = new Result<>();
        _url.data = url;
        return _url;
    }

    @PostMapping(value = "/statistics/organization")
    @ResponseBody
    public Result<MonitoringScreenRepository.OrganizationDTO> organization(@RequestBody JsonObject data) {
        String _regionFullName = resolveRegionFullName(data);

        Result<MonitoringScreenRepository.OrganizationDTO> _dto = new Result<>();
        _dto.data = monitoringScreenRepository.organization(_regionFullName, loginUtil.getUser());
        return _dto;
    }

    @PostMapping(value = "/statistics/subject")
    @ResponseBody
    public Result<MonitoringScreenRepository.SubjectDTO> subject(@RequestBody JsonObject data) {
        String _regionFullName = resolveRegionFullName(data);

        Result<MonitoringScreenRepository.SubjectDTO> _dto = new Result<>();
        _dto.data = monitoringScreenRepository.subject(_regionFullName, loginUtil.getUser());
        return _dto;
    }

    @PostMapping(value = "/statistics/person")
    @ResponseBody
    public Result<List<Pair<PersonDescription.ENUM_TYPE, Long>>> person(@RequestBody JsonObject data) {
        String _regionFullName = resolveRegionFullName(data);

        Result<List<Pair<PersonDescription.ENUM_TYPE, Long>>> _dto = new Result<>();
        _dto.data = monitoringScreenRepository.person(_regionFullName, loginUtil.getUser());
        return _dto;
    }

    @PostMapping(value = "/statistics/event")
    @ResponseBody
    public Result<List<Pair<String, Long>>> event(@RequestBody JsonObject data) {
        String _regionFullName = resolveRegionFullName(data);

        Result<List<Pair<String, Long>>> _dto = new Result<>();
        _dto.data = monitoringScreenRepository.event(_regionFullName, loginUtil.getUser());
        return _dto;
    }

    @PostMapping(value = "/statistics/windbreak")
    @ResponseBody
    public Result<List<Pair<String, Long>>> windbreak(@RequestBody JsonObject data) {
        String _regionFullName = resolveRegionFullName(data);

        Result<List<Pair<String, Long>>> _dto = new Result<>();
        _dto.data = monitoringScreenRepository.windbreak(_regionFullName, loginUtil.getUser());
        return _dto;
    }

    @PostMapping(value = "/alarm/open")
    @ResponseBody
    public Result<Void> openAlarm() {
        publishService.publish(alarmRingTopic, org.apache.commons.lang3.tuple.Pair.of(loginUtil.getUser().getDeptFullName(), "play"));

        return new Result<>();
    }

    @PostMapping(value = "/alarm/close")
    @ResponseBody
    public Result<String> closeAlarm() {
        publishService.publish(alarmRingTopic, org.apache.commons.lang3.tuple.Pair.of(loginUtil.getUser().getDeptFullName(), "stop"));

        return new Result<>();
    }

    String resolveRegionFullName(JsonObject data) {
        return Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
    }

}