package com.chinamobile.healthcode.controller.instalibity;

import com.chinamobile.healthcode.model.instability.BasicInfo;
import com.chinamobile.healthcode.repository.instability.BasicInfoRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

@Controller
@RequestMapping("instability/basic-info")
public class BasicInfoController {
    private final LoginUtil loginUtil;
    private final BasicInfoRepository basicInfoRepository;

    public BasicInfoController(LoginUtil loginUtil, BasicInfoRepository basicInfoRepository) {
        this.loginUtil = loginUtil;
        this.basicInfoRepository = basicInfoRepository;
    }

    @GetMapping
    @RequiresPermissions("instability:basic-info:index")
    public String index() {
        return "/instability/basic-info";
    }

    @PostMapping("/fuzzy")
    @RequiresPermissions("instability:basic-info:fuzzy")
    @ResponseBody
    public Result<PagingItems<BasicInfo>> fuzzy(@RequestBody JsonObject data) {
        Result<PagingItems<BasicInfo>> pagingResult = new Result<>();

        int count = data.get("count").getAsInt();
        int index = data.get("index").getAsInt();
        String regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        pagingResult.data = basicInfoRepository.fuzzy(count, index, regionFullName, loginUtil.getUser());

        return pagingResult;
    }

    @PostMapping(value = "/save")
    @RequiresPermissions("instability:basic-info:save")
    @ResponseBody
    public Result<String> save(@RequestBody BasicInfo item) {
        return basicInfoRepository.saveOrUpdate(item, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @RequiresPermissions("instability:basic-info:remove")
    @ResponseBody
    public Result<BasicInfo> remove(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();
        return basicInfoRepository.remove(id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @RequiresPermissions("instability:basic-info:import")
    @ResponseBody
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile multipartFile) throws Exception {
        return basicInfoRepository.importFromExcel(multipartFile, 0, 1, loginUtil.getUser());
    }
}
