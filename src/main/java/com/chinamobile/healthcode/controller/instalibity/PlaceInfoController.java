package com.chinamobile.healthcode.controller.instalibity;

import com.chinamobile.healthcode.model.instability.PlaceInfo;
import com.chinamobile.healthcode.repository.instability.PlaceInfoRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

@Controller
@RequestMapping("instability/place-info")
public class PlaceInfoController {
    private final LoginUtil loginUtil;
    private final PlaceInfoRepository placeInfoRepository;

    public PlaceInfoController(LoginUtil loginUtil, PlaceInfoRepository placeInfoRepository) {
        this.loginUtil = loginUtil;
        this.placeInfoRepository = placeInfoRepository;
    }

    @GetMapping
    @RequiresPermissions("instability:place-info:index")
    public String index() {
        return "/instability/place-info";
    }

    @PostMapping("/fuzzy")
    @RequiresPermissions("instability:place-info:fuzzy")
    @ResponseBody
    public Result<PagingItems<PlaceInfo>> fuzzy(@RequestBody JsonObject data) {
        Result<PagingItems<PlaceInfo>> pagingResult = new Result<>();

        int count = data.get("count").getAsInt();
        int index = data.get("index").getAsInt();
        String regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        pagingResult.data = placeInfoRepository.fuzzy(count, index, regionFullName, loginUtil.getUser());

        return pagingResult;
    }

    @PostMapping(value = "/save")
    @RequiresPermissions("instability:place-info:save")
    @ResponseBody
    public Result<String> save(@RequestBody PlaceInfo item) {
        return placeInfoRepository.saveOrUpdate(item, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @RequiresPermissions("instability:place-info:remove")
    @ResponseBody
    public Result<PlaceInfo> remove(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();
        return placeInfoRepository.remove(id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @RequiresPermissions("instability:place-info:import")
    @ResponseBody
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile multipartFile) throws Exception {
        return placeInfoRepository.importFromExcel(multipartFile, 0, 1, loginUtil.getUser());
    }
}
