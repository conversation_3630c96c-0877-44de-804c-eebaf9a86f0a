package com.chinamobile.healthcode.controller.instalibity;

import com.chinamobile.healthcode.model.instability.PersonnelInfo;
import com.chinamobile.healthcode.repository.instability.PersonnelInfoRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

@Controller
@RequestMapping("instability/personnel-info")
public class PersonnelInfoController {
    private final LoginUtil loginUtil;
    private final PersonnelInfoRepository personnelInfoRepository;

    public PersonnelInfoController(LoginUtil loginUtil, PersonnelInfoRepository personnelInfoRepository) {
        this.loginUtil = loginUtil;
        this.personnelInfoRepository = personnelInfoRepository;
    }

    @GetMapping
    @RequiresPermissions("instability:personnel-info:index")
    public String index() {
        return "/instability/personnel-info";
    }

    @PostMapping("/fuzzy")
    @RequiresPermissions("instability:personnel-info:fuzzy")
    @ResponseBody
    public Result<PagingItems<PersonnelInfo>> fuzzy(@RequestBody JsonObject data) {
        Result<PagingItems<PersonnelInfo>> pagingResult = new Result<>();

        int count = data.get("count").getAsInt();
        int index = data.get("index").getAsInt();
        String regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        pagingResult.data = personnelInfoRepository.fuzzy(count, index, regionFullName, loginUtil.getUser());

        return pagingResult;
    }

    @PostMapping(value = "/save")
    @RequiresPermissions("instability:personnel-info:save")
    @ResponseBody
    public Result<String> save(@RequestBody PersonnelInfo item) {
        return personnelInfoRepository.saveOrUpdate(item, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @RequiresPermissions("instability:personnel-info:remove")
    @ResponseBody
    public Result<PersonnelInfo> remove(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();
        return personnelInfoRepository.remove(id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @RequiresPermissions("instability:personnel-info:import")
    @ResponseBody
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile multipartFile) throws Exception {
        return personnelInfoRepository.importFromExcel(multipartFile, 0, 1, loginUtil.getUser());
    }
}
