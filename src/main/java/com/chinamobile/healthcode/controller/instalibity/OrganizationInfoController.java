package com.chinamobile.healthcode.controller.instalibity;

import com.chinamobile.healthcode.model.instability.OrganizationInfo;
import com.chinamobile.healthcode.repository.instability.OrganizationInfoRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

@Controller
@RequestMapping("instability/organization-info")
public class OrganizationInfoController {
    private final LoginUtil loginUtil;
    private final OrganizationInfoRepository organizationInfoRepository;

    public OrganizationInfoController(LoginUtil loginUtil, OrganizationInfoRepository organizationInfoRepository) {
        this.loginUtil = loginUtil;
        this.organizationInfoRepository = organizationInfoRepository;
    }

    @GetMapping
    @RequiresPermissions("instability:organization-info:index")
    public String index() {
        return "/instability/organization-info";
    }

    @PostMapping("/fuzzy")
    @RequiresPermissions("instability:organization-info:fuzzy")
    @ResponseBody
    public Result<PagingItems<OrganizationInfo>> fuzzy(@RequestBody JsonObject data) {
        Result<PagingItems<OrganizationInfo>> pagingResult = new Result<>();

        int count = data.get("count").getAsInt();
        int index = data.get("index").getAsInt();
        String regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        pagingResult.data = organizationInfoRepository.fuzzy(count, index, regionFullName, loginUtil.getUser());

        return pagingResult;
    }

    @PostMapping(value = "/save")
    @RequiresPermissions("instability:organization-info:save")
    @ResponseBody
    public Result<String> save(@RequestBody OrganizationInfo item) {
        return organizationInfoRepository.saveOrUpdate(item, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @RequiresPermissions("instability:organization-info:remove")
    @ResponseBody
    public Result<OrganizationInfo> remove(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();
        return organizationInfoRepository.remove(id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @RequiresPermissions("instability:organization-info:import")
    @ResponseBody
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile multipartFile) throws Exception {
        return organizationInfoRepository.importFromExcel(multipartFile, 0, 1, loginUtil.getUser());
    }
}
