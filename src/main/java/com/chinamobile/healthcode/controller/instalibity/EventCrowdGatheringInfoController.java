package com.chinamobile.healthcode.controller.instalibity;

import com.chinamobile.healthcode.model.instability.EventCrowdGatheringInfo;
import com.chinamobile.healthcode.repository.instability.EventCrowdGatheringInfoRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

@Controller
@RequestMapping("instability/event-crowd-gathering-info")
public class EventCrowdGatheringInfoController {
    private final LoginUtil loginUtil;
    private final EventCrowdGatheringInfoRepository eventInfoRepository;

    public EventCrowdGatheringInfoController(LoginUtil loginUtil, EventCrowdGatheringInfoRepository eventInfoRepository) {
        this.loginUtil = loginUtil;
        this.eventInfoRepository = eventInfoRepository;
    }

    @GetMapping
    @RequiresPermissions("instability:event-crowd-gathering-info:index")
    public String index() {
        return "/instability/event-crowd-gathering-info-table";
    }

    @GetMapping(value = "/form")
    @RequiresPermissions(value = "instability:event-crowd-gathering-info:save")
    public String form() {
        return "/instability/event-crowd-gathering-info-form";
    }

    @PostMapping("/fuzzy")
    @RequiresPermissions("instability:event-crowd-gathering-info:fuzzy")
    @ResponseBody
    public Result<PagingItems<EventCrowdGatheringInfo>> fuzzy(@RequestBody JsonObject data) {
        Result<PagingItems<EventCrowdGatheringInfo>> pagingResult = new Result<>();

        int count = data.get("count").getAsInt();
        int index = data.get("index").getAsInt();
        String regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String category = Optional.ofNullable(data.get("category"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String type = Optional.ofNullable(data.get("type"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String title = Optional.ofNullable(data.get("title"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String overview = Optional.ofNullable(data.get("overview"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        pagingResult.data = eventInfoRepository.fuzzy(count, index, regionFullName, category, type, title, overview, loginUtil.getUser());

        return pagingResult;
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "instability:event-crowd-gathering-info:get")
    public Result<EventCrowdGatheringInfo> get(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();

        return eventInfoRepository.get(id);
    }

    @PostMapping(value = "/categories")
    @ResponseBody
    public Result<JsonObject> categories() {
        return eventInfoRepository.getCategories();
    }

    @PostMapping(value = "/save")
    @RequiresPermissions("instability:event-crowd-gathering-info:save")
    @ResponseBody
    public Result<String> save(@RequestBody EventCrowdGatheringInfo item) {
        return eventInfoRepository.saveOrUpdate(item, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @RequiresPermissions("instability:event-crowd-gathering-info:remove")
    @ResponseBody
    public Result<EventCrowdGatheringInfo> remove(@RequestBody JsonObject data) {
        String id = data.get("id").getAsString();
        return eventInfoRepository.remove(id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @RequiresPermissions("instability:event-crowd-gathering-info:import")
    @ResponseBody
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile multipartFile) throws Exception {
        return eventInfoRepository.importFromExcel(multipartFile, 0, 1, loginUtil.getUser());
    }
}
