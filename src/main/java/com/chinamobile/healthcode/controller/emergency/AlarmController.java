package com.chinamobile.healthcode.controller.emergency;

import com.chinamobile.healthcode.model.Task;
import com.chinamobile.healthcode.model.iot.Alarm;
import com.chinamobile.healthcode.model.iot.Interphone;
import com.chinamobile.healthcode.model.subject.PropertyDescription;
import com.chinamobile.healthcode.repository.TaskRepository;
import com.chinamobile.healthcode.repository.iot.AlarmRepository;
import com.chinamobile.healthcode.repository.iot.InterphoneRepository;
import com.chinamobile.healthcode.repository.subject.PropertyRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.jinq.tuples.Pair;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2/4/2024 11:51
 */
@Controller
@RequestMapping("emergency/alarm")
public class AlarmController {

    final DefaultUserRepository userRepository;
    final AlarmRepository alarmRepository;
    final PropertyRepository propertyRepository;
    final InterphoneRepository interphoneRepository;
    final TaskRepository taskRepository;

    final LoginUtil loginUtil;

    public AlarmController(
            DefaultUserRepository userRepository,
            AlarmRepository alarmRepository,
            PropertyRepository propertyRepository,
            InterphoneRepository interphoneRepository,
            TaskRepository taskRepository,
            LoginUtil loginUtil
    ) {
        this.userRepository = userRepository;
        this.alarmRepository = alarmRepository;
        this.propertyRepository = propertyRepository;
        this.interphoneRepository = interphoneRepository;
        this.taskRepository = taskRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "/task")
    public String task() {
        return "emergency/alarm/task";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @Transactional(readOnly = true)
    public Result<Pair<Alarm, List<DefaultUser>>> get(@RequestBody JsonObject data) {
        Result<Pair<Alarm, List<DefaultUser>>> _dto = new Result<>();

        String _id = data.get("id").getAsString();
        boolean _brief = Optional.ofNullable(data.get("brief"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsBoolean)
                .orElse(true);
        Result<Alarm> _item = alarmRepository.get(_id, _brief);
        if (!_item.isOK()) {
            return _dto.pack(_item);
        }

        List<String> _assigneeIds = taskRepository.stream(Task.ENUM_TYPE.设备告警, Collections.singletonList(_id), null, null)
                .select(i -> i.getAssigneeId())
                .toList();
        List<DefaultUser> _assignees = userRepository.query(_assigneeIds, null, null, null, null, null, null);

        _dto.data = new Pair<>(_item.data, _assignees);
        return _dto;
    }

    @PostMapping(value = "/statistics/total")
    @ResponseBody
    public Result<Long> find(@RequestBody JsonObject data) {
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _from = Optional.ofNullable(data.get("from"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString())).orElse(null);
        Date _to = Optional.ofNullable(data.get("to"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString())).orElse(null);
        Task.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : Task.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);

        Result<Long> _total = new Result<>();
        _total.data = alarmRepository.find(1, 0, _type, _regionFullName, _from, _to, _status, loginUtil.getUser()).total;
        return _total;
    }

    @PostMapping(value = "/statistics/distribution")
    @ResponseBody
    public Result<List<Pair<String, Long>>> distribution(@RequestBody JsonObject data) {
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _from = Optional.ofNullable(data.get("from"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString())).orElse(null);
        Date _to = Optional.ofNullable(data.get("to"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString())).orElse(null);

        Result<List<Pair<String, Long>>> _counts = new Result<>();
        _counts.data = alarmRepository.distribution(_regionFullName, _from, _to, loginUtil.getUser());
        return _counts;
    }

}
