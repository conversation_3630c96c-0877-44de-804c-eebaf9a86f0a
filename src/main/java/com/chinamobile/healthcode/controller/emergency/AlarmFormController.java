package com.chinamobile.healthcode.controller.emergency;

import com.chinamobile.healthcode.model.emergency.AlarmForm;
import com.chinamobile.healthcode.model.emergency.AlarmFormQueryDto;
import com.chinamobile.healthcode.model.project.FormQueryDto;
import com.chinamobile.healthcode.repository.emergency.AlarmFormRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 1/31/2024 9:27
 */
@Controller
@RequestMapping(value = "emergency/alarm/form")
public class AlarmFormController {

    final AlarmFormRepository alarmFormRepository;
    final AbstractMediaRepository mediaRepository;
    final LoginUtil loginUtil;

    public AlarmFormController(
            AlarmFormRepository alarmFormRepository,
            AbstractMediaRepository mediaRepository,
            LoginUtil loginUtil
    ) {
        this.alarmFormRepository = alarmFormRepository;
        this.mediaRepository = mediaRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping
    @RequiresPermissions(value = "emergency:alarm:form:index")
    public String forms() {
        return "emergency/alarm/forms";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "emergency:alarm:form:get")
    public Result<AlarmForm> get(@RequestBody JsonObject data) {
        String _id = Optional.ofNullable(data.get("id"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String _alarmId = Optional.ofNullable(data.get("alarmId"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String _taskId = Optional.ofNullable(data.get("taskId"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        boolean _brief = Optional.ofNullable(data.get("brief"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsBoolean)
                .orElse(false);

        Result<AlarmForm> _item = alarmFormRepository.get(_id, _alarmId, _taskId, _brief, loginUtil.getUserId());
        if (_item.isOK()) {
            alarmFormRepository.parseActors(Collections.singletonList(_item.data));
        }

        return _item;
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    @RequiresPermissions(value = "emergency:alarm:form:fuzzy")
    public Result<PagingItems<AlarmForm>> fuzzy(@RequestBody JsonObject data) {
        int _count = Optional.ofNullable(data.get("count"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(10);
        int _index = Optional.ofNullable(data.get("index"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(0);
        String _alarmId = Optional.ofNullable(data.get("alarmId"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String _taskId = Optional.ofNullable(data.get("taskId"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        Gson gson = new GsonBuilder()
                .registerTypeAdapter(Date.class, new FormQueryDto.JsonDateDeserializer())
                .create();
        AlarmFormQueryDto _alarmFormQueryDto = gson.fromJson(data, AlarmFormQueryDto.class);

        Result<PagingItems<AlarmForm>> _page = new Result<>();
        _page.data = alarmFormRepository.fuzzy(_count, _index, _alarmId, _taskId, _alarmFormQueryDto, loginUtil.getUser());
        alarmFormRepository.parseActors(_page.data.items);
        return _page;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "emergency:alarm:form:save")
    public Result<String> save(@Validated @RequestBody AlarmForm item) {
        return alarmFormRepository.save(item, loginUtil.getUser());
    }

    @PostMapping(value = "/save/files")
    @ResponseBody
    @RequiresPermissions(value = "emergency:alarm:form:save")
    public Result<String> saveWithFiles(@RequestPart(value = "files", required = false) MultipartFile[] files, @Validated @RequestPart AlarmForm item) throws IOException {
        List<File> _files = new ArrayList<>();
        if (files != null) {
            for (MultipartFile i : files) {
                File _file = new File(FileUtils.getTempDirectory() + File.separator + i.getOriginalFilename());
                FileUtils.copyInputStreamToFile(i.getInputStream(), _file);

                _files.add(_file);
            }
        }

        List<String> _attachmentIds = new ArrayList<>();
        for (File i : _files) {
            Result<Media> _media = mediaRepository.add(null, i, true, loginUtil.getUserId());
            if (_media.isOK()) {
                _attachmentIds.add(_media.data.getId());
            }
        }

        if (CollectionUtils.isEmpty(item.getAttachmentIds())) {
            item.setAttachmentIds(_attachmentIds);
        } else {
            item.getAttachmentIds().addAll(_attachmentIds);
        }

        return alarmFormRepository.save(item, loginUtil.getUser());
    }

}