package com.chinamobile.healthcode.controller.emergency;

import com.chinamobile.healthcode.model.Task;
import com.chinamobile.healthcode.model.iot.Alarm;
import com.chinamobile.healthcode.model.iot.Interphone;
import com.chinamobile.healthcode.model.subject.PropertyDescription;
import com.chinamobile.healthcode.repository.iot.AlarmRepository;
import com.chinamobile.healthcode.repository.iot.InterphoneRepository;
import com.chinamobile.healthcode.repository.subject.PropertyRepository;
import com.chinamobile.healthcode.service.WebSocketClientService;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.WebSocketMessagePayload;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.springboot.web.ShiroHandshakeInterceptorImpl;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.apache.shiro.web.subject.support.WebDelegatingSubject;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class AlarmWebSocketHandler extends AbstractWebSocketHandler {

    final AlarmRepository alarmRepository;
    final PropertyRepository propertyRepository;
    final InterphoneRepository interphoneRepository;
    final WebSocketClientService webSocketClientService;

    public AlarmWebSocketHandler(
            AlarmRepository alarmRepository,
            PropertyRepository propertyRepository,
            InterphoneRepository interphoneRepository,
            WebSocketClientService webSocketClientService
    ) {
        this.alarmRepository = alarmRepository;
        this.propertyRepository = propertyRepository;
        this.interphoneRepository = interphoneRepository;
        this.webSocketClientService = webSocketClientService;
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        WebSocketMessagePayload<JsonObject> _payload = ConverterUtil.json2Object(message.getPayload(), new TypeToken<WebSocketMessagePayload<JsonObject>>() {
        }.getType());

        switch (_payload.getSubject()) {
            case "alarm_register":
            case "register_alarm":
                String _regionFullName = _payload.getData().get("regionFullName").getAsString();

                webSocketClientService.registerAlarmClient(_regionFullName, session);

                break;
            case "find":
                find(session, _payload.getData());

                break;
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        webSocketClientService.unregisterClient(session);
    }

    void find(WebSocketSession session, JsonObject data) throws IOException {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _regionFullName = Optional.ofNullable(data.get("regionFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _from = Optional.ofNullable(data.get("from"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString())).orElse(null);
        Date _to = Optional.ofNullable(data.get("to"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString())).orElse(null);
        Task.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : Task.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);

        PagingItems<Alarm> _page = alarmRepository.find(_count, _index, _type, _regionFullName, _from, _to, _status, parse(session));

        List<Alarm.ENUM_DEVICE_TYPE> _types = Arrays.asList(Alarm.ENUM_DEVICE_TYPE.CAMERA, Alarm.ENUM_DEVICE_TYPE.SMOKE_DETECTOR);
        List<String> _deviceIds = _page.items.stream()
                .filter(i -> _types.contains(i.getDeviceType()))
                .map(Alarm::getDeviceId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(_deviceIds)) {
            List<PropertyDescription> _devices = propertyRepository.query(_deviceIds);

            _page.items.stream()
                    .filter(i -> _deviceIds.contains(i.getDeviceId()))
                    .forEach(i -> _devices.stream()
                            .filter(j -> Objects.equals(i.getDeviceId(), j.getId()))
                            .findFirst()
                            .ifPresent(i::setDevice));
        }

        List<Alarm.ENUM_DEVICE_TYPE> _types2 = Collections.singletonList(Alarm.ENUM_DEVICE_TYPE.INTERPHONE);
        List<String> _deviceIds2 = _page.items.stream()
                .filter(i -> _types2.contains(i.getDeviceType()))
                .map(Alarm::getDeviceId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(_deviceIds)) {
            List<Interphone> _devices = interphoneRepository.find(_deviceIds2, null);

            _page.items.stream()
                    .filter(i -> _deviceIds2.contains(i.getDeviceId()))
                    .forEach(i -> _devices.stream()
                            .filter(j -> Objects.equals(i.getDeviceId(), j.getId()))
                            .findFirst()
                            .ifPresent(i::setDevice));
        }

        WebSocketMessagePayload<PagingItems<JsonObject>> _temp = new WebSocketMessagePayload<>();
        _temp.setSubject("find");

        PagingItems<JsonObject> __temp = new PagingItems<>(_page.count, _page.index);
        __temp.total = _page.total;
        __temp.items = _page.items.stream()
                .map(i -> (JsonObject) ConverterUtil.gson.toJsonTree(i))
                .collect(Collectors.toList());
        _temp.setData(__temp);

        session.sendMessage(new TextMessage(ConverterUtil.toJson(_temp)));
    }

    DefaultUser parse(WebSocketSession session) {
        return (DefaultUser) Optional.ofNullable(session.getAttributes().get(ShiroHandshakeInterceptorImpl.SUBJECT_KEY))
                .map(i -> ((WebDelegatingSubject) i).getPrincipal())
                .filter(i -> i instanceof DefaultUser)
                .orElse(null);
    }

}
