package com.chinamobile.healthcode.controller.emergency;

import com.chinamobile.healthcode.repository.emergency.UnitManagerRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 1/31/2024 10:22
 */
@Controller("emergencyUnitController")
@RequestMapping(value = "emergency/unit")
public class UnitController {
    final RoleRepository roleRepository;
    final UnitManagerRepository unitManagerRepository;
    final LoginUtil loginUtil;

    public UnitController(RoleRepository roleRepository,
                          UnitManagerRepository unitManagerRepository,
                          LoginUtil loginUtil) {
        this.roleRepository = roleRepository;
        this.unitManagerRepository = unitManagerRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/operate")
    @ResponseBody
    public Result<Boolean> operate() {
        Result<Boolean> _success = new Result<>();
        _success.data = roleRepository.isUserInRole(loginUtil.getUserId(), UnitManagerRepository.UNIT_ADMIN_ROLE);
        return _success;
    }

    @GetMapping(value = "/index")
    @RequiresPermissions(value = "emergency:unit:index")
    public String index() {
        return "emergency/unit/index";
    }

    @PostMapping(value = "/fuzzy")
    @RequiresPermissions(value = "emergency:unit:fuzzy")
    @ResponseBody
    public Result<PagingItems<DefaultDepartment>> fuzzy(@RequestBody JsonObject data) {
        Result<PagingItems<DefaultDepartment>> _r = new Result<>();
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _regionId = Optional.ofNullable(data.get("regionId"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        _r.data = unitManagerRepository.fuzzyUnits(_count, _index, _regionId, _name);
        return _r;
    }

    @GetMapping(value = "/managers")
    @RequiresPermissions(value = "emergency:unit:manager:index")
    public String appointment() {
        return "emergency/unit/managers";
    }

    @PostMapping(value = "/manager/query")
    @ResponseBody
    @RequiresPermissions(value = "emergency:unit:manager:query")
    public Result<List<DefaultUser>> managers(@RequestBody JsonObject data) {
        String _regionId = data.get("regionId").getAsString();

        Result<List<DefaultUser>> _users = new Result<>();
        _users.data = unitManagerRepository.list(_regionId);

        return _users;
    }

    @PostMapping(value = "/manager/add")
    @ResponseBody
    @RequiresPermissions(value = "emergency:unit:manager:add")
    public Result<Void> addManager(@RequestBody JsonObject data) {
        String _regionId = data.get("regionId").getAsString();
        String _userId = data.get("userId").getAsString();

        return unitManagerRepository.save(_regionId, _userId, loginUtil.getUserId());
    }

    @PostMapping(value = "/manager/remove")
    @ResponseBody
    @RequiresPermissions(value = "emergency:unit:manager:remove")
    public Result<Void> removeManager(@RequestBody JsonObject data) {
        String _regionId = data.get("regionId").getAsString();
        String _userId = data.get("userId").getAsString();

        return unitManagerRepository.delete(_regionId, _userId);
    }
}
