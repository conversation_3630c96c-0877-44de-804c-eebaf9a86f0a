package com.chinamobile.healthcode.controller;

import com.chinamobile.healthcode.model.DataOperationLogEntity;
import com.chinamobile.healthcode.repository.DataOperationLogRepository;
import com.chinamobile.healthcode.service.DataOperationLogService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 数据操作日志控制器
 */
@RestController
@RequestMapping("/api/data-operation-logs")
public class DataOperationLogController {

    private final DataOperationLogRepository dataOperationLogRepository;

    public DataOperationLogController(DataOperationLogRepository dataOperationLogRepository) {
        this.dataOperationLogRepository = dataOperationLogRepository;
    }

    /**
     * 根据ID查询操作日志
     */
    @GetMapping("/{id}")
    public Result<DataOperationLogEntity> getById(@PathVariable String id) {
        return dataOperationLogRepository.findById(id);
    }

    /**
     * 删除操作日志
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(@PathVariable String id) {
        return dataOperationLogRepository.deleteById(id);
    }

    /**
     * 清理过期日志
     */
    @PostMapping("/clean-expired")
    public Result<Integer> cleanExpiredLogs(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date expireTime) {
        int count = 0;
        try {
            // 这里需要实现具体的清理逻辑
            // count = dataOperationLogService.cleanExpiredLogs(expireTime);
        } catch (Exception e) {
            Result<Integer> result = new Result<>();
            result.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{e.getMessage()});
            return result;
        }
        
        Result<Integer> result = new Result<>();
        result.data = count;
        return result;
    }
} 