package com.chinamobile.healthcode.controller.project;

import com.chinamobile.healthcode.model.project.Target;
import com.chinamobile.healthcode.repository.project.TargetRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Tuple4;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "project/target")
public class TargetController {

    final TargetRepository targetRepository;
    final LoginUtil loginUtil;

    public TargetController(TargetRepository targetRepository, LoginUtil loginUtil) {
        this.targetRepository = targetRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/get")
    @ResponseBody
    public Result<Tuple4<<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>>> get(@RequestBody JsonObject data) {
        String _projectId = data.get("projectId").getAsString();

        Result<Tuple4<Boolean, Boolean, Boolean, Target>> _result = new Result<>();
        _result.data = targetRepository.getByProjectId(_projectId, loginUtil.getUser());
        return _result;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "project:target:save")
    public Result<String> save(@Validated @RequestBody Target item) {
        return targetRepository.save(item, loginUtil.getUser());
    }

}