package com.chinamobile.healthcode.controller.project;

import com.chinamobile.healthcode.model.Task;
import com.chinamobile.healthcode.model.project.Record;
import com.chinamobile.healthcode.model.project.Target;
import com.chinamobile.healthcode.repository.TaskRepository;
import com.chinamobile.healthcode.repository.project.RecordRepository;
import com.chinamobile.healthcode.repository.project.TargetRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "project/record")
public class RecordController {

    final RecordRepository recordRepository;
    final TargetRepository targetRepository;
    final TaskRepository taskRepository;
    final DictionaryRepository dictionaryRepository;
    final LoginUtil loginUtil;

    public RecordController(RecordRepository recordRepository, TargetRepository targetRepository, TaskRepository taskRepository, DictionaryRepository dictionaryRepository, LoginUtil loginUtil) {
        this.recordRepository = recordRepository;
        this.targetRepository = targetRepository;
        this.taskRepository = taskRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "project:record:index")
    public String index() {
        return "project/record";
    }

    /**
     * 编辑器视图
     *
     * @return
     */
    @GetMapping(value = "/editor")
    @RequiresPermissions(value = "project:record:editor")
    public String editor() {
        return "project/record-editor";
    }

    /**
     * 任务视图
     *
     * @return
     */
    @GetMapping(value = "/task")
    public String task() {
        return "project/record-task";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "project:record:get")
    public Result<Record> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        boolean _brief = Optional.ofNullable(data.get("brief"))
                .map(i -> i.isJsonNull() ? false : i.getAsBoolean()).orElse(false);

        return recordRepository.get(_id, _brief, loginUtil.getUserId());
    }

    @PostMapping(value = "/get/definition")
    @ResponseBody
    @RequiresPermissions(value = "project:record:get")
    public Result<JsonArray> getDefinition(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return recordRepository.getDefinition(_id);
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    @RequiresPermissions(value = "project:record:fuzzy")
    public Result<PagingItems<Record>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _title = Optional.ofNullable(data.get("title"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Record.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : Record.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);

        Result<PagingItems<Record>> _page = new Result<>();
        _page.data = recordRepository.fuzzy(_count, _index, _title, _status, loginUtil.getUser());
        recordRepository.parseActors(_page.data.items);
        return _page;
    }

    @PostMapping(value = "/save", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseBody
    @RequiresPermissions(value = "project:record:save")
    public Result<String> save(@RequestPart @Validated Record item, @RequestPart(required = false) MultipartFile[] legendFiles) throws NoSuchFieldException, IllegalAccessException, IOException {
        return recordRepository.saveWithFiles(item, loginUtil.getUserId(), null, legendFiles);
    }

    @PostMapping(value = "/update-deadline")
    @ResponseBody
    @RequiresPermissions(value = "project:record:save")
    public Result<String> updateDeadline(@RequestBody @Validated Record item) throws NoSuchFieldException, IllegalAccessException {
        return recordRepository.save(item, loginUtil.getUserId(), Arrays.asList("deadline"));
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "project:record:remove")
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return recordRepository.remove(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/launch")
    @ResponseBody
    @RequiresPermissions(value = "project:record:save")
    public Result<String> launch(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return recordRepository.launch(_id, loginUtil.getUser());
    }

    @PostMapping(value = "/close")
    @ResponseBody
    @RequiresPermissions(value = "project:record:save")
    public Result close(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return recordRepository.close(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/urge")
    @ResponseBody
    @RequiresPermissions(value = "project:record:urge")
    public Result urge(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        recordRepository.urge(_id, loginUtil.getUserId());

        return new Result();
    }

    @PostMapping(value = "/type")
    @ResponseBody
    public Result<String> type() {
        Result<String> _config = new Result<>();
        _config.data = dictionaryRepository.getVal("专项工作", "任务类别", true);
        return _config;
    }

    @PostMapping(value = "/stat/task")
    @ResponseBody
    public Result<List<Tuple4<String, String, String, Long>>> statTasks(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _rootDeptId = Optional.ofNullable(data.get("rootDeptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Tuple4<String, String, String, Long>>> _counts = new Result<>();
        _counts.data = recordRepository.drillDownTaskAll(_id, _rootDeptId, loginUtil.getUser());
        return _counts;
    }

    @PostMapping(value = "/stat/task/read")
    @ResponseBody
    public Result<List<Tuple4<String, String, String, Long>>> statTasksRead(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _rootDeptId = Optional.ofNullable(data.get("rootDeptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Tuple4<String, String, String, Long>>> _counts = new Result<>();
        _counts.data = recordRepository.drillDownTaskRead(_id, _rootDeptId, loginUtil.getUser());
        return _counts;
    }

    @PostMapping(value = "/stat/task/complete")
    @ResponseBody
    public Result<List<Tuple3<String, Long, Long>>> statTaskComplete(@RequestBody JsonObject data) {
        Integer _topN = Optional.ofNullable(data.get("topN"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt())
                .orElse(null);

        Result<List<Tuple3<String, Long, Long>>> _pc = new Result<>();
        _pc.data = recordRepository.statTaskComplete(_topN, loginUtil.getUser());
        return _pc;
    }

    @PostMapping(value = "/stat/task/target")
    @ResponseBody
    public Result<List<Tuple4<String, String, String, Long>>> statTarget(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _rootDeptId = Optional.ofNullable(data.get("rootDeptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Tuple4<String, String, String, Long>>> _counts = new Result<>();
        _counts.data = targetRepository.thenTF(_id, _rootDeptId, loginUtil.getUser());
        return _counts;
    }

    @PostMapping(value = "/task/receive")
    @ResponseBody
    @RequiresPermissions(value = "project:record:task:action")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Result receive(@RequestBody Target item) {
        Result _success = new Result();

        Result<String> _id = targetRepository.save(item, loginUtil.getUser());
        if (!_id.isOK()) {
            return _success.pack(_id);
        }

        _success = taskRepository.readPeers(item.getTaskId(), loginUtil.getUser());
        if (!_success.isOK()) {
            // 回滚事务
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

        return _success;
    }

    @PostMapping(value = "/task/qualify")
    @ResponseBody
    @RequiresPermissions(value = "project:record:task:action")
    public Result<Tuple3<Long, Long, Boolean>> qualify(@RequestBody JsonObject data) {
        String _taskId = data.get("taskId").getAsString();

        Result<Tuple3<Long, Long, Boolean>> _qualified = new Result<>();
        _qualified.data = targetRepository.qualify(_taskId);
        return _qualified;
    }

    @PostMapping(value = "/task/close")
    @ResponseBody
    @RequiresPermissions(value = "project:record:task:action")
    @Transactional
    public Result<Integer> closeTasks(@RequestBody JsonObject data) {
        String _taskId = data.get("taskId").getAsString();

        Result<Integer> _count = new Result<>();
        _count.data = taskRepository.doneChildren(_taskId, loginUtil.getUser().getDeptFullName(), loginUtil.getUserId());
        return _count;
    }

    @PostMapping(value = "/task/retract")
    @ResponseBody
    @RequiresPermissions(value = "project:record:task:action")
    @Transactional
    public Result<Integer> retractTasks(@RequestBody JsonObject data) {
        String _subjectId = data.get("subjectId").getAsString();

        Result<Integer> _count = new Result<>();
        _count.data = taskRepository.retractChildren(_subjectId, loginUtil.getUser().getDeptFullName(), loginUtil.getUserId());
        return _count;
    }

    @PostMapping(value = "/configs")
    @ResponseBody
    public Result<List<Dictionary>> configs() {
        Result<List<Dictionary>> _configs = new Result<>();
        _configs.data = dictionaryRepository.fuzzy("专项工作配置", null, true, null);
        return _configs;
    }

    @PostMapping("/stats/my")
    @ResponseBody
    public Result<List<Tuple3<Task, Long, Long>>> myStats() {
        return recordRepository.myStats(loginUtil.getUser());
    }

}
