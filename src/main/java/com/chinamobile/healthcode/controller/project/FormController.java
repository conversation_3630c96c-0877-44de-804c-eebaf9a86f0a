package com.chinamobile.healthcode.controller.project;

import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.healthcode.model.project.FormQueryDto;
import com.chinamobile.healthcode.repository.project.FormRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.*;
import org.apache.commons.io.FileUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Tuple4;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "project/form")
public class FormController {

    final FormRepository formRepository;
    final AbstractMediaRepository mediaRepository;
    final LoginUtil loginUtil;

    public FormController(FormRepository formRepository, AbstractMediaRepository mediaRepository, LoginUtil loginUtil) {
        this.formRepository = formRepository;
        this.mediaRepository = mediaRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping
    @RequiresPermissions(value = "project:form:index")
    public String forms() {
        return "project/forms";
    }

    @GetMapping("/view")
    @RequiresPermissions(value = "project:form:index")
    public String form() {
        return "project/form";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "project:form:get")
    public Result<Form> get(@RequestBody JsonObject data) {
        String _id = Optional.ofNullable(data.get("id"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        return formRepository.get(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    @RequiresPermissions(value = "project:form:fuzzy")
    public Result<PagingItems<Form>> fuzzy(@RequestBody JsonObject data) {
        int _count = Optional.ofNullable(data.get("count"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(10);
        int _index = Optional.ofNullable(data.get("index"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(0);
        String _projectId = Optional.ofNullable(data.get("projectId"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        String _taskId = Optional.ofNullable(data.get("taskId"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);

        Gson gson = new GsonBuilder()
                .registerTypeAdapter(Date.class, new FormQueryDto.JsonDateDeserializer())
                .create();
        FormQueryDto _formQueryDto = gson.fromJson(data, FormQueryDto.class);

        Result<PagingItems<Form>> _page = new Result<>();
        _page.data = formRepository.fuzzy(_count, _index, _projectId, _taskId, _formQueryDto, loginUtil.getUser());
        formRepository.parseActors(_page.data.items);
        return _page;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "project:form:save")
    public Result<String> save(@Validated @RequestBody Form item) {
        return formRepository.save(item, loginUtil.getUser());
    }

    @PostMapping(value = "/save/files")
    @ResponseBody
    @RequiresPermissions(value = "project:form:save")
    public Result<String> saveWithFiles(@RequestPart(value = "files", required = false) MultipartFile[] files, @Validated @RequestPart Form item) throws IOException {
        List<File> _files = new ArrayList<>();
        if (files != null) {
            for (MultipartFile i : files) {
                File _file = new File(FileUtils.getTempDirectory() + File.separator + i.getOriginalFilename());
                FileUtils.copyInputStreamToFile(i.getInputStream(), _file);

                _files.add(_file);
            }
        }

        List<String> _attachmentIds = new ArrayList<>();
        for (File i : _files) {
            Result<Media> _media = mediaRepository.add(null, i, true, loginUtil.getUserId());
            if (_media.isOK()) {
                _attachmentIds.add(_media.data.getId());
            }
        }

        if (CollectionUtils.isEmpty(item.getAttachmentIds())) {
            item.setAttachmentIds(_attachmentIds);
        } else {
            item.getAttachmentIds().addAll(_attachmentIds);
        }

        return formRepository.save(item, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "project:form:remove")
    public Result<Void> remove(@RequestBody JsonObject data) {
        String _id = Optional.ofNullable(data.get("id"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsString)
                .orElse(null);
        return formRepository.remove(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/statistic")
    @ResponseBody
    public Result<List<Tuple4<String, String, String, Long>>> tf(@RequestBody JsonObject data) {
        String _projectId = data.get("projectId").getAsString();
        String _deptId = Optional.ofNullable(data.get("deptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Tuple4<String, String, String, Long>>> _counts = new Result<>();
        _counts.data = formRepository.thenTF(_projectId, _deptId, loginUtil.getUser());
        return _counts;
    }

    @PostMapping("/init/count")
    @RequiresPermissions(value = "project:form:fuzzy")
    @ResponseBody
    public Result<Long> countInitForms(@RequestBody JsonObject data) {
        String _projectId = data.get("projectId").getAsString();
        Result _result = new Result();
        _result.data = formRepository.countInitForms(_projectId, loginUtil.getUser());
        return _result;
    }

}
