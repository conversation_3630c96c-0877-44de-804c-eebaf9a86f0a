package com.chinamobile.healthcode.controller.citizen;

import com.chinamobile.healthcode.model.citizen.CitizenForm;
import com.chinamobile.healthcode.model.citizen.Grid;
import com.chinamobile.healthcode.repository.citizen.FormRepository;
import com.chinamobile.healthcode.repository.citizen.GridRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;

@Controller(value = "citizenFormController")
@RequestMapping(value = "citizen/form")
public class FormController {

    final FormRepository formRepository;
    final GridRepository gridRepository;
    final DepartmentRepository departmentRepository;
    final DefaultRoleRepository roleRepository;
    final LoginUtil loginUtil;

    public FormController(FormRepository formRepository, GridRepository gridRepository, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository, LoginUtil loginUtil) {
        this.formRepository = formRepository;
        this.gridRepository = gridRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "citizen:form:index")
    public String index(@RequestParam(value = "id", required = false) String id) {
        return StringUtils.isEmpty(id) ? "citizen/forms" : "citizen/form";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    public Result<CitizenForm> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<CitizenForm> _item = formRepository.get(_id);
        if (!_item.isOK()) {
            return _item;
        }

        // 脱敏
        if (!roleRepository.isUserInRoleCached(loginUtil.getUserId(), FormRepository.ADMIN_ROLE) && !Objects.equals(loginUtil.getUserId(), _item.data.getCreatorId())) {
            if (_item.data.getCredentialNo() != null && _item.data.getCredentialNo().length() > 16) {
                _item.data.setCredentialNo(_item.data.getCredentialNo().substring(0, 2) + "**************" + _item.data.getCredentialNo().substring(16));
            }
        }

        return _item;
    }

    @PostMapping(value = "/add")
    @ResponseBody
    public Result<String> add(@RequestBody JsonObject data) {
        CitizenForm _item = ConverterUtil.json2Object(data.get("item").toString(), CitizenForm.class);

        return formRepository.add(_item, loginUtil.getUser());
    }

    @PostMapping(value = "/add/agent")
    @ResponseBody
    public Result<String> addByAgent(@RequestBody JsonObject data) {
        CitizenForm _item = ConverterUtil.json2Object(data.get("item").toString(), CitizenForm.class);

        return formRepository.addByAgent(_item, loginUtil.getUserId());
    }

    @PostMapping(value = "/query")
    @ResponseBody
    public Result<PagingItems<CitizenForm>> query(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH:mm:ss")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH:mm:ss")).orElse(null);

        Result<PagingItems<CitizenForm>> _page = new Result<>();
        _page.data = formRepository.queryByUserId(_count, _index, _beginTime, _endTime, loginUtil.getUserId());
        return _page;
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    public Result<PagingItems<CitizenForm>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<SortField> _sortFields = (List<SortField>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new com.google.gson.reflect.TypeToken<List<SortField>>() {
                }.getType())).orElse(null);
        String _grid = Optional.ofNullable(data.get("grid"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Boolean _gridType = Optional.ofNullable(data.get("gridType"))
                .map(i -> i.isJsonNull() ? null : i.getAsBoolean()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _credentialNo = Optional.ofNullable(data.get("credentialNo"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _subdistrictFullName = Optional.ofNullable(data.get("subdistrictFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _crowdId = Optional.ofNullable(data.get("crowdId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);

        Result<PagingItems<CitizenForm>> _page = new Result<>();
        _page.data = formRepository.fuzzy(_count, _index, _sortFields, _grid, _gridType, _name, _credentialNo, _mp, _subdistrictFullName, _crowdId, _beginTime, _endTime, loginUtil.getUser());

        // 脱敏
        if (!roleRepository.isUserInRoleCached(loginUtil.getUserId(), FormRepository.ADMIN_ROLE)) {
            for (CitizenForm i : _page.data.items) {
                if (i.getCredentialNo() != null && i.getCredentialNo().length() > 16) {
                    i.setCredentialNo(i.getCredentialNo().substring(0, 2) + "**************" + i.getCredentialNo().substring(16));
                }
            }
        }


        return _page;
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return formRepository.remove(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/import")
    @ResponseBody
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile file) throws Exception {
        File _file = null;

        try {
            _file = new File(FileUtils.getTempDirectory() + File.separator + UUID.randomUUID() + "." + FilenameUtils.getExtension(file.getOriginalFilename()));
            FileUtils.copyInputStreamToFile(file.getInputStream(), _file);
            return formRepository.importFromExcel(_file, loginUtil.getUserId());
        } finally {
            if (_file != null) {
                _file.delete();
            }
        }
    }

    @RequiresPermissions(value = "citizen:form:export")
    @PostMapping(value = "/export/base64")
    @ResponseBody
    public Result<String> export(@RequestBody JsonObject data) throws IOException {
        String _grid = Optional.ofNullable(data.get("grid"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Boolean _gridType = Optional.ofNullable(data.get("gridType"))
                .map(i -> i.isJsonNull() ? null : i.getAsBoolean()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _credentialNo = Optional.ofNullable(data.get("credentialNo"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _subdistrictFullName = Optional.ofNullable(data.get("subdistrictFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _crowdId = Optional.ofNullable(data.get("crowdId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);

        Result<String> _base64 = new Result<>();
        _base64.data = formRepository.export(_grid, _gridType, _name, _credentialNo, _mp, _subdistrictFullName, _crowdId, _beginTime, _endTime, loginUtil.getUser());
        return _base64;
    }

    @PostMapping(value = "/{channel}/{gridId}/exist")
    @ResponseBody
    public Result<JsonObject> exist(@PathVariable(value = "channel") String channel, @PathVariable(value = "gridId") String gridId) {
        Result<JsonObject> _exist = new Result<>();
        _exist.data = new JsonObject();

        // 设置网格名称
        switch (channel) {
            case "dept":
                Result<Department> _department = departmentRepository.get(gridId, true);
                if (!_department.isOK()) {
                    return _exist.pack(_department);
                }

                _exist.data.addProperty("name", _department.data.getFullName());
                break;
            case "grid":
                Result<Grid> _grid = gridRepository.get(gridId);
                if (!_grid.isOK()) {
                    return _exist.pack(_grid);
                }

                _exist.data.addProperty("name", _grid.data.getFullName());
                _exist.data.addProperty("type", _grid.data.getType());
                break;
            default:
                _exist.setCode(Result.DATABASE_RECORD_NOT_FOUND);
                return _exist;
        }

        CitizenForm _item = formRepository.getByUser(gridId, loginUtil.getUser().getMp());
        if (_item == null) {
            _exist.data.addProperty("exist", false);
        } else {
            _exist.data.addProperty("exist", true);

            // 是否他地或他人打卡
            String _deptId = StringUtils.hasLength(_item.getDeptId()) ? _item.getDeptId() : _item.getGridId();
            CitizenForm.ENUM_ACTOR _actor = CitizenForm.ENUM_ACTOR.本人;
            _exist.data.addProperty("agent", !Objects.equals(gridId, _deptId) || _actor != _item.getActor());

            // 表单
            _exist.data.addProperty("form", ConverterUtil.toJson(_item));
        }

        return _exist;
    }

    @PostMapping(value = "/crowds")
    @ResponseBody
    public Result<List<Dictionary>> crowds() {
        Result<List<Dictionary>> _options = new Result<>();
        _options.data = formRepository.crowds();
        return _options;
    }

}
