package com.chinamobile.healthcode.controller.citizen;

import com.chinamobile.healthcode.model.citizen.Grid;
import com.chinamobile.healthcode.repository.citizen.GridRepository;
import com.chinamobile.healthcode.repository.grid.GeoRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.gson.JsonObject;
import org.apache.commons.io.IOUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "citizen")
public class GridController {

    final GridRepository gridRepository;
    final GeoRepository geoRepository;
    final DefaultRoleRepository roleRepository;
    final DefaultDepartmentRepository departmentRepository;
    final LoginUtil loginUtil;

    public GridController(GridRepository gridRepository, GeoRepository geoRepository, DefaultRoleRepository roleRepository, DefaultDepartmentRepository departmentRepository, LoginUtil loginUtil) {
        this.gridRepository = gridRepository;
        this.geoRepository = geoRepository;
        this.roleRepository = roleRepository;
        this.departmentRepository = departmentRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "/grid")
    @RequiresPermissions(value = "citizen:grid:index")
    public String grid() {
        return "citizen/grid/detail";
    }

    @GetMapping(value = "/grids")
    @RequiresPermissions(value = "citizen:grids:index")
    public String grids() {
        return "citizen/grid/list";
    }

    @PostMapping(value = "/grid/operate")
    @ResponseBody
    public Result operate() {
        Result _success = new Result();
        _success.data = roleRepository.isUserInRole(loginUtil.getUserId(), GridRepository.GRID_ADMIN_ROLE);
        return _success;
    }

    @PostMapping(value = "/grid/get")
    @ResponseBody
    @RequiresPermissions(value = "citizen:grid:get")
    public Result<Grid> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return gridRepository.get(_id);
    }

    @PostMapping(value = "/grid/add")
    @ResponseBody
    @RequiresPermissions(value = "citizen:grid:add")
    public Result add(@RequestBody JsonObject data) {
        Grid _item = ConverterUtil.json2Object(data.get("item").toString(), Grid.class);

        return gridRepository.add(_item, loginUtil.getUserId());
    }

    @PostMapping(value = "/grid/update")
    @ResponseBody
    @RequiresPermissions(value = "citizen:grid:update")
    public Result update(@RequestBody JsonObject data) {
        Grid _item = ConverterUtil.json2Object(data.get("item").toString(), Grid.class);

        return gridRepository.update(_item, loginUtil.getUserId());
    }

    @PostMapping(value = "/grid/fuzzy")
    @ResponseBody
    @RequiresPermissions(value = "citizen:grid:fuzzy")
    public Result<PagingItems<Grid>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _member = Optional.ofNullable(data.get("member"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PagingItems<Grid>> _page = new Result<>();
        _page.data = gridRepository.fuzzy(_count, _index, _name, _type, _member);
        return _page;
    }

    @PostMapping(value = "/grid/remove")
    @ResponseBody
    @RequiresPermissions(value = "citizen:grid:remove")
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return gridRepository.remove(_id, loginUtil.getUserId());
    }

    @GetMapping(value = "/grid/appointment")
    @RequiresPermissions(value = "citizen:grid:appointment")
    public String appointment() {
        return "citizen/grid/appointment";
    }

    @PostMapping(value = "/grid/user/query")
    @ResponseBody
    @RequiresPermissions(value = "citizen:grid:user:query")
    public Result getUsers(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<List<User>> _users = new Result<>();
        _users.data = gridRepository.getUsers(_id);
        return _users;
    }

    @PostMapping(value = "/grid/user/add")
    @ResponseBody
    @RequiresPermissions(value = "citizen:grid:user:add")
    public Result addUser(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _userId = data.get("userId").getAsString();

        return gridRepository.addUser(_id, _userId, loginUtil.getUserId());
    }

    @PostMapping(value = "/grid/user/remove")
    @ResponseBody
    @RequiresPermissions(value = "citizen:grid:user:remove")
    public Result removeUser(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return gridRepository.removeUser(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/grid/qrcode")
    @RequiresPermissions(value = "citizen:grid:qrcode")
    public void qrCode(HttpServletResponse response, @RequestBody JsonObject data) throws Exception {
        String _content = data.get("content").getAsString();
        String _desc = data.get("desc").getAsString();

        File _file = null;
        try {
            _file = gridRepository.getQRCode(_content, _desc);

            response.reset();
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(_file.getName()));

            IOUtils.copy(new FileInputStream(_file), response.getOutputStream());
        } finally {
            if (_file != null) {
                _file.delete();
            }
        }
    }

    @PostMapping(value = "/grid/qrcode/wx")
    @RequiresPermissions(value = "citizen:grid:qrcode")
    public void wxQRCode(HttpServletResponse response, @RequestBody JsonObject data) throws Exception {
        String _type = data.get("type").getAsString();
        String _id = data.get("id").getAsString();
        String _desc = data.get("desc").getAsString();
        String _page = Optional.ofNullable(data.get("page"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        File _file = null;
        try {
            _file = gridRepository.getWxMaQRCode(_type, _id, _desc, _page);

            response.reset();
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(_file.getName()));

            IOUtils.copy(new FileInputStream(_file), response.getOutputStream());
        } finally {
            if (_file != null) {
                _file.delete();
            }
        }
    }

    @GetMapping(value = "/grid/board")
    @RequiresPermissions(value = "citizen:grid:draw")
    public String board() {
        return "citizen/grid/board";
    }

    @PostMapping(value = "/grid/board/url")
    @ResponseBody
    public Result<String> boardUrl(@Value(value = "${citizen.grid.draw-url}") String url) {
        Result<String> _url = new Result<>();
        _url.data = url;
        return _url;
    }

    @GetMapping("/grid/survey")
    @RequiresPermissions("citizen:grid:survey:index")
    public String report() {
        return "citizen/grid/survey";
    }

    @PostMapping("/grid/survey/query")
    @ResponseBody
    @RequiresPermissions("citizen:grid:survey:fuzzy")
    public Result<List<GeoRepository.GridSurveyDTO>> querySurveys(@RequestBody JsonObject data) {
        String _regionId = Optional.ofNullable(data.get("regionId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(loginUtil.getUser().getDeptId());
        Integer _min = Optional.ofNullable(data.get("min"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Integer _max = Optional.ofNullable(data.get("max"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);

        Result<List<GeoRepository.GridSurveyDTO>> _surveys = new Result<>();
        _surveys.data = geoRepository.querySurveys(_regionId, _min, _max, loginUtil.getUser());
        return _surveys;
    }

    @PostMapping(value = "/grid/survey/export/base64")
    @ResponseBody
    @RequiresPermissions(value = "citizen:grid:survey:export")
    public Result<String> export(@RequestBody JsonObject data) throws IOException {
        String _regionId = Optional.ofNullable(data.get("regionId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(loginUtil.getUser().getDeptId());
        Integer _min = Optional.ofNullable(data.get("min"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Integer _max = Optional.ofNullable(data.get("max"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);

        Result<String> _base64 = new Result<>();
        _base64.data = geoRepository.exportSurveys(_regionId, _min, _max, loginUtil.getUser());
        return _base64;
    }

}