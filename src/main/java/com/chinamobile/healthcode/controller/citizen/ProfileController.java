package com.chinamobile.healthcode.controller.citizen;

import com.chinamobile.healthcode.model.citizen.Profile;
import com.chinamobile.healthcode.model.citizen.ProfileValidation;
import com.chinamobile.healthcode.repository.citizen.ProfileRepository;
import com.chinamobile.healthcode.repository.citizen.ProfileStatisticRepository;
import com.chinamobile.healthcode.repository.citizen.ProfileValidationRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Tuple4;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Controller(value = "citizenProfileController")
@RequestMapping(value = "citizen/profile")
public class ProfileController {

    final ProfileRepository profileRepository;
    final ProfileValidationRepository profileValidationRepository;
    final ProfileStatisticRepository profileStatisticRepository;
    final DepartmentRepository departmentRepository;
    final DefaultRoleRepository roleRepository;
    final LoginUtil loginUtil;

    public ProfileController(ProfileRepository profileRepository, ProfileValidationRepository profileValidationRepository, ProfileStatisticRepository profileStatisticRepository, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository, LoginUtil loginUtil) {
        this.profileRepository = profileRepository;
        this.profileValidationRepository = profileValidationRepository;
        this.profileStatisticRepository = profileStatisticRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "citizen:profile:index")
    public String index() {
        return "citizen/profiles";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    public Result<Profile> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<Profile> _item = profileRepository.getBrief(_id);
        if (!_item.isOK()) {
            return _item;
        }

        // 脱敏
        if (!roleRepository.isUserInRoleCached(loginUtil.getUserId(), ProfileRepository.ADMIN_ROLE) && !Objects.equals(loginUtil.getUserId(), _item.data.getCreatorId())) {
            if (_item.data.getCredentialNo() != null && _item.data.getCredentialNo().length() > 16) {
                _item.data.setCredentialNo(_item.data.getCredentialNo().substring(0, 2) + "**************" + _item.data.getCredentialNo().substring(16));
            }
        }

        return _item;
    }

    @PostMapping(value = "/get-by-credential")
    @ResponseBody
    public Result<Profile> getByCredentialNo(@RequestBody JsonObject data) {
        String _credentialNo = data.get("credentialNo").getAsString();

        Result<Profile> _item = profileRepository.getBriefByCredentialNo(_credentialNo);
        if (!_item.isOK()) {
            return _item;
        }

        // 脱敏
        if (!roleRepository.isUserInRoleCached(loginUtil.getUserId(), ProfileRepository.ADMIN_ROLE) && !Objects.equals(loginUtil.getUserId(), _item.data.getCreatorId())) {
            if (_item.data.getCredentialNo() != null && _item.data.getCredentialNo().length() > 16) {
                _item.data.setCredentialNo(_item.data.getCredentialNo().substring(0, 2) + "**************" + _item.data.getCredentialNo().substring(16));
            }
        }

        return _item;
    }

    @PostMapping(value = "/add")
    @ResponseBody
    @RequiresPermissions(value = "citizen:profile:add")
    public Result<String> add(@RequestBody Profile item) {
        return profileRepository.add(item, loginUtil.getUser());
    }

    @PostMapping(value = "/update")
    @ResponseBody
    @RequiresPermissions(value = "citizen:profile:update")
    public Result<String> update(@RequestBody Profile item) {
        return profileRepository.update(item, null, loginUtil.getUser());
    }

    @PostMapping(value = "/raze-then-update")
    @ResponseBody
    @RequiresPermissions(value = "citizen:profile:update")
    public Result<String> razeThenUpdate(@RequestBody Profile item) {
        return profileRepository.razeThenUpdate(item, loginUtil.getUser());
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    public Result<PagingItems<Profile>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<SortField> _sortFields = (List<SortField>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new com.google.gson.reflect.TypeToken<List<SortField>>() {
                }.getType())).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _credentialNo = Optional.ofNullable(data.get("credentialNo"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _subdistrictFullName = Optional.ofNullable(data.get("subdistrictFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Profile.ENUM_STAY _stay = Optional.ofNullable(data.get("stay"))
                .map(i -> i.isJsonNull() ? null : Profile.ENUM_STAY.valueOf(i.getAsString())).orElse(null);
        String _crowdId = Optional.ofNullable(data.get("crowdId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Integer _minAge = Optional.ofNullable(data.get("minAge"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Integer _maxAge = Optional.ofNullable(data.get("maxAge"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Profile.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : Profile.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);
        Profile.ENUM_VALIDATION _validation = Optional.ofNullable(data.get("validation"))
                .map(i -> i.isJsonNull() ? null : Profile.ENUM_VALIDATION.valueOf(i.getAsString())).orElse(null);
        String _error = Optional.ofNullable(data.get("error"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PagingItems<Profile>> _page = new Result<>();
        _page.data = profileRepository.fuzzy(_count, _index, _sortFields, _name, _credentialNo, _mp, _subdistrictFullName, _stay, _crowdId, _minAge, _maxAge, _beginTime, _endTime, _status, _validation, _error, loginUtil.getUser());

        // 匹配验证结果
        List<String> _ids = _page.data.items.stream()
                .map(i -> i.getId())
                .collect(Collectors.toList());
        List<ProfileValidation> _validations = profileValidationRepository.query(_ids);
        for (Profile i : _page.data.items) {
            ProfileValidation _temp = _validations.stream()
                    .filter(j -> Objects.equals(i.getId(), j.getProfileId()))
                    .findFirst().orElse(null);
            if (_temp != null) {
                i.setValidationDescription(_temp.getDescriptions());
            }
        }

        // 脱敏
        if (!roleRepository.isUserInRoleCached(loginUtil.getUserId(), ProfileRepository.ADMIN_ROLE)) {
            for (Profile i : _page.data.items) {
                if (i.getCredentialNo() != null && i.getCredentialNo().length() > 16) {
                    i.setCredentialNo(i.getCredentialNo().substring(0, 2) + "**************" + i.getCredentialNo().substring(16));
                }
            }
        }

        return _page;
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "citizen:profile:remove")
    public Result remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return profileRepository.remove(_id, loginUtil.getUser());
    }

    @PostMapping(value = "/import")
    @ResponseBody
    @RequiresPermissions(value = "citizen:profile:import")
    public Result<String> importFromFile(@RequestParam(value = "file") MultipartFile file) throws Exception {
        File _file = null;

        try {
            _file = new File(FileUtils.getTempDirectory() + File.separator + UUID.randomUUID() + "." + FilenameUtils.getExtension(file.getOriginalFilename()));
            FileUtils.copyInputStreamToFile(file.getInputStream(), _file);
            return profileRepository.importFromExcel(_file, loginUtil.getUser());
        } finally {
            if (_file != null) {
                _file.delete();
            }
        }
    }

    @PostMapping(value = "/export/base64")
    @ResponseBody
    @RequiresPermissions(value = "citizen:profile:export")
    public Result<String> export(@RequestBody JsonObject data) throws IOException {
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _credentialNo = Optional.ofNullable(data.get("credentialNo"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _subdistrictFullName = Optional.ofNullable(data.get("subdistrictFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Profile.ENUM_STAY _stay = Optional.ofNullable(data.get("stay"))
                .map(i -> i.isJsonNull() ? null : Profile.ENUM_STAY.valueOf(i.getAsString())).orElse(null);
        String _crowdId = Optional.ofNullable(data.get("crowdId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Integer _minAge = Optional.ofNullable(data.get("minAge"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Integer _maxAge = Optional.ofNullable(data.get("maxAge"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH")).orElse(null);
        Profile.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : Profile.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);
        Profile.ENUM_VALIDATION _validation = Optional.ofNullable(data.get("validation"))
                .map(i -> i.isJsonNull() ? null : Profile.ENUM_VALIDATION.valueOf(i.getAsString())).orElse(null);
        String _error = Optional.ofNullable(data.get("error"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<String> _base64 = new Result<>();
        _base64.data = profileRepository.export(_name, _credentialNo, _mp, _subdistrictFullName, _stay, _crowdId, _minAge, _maxAge, _beginTime, _endTime, _status, _validation, _error, loginUtil.getUser());
        return _base64;
    }

    @PostMapping(value = "/credential-no/exist")
    @ResponseBody
    public Result<Boolean> isCredentialNoExist(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _credentialNo = data.get("credentialNo").getAsString();

        Result<Boolean> _exist = new Result<>();
        _exist.data = profileRepository.isCredentialNoExist(_id, _credentialNo);
        return _exist;
    }

    @PostMapping(value = "/crowds")
    @ResponseBody
    public Result<List<Dictionary>> crowds() {
        Result<List<Dictionary>> _options = new Result<>();
        _options.data = profileRepository.crowds();
        return _options;
    }

    @PostMapping(value = "/statistic/tf")
    @ResponseBody
    public Result<List<Tuple4<String, String, String, Long>>> tf(@RequestBody JsonObject data) {
        String _deptId = Optional.ofNullable(data.get("deptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _crowdId = Optional.ofNullable(data.get("crowdId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Tuple4<String, String, String, Long>>> _counts = new Result<>();
        _counts.data = profileStatisticRepository.thenTF(_deptId, _crowdId, loginUtil.getUser());
        return _counts;
    }

    @PostMapping(value = "/statistic/population")
    @ResponseBody
    public Result<ProfileStatisticRepository.PopulationDTO> population(@RequestBody JsonObject data) {
        String _divisionCode = data.get("divisionCode").getAsString();

        return profileStatisticRepository.population(_divisionCode);
    }

    @PostMapping(value = "/statistic/crowds")
    @ResponseBody
    public Result<List<Pair<String, Long>>> statisticCrowds(@RequestBody JsonObject data) {
        String _divisionCode = data.get("divisionCode").getAsString();

        return profileStatisticRepository.crowds(_divisionCode);
    }

}
