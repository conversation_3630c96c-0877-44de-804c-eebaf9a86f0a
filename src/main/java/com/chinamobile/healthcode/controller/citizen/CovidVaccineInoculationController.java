package com.chinamobile.healthcode.controller.citizen;

import com.chinamobile.healthcode.model.citizen.CovidVaccineInoculation;
import com.chinamobile.healthcode.repository.citizen.CovidVaccineInoculationRepository;
import com.chinamobile.healthcode.repository.citizen.FormRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "citizen/vaccine")
public class CovidVaccineInoculationController {

    final CovidVaccineInoculationRepository covidVaccineInoculationRepository;
    final DepartmentRepository departmentRepository;
    final DefaultRoleRepository roleRepository;
    final LoginUtil loginUtil;

    public CovidVaccineInoculationController(CovidVaccineInoculationRepository covidVaccineInoculationRepository, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository, LoginUtil loginUtil) {
        this.covidVaccineInoculationRepository = covidVaccineInoculationRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping(value = "")
    @RequiresPermissions(value = "citizen:vaccine:index")
    public String index() {
        return "citizen/vaccine";
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    public Result<PagingItems<CovidVaccineInoculation>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<SortField> _sortFields = (List<SortField>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new com.google.gson.reflect.TypeToken<List<SortField>>() {
                }.getType())).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _credentialNo = Optional.ofNullable(data.get("credentialNo"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _subdistrictFullName = Optional.ofNullable(data.get("subdistrictFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _crowdId = Optional.ofNullable(data.get("crowdId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Integer _ageMin = Optional.ofNullable(data.get("ageMin"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Integer _ageMax = Optional.ofNullable(data.get("ageMax"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Integer _injectTimes = Optional.ofNullable(data.get("injectTimes"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);

        Result<PagingItems<CovidVaccineInoculation>> _page = new Result<>();
        _page.data = covidVaccineInoculationRepository.fuzzy(_count, _index, _sortFields, _name, _credentialNo, _mp, _subdistrictFullName, _crowdId, _ageMin, _ageMax, _injectTimes, _beginTime, _endTime, loginUtil.getUser());

        // 脱敏
        if (!roleRepository.isUserInRoleCached(loginUtil.getUserId(), FormRepository.ADMIN_ROLE)) {
            for (CovidVaccineInoculation i : _page.data.items) {
                if (i.getCredentialNo() != null && i.getCredentialNo().length() > 16) {
                    i.setCredentialNo(i.getCredentialNo().substring(0, 2) + "**************" + i.getCredentialNo().substring(16));
                }
            }
        }

        return _page;
    }

    @PostMapping(value = "/export/base64")
    @ResponseBody
    @RequiresPermissions(value = "citizen:vaccine:export")
    public Result<String> export(@RequestBody JsonObject data) throws IOException {
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _credentialNo = Optional.ofNullable(data.get("credentialNo"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _subdistrictFullName = Optional.ofNullable(data.get("subdistrictFullName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _crowdId = Optional.ofNullable(data.get("crowdId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Integer _ageMin = Optional.ofNullable(data.get("ageMin"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Integer _ageMax = Optional.ofNullable(data.get("ageMax"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Integer _injectTimes = Optional.ofNullable(data.get("injectTimes"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);

        Result<String> _base64 = new Result<>();
        _base64.data = covidVaccineInoculationRepository.export(_name, _credentialNo, _mp, _subdistrictFullName, _crowdId, _ageMin, _ageMax, _injectTimes, _beginTime, _endTime, loginUtil.getUser());
        return _base64;
    }

}