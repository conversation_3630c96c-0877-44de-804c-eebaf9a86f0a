package com.chinamobile.healthcode.controller.citizen;

import com.chinamobile.healthcode.model.citizen.Profile;
import com.chinamobile.healthcode.model.citizen.ProfileAssociation;
import com.chinamobile.healthcode.repository.citizen.ProfileAssociationRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.google.gson.JsonObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping(value = "citizen/profile/association")
public class ProfileAssociationController {

    final ProfileAssociationRepository profileAssociationRepository;
    final LoginUtil loginUtil;

    public ProfileAssociationController(ProfileAssociationRepository profileAssociationRepository, LoginUtil loginUtil) {
        this.profileAssociationRepository = profileAssociationRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/add")
    @ResponseBody
    public Result<Boolean> add(@RequestBody ProfileAssociation item) {
        return profileAssociationRepository.add(item, loginUtil.getUser());
    }

    @PostMapping(value = "/change")
    @ResponseBody
    public Result<String> update(@RequestBody JsonObject data) {
        String _credentialNo = data.get("credentialNo").getAsString();
        Profile.ENUM_ACTOR _actor = Profile.ENUM_ACTOR.valueOf(data.get("actor").getAsString());

        return profileAssociationRepository.update(_credentialNo, _actor, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    public Result remove(@RequestBody JsonObject data) {
        String _credentialNo = data.get("credentialNo").getAsString();

        return profileAssociationRepository.remove(_credentialNo, loginUtil.getUser());
    }

    @PostMapping(value = "/get")
    @ResponseBody
    public Result<Profile> getProfile() {
        return profileAssociationRepository.getProfile(loginUtil.getUser());
    }

    @PostMapping(value = "/query")
    @ResponseBody
    public Result<List<Profile>> queryProfiles(@RequestBody JsonObject data) {
        boolean _includeOneself = data.get("includeOneself").getAsBoolean();

        Result<List<Profile>> _page = new Result<>();
        _page.data = profileAssociationRepository.queryProfiles(_includeOneself, loginUtil.getUser());
        return _page;
    }

    @PostMapping(value = "/update")
    @ResponseBody
    public Result<String> updateProfile(@RequestBody Profile item) {
        return profileAssociationRepository.updateProfile(item, loginUtil.getUser());
    }

}