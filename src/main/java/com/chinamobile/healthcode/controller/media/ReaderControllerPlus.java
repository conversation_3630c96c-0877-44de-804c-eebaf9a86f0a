package com.chinamobile.healthcode.controller.media;

import com.chinamobile.healthcode.model.iot.Camera;
import com.chinamobile.healthcode.repository.iot.CameraRepository;
import com.chinamobile.healthcode.service.AcsService;
import com.chinamobile.healthcode.service.AndmuService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.springboot.web.controller.media.ReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.RecordController;
import com.google.gson.JsonObject;
import org.springframework.stereotype.Controller;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 6/12/2023 10:55
 */
@Controller
@RequestMapping({"media"})
public class ReaderControllerPlus extends ReaderController {

    final CameraRepository cameraRepository;

    final AcsService acsService;
    final AndmuService andmuService;

    public ReaderControllerPlus(
            AbstractMediaRepository mediaRepository,
            RecordController recordController,
            CameraRepository cameraRepository,
            AcsService acsService,
            AndmuService andmuService,
            LoginUtil loginUtil
    ) {
        super(mediaRepository, recordController, loginUtil);
        this.cameraRepository = cameraRepository;
        this.acsService = acsService;
        this.andmuService = andmuService;
    }

    @Override
    @GetMapping(value = "/read")
    public void read(HttpServletRequest request, HttpServletResponse response, @RequestParam("id") String id) throws Exception {
        writeToResponse(response, id, false);
    }

    @PostMapping("/preview")
    @ResponseBody
    public Result<String> preview(@RequestBody JsonObject data) throws IOException {
        String _cameraId = data.get("cameraId").getAsString();

        Result<Camera> _camera = cameraRepository.get(_cameraId);
        if (!_camera.isOK()) {
            Result<String> _uri = new Result<>();
            return _uri.pack(_camera);
        }

        switch (_camera.data.getPlatform()) {
            case ACS:
                return acsService.mediaFacade(_camera.data.getClientId()).preview(_cameraId, 2);
            case ANDMU:
                return andmuService.cameraFacade(_camera.data.getClientId()).player(_cameraId, 5);
            default:
                return new Result<>();
        }
    }

    @PostMapping("/playback")
    @ResponseBody
    public Result<String> playback(@RequestBody JsonObject data) throws IOException {
        String _cameraId = data.get("cameraId").getAsString();
        Date _from = DateUtil.from(data.get("from").getAsString(), "yyyy-MM-dd HH:mm:ss");
        Date _to = DateUtil.from(data.get("to").getAsString(), "yyyy-MM-dd HH:mm:ss");

        Result<Camera> _camera = cameraRepository.get(_cameraId);
        if (!_camera.isOK()) {
            Result<String> _uri = new Result<>();
            return _uri.pack(_camera);
        }

        switch (_camera.data.getPlatform()) {
            case ACS:
                return acsService.mediaFacade(_camera.data.getClientId()).playback(_cameraId, 1, _from, _to);
            case ANDMU:
                return andmuService.cameraFacade(_camera.data.getClientId()).playback(_cameraId, _from, _to);
            default:
                return new Result<>();
        }
    }

    public void writeToResponse(HttpServletResponse response, String id, boolean asAttachment) throws Exception {
        Result<Media> _item = this.mediaRepository.get(id, null);
        if (!_item.isOK()) {
            throw new Exception(_item.message);
        }

        response.reset();
        if (asAttachment) {
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(_item.data.getName()));
        }

        Result<InputStream> _input = this.mediaRepository.getInputStream(_item.data);
        if (_input.isOK()) {
            FileCopyUtils.copy(_input.data, response.getOutputStream());
        } else {
            throw new Exception(_input.message);
        }
    }

}