package com.chinamobile.healthcode;

import com.chinamobile.sparrow.domain.infra.sec.shiro.*;
import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.*;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade;
import com.chinamobile.sparrow.springboot.web.autoconfigure.ShiroAutoConfiguration;
import org.apache.shiro.authc.AuthenticationListener;
import org.apache.shiro.authc.pam.FirstSuccessfulStrategy;
import org.apache.shiro.authc.pam.ModularRealmAuthenticator;
import org.apache.shiro.authz.ModularRealmAuthorizer;
import org.apache.shiro.cache.CacheManager;
import org.apache.shiro.mgt.RememberMeManager;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.session.SessionListenerAdapter;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import javax.servlet.Filter;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

@Configuration
@AutoConfigureBefore(value = ShiroAutoConfiguration.class)
public class ShiroConfiguration {

    @Bean
    public ConfigUserAuthorizingRealm configUserAuthorizingRealm(
            @Value(value = "${shiro.redis-key-template.permission}") String redisPermissionKeyTemplate,
            @Value(value = "${shiro.redis-key-template.role}") String redisRoleKeyTemplate,
            UserRepository userRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            RedisTemplate<String, Object> redisTemplate
    ) {
        CachedAuthorizationConfigUserAuthorizingRealm _realm = new CachedAuthorizationConfigUserAuthorizingRealm(redisPermissionKeyTemplate, redisRoleKeyTemplate, userRepository, roleRepository, permissionRepository, redisTemplate);
        _realm.setAuthorizationCachingEnabled(false);

        return _realm;
    }

    @Bean
    public RsaCredentialsMatcher rsaCredentialsMatcher(@Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey) {
        return new RsaCredentialsMatcher(rsaPrivateKey);
    }

    @Bean
    public UsernamePasswordAuthorizingRealm usernamePasswordAuthorizingRealm(
            DefaultUserRepository userRepository,
            DefaultRoleRepository roleRepository,
            PermissionRepository permissionRepository,
            RsaCredentialsMatcher rsaCredentialsMatcher
    ) {
        UsernamePasswordAuthorizingRealm _realm = new UsernamePasswordAuthorizingRealm(userRepository, roleRepository, permissionRepository);
        _realm.setCredentialsMatcher(rsaCredentialsMatcher);
        _realm.setAuthorizationCachingEnabled(false);

        return _realm;
    }

    @Bean
    public JWTAuthorizingRealm jwtAuthorizingRealm(
            @Value(value = "${sec.sha256.default.secret}") String sha256Secret,
            @Value(value = "${shiro.redis-key-template.permission}") String redisPermissionKeyTemplate,
            @Value(value = "${shiro.redis-key-template.role}") String redisRoleKeyTemplate,
            UserRepository userRepository,
            RedisTemplate<String, Object> redisTemplate
    ) {
        JWTAuthorizingRealm _realm = new JWTAuthorizingRealm(sha256Secret, redisPermissionKeyTemplate, redisRoleKeyTemplate, userRepository, redisTemplate);
        _realm.setAuthorizationCachingEnabled(false);

        return _realm;
    }

    @Bean
    public WxMaAuthorizingRealm miniAppAuthorizingRealm(
            @Value(value = "${shiro.redis-key-template.permission}") String redisPermissionKeyTemplate,
            @Value(value = "${shiro.redis-key-template.role}") String redisRoleKeyTemplate,
            DefaultUserRepository userRepository,
            @Qualifier(value = "wxMaSTFYAccessFacade") AccessFacade wxMaSTFYAccessFacade,
            @Qualifier(value = "wxMaJFMAccessFacade") AccessFacade wxMaJFMAccessFacade,
            RedisTemplate<String, Object> redisTemplate
    ) {
        WxMaAuthorizingRealm _realm = new WxMaAuthorizingRealm(redisPermissionKeyTemplate, redisRoleKeyTemplate, userRepository, wxMaSTFYAccessFacade, wxMaJFMAccessFacade, redisTemplate);
        _realm.setAuthorizationCachingEnabled(false);

        return _realm;
    }

    @Bean
    public SMSAuthorizingRealm smsAuthorizingRealm(
            @Value(value = "${sec.oauth2.client-id}") String clientId,
            UserRepository userRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            VerificationCodeRepository verificationCodeRepository,
            DictionaryRepository dictionaryRepository
    ) {
        RoleSMSAuthorizingRealm _realm = new RoleSMSAuthorizingRealm(clientId, userRepository, roleRepository, permissionRepository, verificationCodeRepository, dictionaryRepository);
        _realm.setAuthorizationCachingEnabled(false);

        return _realm;
    }

    @Bean
    @ConditionalOnMissingBean
    public AuthenticationListener defaultAuthenticationListener(DefaultUserRepository userRepository) {
        return new CaptchaSupportAuthenticationListener(userRepository);
    }

    @Bean
    public ModularRealmAuthenticator authenticator(
            AuthenticationListener authenticationListener,
            ConfigUserAuthorizingRealm configUserAuthorizingRealm,
            UsernamePasswordAuthorizingRealm usernamePasswordAuthorizingRealm,
            SMSAuthorizingRealm smsAuthorizingRealm,
            JWTAuthorizingRealm jwtAuthorizingRealm,
            WxMaAuthorizingRealm wxMaAuthorizingRealm
    ) {
        DefaultModularRealmAuthenticator _authenticator = new DefaultModularRealmAuthenticator(configUserAuthorizingRealm, usernamePasswordAuthorizingRealm, smsAuthorizingRealm, jwtAuthorizingRealm, wxMaAuthorizingRealm);
        _authenticator.setAuthenticationStrategy(new FirstSuccessfulStrategy());
        _authenticator.setAuthenticationListeners(Collections.singletonList(authenticationListener));

        return _authenticator;
    }

    @Bean
    public SessionManager sessionManager(
            SessionDAO sessionDAO,
            ShiroConfigurationProperties shiroConfigurationProperties,
            SessionListenerAdapter sessionListenerAdapter
    ) {
        DefaultWebSessionManager _sessionManager = new DefaultWebSessionManager();

        // 设置cookie
        _sessionManager.setSessionIdCookie(shiroConfigurationProperties.getSessionIdCookie());
        _sessionManager.setSessionIdUrlRewritingEnabled(false);

        _sessionManager.setSessionDAO(sessionDAO);

        // 设置session过期时间
        _sessionManager.setGlobalSessionTimeout(shiroConfigurationProperties.getSession().getTimeout() * 1000);
        // 设置session验证间隔
        _sessionManager.setSessionValidationInterval(shiroConfigurationProperties.getSession().getValidationInterval() * 1000);
        // 设置session监听器
        _sessionManager.setSessionListeners(Collections.singletonList(sessionListenerAdapter));

        return _sessionManager;
    }

    @Bean
    public DefaultWebSecurityManager securityManager(
            ConfigUserAuthorizingRealm configUserAuthorizingRealm,
            UsernamePasswordAuthorizingRealm usernamePasswordAuthorizingRealm,
            SMSAuthorizingRealm smsAuthorizingRealm,
            JWTAuthorizingRealm jwtAuthorizingRealm,
            WxMaAuthorizingRealm wxMaAuthorizingRealm,
            ModularRealmAuthenticator authenticator,
            ModularRealmAuthorizer authorizer,
            SessionManager sessionManager,
            CacheManager cacheManager,
            RememberMeManager rememberMeManager
    ) {
        DefaultWebSecurityManager _manager = new DefaultWebSecurityManager();
        _manager.setAuthenticator(authenticator);
        _manager.setAuthorizer(authorizer);

        // 设置认证域
        _manager.setRealms(Arrays.asList(configUserAuthorizingRealm, usernamePasswordAuthorizingRealm, smsAuthorizingRealm, jwtAuthorizingRealm, wxMaAuthorizingRealm));

        // 设置会话管理器
        _manager.setSessionManager(sessionManager);

        // 设置缓存管理器
        _manager.setCacheManager(cacheManager);

        // 设置记住管理器
        _manager.setRememberMeManager(rememberMeManager);

        return _manager;
    }

    @Bean
    public ShiroFilterFactoryBean shiroFilterFactoryBean(
            @Value(value = "${sec.username}") String userName,
            @Value(value = "${sec.login-url}") String loginUrl,
            SecurityManager securityManager,
            ShiroConfigurationProperties shiroConfigurationProperties,
            LoginUtil loginUtil
    ) {
        ShiroFilterFactoryBean _filterFactory = new ShiroFilterFactoryBean();
        _filterFactory.setSecurityManager(securityManager);

        // 设置认证过滤器
        Map<String, Filter> _filters = new LinkedHashMap<>();
        _filters.put("user", new DefaultAuthenticationFilter(userName, loginUrl, loginUtil));
        _filters.put("fine-report", new FineReportAuthenticationFilter(userName, loginUrl, loginUtil));
        _filterFactory.setFilters(_filters);
        _filterFactory.setFilterChainDefinitionMap(shiroConfigurationProperties.getFilterChainDefinitionMap());
        return _filterFactory;
    }

}