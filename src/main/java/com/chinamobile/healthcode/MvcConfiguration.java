package com.chinamobile.healthcode;

import com.chinamobile.healthcode.repository.FormTraceRepository;
import com.chinamobile.healthcode.repository.TaskRepository;
import com.chinamobile.healthcode.repository.citizen.ProfileValidationRepository;
import com.chinamobile.healthcode.repository.grid.GeoRepository;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.*;
import com.chinamobile.sparrow.domain.repository.sys.*;
import com.chinamobile.sparrow.domain.repository.sys.log.LogRepository;
import com.chinamobile.sparrow.domain.repository.sys.log.StandaloneLogRepository;
import com.chinamobile.sparrow.domain.service.cmpassport.Facade;
import com.chinamobile.sparrow.domain.service.wx.cp.AccessFacade;
import com.chinamobile.sparrow.springboot.web.ShiroHandshakeInterceptorImpl;
import com.chinamobile.sparrow.springboot.web.controller.sec.DefaultLoginController;
import com.chinamobile.sparrow.springboot.web.controller.sec.DefaultRoleController;
import com.chinamobile.sparrow.springboot.web.controller.sys.*;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;

import javax.persistence.EntityManagerFactory;

@Configuration
public class MvcConfiguration {

    @Bean
    public BulletinRepository bulletinRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            AbstractMediaRepository mediaRepository
    ) {
        return new BulletinRepository(entityManagerFactory, jinqJPAStreamProvider, mediaRepository);
    }


    @Bean
    public DepartmentRepository departmentRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            UserRepository userRepository,
            RoleRepository roleRepository,
            @Lazy GeoRepository geoRepository
    ) {
        return new DefaultDepartmentRepository(entityManagerFactory, jinqJPAStreamProvider, userRepository, roleRepository, geoRepository);
    }

    @Bean
    public LogRepository logRepository(
            @Qualifier(value = "logSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "logJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            UserRepository userRepository
    ) {
        return new StandaloneLogRepository(entityManagerFactory, jinqJPAStreamProvider, userRepository);
    }

    @Bean
    public MenuRepository meunRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Lazy PermissionRepository permissionRepository
    ) {
        return new DefaultMenuRepository(entityManagerFactory, jinqJPAStreamProvider, permissionRepository);
    }

    @Bean
    public PermissionRepository permissionRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${shiro.redis-key-template.permission}") String redisPermissionKeyTemplate,
            UserRepository userRepository,
            MenuRepository menuRepository,
            RedisTemplate<String, Object> redisTemplate
    ) {
        return new DefaultPermissionRepository(entityManagerFactory, jinqJPAStreamProvider, redisPermissionKeyTemplate, userRepository, menuRepository, redisTemplate);
    }

    @Bean
    public RoleRepository roleRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${shiro.redis-key-template.role}") String redisRoleKeyTemplate,
            UserRepository userRepository,
            PermissionRepository permissionRepository,
            RedisTemplate<String, Object> redisTemplate
    ) {
        return new DefaultRoleRepository(entityManagerFactory, jinqJPAStreamProvider, redisRoleKeyTemplate, userRepository, permissionRepository, redisTemplate);
    }

    @Bean
    public StatisticService statisticService(
            @Qualifier(value = "logSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "logJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            UserRepository userRepository,
            RoleRepository roleRepository,
            DictionaryRepository dictionaryRepository,
            DefaultDepartmentRepository departmentRepository
    ) {
        return new StandaloneStatisticService(entityManagerFactory, jinqJPAStreamProvider, userRepository, roleRepository, dictionaryRepository, departmentRepository);
    }

    @Bean
    public UserRepository userRepository(
            @Value(value = "${sec.password-constraint}") String passwordConstraint,
            @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey,
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Lazy DefaultDepartmentRepository departmentRepository,
            @Lazy RoleRepository roleRepository,
            AbstractMediaRepository mediaRepository
    ) {
        return new DefaultUserRepository(entityManagerFactory, jinqJPAStreamProvider, passwordConstraint, rsaPrivateKey, departmentRepository, roleRepository, mediaRepository);
    }


    @Bean
    public BulletinController bulletinController(
            BulletinRepository bulletinRepository,
            AbstractMediaRepository mediaRepository,
            LoginUtil loginUtil
    ) {
        return new BulletinController(bulletinRepository, mediaRepository, loginUtil);
    }

    @Bean
    public DefaultDepartmentController departmentController(
            DefaultDepartmentRepository departmentRepository,
            DefaultRoleRepository roleRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultDepartmentController(departmentRepository, roleRepository, loginUtil);
    }

    @Bean
    public DefaultLoginController loginController(
            @Value(value = "${sec.jwt.expires-in}") int jwtExpiresIn,
            @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey,
            @Value(value = "${sec.sha256.default.secret}") String sha256Secret,
            @Value(value = "${sec.captcha.enabled:true}") boolean captchaEnabled,
            @Value(value = "${sec.captcha.redis-key-template}") String captchaRedisKeyTemplate,
            @Value(value = "${sec.captcha.redis-expires-in}") int captchaRedisExpiresIn,
            UserRepository userRepository,
            VerificationCodeRepository verificationCodeRepository,
            @Autowired(required = false) Facade cmpassportFacade,
            @Autowired(required = false) AccessFacade wxAccessFacade,
            @Autowired(required = false) AccessFacade yzyAccessFacade,
            LoginUtil loginUtil,
            DepartmentRepository<Department> departmentRepository,
            DefaultRoleRepository roleRepository,
            RedisTemplate<String, Object> redisTemplate
    ) {
        return new DefaultLoginController(jwtExpiresIn, rsaPrivateKey, sha256Secret, captchaEnabled, captchaRedisKeyTemplate, captchaRedisExpiresIn, userRepository, verificationCodeRepository, cmpassportFacade, wxAccessFacade, yzyAccessFacade, loginUtil, departmentRepository, roleRepository, redisTemplate);
    }

    @Bean
    public DefaultRoleController roleController(
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultRoleController(roleRepository, permissionRepository, loginUtil);
    }

    @Bean
    public DefaultProfileController profileController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultProfileController(userRepository, loginUtil);
    }

    @Bean
    public DefaultSettingController settingController(
            UserRepository userRepository,
            DepartmentRepository departmentRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            MenuRepository menuRepository,
            LoginUtil loginUtil,
            TaskRepository taskRepository,
            ProfileValidationRepository profileValidationRepository,
            FormTraceRepository formTraceRepository
    ) {
        return new DefaultSettingController(userRepository, departmentRepository, roleRepository, permissionRepository, menuRepository, loginUtil, taskRepository, profileValidationRepository, formTraceRepository);
    }

    @Bean
    public DefaultStatisticController statisticController(
            UserRepository userRepository,
            StatisticService statisticService,
            DefaultRoleRepository roleRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultStatisticController(userRepository, statisticService, roleRepository, loginUtil);
    }

    @Bean
    public DefaultUserController userController(
            DefaultUserRepository userRepository,
            DepartmentRepository<Department> departmentRepository,
            DefaultRoleRepository roleRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultUserController(userRepository, departmentRepository, roleRepository, loginUtil);
    }

    @Bean
    public DefaultUtilController utilController(
            @Value("${cmpassport.app-id}") String cmPassportAppId,
            @Value("${amap.js-key}") String aMapJsKey,
            @Value("${qqmap.js-key}") String qqMapJsKey
    ) {
        return new DefaultUtilController(cmPassportAppId, aMapJsKey, qqMapJsKey);
    }

    @Bean
    public ShiroHandshakeInterceptorImpl shiroHandshakeInterceptor() {
        return new ShiroHandshakeInterceptorImpl();
    }

}
