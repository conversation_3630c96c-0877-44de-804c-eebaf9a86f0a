package com.chinamobile.healthcode;

import com.chinamobile.sparrow.domain.infra.orm.jing.MySqlFunctions;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.shardingsphere.driver.api.yaml.YamlShardingSphereDataSourceFactory;
import org.hibernate.SessionFactory;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.quartz.QuartzDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.hibernate5.HibernateTransactionManager;
import org.springframework.orm.hibernate5.LocalSessionFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Date;
import java.util.Properties;

@Configuration
public class DataSourceConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.main")
    public DataSource mainDataSource() {
        return new HikariDataSource();
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.main.properties")
    public Properties mainDataSourceHibernateProperties() {
        return new Properties();
    }

    @Bean
    public LocalSessionFactoryBean mainSessionFactory(@Value(value = "${spring.datasource.main.packages}") String packages, @Qualifier(value = "mainDataSource") DataSource dataSource, @Qualifier(value = "mainDataSourceHibernateProperties") Properties properties) {
        LocalSessionFactoryBean _localSessionFactoryBean = new LocalSessionFactoryBean();
        _localSessionFactoryBean.setDataSource(dataSource);
        String[] _packages = StringUtils.hasLength(packages) ? packages.split(",") : new String[]{"com.chinamobile.sparrow.domain.model"};

        for (int i = 0; i < _packages.length; ++i) {
            _packages[i] = _packages[i].trim();
        }

        _localSessionFactoryBean.setPackagesToScan(_packages);
        _localSessionFactoryBean.setHibernateProperties(properties);

        return _localSessionFactoryBean;
    }

    @Bean
    @Primary
    public PlatformTransactionManager mainTransactionManager(@Qualifier(value = "mainSessionFactory") SessionFactory sessionFactory) {
        return new HibernateTransactionManager(sessionFactory);
    }

    @Bean
    public JinqJPAStreamProvider mainJinqJPAStreamProvider(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory) throws NoSuchMethodException {
        JinqJPAStreamProvider _provider = new JinqJPAStreamProvider(entityManagerFactory.createEntityManager().getMetamodel());
        _provider.registerCustomSqlFunction(MySqlFunctions.class.getDeclaredMethod("dateFormat", Date.class, String.class), "date_format");
        _provider.registerCustomSqlFunction(MySqlFunctions.class.getDeclaredMethod("substringIndex", String.class, String.class, int.class), "substring_index");

        return _provider;
    }

    @Bean
    public DataSource logDataSource(@Value(value = "${spring.datasource.log.config}") String path) throws IOException, SQLException {
        File _yaml = ResourceUtils.getFile(path);
        return YamlShardingSphereDataSourceFactory.createDataSource(_yaml);
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.log.properties")
    public Properties logDataSourceHibernateProperties() {
        return new Properties();
    }

    @Bean
    public LocalSessionFactoryBean logSessionFactory(@Qualifier(value = "logDataSource") DataSource dataSource, @Qualifier(value = "logDataSourceHibernateProperties") Properties properties) {
        LocalSessionFactoryBean _localSessionFactoryBean = new LocalSessionFactoryBean();
        _localSessionFactoryBean.setDataSource(dataSource);
        _localSessionFactoryBean.setPackagesToScan("com.chinamobile.sparrow.domain.model.sys");
        _localSessionFactoryBean.setHibernateProperties(properties);

        return _localSessionFactoryBean;
    }

    @Bean
    public PlatformTransactionManager logTransactionManager(@Qualifier(value = "logSessionFactory") SessionFactory sessionFactory) {
        return new HibernateTransactionManager(sessionFactory);
    }

    @Bean
    public JinqJPAStreamProvider logJinqJPAStreamProvider(@Qualifier(value = "logSessionFactory") EntityManagerFactory entityManagerFactory) throws NoSuchMethodException {
        JinqJPAStreamProvider _provider = new JinqJPAStreamProvider(entityManagerFactory.createEntityManager().getMetamodel());
        _provider.registerCustomSqlFunction(MySqlFunctions.class.getDeclaredMethod("dateFormat", Date.class, String.class), "date_format");
        _provider.registerCustomSqlFunction(MySqlFunctions.class.getDeclaredMethod("substringIndex", String.class, String.class, int.class), "substring_index");

        return _provider;
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.vaccine")
    public DataSource vaccineDataSource() {
        return new HikariDataSource();
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.vaccine.properties")
    public Properties vaccineDataSourceHibernateProperties() {
        return new Properties();
    }

    @Bean
    public LocalSessionFactoryBean vaccineSessionFactory(@Qualifier(value = "vaccineDataSource") DataSource dataSource, @Qualifier(value = "vaccineDataSourceHibernateProperties") Properties properties) {
        LocalSessionFactoryBean _localSessionFactoryBean = new LocalSessionFactoryBean();
        _localSessionFactoryBean.setDataSource(dataSource);
        _localSessionFactoryBean.setPackagesToScan("com.chinamobile.healthcode.model.citizen");
        _localSessionFactoryBean.setHibernateProperties(properties);

        return _localSessionFactoryBean;
    }

    @Bean
    public PlatformTransactionManager vaccineTransactionManager(@Qualifier(value = "vaccineSessionFactory") SessionFactory sessionFactory) {
        return new HibernateTransactionManager(sessionFactory);
    }

    @Bean
    public JinqJPAStreamProvider vaccineJinqJPAStreamProvider(@Qualifier(value = "vaccineSessionFactory") EntityManagerFactory entityManagerFactory) throws NoSuchMethodException {
        JinqJPAStreamProvider _provider = new JinqJPAStreamProvider(entityManagerFactory.createEntityManager().getMetamodel());
        _provider.registerCustomSqlFunction(MySqlFunctions.class.getDeclaredMethod("dateFormat", Date.class, String.class), "date_format");
        _provider.registerCustomSqlFunction(MySqlFunctions.class.getDeclaredMethod("substringIndex", String.class, String.class, int.class), "substring_index");

        return _provider;
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.fr")
    public DataSource frDataSource() {
        return new HikariDataSource();
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.fr.properties")
    public Properties frDataSourceHibernateProperties() {
        return new Properties();
    }

    @Bean
    public LocalSessionFactoryBean frSessionFactory(@Qualifier(value = "frDataSource") DataSource dataSource, @Qualifier(value = "frDataSourceHibernateProperties") Properties properties) {
        LocalSessionFactoryBean _localSessionFactoryBean = new LocalSessionFactoryBean();
        _localSessionFactoryBean.setDataSource(dataSource);
        _localSessionFactoryBean.setPackagesToScan("com.chinamobile.healthcode.model.grid");
        _localSessionFactoryBean.setHibernateProperties(properties);

        return _localSessionFactoryBean;
    }

    @Bean
    public PlatformTransactionManager frTransactionManager(@Qualifier(value = "frSessionFactory") SessionFactory sessionFactory) {
        return new HibernateTransactionManager(sessionFactory);
    }

    @Bean
    public JinqJPAStreamProvider frJinqJPAStreamProvider(@Qualifier(value = "frSessionFactory") EntityManagerFactory entityManagerFactory) throws NoSuchMethodException {
        JinqJPAStreamProvider _provider = new JinqJPAStreamProvider(entityManagerFactory.createEntityManager().getMetamodel());
        _provider.registerCustomSqlFunction(MySqlFunctions.class.getDeclaredMethod("dateFormat", Date.class, String.class), "date_format");
        _provider.registerCustomSqlFunction(MySqlFunctions.class.getDeclaredMethod("substringIndex", String.class, String.class, int.class), "substring_index");

        return _provider;
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.health-commission")
    public DataSource healthCommissionDataSource() {
        return new HikariDataSource();
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.health-commission.properties")
    public Properties healthCommissionDataSourceHibernateProperties() {
        return new Properties();
    }

    @Bean
    public LocalSessionFactoryBean healthCommissionSessionFactory(@Qualifier(value = "healthCommissionDataSource") DataSource dataSource, @Qualifier(value = "healthCommissionDataSourceHibernateProperties") Properties properties) {
        LocalSessionFactoryBean _localSessionFactoryBean = new LocalSessionFactoryBean();
        _localSessionFactoryBean.setDataSource(dataSource);
        _localSessionFactoryBean.setPackagesToScan("com.chinamobile.healthcode.model.citizen");
        _localSessionFactoryBean.setHibernateProperties(properties);

        return _localSessionFactoryBean;
    }

    @Bean
    public PlatformTransactionManager healthCommissionTransactionManager(@Qualifier(value = "healthCommissionSessionFactory") SessionFactory sessionFactory) {
        return new HibernateTransactionManager(sessionFactory);
    }

    @Bean
    public JinqJPAStreamProvider healthCommissionJinqJPAStreamProvider(@Qualifier(value = "healthCommissionSessionFactory") EntityManagerFactory entityManagerFactory) throws NoSuchMethodException {
        JinqJPAStreamProvider _provider = new JinqJPAStreamProvider(entityManagerFactory.createEntityManager().getMetamodel());
        _provider.registerCustomSqlFunction(MySqlFunctions.class.getDeclaredMethod("dateFormat", Date.class, String.class), "date_format");
        _provider.registerCustomSqlFunction(MySqlFunctions.class.getDeclaredMethod("substringIndex", String.class, String.class, int.class), "substring_index");

        return _provider;
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.quartz")
    @QuartzDataSource
    public DataSource quartzDataSource() {
        return new HikariDataSource();
    }

}