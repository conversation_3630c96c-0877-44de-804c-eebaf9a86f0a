package com.chinamobile.healthcode.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "workbench_tasks")
public class Task extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Transient
    String title;

    @Transient
    String content;

    ENUM_TYPE subjectType;

    @Column(length = 36)
    String subjectId;

    @Column(length = 36)
    String previousId;

    ENUM_ASSIGNEE assignee;

    @Column(length = 36)
    String assigneeId;

    @Column(length = 36)
    String deptId;

    @Column(length = 128)
    String deptFullName;

    @Column(columnDefinition = "text")
    String transition;

    /**
     * 备忘
     */
    @Column(columnDefinition = "text")
    String memo;

    ENUM_STATUS status;

    boolean readonly;

    Date readTime;

    Date completeTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public ENUM_TYPE getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(ENUM_TYPE subjectType) {
        this.subjectType = subjectType;
    }

    public String getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(String projectId) {
        this.subjectId = projectId;
    }

    public String getPreviousId() {
        return previousId;
    }

    public void setPreviousId(String previousId) {
        this.previousId = previousId;
    }

    public ENUM_ASSIGNEE getAssignee() {
        return assignee;
    }

    public void setAssignee(ENUM_ASSIGNEE assignee) {
        this.assignee = assignee;
    }

    public String getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(String assigneeId) {
        this.assigneeId = assigneeId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptFullName() {
        return deptFullName;
    }

    public void setDeptFullName(String deptFullName) {
        this.deptFullName = deptFullName;
    }

    public String getTransition() {
        return transition;
    }

    public void setTransition(String transition) {
        this.transition = transition;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public ENUM_STATUS getStatus() {
        return status;
    }

    public void setStatus(ENUM_STATUS status) {
        this.status = status;
    }

    public boolean getReadonly() {
        return readonly;
    }

    public void setReadonly(boolean readonly) {
        this.readonly = readonly;
    }

    public Date getReadTime() {
        return readTime;
    }

    public void setReadTime(Date receiveTime) {
        this.readTime = receiveTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public enum ENUM_TYPE {

        专项工作(0),
        设备告警(1),
        现场上报(2),
        村社专项管理(3),
        综治专题管理(4);

        int value;

        ENUM_TYPE(int value) {
            this.value = value;
        }

        public static ENUM_TYPE valueOf(int value) {
            switch (value) {
                case 0:
                    return 专项工作;
                case 1:
                    return 设备告警;
                case 2:
                    return 现场上报;
                case 3:
                    return 村社专项管理;
                case 4:
                    return 综治专题管理;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }

    }

    public enum ENUM_ASSIGNEE {

        GROUP(0),
        USER(1);

        int value;

        ENUM_ASSIGNEE(int value) {
            this.value = value;
        }

        public static ENUM_ASSIGNEE valueOf(int value) {
            switch (value) {
                case 0:
                    return GROUP;
                case 1:
                    return USER;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }

    }

    public enum ENUM_STATUS {

        TODO(0),
        DONE(1),
        CLOSED(2);

        int value;

        ENUM_STATUS(int value) {
            this.value = value;
        }

        public static ENUM_STATUS valueOf(int value) {
            switch (value) {
                case 0:
                    return TODO;
                case 1:
                    return DONE;
                case 2:
                    return CLOSED;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }

    }

}