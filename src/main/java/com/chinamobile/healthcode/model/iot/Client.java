package com.chinamobile.healthcode.model.iot;

import com.chinamobile.sparrow.domain.model.AbstractEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "iot_tenant_clients")
public class Client extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id;

    @Column(length = 36)
    String tenantId;

    ENUM_PLATFORM platform;

    @Column(length = 36)
    String appId;

    @Column(length = 36)
    String appSecret;

    @Column(columnDefinition = "text")
    String info;

    @Column(columnDefinition = "text")
    String description;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public ENUM_PLATFORM getPlatform() {
        return platform;
    }

    public void setPlatform(ENUM_PLATFORM platform) {
        this.platform = platform;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public enum ENUM_PLATFORM {
        ACS(0), ANDMU(1), POC(2), UAV(3);

        final int value;

        ENUM_PLATFORM(int value) {
            this.value = value;
        }

        public static ENUM_PLATFORM valueOf(int value) {
            switch (value) {
                case 0:
                    return ACS;
                case 1:
                    return ANDMU;
                case 2:
                    return POC;
                case 3:
                    return UAV;
            }

            return null;
        }

        public int value() {
            return value;
        }

    }

}