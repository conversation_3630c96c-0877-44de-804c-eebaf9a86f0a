package com.chinamobile.healthcode.model.iot;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "iot_monitoring_person_positionings")
public class PersonPositioning extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    ENUM_SOURCE_TYPE sourceType;

    @Column(length = 36)
    String sourceId;

    @Column(length = 36)
    String personId;

    @Column(length = 36)
    String regionId;

    @Column(length = 128)
    String regionFullName;

    Double lng;

    Double lat;

    Double lngInWgs;

    Double latInWgs;

    public PersonPositioning() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public ENUM_SOURCE_TYPE getSourceType() {
        return sourceType;
    }

    public void setSourceType(ENUM_SOURCE_TYPE sourceType) {
        this.sourceType = sourceType;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLngInWgs() {
        return lngInWgs;
    }

    public void setLngInWgs(Double lngInWgs) {
        this.lngInWgs = lngInWgs;
    }

    public Double getLatInWgs() {
        return latInWgs;
    }

    public void setLatInWgs(Double latInWgs) {
        this.latInWgs = latInWgs;
    }

    public enum ENUM_SOURCE_TYPE {
        ALARM(0);

        final int value;

        ENUM_SOURCE_TYPE(int value) {
            this.value = value;
        }

        public static ENUM_SOURCE_TYPE valueOf(int value) {
            if (value == 0) {
                return ALARM;
            }

            return null;
        }

        public int value() {
            return this.value;
        }
    }

}