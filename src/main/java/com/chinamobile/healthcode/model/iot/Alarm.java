package com.chinamobile.healthcode.model.iot;

import com.chinamobile.healthcode.model.Task;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "iot_alarms")
public class Alarm extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    String type;

    @Column(length = 36)
    String regionId;

    @Column(length = 128)
    String regionFullName;

    @Column(length = 36)
    String deviceId;

    String deviceName;

    ENUM_DEVICE_TYPE deviceType;

    Date alarmTime;
    Date clearTime;

    int alarmState;
    int checkState;
    int confirmState;

    String attachmentKeys;

    @Transient
    List<String> attachmentIds;

    @Transient
    List<Media> attachments;

    @Transient
    Task.ENUM_STATUS status;

    @Transient
    Object device;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public ENUM_DEVICE_TYPE getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(ENUM_DEVICE_TYPE deviceType) {
        this.deviceType = deviceType;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Date getClearTime() {
        return clearTime;
    }

    public void setClearTime(Date clearTime) {
        this.clearTime = clearTime;
    }

    public int getAlarmState() {
        return alarmState;
    }

    public void setAlarmState(int alarmState) {
        this.alarmState = alarmState;
    }

    public int getCheckState() {
        return checkState;
    }

    public void setCheckState(int checkState) {
        this.checkState = checkState;
    }

    public int getConfirmState() {
        return confirmState;
    }

    public void setConfirmState(int confirmState) {
        this.confirmState = confirmState;
    }

    public String getAttachmentKeys() {
        return attachmentKeys;
    }

    public void setAttachmentKeys(String attachmentIdJson) {
        this.attachmentKeys = attachmentIdJson;
    }

    public List<String> getAttachmentIds() {
        return attachmentIds;
    }

    public void setAttachmentIds(List<String> attachmentIds) {
        this.attachmentIds = attachmentIds;
    }

    public List<Media> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Media> attachments) {
        this.attachments = attachments;
    }

    public Task.ENUM_STATUS getStatus() {
        return status;
    }

    public void setStatus(Task.ENUM_STATUS status) {
        this.status = status;
    }

    public Object getDevice() {
        return device;
    }

    public void setDevice(Object device) {
        this.device = device;
    }

    public enum ENUM_DEVICE_TYPE {
        CAMERA(0), INTERPHONE(1), SMOKE_DETECTOR(2);

        int value;

        ENUM_DEVICE_TYPE(int value) {
            this.value = value;
        }

        public static ENUM_DEVICE_TYPE valueOf(int value) {
            if (value == 0) {
                return CAMERA;
            } else if (value == 1) {
                return INTERPHONE;
            } else if (value == 2) {
                return SMOKE_DETECTOR;
            }

            return null;
        }

        public int value() {
            return this.value;
        }
    }

}