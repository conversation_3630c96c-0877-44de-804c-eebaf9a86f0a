package com.chinamobile.healthcode.model.iot;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.service.poc.pro.lang.Device;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "iot_interphones")
public class Interphone extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id;

    String name;
    String mp;
    String organizationId;
    String organizationName;

    @Column(length = 36)
    String regionId;

    @Column(length = 128)
    String regionFullName;

    // gcj经度
    Double lng;

    // gcj纬度
    Double lat;

    // wgs经度
    Double lngInWgs;

    // wgs纬度
    Double latInWgs;
    int locateMode;
    Date locateTime;

    boolean isOnline = false;

    public Interphone() {
    }

    public Interphone(Device device) {
        this.id = device.getKey();
        this.name = device.getName();
        this.isOnline = "1".equals(device.getOnline());
        this.locateMode = device.getPositionType();
        this.locateTime = device.getReportTime();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMp() {
        return mp;
    }

    public void setMp(String mp) {
        this.mp = mp;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLngInWgs() {
        return lngInWgs;
    }

    public void setLngInWgs(Double lngInWgs) {
        this.lngInWgs = lngInWgs;
    }

    public Double getLatInWgs() {
        return latInWgs;
    }

    public void setLatInWgs(Double latInWgs) {
        this.latInWgs = latInWgs;
    }

    public int getLocateMode() {
        return locateMode;
    }

    public void setLocateMode(int locateMode) {
        this.locateMode = locateMode;
    }

    public Date getLocateTime() {
        return locateTime;
    }

    public void setLocateTime(Date locateTime) {
        this.locateTime = locateTime;
    }

    public boolean getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(boolean online) {
        isOnline = online;
    }
}
