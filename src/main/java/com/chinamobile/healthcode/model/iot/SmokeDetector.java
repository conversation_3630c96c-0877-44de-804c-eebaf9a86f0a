package com.chinamobile.healthcode.model.iot;

import com.chinamobile.healthcode.model.subject.PropertyDescription;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "iot_smoke_detectors")
public class SmokeDetector extends PropertyDescription {

    @Column(length = 36)
    String deviceId;

    int deviceType;
    ENUM_PLATFORM platform;
    int classificationType;
    int isVirtual;

    public SmokeDetector() {
        this.setType("烟雾报警器");
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public int getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(int deviceType) {
        this.deviceType = deviceType;
    }

    public ENUM_PLATFORM getPlatform() {
        return platform;
    }

    public void setPlatform(ENUM_PLATFORM platform) {
        this.platform = platform;
    }

    public int getClassificationType() {
        return classificationType;
    }

    public void setClassificationType(int classificationType) {
        this.classificationType = classificationType;
    }

    public int getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(int isVirtual) {
        this.isVirtual = isVirtual;
    }

    public enum ENUM_PLATFORM {
        ANDMU(0);

        int value;

        ENUM_PLATFORM(int value) {
            this.value = value;
        }

        public static ENUM_PLATFORM valueOf(int value) {
            if (value == 0) {
                return ANDMU;
            }

            return null;
        }

        public int value() {
            return this.value;
        }
    }

}