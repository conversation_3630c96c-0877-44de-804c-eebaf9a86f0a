package com.chinamobile.healthcode.model.iot;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "iot_monitoring_persons")
public class Person extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 32)
    String idNo;

    String name;

    boolean isMale;

    @Column(length = 36)
    String andmuId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isMale() {
        return isMale;
    }

    public void setIsMale(boolean male) {
        isMale = male;
    }

    public String getAndmuId() {
        return andmuId;
    }

    public void setAndmuId(String andmuId) {
        this.andmuId = andmuId;
    }

}