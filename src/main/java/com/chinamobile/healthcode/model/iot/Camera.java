package com.chinamobile.healthcode.model.iot;

import com.chinamobile.healthcode.model.subject.PropertyDescription;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "iot_cameras")
public class Camera extends PropertyDescription {

    @Column(length = 36)
    String deviceId;

    @Column(length = 36)
    String groupId;

    ENUM_PLATFORM platform;


    String ptzType;

    public Camera() {
        this.setType("摄像头");
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public ENUM_PLATFORM getPlatform() {
        return platform;
    }

    public void setPlatform(ENUM_PLATFORM platform) {
        this.platform = platform;
    }

    public String getPtzType() {
        return ptzType;
    }

    public void setPtzType(String ptzType) {
        this.ptzType = ptzType;
    }

    public enum ENUM_PLATFORM {
        ACS(0), ANDMU(1);

        int value;

        ENUM_PLATFORM(int value) {
            this.value = value;
        }

        public static ENUM_PLATFORM valueOf(int value) {
            if (value == 0) {
                return ACS;
            } else if (value == 1) {
                return ANDMU;
            }

            return null;
        }

        public int value() {
            return this.value;
        }
    }

}