package com.chinamobile.healthcode.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;

@Entity
@Table(name = "set_form_traces", indexes = {
        @Index(columnList = "formId, formType")
})
public class FormTrace extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36)
    String formId;

    ENUM_FORM_TYPE formType;

    @Column(length = 16)
    String event;

    @Column(columnDefinition = "text")
    String opinion;

    @Column(columnDefinition = "text")
    String address;

    @Transient
    String creatorName;

    @Transient
    String creatorMp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId;
    }

    public ENUM_FORM_TYPE getFormType() {
        return formType;
    }

    public void setFormType(ENUM_FORM_TYPE formType) {
        this.formType = formType;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String step) {
        this.event = step;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatorMp() {
        return creatorMp;
    }

    public void setCreatorMp(String creatorMp) {
        this.creatorMp = creatorMp;
    }

    public enum ENUM_FORM_TYPE {

        抵返汕人员报备(0),
        货运司机报备(1),
        金凤码(2);

        int value;

        ENUM_FORM_TYPE(int value) {
            this.value = value;
        }

        public static ENUM_FORM_TYPE valueOf(int value) {
            switch (value) {
                case 0:
                    return 抵返汕人员报备;
                case 1:
                    return 货运司机报备;
                case 2:
                    return 金凤码;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }
    }

}