package com.chinamobile.healthcode.model.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Entity
@Table(name = "unit_descriptions")
public class UnitDescription extends AbstractEntity implements ProjectDescription {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 128, nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    @Size(max = 128)
    String name;

    @Column(length = 36)
    String regionId;

    @Column(length = 128)
    String regionFullName;

    @Column(length = 36)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    @Size(max = 36)
    String type;

    @Column(columnDefinition = "text")
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    String address;

    // gcj经度
    Double lng;

    // gcj纬度
    Double lat;

    // wgs经度
    Double lngInWgs;

    // wgs纬度
    Double latInWgs;

    @Override
    public Form toForm() {
        Form _form = new Form();
        _form.setRefId(getId());

        _form.setUnitName(getName());
        _form.setUnitAddress(getAddress());

        _form.setRegionId(getRegionId());
        _form.setRegionFullName(getRegionFullName());
        _form.setLng(getLng());
        _form.setLat(getLat());
        _form.setLngInWgs(getLngInWgs());
        _form.setLatInWgs(getLatInWgs());

        return _form;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String title) {
        this.name = StringUtils.trimWhitespace(title);
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = StringUtils.trimWhitespace(address);
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLngInWgs() {
        return lngInWgs;
    }

    public void setLngInWgs(Double lngInWgs) {
        this.lngInWgs = lngInWgs;
    }

    public Double getLatInWgs() {
        return latInWgs;
    }

    public void setLatInWgs(Double latInWgs) {
        this.latInWgs = latInWgs;
    }

}
