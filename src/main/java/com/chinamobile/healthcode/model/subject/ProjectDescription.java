package com.chinamobile.healthcode.model.subject;

import com.chinamobile.healthcode.model.project.Form;

/**
 * <AUTHOR>
 * @date 9/26/2023 15:55
 */
public interface ProjectDescription {
    /**
     * 专题数据转换至专项工作
     *
     * @return 专项工作表单
     */
    Form toForm();

    /**
     * 获取id
     *
     * @return id
     */
    String getId();

    /**
     * 获取行政区域id
     *
     * @param regionId 行政区域id
     */
    void setRegionId(String regionId);

    /**
     * 获取行政区域
     *
     * @param regionFullName 行政区域
     */
    void setRegionFullName(String regionFullName);

    /**
     * 获取经度
     *
     * @param lng 经度
     */
    void setLng(Double lng);

    /**
     * 获取纬度
     *
     * @param lat 纬度
     */
    void setLat(Double lat);

    /**
     * 获取经度
     *
     * @param lngInWgs wgs经度
     */
    void setLngInWgs(Double lngInWgs);

    /**
     * 获取纬度
     *
     * @param latInWgs wgs纬度
     */
    void setLatInWgs(Double latInWgs);
}
