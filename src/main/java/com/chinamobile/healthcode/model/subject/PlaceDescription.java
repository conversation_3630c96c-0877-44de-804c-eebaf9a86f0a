package com.chinamobile.healthcode.model.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 5/19/2023 17:12
 */
@Entity
@Table(name = "subject_place", indexes = {
//        @Index(columnList = "regionFullName, placeName, placeTypeValue"),
        @Index(columnList = "createTime")
})
public class PlaceDescription extends AbstractEntity implements ProjectDescription {
    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36, nullable = false)
    String regionId;

    @Column(length = 128, nullable = false)
    String regionFullName;

    @Transient
    String placeTypeName;

    @Column(length = 3, nullable = false)
    @NotNull
    String placeTypeValue;

    @Column(length = 128, nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    String placeName;

    @Column(length = 32)
    String personInCharge;

    @Column(length = 64)
    String credentialNo;

    @Column(length = 16)
    String contact;

    Integer permanentPopulation;

    Integer externalPopulation;

    @Column(columnDefinition = "text", nullable = false)
    @NotBlank(groups = {ValidationGroup.Insert.class, ValidationGroup.Update.class})
    String address;

    Double lng;

    Double lat;

    Double lngInWgs;

    Double latInWgs;

    Double area;

    @Column(length = 255)
    String remarks;

    /**
     * 专项工作
     * 附件
     * 7/10/2023改为佐证材料
     */
    @Transient
    List<String> attachmentIds;


    @Transient
    List<Media> attachments;


    @Column(columnDefinition = "text")
    String attachmentIdsJSON;

    @Transient
    private List<PlacePersonDescription> persons;

    @Override
    public Form toForm() {
        Form _form = new Form();
        _form.setRefId(getId());

        _form.setSiteName(getPlaceName());
        _form.setPersonInCharge(getPersonInCharge());
        _form.setPersonInChargeCredentialNo(getCredentialNo());
        _form.setPersonInChargeContact(getContact());
        _form.setDetailedAddress(getAddress());
        _form.setPermanentPopulation(getPermanentPopulation());
        _form.setExternalPopulation(getExternalPopulation());
        _form.setArea(getArea());
        _form.setAttachmentIdsJSON(getAttachmentIdsJSON());

        _form.setRegionId(getRegionId());
        _form.setRegionFullName(getRegionFullName());
        _form.setLng(getLng());
        _form.setLat(getLat());
        _form.setLngInWgs(getLngInWgs());
        _form.setLatInWgs(getLatInWgs());

        return _form;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public String getPlaceTypeName() {
        return placeTypeName;
    }

    public void setPlaceTypeName(String placeTypeName) {
        this.placeTypeName = placeTypeName;
    }

    public String getPlaceTypeValue() {
        return placeTypeValue;
    }

    public void setPlaceTypeValue(String placeTypeValue) {
        this.placeTypeValue = placeTypeValue;
    }

    public String getPlaceName() {
        return placeName;
    }

    public void setPlaceName(String placeName) {
        this.placeName = placeName;
    }

    public String getPersonInCharge() {
        return personInCharge;
    }

    public void setPersonInCharge(String personInCharge) {
        this.personInCharge = personInCharge;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNo) {
        this.credentialNo = credentialNo;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public Integer getPermanentPopulation() {
        return permanentPopulation;
    }

    public void setPermanentPopulation(Integer permanentPopulation) {
        this.permanentPopulation = permanentPopulation;
    }

    public Integer getExternalPopulation() {
        return externalPopulation;
    }

    public void setExternalPopulation(Integer externalPopulation) {
        this.externalPopulation = externalPopulation;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLngInWgs() {
        return lngInWgs;
    }

    public void setLngInWgs(Double lngInWgs) {
        this.lngInWgs = lngInWgs;
    }

    public Double getLatInWgs() {
        return latInWgs;
    }

    public void setLatInWgs(Double latInWgs) {
        this.latInWgs = latInWgs;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public List<String> getAttachmentIds() {
        return attachmentIds;
    }

    public void setAttachmentIds(List<String> attachmentIds) {
        this.attachmentIds = attachmentIds;
    }

    public List<Media> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Media> attachments) {
        this.attachments = attachments;
    }

    public String getAttachmentIdsJSON() {
        return attachmentIdsJSON;
    }

    public void setAttachmentIdsJSON(String attachmentIdsJSON) {
        this.attachmentIdsJSON = attachmentIdsJSON;
    }

    public List<PlacePersonDescription> getPersons() {
        return persons;
    }

    public void setPersons(List<PlacePersonDescription> persons) {
        this.persons = persons;
    }
}
