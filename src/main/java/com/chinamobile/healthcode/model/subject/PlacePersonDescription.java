package com.chinamobile.healthcode.model.subject;

import com.chinamobile.healthcode.model.citizen.Profile;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 5/19/2023 18:10
 */
@Entity
@Table(name = "subject_place_person")
public class PlacePersonDescription extends AbstractEntity {
    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36, nullable = false)
    String placeId;

    @Column(length = 36)
    String profileId;

    @Column(length = 36, nullable = false)
    String regionId;

    @Column(length = 128, nullable = false)
    String regionFullName;

    @Column(length = 32, nullable = false)
    String name;

    @Column(length = 64, nullable = false)
    String credentialNo;

    @Column(length = 16, nullable = false)
    String contact;

    /**
     * 户籍地址
     */
    @Column(length = 255, nullable = false)
    String hkLocation;

    @Column(columnDefinition = "text", nullable = false)
    String address;

    /**
     * 从业/学习情况
     */
    @Column(length = 64, nullable = false)
    String careerSituation;

    /**
     * 现工作/学习地点
     */
    @Column(columnDefinition = "text", nullable = false)
    String workplace;

    @Transient
    String subjectCategoryName;

    /**
     * 重点人群类别
     */
    @Column(length = 128)
    String subjectCategoryValue;

    /**
     * 外出情况
     */
    @Column(length = 128)
    String migrantSituation;

    @Transient
    String occupancyStateName;

    /**
     * 人员状态
     */
    @Column(length = 3, nullable = false)
    String occupancyStateValue;

    @Column(length = 255)
    String remarks;

    public Profile toProfile() {
        Profile _profile = new Profile();
        _profile.setSubdistrictId(getRegionId());
        _profile.setSubdistrictFullName(getRegionFullName());
        _profile.setName(getName());
        _profile.setCredentialType(Profile.ENUM_CREDENTIAL_TYPE.身份证);
        _profile.setCredentialNo(getCredentialNo());
        _profile.setAddress(getAddress());
        _profile.setMp(getContact());
        _profile.setStatus("1".equals(getOccupancyStateValue()) ? Profile.ENUM_STATUS.在册 : Profile.ENUM_STATUS.外出);
        _profile.setStay(Profile.ENUM_STAY.暂住);
        _profile.setActor(Profile.ENUM_ACTOR.网格员);
        _profile.setValidation(Profile.ENUM_VALIDATION.待验证);

        String _subjectCategoryValue = Optional.ofNullable(getSubjectCategoryValue())
                .orElse("");
        String _crowdIdJsons = Arrays.stream(_subjectCategoryValue.split(","))
                .filter(StringUtils::hasLength)
                .collect(Collectors.joining("\",\""));
        _profile.setCrowdIdsJSON("[\"" + _crowdIdJsons + "\"]");

        return _profile;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPlaceId() {
        return placeId;
    }

    public void setPlaceId(String placeId) {
        this.placeId = placeId;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNo) {
        this.credentialNo = credentialNo;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getHkLocation() {
        return hkLocation;
    }

    public void setHkLocation(String hkLocation) {
        this.hkLocation = hkLocation;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCareerSituation() {
        return careerSituation;
    }

    public void setCareerSituation(String careerSituation) {
        this.careerSituation = careerSituation;
    }

    public String getWorkplace() {
        return workplace;
    }

    public void setWorkplace(String workplace) {
        this.workplace = workplace;
    }

    public String getSubjectCategoryName() {
        return subjectCategoryName;
    }

    public void setSubjectCategoryName(String subjectCategoryName) {
        this.subjectCategoryName = subjectCategoryName;
    }

    public String getSubjectCategoryValue() {
        return subjectCategoryValue;
    }

    public void setSubjectCategoryValue(String subjectCategoryValue) {
        this.subjectCategoryValue = subjectCategoryValue;
    }

    public String getMigrantSituation() {
        return migrantSituation;
    }

    public void setMigrantSituation(String migrantSituation) {
        this.migrantSituation = migrantSituation;
    }

    public String getOccupancyStateName() {
        return occupancyStateName;
    }

    public void setOccupancyStateName(String occupancyStateName) {
        this.occupancyStateName = occupancyStateName;
    }

    public String getOccupancyStateValue() {
        return occupancyStateValue;
    }

    public void setOccupancyStateValue(String occupancyStateValue) {
        this.occupancyStateValue = occupancyStateValue;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
