package com.chinamobile.healthcode.model.truck;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "truck_enter_registration_ticket", indexes = {
        @Index(columnList = "id, driver_mobile_number, examineTime"),
        @Index(columnList = "destination_region_id"),
        @Index(columnList = "destination_company_id"),
        @Index(columnList = "driver_mobile_number"),
        @Index(columnList = "state"),
        @Index(columnList = "creatorId"),
        @Index(columnList = "createTime")
})
public class TruckEnterRegistrationTicket extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(name = "origin_region_id", length = 18)
    String originRegionId;

    @Column(name = "origin_region_full_name", columnDefinition = "text")
    String originRegionFullName;

    @Column(name = "origin_company_name", columnDefinition = "text")
    String originCompanyName;

    @Column(name = "origin_contact_name")
    String originContactName;

    @Column(name = "origin_contact_mobile_number", length = 11)
    String originContactMobileNumber;

    @Column(name = "`license_plate_number`", length = 16, nullable = false)
    String licensePlateNumber;

    @Column(name = "`from_location`", columnDefinition = "text", nullable = false)
    String fromLocation;

    Boolean risk;

    @Column(name = "destination_region_id", length = 18)
    String destinationRegionId;

    @Column(name = "destination_region_full_name", columnDefinition = "text")
    String destinationRegionFullName;

    @Column(name = "destination_company_id", length = 18)
    String destinationCompanyId;

    @Column(name = "destination_company_name", columnDefinition = "text")
    String destinationCompanyName;

    @Column(nullable = false)
    Date eta;

    Date etd;

    @Column(name = "driver_name", length = 16, nullable = false)
    String driverName;

    @Column(name = "credential_type", nullable = false)
    ENUM_CREDENTIAL_TYPE credentialType = ENUM_CREDENTIAL_TYPE.身份证;

    @Column(name = "driver_identity_card_number", length = 32)
    String driverIdentityCardNumber;

    @Column(name = "driver_mobile_number", length = 11, nullable = false)
    String driverMobileNumber;

    @Column(name = "driver_green_health_code")
    Boolean driverGreenHealthCode;

    @Column(name = "`driver_nucleic_acid_negative`")
    Boolean driverNucleicAcidNegative;

    @Column(nullable = false)
    ENUM_STATUS state;

    @Column(length = 18)
    String dispatchedCommunityId;

    @Column(length = 18)
    String dispatchedCommunityFullName;

    @Column(length = 18)
    String examinerId;

    @Column(length = 16)
    String examinerName;

    @Column(columnDefinition = "text")
    String examinerAddress;

    Date examineTime;

    @Transient
    List<TruckEnterRegistrationPassenger> passengerList;

    @Transient
    boolean editable;

    @Transient
    String url;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOriginRegionId() {
        return originRegionId;
    }

    public void setOriginRegionId(String originRegionId) {
        this.originRegionId = originRegionId;
    }

    public String getOriginRegionFullName() {
        return originRegionFullName;
    }

    public void setOriginRegionFullName(String originRegionFullName) {
        this.originRegionFullName = originRegionFullName;
    }

    public String getOriginCompanyName() {
        return originCompanyName;
    }

    public void setOriginCompanyName(String originCompanyName) {
        this.originCompanyName = StringUtils.trimWhitespace(originCompanyName);
    }

    public String getOriginContactName() {
        return originContactName;
    }

    public void setOriginContactName(String originContactName) {
        this.originContactName = StringUtils.trimWhitespace(originContactName);
    }

    public String getOriginContactMobileNumber() {
        return originContactMobileNumber;
    }

    public void setOriginContactMobileNumber(String originContactMobileNumber) {
        this.originContactMobileNumber = StringUtils.trimWhitespace(originContactMobileNumber);
    }

    public String getLicensePlateNumber() {
        return licensePlateNumber;
    }

    public void setLicensePlateNumber(String licensePlateNumber) {
        this.licensePlateNumber = StringUtils.trimWhitespace(licensePlateNumber);
    }

    public String getFromLocation() {
        return fromLocation;
    }

    public void setFromLocation(String fromLocation) {
        this.fromLocation = StringUtils.trimWhitespace(fromLocation);
    }

    public boolean getRisk() {
        return risk;
    }

    public void setRisk(boolean risk) {
        this.risk = risk;
    }

    public String getDestinationRegionId() {
        return destinationRegionId;
    }

    public void setDestinationRegionId(String destinationRegionId) {
        this.destinationRegionId = destinationRegionId;
    }

    public String getDestinationRegionFullName() {
        return destinationRegionFullName;
    }

    public void setDestinationRegionFullName(String destinationRegionFullName) {
        this.destinationRegionFullName = destinationRegionFullName;
    }

    public String getDestinationCompanyId() {
        return destinationCompanyId;
    }

    public void setDestinationCompanyId(String destinationCompanyId) {
        this.destinationCompanyId = destinationCompanyId;
    }

    public String getDestinationCompanyName() {
        return destinationCompanyName;
    }

    public void setDestinationCompanyName(String destinationCompanyName) {
        this.destinationCompanyName = destinationCompanyName;
    }

    public Date getEta() {
        return eta;
    }

    public void setEta(Date eta) {
        this.eta = eta;
    }

    public Date getEtd() {
        return etd;
    }

    public void setEtd(Date etd) {
        this.etd = etd;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = StringUtils.trimWhitespace(driverName);
    }

    public ENUM_CREDENTIAL_TYPE getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(ENUM_CREDENTIAL_TYPE credentialType) {
        this.credentialType = credentialType;
    }

    public String getDriverIdentityCardNumber() {
        return driverIdentityCardNumber;
    }

    public void setDriverIdentityCardNumber(String driverIdentityCardNumber) {
        this.driverIdentityCardNumber = StringUtils.trimWhitespace(driverIdentityCardNumber);
    }

    public String getDriverMobileNumber() {
        return driverMobileNumber;
    }

    public void setDriverMobileNumber(String driverMobileNumber) {
        this.driverMobileNumber = StringUtils.trimWhitespace(driverMobileNumber);
    }

    public Boolean getDriverGreenHealthCode() {
        return driverGreenHealthCode;
    }

    public void setDriverGreenHealthCode(Boolean driverGreenHealthCode) {
        this.driverGreenHealthCode = driverGreenHealthCode;
    }

    public Boolean getDriverNucleicAcidNegative() {
        return driverNucleicAcidNegative;
    }

    public void setDriverNucleicAcidNegative(Boolean driverNucleicAcidNegative) {
        this.driverNucleicAcidNegative = driverNucleicAcidNegative;
    }

    public ENUM_STATUS getState() {
        return state;
    }

    public void setState(ENUM_STATUS state) {
        this.state = state;
    }

    public String getDispatchedCommunityId() {
        return dispatchedCommunityId;
    }

    public void setDispatchedCommunityId(String dispatchedCommunityId) {
        this.dispatchedCommunityId = dispatchedCommunityId;
    }

    public String getDispatchedCommunityFullName() {
        return dispatchedCommunityFullName;
    }

    public void setDispatchedCommunityFullName(String dispatchedCommunityFullName) {
        this.dispatchedCommunityFullName = dispatchedCommunityFullName;
    }

    public String getExaminerId() {
        return examinerId;
    }

    public void setExaminerId(String examinerId) {
        this.examinerId = examinerId;
    }

    public String getExaminerName() {
        return examinerName;
    }

    public void setExaminerName(String examinerName) {
        this.examinerName = examinerName;
    }

    public String getExaminerAddress() {
        return examinerAddress;
    }

    public void setExaminerAddress(String examinerAddress) {
        this.examinerAddress = examinerAddress;
    }

    public Date getExamineTime() {
        return examineTime;
    }

    public void setExamineTime(Date examineTime) {
        this.examineTime = examineTime;
    }

    public List<TruckEnterRegistrationPassenger> getPassengerList() {
        return passengerList;
    }

    public void setPassengerList(List<TruckEnterRegistrationPassenger> passengerList) {
        this.passengerList = passengerList;
    }

    public boolean isEditable() {
        return editable;
    }

    public void setEditable(boolean editable) {
        this.editable = editable;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public enum ENUM_STATUS {
        /**
         * 草稿
         */
        DRAFT(0),
        /**
         * 园区确认
         */
        ZONE(1),
        /**
         * 街道+社区确认
         */
        STREET(2),
        /**
         * 检查通过
         */
        PASSED(3),
        /**
         * 园区+街道退回
         */
        DENIED(4),
        /**
         * 街道已指派
         */
        DISPATCHED(5),
        /**
         * 社区拒收
         */
        REJECTED(6);

        int value;

        ENUM_STATUS(int value) {
            this.value = value;
        }

        public static ENUM_STATUS valueOf(int value) {
            switch (value) {
                case 0:
                    return DRAFT;
                case 1:
                    return ZONE;
                case 2:
                    return STREET;
                case 3:
                    return PASSED;
                case 4:
                    return DENIED;
                case 5:
                    return DISPATCHED;
                case 6:
                    return REJECTED;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }
    }

    public enum ENUM_CREDENTIAL_TYPE {
        /**
         * 身份证
         */
        身份证(0),
        /**
         * 其它
         */
        其它(1);

        int value;

        ENUM_CREDENTIAL_TYPE(int value) {
            this.value = value;
        }

        public static ENUM_CREDENTIAL_TYPE valueOf(int value) {
            if (value == 0) {
                return 身份证;
            } else if (value == 1) {
                return 其它;
            }
            return null;
        }

        public int value() {
            return this.value;
        }
    }

}