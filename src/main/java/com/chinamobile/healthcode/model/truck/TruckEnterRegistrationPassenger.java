package com.chinamobile.healthcode.model.truck;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;

@Entity
@Table(name = "truck_enter_registration_passenger", indexes = {
        @Index(columnList = "ticket_id"),
        @Index(columnList = "mobile_number")
})
public class TruckEnterRegistrationPassenger extends AbstractEntity {

    @Id
    @Column(length = 18)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(name = "`ticket_id`", length = 36)
    String ticketId;

    String name;

    @Column(nullable = false)
    TruckEnterRegistrationTicket.ENUM_CREDENTIAL_TYPE credentialType = TruckEnterRegistrationTicket.ENUM_CREDENTIAL_TYPE.身份证;

    @Column(name = "`identity_card_number`", length = 32)
    String identityCardNumber;


    @Column(name = "`mobile_number`", length = 11)
    String mobileNumber;

    @Column(name = "`green_health_code`")
    Boolean greenHealthCode;

    @Column(name = "`nucleic_acid_negative`")
    Boolean nucleicAcidNegative;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public TruckEnterRegistrationTicket.ENUM_CREDENTIAL_TYPE getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(TruckEnterRegistrationTicket.ENUM_CREDENTIAL_TYPE credentialType) {
        this.credentialType = credentialType;
    }

    public String getIdentityCardNumber() {
        return identityCardNumber;
    }

    public void setIdentityCardNumber(String identityCardNumber) {
        this.identityCardNumber = StringUtils.trimWhitespace(identityCardNumber);
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = StringUtils.trimWhitespace(mobileNumber);
    }

    public Boolean getGreenHealthCode() {
        return greenHealthCode;
    }

    public void setGreenHealthCode(Boolean greenHealthCode) {
        this.greenHealthCode = greenHealthCode;
    }

    public Boolean getNucleicAcidNegative() {
        return nucleicAcidNegative;
    }

    public void setNucleicAcidNegative(Boolean nucleicAcidNegative) {
        this.nucleicAcidNegative = nucleicAcidNegative;
    }

}