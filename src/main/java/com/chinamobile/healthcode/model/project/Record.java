package com.chinamobile.healthcode.model.project;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "project_records")
public class Record extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    /**
     * 标题
     */
    @Column(length = 128, nullable = false)
    String title;

    @Column(length = 128)
    String type;

    @Column(length = 128)
    String subtype;

    @Column(columnDefinition = "text")
    String description;

    /**
     * 图例
     */
    @Transient
    List<String> legendIds;


    @Transient
    List<Media> legends;


    @Column(columnDefinition = "text")
    String legendIdsJSON;

    /**
     * 操作指引
     */
    @Column
    String guide;

    /**
     * 个人填报上限
     */
    Integer quota;

    /**
     * 截止日期
     */
    Date deadline;

    @Column(columnDefinition = "text", nullable = false)
    String formDefinition;

    @Column(columnDefinition = "text", nullable = false)
    String assigneeIdsJSON;

    @Transient
    List<String> assigneeIds;

    @Transient
    List<Department> assignees;

    ENUM_STATUS status;

    boolean readonly;

    /**
     * 是否进行初始化，从专题导入数据
     */
    boolean initialized;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubtype() {
        return subtype;
    }

    public void setSubtype(String subtype) {
        this.subtype = subtype;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getLegendIds() {
        return legendIds;
    }

    public void setLegendIds(List<String> legendIds) {
        this.legendIds = legendIds;
    }

    public List<Media> getLegends() {
        return legends;
    }

    public void setLegends(List<Media> legends) {
        this.legends = legends;
    }

    public String getLegendIdsJSON() {
        return legendIdsJSON;
    }

    public void setLegendIdsJSON(String legendIdsJSON) {
        this.legendIdsJSON = legendIdsJSON;
    }

    public Integer getQuota() {
        return quota;
    }

    public void setQuota(Integer quota) {
        this.quota = quota;
    }

    public Date getDeadline() {
        return deadline;
    }

    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    public String getFormDefinition() {
        return formDefinition;
    }

    public void setFormDefinition(String formDefinition) {
        this.formDefinition = formDefinition;
    }

    public String getGuide() {
        return guide;
    }

    public void setGuide(String guide) {
        this.guide = guide;
    }

    public String getAssigneeIdsJSON() {
        return assigneeIdsJSON;
    }

    public void setAssigneeIdsJSON(String assigneeIdsJSON) {
        this.assigneeIdsJSON = assigneeIdsJSON;
    }

    public List<String> getAssigneeIds() {
        return assigneeIds;
    }

    public void setAssigneeIds(List<String> assigneeIds) {
        this.assigneeIds = assigneeIds;
    }

    public List<Department> getAssignees() {
        return assignees;
    }

    public void setAssignees(List<Department> assignees) {
        this.assignees = assignees;
    }

    public ENUM_STATUS getStatus() {
        return status;
    }

    public void setStatus(ENUM_STATUS status) {
        this.status = status;
    }

    public boolean getReadonly() {
        return readonly;
    }

    public void setReadonly(boolean readonly) {
        this.readonly = readonly;
    }

    public enum ENUM_STATUS {

        DRAFT(0),
        PUBLISHED(1),
        CLOSED(2),
        OVERTIME(3);

        int value;

        ENUM_STATUS(int value) {
            this.value = value;
        }

        public static ENUM_STATUS valueOf(int value) {
            switch (value) {
                case 0:
                    return DRAFT;
                case 1:
                    return PUBLISHED;
                case 2:
                    return CLOSED;
                case 3:
                    return OVERTIME;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }

    }

    public boolean isInitialized() {
        return initialized;
    }

    public void setInitialized(boolean initialized) {
        this.initialized = initialized;
    }
}
