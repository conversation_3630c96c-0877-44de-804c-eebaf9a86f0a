package com.chinamobile.healthcode.model.project;

import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import org.jinq.orm.stream.JinqStream;

import java.lang.reflect.Type;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 7/14/2023 10:40
 */
public class FormQueryDto {
    public static class JsonDateDeserializer implements JsonDeserializer<Date> {
        @Override
        public Date deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
            Date date;
            if (jsonElement.isJsonNull()) {
                date = null;
            } else {
                String dateStr = jsonElement.getAsString();
                date = DateUtil.from(dateStr, "yyyy-MM-dd HH:mm:ss");
            }
            return date;
        }
    }

    /**
     * 创建人关键字
     */
    private String creator;

    /**
     * 所需行政区域
     */
    private String regionFullName;

    /**
     * 搜索关键字
     */
    private String keyword;

    private Date startTime;

    private Date endTime;

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}
