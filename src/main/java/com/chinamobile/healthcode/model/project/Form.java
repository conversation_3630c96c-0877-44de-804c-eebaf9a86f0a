package com.chinamobile.healthcode.model.project;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 7/3/2023 16:56
 */
@Entity
@Table(name = "project_forms", indexes = {
        @Index(columnList = "projectId"),
        @Index(columnList = "projectId, taskId")
})
public class Form extends AbstractEntity {
    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36)
    String projectId;

    @Column(length = 36)
    String taskId;

    @Column(length = 36)
    String refId;

    /**
     * 所属行政区划
     */
    @Column(length = 36)
    String regionId;

    @Column(length = 128)
    String regionFullName;

    /**
     * 巡查时间
     * 7/9/2023修改为任务时间
     */
    Date patrolTime;

    /**
     * 详细地址
     */
    @Column
    String address;

    /**
     * gcj经度
     */
    Double lng;

    /**
     * gcj纬度
     */
    Double lat;

    /**
     * wgs经度
     */
    Double lngInWgs;

    /**
     * wgs纬度
     */
    Double latInWgs;

    /**
     * 相关问题描述
     */
    @Column(columnDefinition = "text")
    String description;

    /**
     * 其他说明
     */
    @Column(columnDefinition = "text")
    String remarks;

    /**
     * 备注
     */
    @Column(columnDefinition = "text")
    String memo;

    /**
     * 附件
     * 7/10/2023改为佐证材料
     */
    @Transient
    List<String> attachmentIds;


    @Transient
    List<Media> attachments;


    @Column(columnDefinition = "text")
    String attachmentIdsJSON;

    /**
     * 姓名
     */
    @Column(length = 32)
    String personName;

    /**
     * 性别
     */
    Boolean sex;

    /**
     * 年龄
     */
    Integer age;

    /**
     * 出生年月
     */
    Date dateOfBirth;

    /**
     * 证件类型
     */
    @Column(length = 32)
    String credentialType;

    /**
     * 证件号码
     */
    @Column(length = 64)
    String credentialNo;

    /**
     * 国籍
     */
    @Column(length = 64)
    String nationality;

    /**
     * 民族
     */
    @Column(length = 64)
    String ethnicGroup;

    /**
     * 居住类型
     */
    @Column(length = 8)
    String stay;

    /**
     * 居住地址
     */
    @Column
    String residentialAddress;

    /**
     * 详细地址
     */
    @Column
    String personDetailedAddress;

    /**
     * 户籍地址
     */
    @Column
    String householdRegistrationAddress;

    /**
     * 户口所在地
     */
    @Column
    String householdRegistrationLocation;

    /**
     * 政治面貌
     */
    @Column(length = 8)
    String politicalStatus;

    /**
     * 婚姻情况
     */
    @Column(length = 8)
    String maritalStatus;

    /**
     * 联系方式
     */
    @Column(length = 16)
    String contact;

    /**
     * 学历
     */
    @Column(length = 16)
    String education;

    /**
     * 学位
     */
    @Column(length = 16)
    String degree;

    /**
     * 毕业院校
     */
    @Column(length = 64)
    String university;

    /**
     * 毕业日期
     */
    Date dateOfGraduation;

    /**
     * 工作经历
     */
    @Column(columnDefinition = "text")
    String workExperience;

    /**
     * 工作单位
     */
    @Column
    String workplace;

    /**
     * 人员类别
     */
    @Column(length = 16)
    String actor;

    /**
     * 实有人口人员状态
     */
    @Column(length = 8)
    String personState;

    /**
     * 严重精神障碍患者人数
     */
    Integer numOfPeopleWithMentalIllness;

    /**
     * 刑释安置帮教人员人数
     */
    Integer numOfFormerPrisoner;

    /**
     * 社区矫正对象人数
     */
    Integer numOfCommunityCorrectionPeople;

    /**
     * 吸毒人员人数
     */
    Integer numOfDrugAddict;

    /**
     * 重点青少年人数
     */
    Integer numOfTargetedYouth;

    /**
     * 易受侵害妇女人数
     */
    Integer numOfVulnerableWoman;

    /**
     * 特困户、低保在册人员人数
     */
    Integer numOfLowIncomeFamily;

    /**
     * 孕产妇人数
     */
    Integer numOfPregnantWoman;

    /**
     * 独居残疾人数
     */
    Integer numOfPeopleWithDisabilities;

    /**
     * 独居老人人数
     */
    Integer numOfOlderAdultsLivingAlone;

    /**
     * 重症人数
     */
    Integer numOfPeopleWithSeriousIllness;

    /**
     * 相关人1姓名
     */
    @Column(length = 32)
    String relatedPersonName1;

    /**
     * 相关人1身份证号码
     */
    @Column(length = 32)
    String relatedPersonIdCardNo1;

    /**
     * 相关人1状态
     */
    @Column(length = 8)
    String relatedPersonState1;

    /**
     * 相关人2姓名
     */
    @Column(length = 32)
    String relatedPersonName2;

    /**
     * 相关人2身份证号码
     */
    @Column(length = 32)
    String relatedPersonIdCardNo2;

    /**
     * 相关人2状态
     */
    @Column(length = 8)
    String relatedPersonState2;

    /**
     * 相关人3姓名
     */
    @Column(length = 32)
    String relatedPersonName3;

    /**
     * 相关人3身份证号码
     */
    @Column(length = 32)
    String relatedPersonIdCardNo3;

    /**
     * 相关人3状态
     */
    @Column(length = 8)
    String relatedPersonState3;

    /**
     * 相关人4姓名
     */
    @Column(length = 32)
    String relatedPersonName4;

    /**
     * 相关人4身份证号码
     */
    @Column(length = 32)
    String relatedPersonIdCardNo4;

    /**
     * 相关人4状态
     */
    @Column(length = 8)
    String relatedPersonState4;

    /**
     * 相关人5姓名
     */
    @Column(length = 32)
    String relatedPersonName5;

    /**
     * 相关人5身份证号码
     */
    @Column(length = 32)
    String relatedPersonIdCardNo5;

    /**
     * 相关人5状态
     */
    @Column(length = 8)
    String relatedPersonState5;

    /**
     * 相关人6姓名
     */
    @Column(length = 32)
    String relatedPersonName6;

    /**
     * 相关人6身份证号码
     */
    @Column(length = 32)
    String relatedPersonIdCardNo6;

    /**
     * 相关人6状态
     */
    @Column(length = 8)
    String relatedPersonState6;

    /**
     * 相关人7姓名
     */
    @Column(length = 32)
    String relatedPersonName7;

    /**
     * 相关人7身份证号码
     */
    @Column(length = 32)
    String relatedPersonIdCardNo7;

    /**
     * 相关人7状态
     */
    @Column(length = 8)
    String relatedPersonState7;

    /**
     * 相关人8姓名
     */
    @Column(length = 32)
    String relatedPersonName8;

    /**
     * 相关人8身份证号码
     */
    @Column(length = 32)
    String relatedPersonIdCardNo8;

    /**
     * 相关人8状态
     */
    @Column(length = 8)
    String relatedPersonState8;

    /**
     * 相关人9姓名
     */
    @Column(length = 32)
    String relatedPersonName9;

    /**
     * 相关人9身份证号码
     */
    @Column(length = 32)
    String relatedPersonIdCardNo9;

    /**
     * 相关人9状态
     */
    @Column(length = 8)
    String relatedPersonState9;

    /**
     * 相关人10姓名
     */
    @Column(length = 32)
    String relatedPersonName10;

    /**
     * 相关人10身份证号码
     */
    @Column(length = 32)
    String relatedPersonIdCardNo10;

    /**
     * 相关人10状态
     */
    @Column(length = 8)
    String relatedPersonState10;

    /**
     * 重点人员人员状态
     */
    @Column(length = 8)
    String majorPersonState;

    /**
     * 是否低保户
     */
    Boolean lowIncomeFamily;

    /**
     * 监护人姓名
     */
    @Column(length = 32)
    String guardianName;

    /**
     * 与患者关系
     */
    @Column(length = 16)
    String guardianRelationship;

    /**
     * 最后一次服药
     */
    Date lastMedicineTime;

    /**
     * 监护人联系方式
     */
    @Column(length = 16)
    String guardianContact;

    /**
     * 危险性等级
     */
    @Column(length = 8)
    String riskLevel;

    /**
     * 最后一次尿检
     */
    Date lastUrineTime;

    /**
     * 释放时间
     */
    Date releasedTime;

    /**
     * 上访事由
     */
    @Column(columnDefinition = "text")
    String petition;

    /**
     * 是否签订《息诉罢访协议书》
     */
    Boolean achieveAgreement;

    /**
     * 矫正开始时间
     */
    Date rectifyFrom;

    /**
     * 矫正期限
     */
    @Column(length = 16)
    String rectifyTimeLimit;

    /**
     *事发地
     */
    @Column
    String eventAddress;

    /**
     *事件名称
     */
    @Column(length = 128)
    String eventName;

    /**
     *事件概述
     */
    @Column(columnDefinition = "text")
    String eventDescription;

    /**
     *场所名称
     */
    @Column(length = 128)
    String siteName;

    /**
     * 营业执照
     */
    @Column(length = 64)
    String licenseNo;

    /**
     * 营业执照有效期
     */
    Date expiryDateOfLicense;

    /**
     * 负责人姓名
     */
    @Column(length = 32)
    String personInCharge;

    /**
     * 身份证号码
     */
    @Column(length = 64)
    String personInChargeCredentialNo;

    /**
     * 联系电话
     */
    @Column(length = 16)
    String personInChargeContact;

    /**
     * 详细地址
     */
    @Column(length = 255)
    String detailedAddress;

    /**
     *场所状态
     */
    @Column(length = 16)
    String siteState;

    /**
     * 权属
     */
    @Column(length = 8)
    String ownerType;

    /**
     * 房间数
     */
    Integer numOfRooms;

    /**
     * 常住人口数
     */
    Integer permanentPopulation;

    /**
     * 其中外来人口数
     */
    Integer externalPopulation;

    /**
     * 占地面积
     */
    Double area;

    /**
     * 是否使用煤气
     */
    Boolean gas;

    /**
     * 是否使用瓶装煤气
     */
    Boolean bottledGas;

    /**
     * 是否存在煤气泄漏隐患
     */
    Boolean gasLeakRisk;

    /**
     * 是否存在煤气泄漏问题
     */
    Boolean gasLeakProblem;

    /**
     * 是否已拆除、已倒塌、已封堵、已加固
     */
    String dangerousBuildingState;

    /**
     *设施名称
     */
    @Column(length = 128)
    String installationName;

    /**
     *设施地址
     */
    @Column
    String installationAddress;

    /**
     *单位名称
     */
    @Column(length = 128)
    String unitName;

    /**
     * 单位/组织地址
     */
    @Column
    String unitAddress;

    /**
     * 是否正常操作
     */
    Boolean normalLogin;

    /**
     * 是否知晓操作
     * 7/10/2023改为能否熟练操作
     */
    Boolean knowOperation;

    @Column(length = 64)
    String size;

    @Column(length = 16)
    String ledBoardType;

    @Column(length = 16)
    String ledBoardWayToUpdate;

    @Column(length = 16)
    String ledBoardSelectedWayToUpdate;

    @Column(length = 16)
    String ledBoardPasswordPolicy;

    @Column(length = 16)
    String ledBoardContentType;

    @Column(length = 4)
    String yesOrNo1;

    @Column(length = 4)
    String yesOrNo2;

    @Column(length = 4)
    String yesOrNo3;

    @Column(length = 4)
    String yesOrNo4;

    @Column(length = 4)
    String yesOrNo5;

    @Column(length = 4)
    String noOrYes1;

    @Column(length = 4)
    String noOrYes2;

    @Column(length = 4)
    String noOrYes3;

    @Column(length = 4)
    String noOrYes4;

    @Column(length = 4)
    String noOrYes5;

    @Transient
    Boolean readOnly;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public Date getPatrolTime() {
        return patrolTime;
    }

    public void setPatrolTime(Date patrolTime) {
        this.patrolTime = patrolTime;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLngInWgs() {
        return lngInWgs;
    }

    public void setLngInWgs(Double lngInWgs) {
        this.lngInWgs = lngInWgs;
    }

    public Double getLatInWgs() {
        return latInWgs;
    }

    public void setLatInWgs(Double latInWgs) {
        this.latInWgs = latInWgs;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public List<String> getAttachmentIds() {
        return attachmentIds;
    }

    public void setAttachmentIds(List<String> attachmentIds) {
        this.attachmentIds = attachmentIds;
    }

    public List<Media> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Media> attachments) {
        this.attachments = attachments;
    }

    public String getAttachmentIdsJSON() {
        return attachmentIdsJSON;
    }

    public void setAttachmentIdsJSON(String attachmentIdsJSON) {
        this.attachmentIdsJSON = attachmentIdsJSON;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public Boolean getSex() {
        return sex;
    }

    public void setSex(Boolean sex) {
        this.sex = sex;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(String credentialType) {
        this.credentialType = credentialType;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNo) {
        this.credentialNo = credentialNo;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getEthnicGroup() {
        return ethnicGroup;
    }

    public void setEthnicGroup(String ethnicGroup) {
        this.ethnicGroup = ethnicGroup;
    }

    public String getStay() {
        return stay;
    }

    public void setStay(String stay) {
        this.stay = stay;
    }

    public String getResidentialAddress() {
        return residentialAddress;
    }

    public void setResidentialAddress(String residentialAddress) {
        this.residentialAddress = residentialAddress;
    }

    public String getPersonDetailedAddress() {
        return personDetailedAddress;
    }

    public void setPersonDetailedAddress(String personDetailedAddress) {
        this.personDetailedAddress = personDetailedAddress;
    }

    public String getHouseholdRegistrationAddress() {
        return householdRegistrationAddress;
    }

    public void setHouseholdRegistrationAddress(String householdRegistrationAddress) {
        this.householdRegistrationAddress = householdRegistrationAddress;
    }

    public String getHouseholdRegistrationLocation() {
        return householdRegistrationLocation;
    }

    public void setHouseholdRegistrationLocation(String householdRegistrationLocation) {
        this.householdRegistrationLocation = householdRegistrationLocation;
    }

    public String getPoliticalStatus() {
        return politicalStatus;
    }

    public void setPoliticalStatus(String politicalStatus) {
        this.politicalStatus = politicalStatus;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getUniversity() {
        return university;
    }

    public void setUniversity(String university) {
        this.university = university;
    }

    public Date getDateOfGraduation() {
        return dateOfGraduation;
    }

    public void setDateOfGraduation(Date dateOfGraduation) {
        this.dateOfGraduation = dateOfGraduation;
    }

    public String getWorkExperience() {
        return workExperience;
    }

    public void setWorkExperience(String workExperience) {
        this.workExperience = workExperience;
    }

    public String getWorkplace() {
        return workplace;
    }

    public void setWorkplace(String workplace) {
        this.workplace = workplace;
    }

    public String getActor() {
        return actor;
    }

    public void setActor(String actor) {
        this.actor = actor;
    }

    public String getPersonState() {
        return personState;
    }

    public void setPersonState(String personState) {
        this.personState = personState;
    }

    public Integer getNumOfPeopleWithMentalIllness() {
        return numOfPeopleWithMentalIllness;
    }

    public void setNumOfPeopleWithMentalIllness(Integer numOfPeopleWithMentalIllness) {
        this.numOfPeopleWithMentalIllness = numOfPeopleWithMentalIllness;
    }

    public Integer getNumOfFormerPrisoner() {
        return numOfFormerPrisoner;
    }

    public void setNumOfFormerPrisoner(Integer numOfFormerPrisoner) {
        this.numOfFormerPrisoner = numOfFormerPrisoner;
    }

    public Integer getNumOfCommunityCorrectionPeople() {
        return numOfCommunityCorrectionPeople;
    }

    public void setNumOfCommunityCorrectionPeople(Integer numOfCommunityCorrectionPeople) {
        this.numOfCommunityCorrectionPeople = numOfCommunityCorrectionPeople;
    }

    public Integer getNumOfDrugAddict() {
        return numOfDrugAddict;
    }

    public void setNumOfDrugAddict(Integer numOfDrugAddict) {
        this.numOfDrugAddict = numOfDrugAddict;
    }

    public Integer getNumOfTargetedYouth() {
        return numOfTargetedYouth;
    }

    public void setNumOfTargetedYouth(Integer numOfTargetedYouth) {
        this.numOfTargetedYouth = numOfTargetedYouth;
    }

    public Integer getNumOfVulnerableWoman() {
        return numOfVulnerableWoman;
    }

    public void setNumOfVulnerableWoman(Integer numOfVulnerableWoman) {
        this.numOfVulnerableWoman = numOfVulnerableWoman;
    }

    public Integer getNumOfLowIncomeFamily() {
        return numOfLowIncomeFamily;
    }

    public void setNumOfLowIncomeFamily(Integer numOfLowIncomeFamily) {
        this.numOfLowIncomeFamily = numOfLowIncomeFamily;
    }

    public Integer getNumOfPregnantWoman() {
        return numOfPregnantWoman;
    }

    public void setNumOfPregnantWoman(Integer numOfPregnantWoman) {
        this.numOfPregnantWoman = numOfPregnantWoman;
    }

    public Integer getNumOfPeopleWithDisabilities() {
        return numOfPeopleWithDisabilities;
    }

    public void setNumOfPeopleWithDisabilities(Integer numOfPeopleWithDisabilities) {
        this.numOfPeopleWithDisabilities = numOfPeopleWithDisabilities;
    }

    public Integer getNumOfOlderAdultsLivingAlone() {
        return numOfOlderAdultsLivingAlone;
    }

    public void setNumOfOlderAdultsLivingAlone(Integer numOfOlderAdultsLivingAlone) {
        this.numOfOlderAdultsLivingAlone = numOfOlderAdultsLivingAlone;
    }

    public Integer getNumOfPeopleWithSeriousIllness() {
        return numOfPeopleWithSeriousIllness;
    }

    public void setNumOfPeopleWithSeriousIllness(Integer numOfPeopleWithSeriousIllness) {
        this.numOfPeopleWithSeriousIllness = numOfPeopleWithSeriousIllness;
    }

    public String getRelatedPersonName1() {
        return relatedPersonName1;
    }

    public void setRelatedPersonName1(String relatedPersonName1) {
        this.relatedPersonName1 = relatedPersonName1;
    }

    public String getRelatedPersonIdCardNo1() {
        return relatedPersonIdCardNo1;
    }

    public void setRelatedPersonIdCardNo1(String relatedPersonIdCardNo1) {
        this.relatedPersonIdCardNo1 = relatedPersonIdCardNo1;
    }

    public String getRelatedPersonState1() {
        return relatedPersonState1;
    }

    public void setRelatedPersonState1(String relatedPersonState1) {
        this.relatedPersonState1 = relatedPersonState1;
    }

    public String getRelatedPersonName2() {
        return relatedPersonName2;
    }

    public void setRelatedPersonName2(String relatedPersonName2) {
        this.relatedPersonName2 = relatedPersonName2;
    }

    public String getRelatedPersonIdCardNo2() {
        return relatedPersonIdCardNo2;
    }

    public void setRelatedPersonIdCardNo2(String relatedPersonIdCardNo2) {
        this.relatedPersonIdCardNo2 = relatedPersonIdCardNo2;
    }

    public String getRelatedPersonState2() {
        return relatedPersonState2;
    }

    public void setRelatedPersonState2(String relatedPersonState2) {
        this.relatedPersonState2 = relatedPersonState2;
    }

    public String getRelatedPersonName3() {
        return relatedPersonName3;
    }

    public void setRelatedPersonName3(String relatedPersonName3) {
        this.relatedPersonName3 = relatedPersonName3;
    }

    public String getRelatedPersonIdCardNo3() {
        return relatedPersonIdCardNo3;
    }

    public void setRelatedPersonIdCardNo3(String relatedPersonIdCardNo3) {
        this.relatedPersonIdCardNo3 = relatedPersonIdCardNo3;
    }

    public String getRelatedPersonState3() {
        return relatedPersonState3;
    }

    public void setRelatedPersonState3(String relatedPersonState3) {
        this.relatedPersonState3 = relatedPersonState3;
    }

    public String getRelatedPersonName4() {
        return relatedPersonName4;
    }

    public void setRelatedPersonName4(String relatedPersonName4) {
        this.relatedPersonName4 = relatedPersonName4;
    }

    public String getRelatedPersonIdCardNo4() {
        return relatedPersonIdCardNo4;
    }

    public void setRelatedPersonIdCardNo4(String relatedPersonIdCardNo4) {
        this.relatedPersonIdCardNo4 = relatedPersonIdCardNo4;
    }

    public String getRelatedPersonState4() {
        return relatedPersonState4;
    }

    public void setRelatedPersonState4(String relatedPersonState4) {
        this.relatedPersonState4 = relatedPersonState4;
    }

    public String getRelatedPersonName5() {
        return relatedPersonName5;
    }

    public void setRelatedPersonName5(String relatedPersonName5) {
        this.relatedPersonName5 = relatedPersonName5;
    }

    public String getRelatedPersonIdCardNo5() {
        return relatedPersonIdCardNo5;
    }

    public void setRelatedPersonIdCardNo5(String relatedPersonIdCardNo5) {
        this.relatedPersonIdCardNo5 = relatedPersonIdCardNo5;
    }

    public String getRelatedPersonState5() {
        return relatedPersonState5;
    }

    public void setRelatedPersonState5(String relatedPersonState5) {
        this.relatedPersonState5 = relatedPersonState5;
    }

    public String getRelatedPersonName6() {
        return relatedPersonName6;
    }

    public void setRelatedPersonName6(String relatedPersonName6) {
        this.relatedPersonName6 = relatedPersonName6;
    }

    public String getRelatedPersonIdCardNo6() {
        return relatedPersonIdCardNo6;
    }

    public void setRelatedPersonIdCardNo6(String relatedPersonIdCardNo6) {
        this.relatedPersonIdCardNo6 = relatedPersonIdCardNo6;
    }

    public String getRelatedPersonState6() {
        return relatedPersonState6;
    }

    public void setRelatedPersonState6(String relatedPersonState6) {
        this.relatedPersonState6 = relatedPersonState6;
    }

    public String getRelatedPersonName7() {
        return relatedPersonName7;
    }

    public void setRelatedPersonName7(String relatedPersonName7) {
        this.relatedPersonName7 = relatedPersonName7;
    }

    public String getRelatedPersonIdCardNo7() {
        return relatedPersonIdCardNo7;
    }

    public void setRelatedPersonIdCardNo7(String relatedPersonIdCardNo7) {
        this.relatedPersonIdCardNo7 = relatedPersonIdCardNo7;
    }

    public String getRelatedPersonState7() {
        return relatedPersonState7;
    }

    public void setRelatedPersonState7(String relatedPersonState7) {
        this.relatedPersonState7 = relatedPersonState7;
    }

    public String getRelatedPersonName8() {
        return relatedPersonName8;
    }

    public void setRelatedPersonName8(String relatedPersonName8) {
        this.relatedPersonName8 = relatedPersonName8;
    }

    public String getRelatedPersonIdCardNo8() {
        return relatedPersonIdCardNo8;
    }

    public void setRelatedPersonIdCardNo8(String relatedPersonIdCardNo8) {
        this.relatedPersonIdCardNo8 = relatedPersonIdCardNo8;
    }

    public String getRelatedPersonState8() {
        return relatedPersonState8;
    }

    public void setRelatedPersonState8(String relatedPersonState8) {
        this.relatedPersonState8 = relatedPersonState8;
    }

    public String getRelatedPersonName9() {
        return relatedPersonName9;
    }

    public void setRelatedPersonName9(String relatedPersonName9) {
        this.relatedPersonName9 = relatedPersonName9;
    }

    public String getRelatedPersonIdCardNo9() {
        return relatedPersonIdCardNo9;
    }

    public void setRelatedPersonIdCardNo9(String relatedPersonIdCardNo9) {
        this.relatedPersonIdCardNo9 = relatedPersonIdCardNo9;
    }

    public String getRelatedPersonState9() {
        return relatedPersonState9;
    }

    public void setRelatedPersonState9(String relatedPersonState9) {
        this.relatedPersonState9 = relatedPersonState9;
    }

    public String getRelatedPersonName10() {
        return relatedPersonName10;
    }

    public void setRelatedPersonName10(String relatedPersonName10) {
        this.relatedPersonName10 = relatedPersonName10;
    }

    public String getRelatedPersonIdCardNo10() {
        return relatedPersonIdCardNo10;
    }

    public void setRelatedPersonIdCardNo10(String relatedPersonIdCardNo10) {
        this.relatedPersonIdCardNo10 = relatedPersonIdCardNo10;
    }

    public String getRelatedPersonState10() {
        return relatedPersonState10;
    }

    public void setRelatedPersonState10(String relatedPersonState10) {
        this.relatedPersonState10 = relatedPersonState10;
    }

    public String getMajorPersonState() {
        return majorPersonState;
    }

    public void setMajorPersonState(String majorPersonState) {
        this.majorPersonState = majorPersonState;
    }

    public Boolean getLowIncomeFamily() {
        return lowIncomeFamily;
    }

    public void setLowIncomeFamily(Boolean lowIncomeFamily) {
        this.lowIncomeFamily = lowIncomeFamily;
    }

    public String getGuardianName() {
        return guardianName;
    }

    public void setGuardianName(String guardianName) {
        this.guardianName = guardianName;
    }

    public String getGuardianRelationship() {
        return guardianRelationship;
    }

    public void setGuardianRelationship(String guardianRelationship) {
        this.guardianRelationship = guardianRelationship;
    }

    public Date getLastMedicineTime() {
        return lastMedicineTime;
    }

    public void setLastMedicineTime(Date lastMedicineTime) {
        this.lastMedicineTime = lastMedicineTime;
    }

    public String getGuardianContact() {
        return guardianContact;
    }

    public void setGuardianContact(String guardianContact) {
        this.guardianContact = guardianContact;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public Date getLastUrineTime() {
        return lastUrineTime;
    }

    public void setLastUrineTime(Date lastUrineTime) {
        this.lastUrineTime = lastUrineTime;
    }

    public Date getReleasedTime() {
        return releasedTime;
    }

    public void setReleasedTime(Date releasedTime) {
        this.releasedTime = releasedTime;
    }

    public String getPetition() {
        return petition;
    }

    public void setPetition(String petition) {
        this.petition = petition;
    }

    public Boolean getAchieveAgreement() {
        return achieveAgreement;
    }

    public void setAchieveAgreement(Boolean achieveAgreement) {
        this.achieveAgreement = achieveAgreement;
    }

    public Date getRectifyFrom() {
        return rectifyFrom;
    }

    public void setRectifyFrom(Date rectifyFrom) {
        this.rectifyFrom = rectifyFrom;
    }

    public String getRectifyTimeLimit() {
        return rectifyTimeLimit;
    }

    public void setRectifyTimeLimit(String rectifyTimeLimit) {
        this.rectifyTimeLimit = rectifyTimeLimit;
    }

    public String getEventAddress() {
        return eventAddress;
    }

    public void setEventAddress(String eventAddress) {
        this.eventAddress = eventAddress;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    public Date getExpiryDateOfLicense() {
        return expiryDateOfLicense;
    }

    public void setExpiryDateOfLicense(Date expiryDateOfLicense) {
        this.expiryDateOfLicense = expiryDateOfLicense;
    }

    public String getPersonInCharge() {
        return personInCharge;
    }

    public void setPersonInCharge(String personInCharge) {
        this.personInCharge = personInCharge;
    }

    public String getPersonInChargeCredentialNo() {
        return personInChargeCredentialNo;
    }

    public void setPersonInChargeCredentialNo(String personInChargeCredentialNo) {
        this.personInChargeCredentialNo = personInChargeCredentialNo;
    }

    public String getPersonInChargeContact() {
        return personInChargeContact;
    }

    public void setPersonInChargeContact(String personInChargeContact) {
        this.personInChargeContact = personInChargeContact;
    }

    public String getDetailedAddress() {
        return detailedAddress;
    }

    public void setDetailedAddress(String detailedAddress) {
        this.detailedAddress = detailedAddress;
    }

    public String getSiteState() {
        return siteState;
    }

    public void setSiteState(String siteState) {
        this.siteState = siteState;
    }

    public String getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(String ownerType) {
        this.ownerType = ownerType;
    }

    public Integer getNumOfRooms() {
        return numOfRooms;
    }

    public void setNumOfRooms(Integer numOfRooms) {
        this.numOfRooms = numOfRooms;
    }

    public Integer getPermanentPopulation() {
        return permanentPopulation;
    }

    public void setPermanentPopulation(Integer permanentPopulation) {
        this.permanentPopulation = permanentPopulation;
    }

    public Integer getExternalPopulation() {
        return externalPopulation;
    }

    public void setExternalPopulation(Integer externalPopulation) {
        this.externalPopulation = externalPopulation;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public Boolean getGas() {
        return gas;
    }

    public void setGas(Boolean gas) {
        this.gas = gas;
    }

    public Boolean getBottledGas() {
        return bottledGas;
    }

    public void setBottledGas(Boolean bottledGas) {
        this.bottledGas = bottledGas;
    }

    public Boolean getGasLeakRisk() {
        return gasLeakRisk;
    }

    public void setGasLeakRisk(Boolean gasLeakRisk) {
        this.gasLeakRisk = gasLeakRisk;
    }

    public Boolean getGasLeakProblem() {
        return gasLeakProblem;
    }

    public void setGasLeakProblem(Boolean gasLeakProblem) {
        this.gasLeakProblem = gasLeakProblem;
    }

    public String getDangerousBuildingState() {
        return dangerousBuildingState;
    }

    public void setDangerousBuildingState(String dangerousBuildingState) {
        this.dangerousBuildingState = dangerousBuildingState;
    }

    public String getInstallationName() {
        return installationName;
    }

    public void setInstallationName(String installationName) {
        this.installationName = installationName;
    }

    public String getInstallationAddress() {
        return installationAddress;
    }

    public void setInstallationAddress(String installationAddress) {
        this.installationAddress = installationAddress;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUnitAddress() {
        return unitAddress;
    }

    public void setUnitAddress(String unitAddress) {
        this.unitAddress = unitAddress;
    }

    public Boolean getNormalLogin() {
        return normalLogin;
    }

    public void setNormalLogin(Boolean normalLogin) {
        this.normalLogin = normalLogin;
    }

    public Boolean getKnowOperation() {
        return knowOperation;
    }

    public void setKnowOperation(Boolean knowOperation) {
        this.knowOperation = knowOperation;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getLedBoardType() {
        return ledBoardType;
    }

    public void setLedBoardType(String ledBoardType) {
        this.ledBoardType = ledBoardType;
    }

    public String getLedBoardWayToUpdate() {
        return ledBoardWayToUpdate;
    }

    public void setLedBoardWayToUpdate(String ledBoardWayToUpdate) {
        this.ledBoardWayToUpdate = ledBoardWayToUpdate;
    }

    public String getLedBoardSelectedWayToUpdate() {
        return ledBoardSelectedWayToUpdate;
    }

    public void setLedBoardSelectedWayToUpdate(String ledBoardSelectedWayToUpdate) {
        this.ledBoardSelectedWayToUpdate = ledBoardSelectedWayToUpdate;
    }

    public String getLedBoardPasswordPolicy() {
        return ledBoardPasswordPolicy;
    }

    public void setLedBoardPasswordPolicy(String ledBoardPasswordPolicy) {
        this.ledBoardPasswordPolicy = ledBoardPasswordPolicy;
    }

    public String getLedBoardContentType() {
        return ledBoardContentType;
    }

    public void setLedBoardContentType(String ledBoardContentType) {
        this.ledBoardContentType = ledBoardContentType;
    }

    public String getYesOrNo1() {
        return yesOrNo1;
    }

    public void setYesOrNo1(String yesOrNo1) {
        this.yesOrNo1 = yesOrNo1;
    }

    public String getYesOrNo2() {
        return yesOrNo2;
    }

    public void setYesOrNo2(String yesOrNo2) {
        this.yesOrNo2 = yesOrNo2;
    }

    public String getYesOrNo3() {
        return yesOrNo3;
    }

    public void setYesOrNo3(String yesOrNo3) {
        this.yesOrNo3 = yesOrNo3;
    }

    public String getYesOrNo4() {
        return yesOrNo4;
    }

    public void setYesOrNo4(String yesOrNo4) {
        this.yesOrNo4 = yesOrNo4;
    }

    public String getYesOrNo5() {
        return yesOrNo5;
    }

    public void setYesOrNo5(String yesOrNo5) {
        this.yesOrNo5 = yesOrNo5;
    }

    public String getNoOrYes1() {
        return noOrYes1;
    }

    public void setNoOrYes1(String noOrYes1) {
        this.noOrYes1 = noOrYes1;
    }

    public String getNoOrYes2() {
        return noOrYes2;
    }

    public void setNoOrYes2(String noOrYes2) {
        this.noOrYes2 = noOrYes2;
    }

    public String getNoOrYes3() {
        return noOrYes3;
    }

    public void setNoOrYes3(String noOrYes3) {
        this.noOrYes3 = noOrYes3;
    }

    public String getNoOrYes4() {
        return noOrYes4;
    }

    public void setNoOrYes4(String noOrYes4) {
        this.noOrYes4 = noOrYes4;
    }

    public String getNoOrYes5() {
        return noOrYes5;
    }

    public void setNoOrYes5(String noOrYes5) {
        this.noOrYes5 = noOrYes5;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }
}
