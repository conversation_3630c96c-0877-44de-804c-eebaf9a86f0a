package com.chinamobile.healthcode.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import java.util.List;

@MappedSuperclass
public class Record extends AbstractEntity {

    @Id
    @Column(length = 36)
    protected String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36)
    protected String regionId;

    protected String regionFullName;

    protected String attachmentKeys;

    @Transient
    protected List<String> attachmentIds;

    @Transient
    protected List<Media> attachments;

    boolean isCreator;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public String getAttachmentKeys() {
        return attachmentKeys;
    }

    public void setAttachmentKeys(String attachmentKey) {
        this.attachmentKeys = attachmentKey;
    }

    public List<String> getAttachmentIds() {
        return attachmentIds;
    }

    public void setAttachmentIds(List<String> attachmentIds) {
        this.attachmentIds = attachmentIds;
    }

    public List<Media> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Media> attachments) {
        this.attachments = attachments;
    }

    public boolean getIsCreator() {
        return isCreator;
    }

    public void setIsCreator(boolean creator) {
        isCreator = creator;
    }

}