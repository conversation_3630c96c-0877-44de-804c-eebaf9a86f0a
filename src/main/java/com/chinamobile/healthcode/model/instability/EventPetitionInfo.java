package com.chinamobile.healthcode.model.instability;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * 2021年以来到市、省、京信访情况
 */
@Entity
@Table(name = "instability_event_petition_info")
public class EventPetitionInfo extends InstabilityEventInfoBaseEntity {
    /**
     * 信访人数
     */
    @NotNull(message = "信访人数不能为空")
    @Column(columnDefinition = "INT COMMENT '信访人数'")
    private Integer numOfPetitioners;

    public Integer getNumOfPetitioners() {
        return numOfPetitioners;
    }

    public void setNumOfPetitioners(Integer numOfPetitioners) {
        this.numOfPetitioners = numOfPetitioners;
    }
}
