package com.chinamobile.healthcode.model.instability;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "instability_place_info")
public class PlaceInfo extends InstabilityBaseEntity {
    /**
     * 党政机关数
     */
    @Column(columnDefinition = "INT COMMENT '党政机关数'")
    private int numOfGovernmentAgencies;

    /**
     * 繁华商圈数
     */
    @Column(columnDefinition = "INT COMMENT '繁华商圈数'")
    private int numOfBusinessAreas;

    /**
     * 步行街数
     */
    @Column(columnDefinition = "INT COMMENT '步行街数'")
    private int numOfPedestrianStreets;

    /**
     * 大型商场数
     */
    @Column(columnDefinition = "INT COMMENT '大型商场数'")
    private int numOfShoppingMalls;

    /**
     * 城市综合体数
     */
    @Column(columnDefinition = "INT COMMENT '城市综合体数'")
    private int numOfUrbanComplexes;

    /**
     * 车站数
     */
    @Column(columnDefinition = "INT COMMENT '车站数'")
    private int numOfStations;

    /**
     * 码头数
     */
    @Column(columnDefinition = "INT COMMENT '码头数'")
    private int numOfDocks;

    /**
     * 广场数
     */
    @Column(columnDefinition = "INT COMMENT '广场数'")
    private int numOfSquares;

    /**
     * 标志性建筑物数
     */
    @Column(columnDefinition = "INT COMMENT '标志性建筑物数'")
    private int numOfLandmarkBuildings;

    /**
     * 天桥数
     */
    @Column(columnDefinition = "INT COMMENT '天桥数'")
    private int numOfOverpasses;

    /**
     * 公共电子显示屏（是否为重点设施-户外LED屏）数
     */
    @Column(columnDefinition = "INT COMMENT '公共电子显示屏数'")
    private int numOfLedDisplays;

    /**
     * 文体场馆数
     */
    @Column(columnDefinition = "INT COMMENT '文体场馆数'")
    private int numOfCulturalAndSportsVenues;

    /**
     * 旅游景区（是否为重点场所-风景区）数
     */
    @Column(columnDefinition = "INT COMMENT '旅游景区数'")
    private int numOfTouristAttractions;

    /**
     * 学校（是否为重点单位-学校）数
     */
    @Column(columnDefinition = "INT COMMENT '学校数'")
    private int numOfSchools;

    /**
     * 大学（大专）数
     */
    @Column(columnDefinition = "INT COMMENT '大学（大专）数'")
    private int numOfCollegesAndUniversities;

    /**
     * 中学数
     */
    @Column(columnDefinition = "INT COMMENT '中学数'")
    private int numOfMiddleSchools;

    /**
     * 小学数
     */
    @Column(columnDefinition = "INT COMMENT '小学数'")
    private int numOfPrimarySchools;

    /**
     * 幼儿园数
     */
    @Column(columnDefinition = "INT COMMENT '幼儿园数'")
    private int numOfKindergartens;

    /**
     * 医疗机构（是否为重点单位-医院和重点场所-卫生站）数
     */
    @Column(columnDefinition = "INT COMMENT '医疗机构数'")
    private int numOfMedicalInstitutions;

    /**
     * 医院数
     */
    @Column(columnDefinition = "INT COMMENT '医院数'")
    private int numOfHospitals;

    /**
     * 卫生站数
     */
    @Column(columnDefinition = "INT COMMENT '卫生站数'")
    private int numOfHealthStations;

    /**
     * 信访接待场所数
     */
    @Column(columnDefinition = "INT COMMENT '信访接待场所数'")
    private int numOfPetitionReceptionPlaces;

    /**
     * 宗教场所数
     */
    @Column(columnDefinition = "INT COMMENT '宗教场所数'")
    private int numOfReligiousPlaces;

    /**
     * 私人宗教聚集点数
     */
    @Column(columnDefinition = "INT COMMENT '私人宗教聚集点数'")
    private int numOfPrivateReligiousPlaces;

    /**
     * 休闲娱乐场所数
     */
    @Column(columnDefinition = "INT COMMENT '休闲娱乐场所数'")
    private int numOfEntertainmentPlaces;

    /**
     * 酒店数
     */
    @Column(columnDefinition = "INT COMMENT '酒店数'")
    private int numOfHotels;

    /**
     * KTV数
     */
    @Column(columnDefinition = "INT COMMENT 'KTV数'")
    private int numOfKtv;

    /**
     * 酒吧数
     */
    @Column(columnDefinition = "INT COMMENT '酒吧数'")
    private int numOfBars;

    /**
     * 歌舞厅数
     */
    @Column(columnDefinition = "INT COMMENT '歌舞厅数'")
    private int numOfDanceHalls;

    /**
     * 沐足按摩数
     */
    @Column(columnDefinition = "INT COMMENT '沐足按摩数'")
    private int numOfMassages;

    /**
     * 棋牌室数
     */
    @Column(columnDefinition = "INT COMMENT '棋牌室数'")
    private int numOfChessAndCardRooms;

    /**
     * 茶座数
     */
    @Column(columnDefinition = "INT COMMENT '茶座数'")
    private int numOfTeaHouses;

    public int getNumOfGovernmentAgencies() {
        return numOfGovernmentAgencies;
    }

    public void setNumOfGovernmentAgencies(int numOfGovernmentAgencies) {
        this.numOfGovernmentAgencies = numOfGovernmentAgencies;
    }

    public int getNumOfBusinessAreas() {
        return numOfBusinessAreas;
    }

    public void setNumOfBusinessAreas(int numOfBusinessAreas) {
        this.numOfBusinessAreas = numOfBusinessAreas;
    }

    public int getNumOfPedestrianStreets() {
        return numOfPedestrianStreets;
    }

    public void setNumOfPedestrianStreets(int numOfPedestrianStreets) {
        this.numOfPedestrianStreets = numOfPedestrianStreets;
    }

    public int getNumOfShoppingMalls() {
        return numOfShoppingMalls;
    }

    public void setNumOfShoppingMalls(int numOfShoppingMalls) {
        this.numOfShoppingMalls = numOfShoppingMalls;
    }

    public int getNumOfUrbanComplexes() {
        return numOfUrbanComplexes;
    }

    public void setNumOfUrbanComplexes(int numOfUrbanComplexes) {
        this.numOfUrbanComplexes = numOfUrbanComplexes;
    }

    public int getNumOfStations() {
        return numOfStations;
    }

    public void setNumOfStations(int numOfStations) {
        this.numOfStations = numOfStations;
    }

    public int getNumOfDocks() {
        return numOfDocks;
    }

    public void setNumOfDocks(int numOfDocks) {
        this.numOfDocks = numOfDocks;
    }

    public int getNumOfSquares() {
        return numOfSquares;
    }

    public void setNumOfSquares(int numOfSquares) {
        this.numOfSquares = numOfSquares;
    }

    public int getNumOfLandmarkBuildings() {
        return numOfLandmarkBuildings;
    }

    public void setNumOfLandmarkBuildings(int numOfLandmarkBuildings) {
        this.numOfLandmarkBuildings = numOfLandmarkBuildings;
    }

    public int getNumOfOverpasses() {
        return numOfOverpasses;
    }

    public void setNumOfOverpasses(int numOfOverpasses) {
        this.numOfOverpasses = numOfOverpasses;
    }

    public int getNumOfLedDisplays() {
        return numOfLedDisplays;
    }

    public void setNumOfLedDisplays(int numOfLedDisplays) {
        this.numOfLedDisplays = numOfLedDisplays;
    }

    public int getNumOfCulturalAndSportsVenues() {
        return numOfCulturalAndSportsVenues;
    }

    public void setNumOfCulturalAndSportsVenues(int numOfCulturalAndSportsVenues) {
        this.numOfCulturalAndSportsVenues = numOfCulturalAndSportsVenues;
    }

    public int getNumOfTouristAttractions() {
        return numOfTouristAttractions;
    }

    public void setNumOfTouristAttractions(int numOfTouristAttractions) {
        this.numOfTouristAttractions = numOfTouristAttractions;
    }

    public int getNumOfSchools() {
        return numOfSchools;
    }

    public void setNumOfSchools(int numOfSchools) {
        this.numOfSchools = numOfSchools;
    }

    public int getNumOfCollegesAndUniversities() {
        return numOfCollegesAndUniversities;
    }

    public void setNumOfCollegesAndUniversities(int numOfCollegesAndUniversities) {
        this.numOfCollegesAndUniversities = numOfCollegesAndUniversities;
    }

    public int getNumOfMiddleSchools() {
        return numOfMiddleSchools;
    }

    public void setNumOfMiddleSchools(int numOfMiddleSchools) {
        this.numOfMiddleSchools = numOfMiddleSchools;
    }

    public int getNumOfPrimarySchools() {
        return numOfPrimarySchools;
    }

    public void setNumOfPrimarySchools(int numOfPrimarySchools) {
        this.numOfPrimarySchools = numOfPrimarySchools;
    }

    public int getNumOfKindergartens() {
        return numOfKindergartens;
    }

    public void setNumOfKindergartens(int numOfKindergartens) {
        this.numOfKindergartens = numOfKindergartens;
    }

    public int getNumOfMedicalInstitutions() {
        return numOfMedicalInstitutions;
    }

    public void setNumOfMedicalInstitutions(int numOfMedicalInstitutions) {
        this.numOfMedicalInstitutions = numOfMedicalInstitutions;
    }

    public int getNumOfHospitals() {
        return numOfHospitals;
    }

    public void setNumOfHospitals(int numOfHospitals) {
        this.numOfHospitals = numOfHospitals;
    }

    public int getNumOfHealthStations() {
        return numOfHealthStations;
    }

    public void setNumOfHealthStations(int numOfHealthStations) {
        this.numOfHealthStations = numOfHealthStations;
    }

    public int getNumOfPetitionReceptionPlaces() {
        return numOfPetitionReceptionPlaces;
    }

    public void setNumOfPetitionReceptionPlaces(int numOfPetitionReceptionPlaces) {
        this.numOfPetitionReceptionPlaces = numOfPetitionReceptionPlaces;
    }

    public int getNumOfReligiousPlaces() {
        return numOfReligiousPlaces;
    }

    public void setNumOfReligiousPlaces(int numOfReligiousPlaces) {
        this.numOfReligiousPlaces = numOfReligiousPlaces;
    }

    public int getNumOfPrivateReligiousPlaces() {
        return numOfPrivateReligiousPlaces;
    }

    public void setNumOfPrivateReligiousPlaces(int numOfPrivateReligiousPlaces) {
        this.numOfPrivateReligiousPlaces = numOfPrivateReligiousPlaces;
    }

    public int getNumOfEntertainmentPlaces() {
        return numOfEntertainmentPlaces;
    }

    public void setNumOfEntertainmentPlaces(int numOfEntertainmentPlaces) {
        this.numOfEntertainmentPlaces = numOfEntertainmentPlaces;
    }

    public int getNumOfHotels() {
        return numOfHotels;
    }

    public void setNumOfHotels(int numOfHotels) {
        this.numOfHotels = numOfHotels;
    }

    public int getNumOfKtv() {
        return numOfKtv;
    }

    public void setNumOfKtv(int numOfKtv) {
        this.numOfKtv = numOfKtv;
    }

    public int getNumOfBars() {
        return numOfBars;
    }

    public void setNumOfBars(int numOfBars) {
        this.numOfBars = numOfBars;
    }

    public int getNumOfDanceHalls() {
        return numOfDanceHalls;
    }

    public void setNumOfDanceHalls(int numOfDanceHalls) {
        this.numOfDanceHalls = numOfDanceHalls;
    }

    public int getNumOfMassages() {
        return numOfMassages;
    }

    public void setNumOfMassages(int numOfMassages) {
        this.numOfMassages = numOfMassages;
    }

    public int getNumOfChessAndCardRooms() {
        return numOfChessAndCardRooms;
    }

    public void setNumOfChessAndCardRooms(int numOfChessAndCardRooms) {
        this.numOfChessAndCardRooms = numOfChessAndCardRooms;
    }

    public int getNumOfTeaHouses() {
        return numOfTeaHouses;
    }

    public void setNumOfTeaHouses(int numOfTeaHouses) {
        this.numOfTeaHouses = numOfTeaHouses;
    }
}
