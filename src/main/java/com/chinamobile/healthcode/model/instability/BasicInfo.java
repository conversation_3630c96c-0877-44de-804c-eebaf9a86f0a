package com.chinamobile.healthcode.model.instability;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "instability_basic_info")
public class BasicInfo extends InstabilityBaseEntity {
    /**
     * 辖区面积
     */
    @Column(columnDefinition = "DOUBLE COMMENT '辖区面积'")
    private double area;

    /**
     * 户籍人口总数
     */
    @Column(columnDefinition = "BIGINT COMMENT '户籍人口总数'")
    private long registeredPopulation;

    /**
     * 常住人口总数
     */
    @Column(columnDefinition = "BIGINT COMMENT '常住人口总数'")
    private long residentPopulation;

    /**
     * 外出人口总数
     */
    @Column(columnDefinition = "BIGINT COMMENT '外出人口总数'")
    private long outPopulation;

    /**
     * 国内外出人口数
     */
    @Column(columnDefinition = "BIGINT COMMENT '国内外出人口数'")
    private long domesticOutPopulation;

    /**
     * 国外外出人口数
     */
    @Column(columnDefinition = "BIGINT COMMENT '国外外出人口数'")
    private long internationalOutPopulation;

    /**
     * 村居“两委”人数
     */
    @Column(columnDefinition = "BIGINT COMMENT '村居“两委”人数'")
    private long numOfVillageCommitteeMembers;

    /**
     * 网格数量
     */
    @Column(columnDefinition = "INT COMMENT '网格数量'")
    private int gridCount;

    /**
     * 村居主要特点
     */
    @Column(columnDefinition = "TEXT COMMENT '村居主要特点以及其他相关情况'")
    private String villageInfo;

    public double getArea() {
        return area;
    }

    public void setArea(double area) {
        this.area = area;
    }

    public long getRegisteredPopulation() {
        return registeredPopulation;
    }

    public void setRegisteredPopulation(long registeredPopulation) {
        this.registeredPopulation = registeredPopulation;
    }

    public long getResidentPopulation() {
        return residentPopulation;
    }

    public void setResidentPopulation(long residentPopulation) {
        this.residentPopulation = residentPopulation;
    }

    public long getOutPopulation() {
        return outPopulation;
    }

    public void setOutPopulation(long outPopulation) {
        this.outPopulation = outPopulation;
    }

    public long getDomesticOutPopulation() {
        return domesticOutPopulation;
    }

    public void setDomesticOutPopulation(long domesticOutPopulation) {
        this.domesticOutPopulation = domesticOutPopulation;
    }

    public long getInternationalOutPopulation() {
        return internationalOutPopulation;
    }

    public void setInternationalOutPopulation(long internationalOutPopulation) {
        this.internationalOutPopulation = internationalOutPopulation;
    }

    public long getNumOfVillageCommitteeMembers() {
        return numOfVillageCommitteeMembers;
    }

    public void setNumOfVillageCommitteeMembers(long numOfVillageCommitteeMembers) {
        this.numOfVillageCommitteeMembers = numOfVillageCommitteeMembers;
    }

    public int getGridCount() {
        return gridCount;
    }

    public void setGridCount(int gridCount) {
        this.gridCount = gridCount;
    }

    public String getVillageInfo() {
        return villageInfo;
    }

    public void setVillageInfo(String villageInfo) {
        this.villageInfo = villageInfo;
    }
}
