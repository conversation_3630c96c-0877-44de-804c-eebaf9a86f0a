package com.chinamobile.healthcode.model.instability;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "instability_organization_info")
public class OrganizationInfo extends InstabilityBaseEntity {
    /**
     * 老人组数量
     */
    @Column(columnDefinition = "INT COMMENT '老人组数量'")
    private int numOfElderlyGroups;

    /**
     * 理事会数量
     */
    @Column(columnDefinition = "INT COMMENT '理事会数量'")
    private int numOfCouncils;

    /**
     * 其他民间组织数量
     */
    @Column(columnDefinition = "INT COMMENT '其他民间组织数量'")
    private int numOfOtherCivilOrganizations;

    /**
     * 活动情况
     */
    @Column(columnDefinition = "TEXT COMMENT '活动情况'")
    private String activitySituation;

    public int getNumOfElderlyGroups() {
        return numOfElderlyGroups;
    }

    public void setNumOfElderlyGroups(int numOfElderlyGroups) {
        this.numOfElderlyGroups = numOfElderlyGroups;
    }

    public int getNumOfCouncils() {
        return numOfCouncils;
    }

    public void setNumOfCouncils(int numOfCouncils) {
        this.numOfCouncils = numOfCouncils;
    }

    public int getNumOfOtherCivilOrganizations() {
        return numOfOtherCivilOrganizations;
    }

    public void setNumOfOtherCivilOrganizations(int numOfOtherCivilOrganizations) {
        this.numOfOtherCivilOrganizations = numOfOtherCivilOrganizations;
    }

    public String getActivitySituation() {
        return activitySituation;
    }

    public void setActivitySituation(String activitySituation) {
        this.activitySituation = activitySituation;
    }
}
