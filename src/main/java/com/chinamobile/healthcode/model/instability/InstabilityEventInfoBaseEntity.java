package com.chinamobile.healthcode.model.instability;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 村（社）专项管理基础实体类
 */
@MappedSuperclass
public abstract class InstabilityEventInfoBaseEntity extends InstabilityBaseEntity {
    /**
     * 事件标题
     */
    @NotBlank(message = "事件标题不能为空")
    @Column(columnDefinition = "VARCHAR(255) COMMENT '事件标题'")
    private String title;

    /**
     * 事件分类
     */
    @NotBlank(message = "事件分类不能为空")
    @Column(columnDefinition = "VARCHAR(64) COMMENT '事件分类'")
    private String category;

    /**
     * 事件类型
     */
    @NotBlank(message = "事件类型不能为空")
    @Column(columnDefinition = "VARCHAR(64) COMMENT '事件类型'")
    private String type;

    /**
     * 事件概述
     */
    @NotBlank(message = "事件概述不能为空")
    @Column(columnDefinition = "TEXT COMMENT '事件概述'")
    private String overview;

    /**
     * 事发地点
     */
    @NotNull(message = "事发地点不能为空", groups = { InstabilityEventInfoValidationGroup.EventInfoWithAddress.class })
    @Column(columnDefinition = "VARCHAR(255) COMMENT '事发地点'")
    private String address;

    /**
     * 国测局经度
     */
    @Column(columnDefinition = "DOUBLE COMMENT '国测局经度'")
    Double lngInGcj;

    /**
     * 国测局纬度
     */
    @Column(columnDefinition = "DOUBLE COMMENT '国测局纬度'")
    Double latInGcj;

    /**
     * WGS经度
     */
    @Column(columnDefinition = "DOUBLE COMMENT 'WGS经度'")
    Double lngInWgs;

    /**
     * WGS纬度
     */
    @Column(columnDefinition = "DOUBLE COMMENT 'WGS纬度'")
    Double latInWgs;

    /**
     * 事发时间
     */
//    @NotNull(message = "事发时间不能为空")
    @Column(columnDefinition = "DATETIME COMMENT '事发时间'")
    private Date incidentTime;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle( String title) {
        this.title = title;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory( String category) {
        this.category = category;
    }

    public String getOverview() {
        return overview;
    }

    public void setOverview(String overview) {
        this.overview = overview;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getLngInGcj() {
        return lngInGcj;
    }

    public void setLngInGcj(Double lngInGcj) {
        this.lngInGcj = lngInGcj;
    }

    public Double getLatInGcj() {
        return latInGcj;
    }

    public void setLatInGcj(Double latInGcj) {
        this.latInGcj = latInGcj;
    }

    public Double getLngInWgs() {
        return lngInWgs;
    }

    public void setLngInWgs(Double lngInWgs) {
        this.lngInWgs = lngInWgs;
    }

    public Double getLatInWgs() {
        return latInWgs;
    }

    public void setLatInWgs(Double latInWgs) {
        this.latInWgs = latInWgs;
    }

    public Date getIncidentTime() {
        return incidentTime;
    }

    public void setIncidentTime(Date incidentTime) {
        this.incidentTime = incidentTime;
    }
}
