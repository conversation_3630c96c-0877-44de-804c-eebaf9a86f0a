package com.chinamobile.healthcode.model.instability;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 2021年以来“两委”干部涉治安、刑事处罚情况
 */
@Entity
@Table(name = "instability_event_committees_punishment_info")
public class EventCommitteesPunishmentInfo extends InstabilityEventInfoBaseEntity {
    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @Size(max = 4, message = "姓名最大长度为4")
    @Column(columnDefinition = "VARCHAR(16) COMMENT '姓名'")
    private String name;

    /**
     * 职务
     */
    @NotBlank(message = "职务不能为空")
    @Column(columnDefinition = "VARCHAR(64) COMMENT '职务'")
    private String position;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }
}
