package com.chinamobile.healthcode.model.instability;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

/**
 * 村（社）专项管理基础实体类
 */
@MappedSuperclass
public abstract class InstabilityBaseEntity extends AbstractEntity {
    @Id
    @Column(length = 36)
    private String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36, columnDefinition = "varchar(36) comment '行政区域id'")
    private String regionId;

    @Column(length = 128, columnDefinition = "varchar(128) comment '行政区域全称'")
    private String regionFullName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }
}
