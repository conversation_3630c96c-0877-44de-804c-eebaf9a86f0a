package com.chinamobile.healthcode.model.instability;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "instability_personnel_info")
public class PersonnelInfo extends InstabilityBaseEntity {
    /**
     * 政治安全重点人员数
     */
    @Column(columnDefinition = "INT COMMENT '政治安全重点人员数'")
    private int numOfPoliticalSecurityKeyPersonnel;

    /**
     * 律师重点人员数
     */
    @Column(columnDefinition = "INT COMMENT '律师重点人员数'")
    private int numOfLawyerKeyPersonnel;

    /**
     * 邪教人员数
     */
    @Column(columnDefinition = "INT COMMENT '邪教人员数'")
    private int numOfCultMembers;

    /**
     * 法轮功人员数
     */
    @Column(columnDefinition = "INT COMMENT '法轮功人员数'")
    private int numOfFaLunGongMembers;

    /**
     * 全能神人员数
     */
    @Column(columnDefinition = "INT COMMENT '全能神人员数'")
    private int numOfAlmightyGodMembers;

    /**
     * 其他邪教人员数
     */
    @Column(columnDefinition = "INT COMMENT '其他邪教人员数'")
    private int numOfOtherCultMembers;

    /**
     * 信访重点人员数
     */
    @Column(columnDefinition = "INT COMMENT '信访重点人员数'")
    private int numOfPetitionKeyPersonnel;

    /**
     * “三跨三分离”人员数
     */
    @Column(columnDefinition = "INT COMMENT '“三跨三分离”人员数'")
    private int numOfThreeCrossThreeSeparationPersonnel;

    /**
     * 网络重点人员数
     */
    @Column(columnDefinition = "INT COMMENT '网络重点人员数'")
    private int numOfOnlineKeyPersonnel;

    /**
     * 涉众金融投资受损人员数
     */
    @Column(columnDefinition = "INT COMMENT '涉众金融投资受损人员数'")
    private int numOfFinanciallyAffectedPersons;

    /**
     * 军队退役人员数
     */
    @Column(columnDefinition = "INT COMMENT '军队退役人员数'")
    private int numOfMilitaryVeterans;

    /**
     * 涉访重点人员数
     */
    @Column(columnDefinition = "INT COMMENT '涉访重点人员数'")
    private int numOfPetitionRelatedKeyPersonnel;

    /**
     * 疫苗“受害”人员数
     */
    @Column(columnDefinition = "INT COMMENT '疫苗“受害”人员数'")
    private int numOfVaccineVictims;

    /**
     * “三新”从业人员数
     */
    @Column(columnDefinition = "INT COMMENT '“三新”从业人员数'")
    private int numOfThreeNewPersonnel;

    /**
     * 严重精神障碍患者数
     */
    @Column(columnDefinition = "INT COMMENT '严重精神障碍患者数'")
    private int numOfSevereMentalDisorderPatients;

    /**
     * 危险性评估三级以上精神障碍患者数
     */
    @Column(columnDefinition = "INT COMMENT '危险性评估三级以上精神障碍患者数'")
    private int numOfHighRiskMentalDisorderPatients;

    /**
     * 刑满释放安置帮教人员数
     */
    @Column(columnDefinition = "INT COMMENT '刑满释放安置帮教人员数'")
    private int numOfReleasedPrisoners;

    /**
     * 重点帮教人员数
     */
    @Column(columnDefinition = "INT COMMENT '重点帮教人员数'")
    private int numOfKeyEducationAndHelpPersonnel;

    /**
     * 社区矫正人员数
     */
    @Column(columnDefinition = "INT COMMENT '社区矫正人员数'")
    private int numOfCommunityCorrectionPersonnel;

    /**
     * 重点社矫人员数
     */
    @Column(columnDefinition = "INT COMMENT '重点社矫人员数'")
    private int numOfKeyCommunityCorrectionPersonnel;

    /**
     * 吸毒人员数
     */
    @Column(columnDefinition = "INT COMMENT '吸毒人员数'")
    private int numOfDrugAddicts;

    /**
     * 社戒社康人员数
     */
    @Column(columnDefinition = "INT COMMENT '社戒社康人员数'")
    private int numOfCommunityDrugRehabilitations;

    /**
     * “八类”重点未成年人数
     */
    @Column(columnDefinition = "INT COMMENT '“八类”重点未成年人数'")
    private int numOfEightCategoriesKeyMinors;

    /**
     * “三失一偏”（生活失意、心态失衡、行为失常人员和性格偏执）人员数
     */
    @Column(columnDefinition = "INT COMMENT '“三失一偏”人员数'")
    private int numOfThreeLossesOneBiasPersonnel;

    /**
     * 低保在册人员数
     */
    @Column(columnDefinition = "INT COMMENT '低保在册人员数'")
    private int numOfRegisteredLowIncomePersonnel;

    public int getNumOfPoliticalSecurityKeyPersonnel() {
        return numOfPoliticalSecurityKeyPersonnel;
    }

    public void setNumOfPoliticalSecurityKeyPersonnel(int numOfPoliticalSecurityKeyPersonnel) {
        this.numOfPoliticalSecurityKeyPersonnel = numOfPoliticalSecurityKeyPersonnel;
    }

    public int getNumOfLawyerKeyPersonnel() {
        return numOfLawyerKeyPersonnel;
    }

    public void setNumOfLawyerKeyPersonnel(int numOfLawyerKeyPersonnel) {
        this.numOfLawyerKeyPersonnel = numOfLawyerKeyPersonnel;
    }

    public int getNumOfCultMembers() {
        return numOfCultMembers;
    }

    public void setNumOfCultMembers(int numOfCultMembers) {
        this.numOfCultMembers = numOfCultMembers;
    }

    public int getNumOfFaLunGongMembers() {
        return numOfFaLunGongMembers;
    }

    public void setNumOfFaLunGongMembers(int numOfFaLunGongMembers) {
        this.numOfFaLunGongMembers = numOfFaLunGongMembers;
    }

    public int getNumOfAlmightyGodMembers() {
        return numOfAlmightyGodMembers;
    }

    public void setNumOfAlmightyGodMembers(int numOfAlmightyGodMembers) {
        this.numOfAlmightyGodMembers = numOfAlmightyGodMembers;
    }

    public int getNumOfOtherCultMembers() {
        return numOfOtherCultMembers;
    }

    public void setNumOfOtherCultMembers(int numOfOtherCultMembers) {
        this.numOfOtherCultMembers = numOfOtherCultMembers;
    }

    public int getNumOfPetitionKeyPersonnel() {
        return numOfPetitionKeyPersonnel;
    }

    public void setNumOfPetitionKeyPersonnel(int numOfPetitionKeyPersonnel) {
        this.numOfPetitionKeyPersonnel = numOfPetitionKeyPersonnel;
    }

    public int getNumOfThreeCrossThreeSeparationPersonnel() {
        return numOfThreeCrossThreeSeparationPersonnel;
    }

    public void setNumOfThreeCrossThreeSeparationPersonnel(int numOfThreeCrossThreeSeparationPersonnel) {
        this.numOfThreeCrossThreeSeparationPersonnel = numOfThreeCrossThreeSeparationPersonnel;
    }

    public int getNumOfOnlineKeyPersonnel() {
        return numOfOnlineKeyPersonnel;
    }

    public void setNumOfOnlineKeyPersonnel(int numOfOnlineKeyPersonnel) {
        this.numOfOnlineKeyPersonnel = numOfOnlineKeyPersonnel;
    }

    public int getNumOfFinanciallyAffectedPersons() {
        return numOfFinanciallyAffectedPersons;
    }

    public void setNumOfFinanciallyAffectedPersons(int numOfFinanciallyAffectedPersons) {
        this.numOfFinanciallyAffectedPersons = numOfFinanciallyAffectedPersons;
    }

    public int getNumOfMilitaryVeterans() {
        return numOfMilitaryVeterans;
    }

    public void setNumOfMilitaryVeterans(int numOfMilitaryVeterans) {
        this.numOfMilitaryVeterans = numOfMilitaryVeterans;
    }

    public int getNumOfPetitionRelatedKeyPersonnel() {
        return numOfPetitionRelatedKeyPersonnel;
    }

    public void setNumOfPetitionRelatedKeyPersonnel(int numOfPetitionRelatedKeyPersonnel) {
        this.numOfPetitionRelatedKeyPersonnel = numOfPetitionRelatedKeyPersonnel;
    }

    public int getNumOfVaccineVictims() {
        return numOfVaccineVictims;
    }

    public void setNumOfVaccineVictims(int numOfVaccineVictims) {
        this.numOfVaccineVictims = numOfVaccineVictims;
    }

    public int getNumOfThreeNewPersonnel() {
        return numOfThreeNewPersonnel;
    }

    public void setNumOfThreeNewPersonnel(int numOfThreeNewPersonnel) {
        this.numOfThreeNewPersonnel = numOfThreeNewPersonnel;
    }

    public int getNumOfSevereMentalDisorderPatients() {
        return numOfSevereMentalDisorderPatients;
    }

    public void setNumOfSevereMentalDisorderPatients(int numOfSevereMentalDisorderPatients) {
        this.numOfSevereMentalDisorderPatients = numOfSevereMentalDisorderPatients;
    }

    public int getNumOfHighRiskMentalDisorderPatients() {
        return numOfHighRiskMentalDisorderPatients;
    }

    public void setNumOfHighRiskMentalDisorderPatients(int numOfHighRiskMentalDisorderPatients) {
        this.numOfHighRiskMentalDisorderPatients = numOfHighRiskMentalDisorderPatients;
    }

    public int getNumOfReleasedPrisoners() {
        return numOfReleasedPrisoners;
    }

    public void setNumOfReleasedPrisoners(int numOfReleasedPrisoners) {
        this.numOfReleasedPrisoners = numOfReleasedPrisoners;
    }

    public int getNumOfKeyEducationAndHelpPersonnel() {
        return numOfKeyEducationAndHelpPersonnel;
    }

    public void setNumOfKeyEducationAndHelpPersonnel(int numOfKeyEducationAndHelpPersonnel) {
        this.numOfKeyEducationAndHelpPersonnel = numOfKeyEducationAndHelpPersonnel;
    }

    public int getNumOfCommunityCorrectionPersonnel() {
        return numOfCommunityCorrectionPersonnel;
    }

    public void setNumOfCommunityCorrectionPersonnel(int numOfCommunityCorrectionPersonnel) {
        this.numOfCommunityCorrectionPersonnel = numOfCommunityCorrectionPersonnel;
    }

    public int getNumOfKeyCommunityCorrectionPersonnel() {
        return numOfKeyCommunityCorrectionPersonnel;
    }

    public void setNumOfKeyCommunityCorrectionPersonnel(int numOfKeyCommunityCorrectionPersonnel) {
        this.numOfKeyCommunityCorrectionPersonnel = numOfKeyCommunityCorrectionPersonnel;
    }

    public int getNumOfDrugAddicts() {
        return numOfDrugAddicts;
    }

    public void setNumOfDrugAddicts(int numOfDrugAddicts) {
        this.numOfDrugAddicts = numOfDrugAddicts;
    }

    public int getNumOfCommunityDrugRehabilitations() {
        return numOfCommunityDrugRehabilitations;
    }

    public void setNumOfCommunityDrugRehabilitations(int numOfCommunityDrugRehabilitations) {
        this.numOfCommunityDrugRehabilitations = numOfCommunityDrugRehabilitations;
    }

    public int getNumOfEightCategoriesKeyMinors() {
        return numOfEightCategoriesKeyMinors;
    }

    public void setNumOfEightCategoriesKeyMinors(int numOfEightCategoriesKeyMinors) {
        this.numOfEightCategoriesKeyMinors = numOfEightCategoriesKeyMinors;
    }

    public int getNumOfThreeLossesOneBiasPersonnel() {
        return numOfThreeLossesOneBiasPersonnel;
    }

    public void setNumOfThreeLossesOneBiasPersonnel(int numOfThreeLossesOneBiasPersonnel) {
        this.numOfThreeLossesOneBiasPersonnel = numOfThreeLossesOneBiasPersonnel;
    }

    public int getNumOfRegisteredLowIncomePersonnel() {
        return numOfRegisteredLowIncomePersonnel;
    }

    public void setNumOfRegisteredLowIncomePersonnel(int numOfRegisteredLowIncomePersonnel) {
        this.numOfRegisteredLowIncomePersonnel = numOfRegisteredLowIncomePersonnel;
    }
}
