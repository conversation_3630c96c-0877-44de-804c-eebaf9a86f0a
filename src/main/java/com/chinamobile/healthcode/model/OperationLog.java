package com.chinamobile.healthcode.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据操作日志实体类
 */
@Entity
@Table(name = "sys_operation_logs", indexes = {
        @Index(columnList = "entityType"),
        @Index(columnList = "entityId"),
        @Index(columnList = "operationType"),
        @Index(columnList = "operatorId"),
        @Index(columnList = "operateTime"),
        @Index(columnList = "deptId")
})
public class OperationLog extends AbstractEntity {

    @Id
    @Column(length = 36)
    private String id = String.valueOf(IdWorker.getInstance().nextId());

    /**
     * 实体类型（类名）
     */
    @Column(length = 128, columnDefinition = "varchar(128) comment '实体类型（类名）'")
    private String entityType;

    /**
     * 实体ID
     */
    @Column(length = 36, columnDefinition = "varchar(36) comment '实体ID'")
    private String entityId;

    /**
     * 操作类型：CREATE, UPDATE, DELETE, IMPORT
     */
    @Enumerated(EnumType.STRING)
    @Column(length = 20, columnDefinition = "varchar(20) comment '操作类型'")
    private OperationType operationType;

    /**
     * 操作描述
     */
    @Column(columnDefinition = "text comment '操作描述'")
    private String description;

    /**
     * 操作前数据（JSON格式）
     */
    @Column(columnDefinition = "longtext comment '操作前数据（JSON格式）'")
    private String beforeData;

    /**
     * 操作后数据（JSON格式）
     */
    @Column(columnDefinition = "longtext comment '操作后数据（JSON格式）'")
    private String afterData;

    /**
     * 操作人ID
     */
    @Column(length = 36, columnDefinition = "varchar(36) comment '操作人ID'")
    private String operatorId;

    /**
     * 操作人姓名
     */
    @Column(length = 64, columnDefinition = "varchar(64) comment '操作人姓名'")
    private String operatorName;

    /**
     * 操作人部门ID
     */
    @Column(length = 36, columnDefinition = "varchar(36) comment '操作人部门ID'")
    private String deptId;

    /**
     * 操作人部门名称
     */
    @Column(length = 128, columnDefinition = "varchar(128) comment '操作人部门名称'")
    private String deptName;

    /**
     * 操作时间
     */
    @Column(columnDefinition = "datetime comment '操作时间'")
    private Date operateTime;

    /**
     * IP地址
     */
    @Column(length = 45, columnDefinition = "varchar(45) comment 'IP地址'")
    private String ipAddress;

    /**
     * 用户代理
     */
    @Column(length = 512, columnDefinition = "varchar(512) comment '用户代理'")
    private String userAgent;

    /**
     * 操作结果：SUCCESS, FAILURE
     */
    @Enumerated(EnumType.STRING)
    @Column(length = 20, columnDefinition = "varchar(20) comment '操作结果'")
    private OperationResult result;

    /**
     * 错误信息
     */
    @Column(columnDefinition = "text comment '错误信息'")
    private String errorMessage;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE("新增"),
        UPDATE("修改"),
        DELETE("删除"),
        IMPORT("导入"),
        EXPORT("导出");

        private final String description;

        OperationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 操作结果枚举
     */
    public enum OperationResult {
        SUCCESS("成功"),
        FAILURE("失败");

        private final String description;

        OperationResult(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public OperationType getOperationType() {
        return operationType;
    }

    public void setOperationType(OperationType operationType) {
        this.operationType = operationType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBeforeData() {
        return beforeData;
    }

    public void setBeforeData(String beforeData) {
        this.beforeData = beforeData;
    }

    public String getAfterData() {
        return afterData;
    }

    public void setAfterData(String afterData) {
        this.afterData = afterData;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public OperationResult getResult() {
        return result;
    }

    public void setResult(OperationResult result) {
        this.result = result;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
