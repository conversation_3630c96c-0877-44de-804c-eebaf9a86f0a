package com.chinamobile.healthcode.model.migrant;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "migrant_visit_forms", indexes = {
        @Index(columnList = "mp"),
        @Index(columnList = "createTime"),
        @Index(columnList = "creatorId"),
        @Index(columnList = "destinationDeptIdDispatched"),
        @Index(columnList = "destination_region_id, destinationDeptIdDispatched")
})
public class MigrantForm extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 16, nullable = false)
    String name;

    @Column(length = 11, nullable = false)
    String mp;

    @Column(nullable = false)
    ENUM_CREDENTIAL_TYPE credentialType = ENUM_CREDENTIAL_TYPE.身份证;

    @Column(length = 32, nullable = false)
    String credentialNo;

    @Column(nullable = false)
    String departure;

    @Column(name = "destination_region_id", length = 18)
    String destinationRegionId;

    @Column(name = "destination_region_full_name", columnDefinition = "text")
    String destinationRegionFullName;

    @Column(length = 18)
    String destinationDeptIdDispatched;

    @Column(columnDefinition = "text")
    String destinationDeptFullNameDispatched;

    String address;

    @Column(columnDefinition = "text")
    String path;

    Date departureDate;

    String transport;

    String shift;

    Date lastNucleicAcidDate;

    String duration;

    @Column(columnDefinition = "text")
    String memo;

    @Column(columnDefinition = "text")
    String opinion;

    ENUM_STATUS status;

    @Transient
    boolean editable;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public String getMp() {
        return mp;
    }

    public void setMp(String mp) {
        this.mp = StringUtils.trimWhitespace(mp);
    }

    public ENUM_CREDENTIAL_TYPE getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(ENUM_CREDENTIAL_TYPE credentialType) {
        this.credentialType = credentialType;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNo) {
        this.credentialNo = StringUtils.trimWhitespace(credentialNo);
    }

    public String getDeparture() {
        return departure;
    }

    public void setDeparture(String departure) {
        this.departure = StringUtils.trimWhitespace(departure);
    }

    public String getDestinationRegionId() {
        return destinationRegionId;
    }

    public void setDestinationRegionId(String destinationRegionId) {
        this.destinationRegionId = destinationRegionId;
    }

    public String getDestinationRegionFullName() {
        return destinationRegionFullName;
    }

    public void setDestinationRegionFullName(String destinationRegionFullName) {
        this.destinationRegionFullName = destinationRegionFullName;
    }

    public String getDestinationDeptIdDispatched() {
        return destinationDeptIdDispatched;
    }

    public void setDestinationDeptIdDispatched(String destinationDeptIdDispatched) {
        this.destinationDeptIdDispatched = destinationDeptIdDispatched;
    }

    public String getDestinationDeptFullNameDispatched() {
        return destinationDeptFullNameDispatched;
    }

    public void setDestinationDeptFullNameDispatched(String destinationDeptFullNameDispatched) {
        this.destinationDeptFullNameDispatched = destinationDeptFullNameDispatched;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = StringUtils.trimWhitespace(address);
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = StringUtils.trimWhitespace(path);
    }

    public Date getDepartureDate() {
        return departureDate;
    }

    public void setDepartureDate(Date departureDate) {
        this.departureDate = departureDate;
    }

    public String getTransport() {
        return transport;
    }

    public void setTransport(String transport) {
        this.transport = StringUtils.trimWhitespace(transport);
    }

    public String getShift() {
        return shift;
    }

    public void setShift(String shift) {
        this.shift = StringUtils.trimWhitespace(shift);
    }

    public Date getLastNucleicAcidDate() {
        return lastNucleicAcidDate;
    }

    public void setLastNucleicAcidDate(Date lastNucleicAcidDate) {
        this.lastNucleicAcidDate = lastNucleicAcidDate;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = StringUtils.trimWhitespace(duration);
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = StringUtils.trimWhitespace(memo);
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = StringUtils.trimWhitespace(opinion);
    }

    public ENUM_STATUS getStatus() {
        return status;
    }

    public void setStatus(ENUM_STATUS status) {
        this.status = status;
    }

    public boolean getEditable() {
        return editable;
    }

    public void setEditable(boolean editable) {
        this.editable = editable;
    }

    public enum ENUM_CREDENTIAL_TYPE {

        身份证(0),
        其它(1);

        int value;

        ENUM_CREDENTIAL_TYPE(int value) {
            this.value = value;
        }

        public static ENUM_CREDENTIAL_TYPE valueOf(int value) {
            if (value == 0) {
                return 身份证;
            } else if (value == 1) {
                return 其它;
            }
            return null;
        }

        public int value() {
            return this.value;
        }
    }

    public enum ENUM_STATUS {

        DRAFT(0),
        DISPATCHED(1),
        RECEIVED(2),
        DENIED(3),
        CLOSED(4),
        REJECTED(5);

        int value;

        ENUM_STATUS(int value) {
            this.value = value;
        }

        public static ENUM_STATUS valueOf(int value) {
            switch (value) {
                case 0:
                    return DRAFT;
                case 1:
                    return DISPATCHED;
                case 2:
                    return RECEIVED;
                case 3:
                    return DENIED;
                case 4:
                    return CLOSED;
                case 5:
                    return REJECTED;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }
    }

}