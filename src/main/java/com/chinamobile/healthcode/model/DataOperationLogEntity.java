package com.chinamobile.healthcode.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据操作日志实体
 */
@Entity
@Table(name = "data_operation_logs")
public class DataOperationLogEntity extends AbstractEntity {

    @Id
    @Column(length = 36)
    private String id = String.valueOf(IdWorker.getInstance().nextId());

    /**
     * 操作类型
     */
    @Column(length = 20, nullable = false)
    private String operationType;

    /**
     * 操作描述
     */
    @Column(length = 500)
    private String description;

    /**
     * 实体类名称
     */
    @Column(length = 100)
    private String entityName;

    /**
     * 实体ID
     */
    @Column(length = 36)
    private String entityId;

    /**
     * 操作前数据（JSON格式）
     */
    @Column(columnDefinition = "text")
    private String beforeData;

    /**
     * 操作后数据（JSON格式）
     */
    @Column(columnDefinition = "text")
    private String afterData;

    /**
     * 请求参数（JSON格式）
     */
    @Column(columnDefinition = "text")
    private String requestParams;

    /**
     * 响应结果（JSON格式）
     */
    @Column(columnDefinition = "text")
    private String responseResult;

    /**
     * 操作人ID
     */
    @Column(length = 36)
    private String operatorId;

    /**
     * 操作人姓名
     */
    @Column(length = 100)
    private String operatorName;

    /**
     * 操作人部门
     */
    @Column(length = 200)
    private String operatorDept;

    /**
     * 操作时间
     */
    @Column(nullable = false)
    private Date operationTime;

    /**
     * 客户端IP
     */
    @Column(length = 50)
    private String clientIp;

    /**
     * 用户代理
     */
    @Column(length = 500)
    private String userAgent;

    /**
     * 请求URL
     */
    @Column(length = 500)
    private String requestUrl;

    /**
     * 请求方法
     */
    @Column(length = 10)
    private String requestMethod;

    /**
     * 执行时长（毫秒）
     */
    private Long executionTime;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息
     */
    @Column(columnDefinition = "text")
    private String errorMessage;

    // 构造函数
    public DataOperationLogEntity() {
        this.operationTime = new Date();
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getBeforeData() {
        return beforeData;
    }

    public void setBeforeData(String beforeData) {
        this.beforeData = beforeData;
    }

    public String getAfterData() {
        return afterData;
    }

    public void setAfterData(String afterData) {
        this.afterData = afterData;
    }

    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }

    public String getResponseResult() {
        return responseResult;
    }

    public void setResponseResult(String responseResult) {
        this.responseResult = responseResult;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOperatorDept() {
        return operatorDept;
    }

    public void setOperatorDept(String operatorDept) {
        this.operatorDept = operatorDept;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}