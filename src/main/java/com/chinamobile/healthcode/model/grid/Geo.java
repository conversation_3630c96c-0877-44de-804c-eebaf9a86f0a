package com.chinamobile.healthcode.model.grid;

import com.chinamobile.sparrow.domain.model.sys.Department;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "VirtualAreaInfo")
public class Geo implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long primaryId;
    String areaId;
    String name;
    String sn;
    String parentId;
    String gridName;
    String areaCode;
    String gridKind;
    String stfyGridId;
    String stfyGridName;
    String gcjGpsPolygonCenter;
    String gcjGpsPolygon;
    String gpsPolygonCenter;
    String gpsPolygon;
    String id;

    public void fromDepartment(Department department) {
        setAreaId(department.getId());
        setName(department.getName());
        setParentId(department.getSuperiorId());
        setGridName(String.join("", "广东省汕头市", department.getFullName().replaceAll("/", "")));
        setGridKind("100008");
        setStfyGridId(department.getId());
        setStfyGridName(department.getFullName());
        setId(department.getId());
    }

    public Long getPrimaryId() {
        return primaryId;
    }

    public void setPrimaryId(Long primaryId) {
        this.primaryId = primaryId;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getGridName() {
        return gridName;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getGridKind() {
        return gridKind;
    }

    public void setGridKind(String gridKind) {
        this.gridKind = gridKind;
    }

    public String getStfyGridId() {
        return stfyGridId;
    }

    public void setStfyGridId(String stfyGridId) {
        this.stfyGridId = stfyGridId;
    }

    public String getStfyGridName() {
        return stfyGridName;
    }

    public void setStfyGridName(String stfyGridName) {
        this.stfyGridName = stfyGridName;
    }

    public String getGcjGpsPolygonCenter() {
        return gcjGpsPolygonCenter;
    }

    public void setGcjGpsPolygonCenter(String gcjGpsPolygonCenter) {
        this.gcjGpsPolygonCenter = gcjGpsPolygonCenter;
    }

    public String getGcjGpsPolygon() {
        return gcjGpsPolygon;
    }

    public void setGcjGpsPolygon(String gcjGpsPolygon) {
        this.gcjGpsPolygon = gcjGpsPolygon;
    }

    public String getGpsPolygonCenter() {
        return gpsPolygonCenter;
    }

    public void setGpsPolygonCenter(String gpsPolygonCenter) {
        this.gpsPolygonCenter = gpsPolygonCenter;
    }

    public String getGpsPolygon() {
        return gpsPolygon;
    }

    public void setGpsPolygon(String gpsPolygon) {
        this.gpsPolygon = gpsPolygon;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

}
