package com.chinamobile.healthcode.model.cmc;

import com.chinamobile.healthcode.model.Record;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

// 领导下沉
@Entity
@Table(name = "cmc_cadre_trainings")
public class CadreTraining extends Record {

    // 综治中心
    @Column(length = 36)
    String unitId;

    @Transient
    String unitName;

    @Column(columnDefinition = "text")
    String workUnit;

    String duties;

    @Column(length = 16)
    String cadre;

    Date fromDate;

    Date toDate;

    // 下沉情况
    @Column(columnDefinition = "text")
    String memo;

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getWorkUnit() {
        return workUnit;
    }

    public void setWorkUnit(String workUnit) {
        this.workUnit = workUnit;
    }

    public String getDuties() {
        return duties;
    }

    public void setDuties(String duties) {
        this.duties = duties;
    }

    public String getCadre() {
        return cadre;
    }

    public void setCadre(String cadre) {
        this.cadre = cadre;
    }

    public Date getFromDate() {
        return fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

}