package com.chinamobile.healthcode.model.cmc;

import com.chinamobile.healthcode.model.Record;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

// 群众来访
@Entity
@Table(name = "cmc_masses_visits")
public class MassesVisit extends Record {

    // 综治中心
    @Column(length = 36)
    String unitId;

    @Transient
    String unitName;

    @Column(length = 16)
    String visitorName;

    @Column(length = 32)
    String visitorIDCardNO;

    boolean isVisitorFemale;

    int visitorAge;

    @Column(length = 11)
    String visitorContact;

    @Column(columnDefinition = "text")
    String visitorAddress;

    @Column(columnDefinition = "text")
    String coplaintiff;

    @Column(columnDefinition = "text")
    String demand;

    @Column(length = 16)
    String assignee;

    // 办结情况
    @Column(columnDefinition = "text")
    String memo;

    // 完成情况
    boolean isClosed;

    // 接访领导
    @Column(length = 16)
    String receiver;

    // 责任领导
    @Column(length = 16)
    String director;

    Date recordingTime;

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getVisitorName() {
        return visitorName;
    }

    public void setVisitorName(String visitorName) {
        this.visitorName = visitorName;
    }

    public String getVisitorIDCardNO() {
        return visitorIDCardNO;
    }

    public void setVisitorIDCardNO(String visitorIDCardNo) {
        this.visitorIDCardNO = visitorIDCardNo;
    }

    public boolean getIsVisitorFemale() {
        return isVisitorFemale;
    }

    public void setIsVisitorFemale(boolean visitorFemale) {
        isVisitorFemale = visitorFemale;
    }

    public int getVisitorAge() {
        return visitorAge;
    }

    public void setVisitorAge(int visitorAge) {
        this.visitorAge = visitorAge;
    }

    public String getVisitorContact() {
        return visitorContact;
    }

    public void setVisitorContact(String visitorContact) {
        this.visitorContact = visitorContact;
    }

    public String getVisitorAddress() {
        return visitorAddress;
    }

    public void setVisitorAddress(String visitorAddress) {
        this.visitorAddress = visitorAddress;
    }

    public String getCoplaintiff() {
        return coplaintiff;
    }

    public void setCoplaintiff(String coplaintiff) {
        this.coplaintiff = coplaintiff;
    }

    public String getDemand() {
        return demand;
    }

    public void setDemand(String demand) {
        this.demand = demand;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public boolean isClosed() {
        return isClosed;
    }

    public void setClosed(boolean closed) {
        isClosed = closed;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getDirector() {
        return director;
    }

    public void setDirector(String director) {
        this.director = director;
    }

    public Date getRecordingTime() {
        return recordingTime;
    }

    public void setRecordingTime(Date recordingTime) {
        this.recordingTime = recordingTime;
    }

}