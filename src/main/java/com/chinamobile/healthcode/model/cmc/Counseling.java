package com.chinamobile.healthcode.model.cmc;

import com.chinamobile.healthcode.model.Record;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

// 心理咨询
@Entity
@Table(name = "cmc_counselings")
public class Counseling extends Record {

    // 综治中心
    @Column(length = 36)
    String unitId;

    @Transient
    String unitName;

    @Column(length = 16)
    String visitorName;

    boolean isVisitorFemale;

    int visitorAge;

    @Column(columnDefinition = "text")
    String problem;

    @Column(length = 16)
    String teacher;

    // 咨询地址
    @Column(columnDefinition = "text")
    String address;

    // 辅导记录
    @Column(columnDefinition = "text")
    String log;

    // 辅导结果
    @Column(columnDefinition = "text")
    String result;

    // 备注
    @Column(columnDefinition = "text")
    String memo;

    Date recordingTime;

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getVisitorName() {
        return visitorName;
    }

    public void setVisitorName(String visitorName) {
        this.visitorName = visitorName;
    }

    public boolean getIsVisitorFemale() {
        return isVisitorFemale;
    }

    public void setIsVisitorFemale(boolean visitorFemale) {
        isVisitorFemale = visitorFemale;
    }

    public int getVisitorAge() {
        return visitorAge;
    }

    public void setVisitorAge(int visitorAge) {
        this.visitorAge = visitorAge;
    }

    public String getProblem() {
        return problem;
    }

    public void setProblem(String problem) {
        this.problem = problem;
    }

    public String getTeacher() {
        return teacher;
    }

    public void setTeacher(String teacher) {
        this.teacher = teacher;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Date getRecordingTime() {
        return recordingTime;
    }

    public void setRecordingTime(Date recordingTime) {
        this.recordingTime = recordingTime;
    }

}