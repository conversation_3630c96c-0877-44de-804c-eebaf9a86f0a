package com.chinamobile.healthcode.model.citizen;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "citizen_visual_grids", indexes = {
        @Index(columnList = "name")
})
public class Grid extends AbstractEntity {

    @Id
    @Column(length = 18)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 18)
    String superiorId;

    @Column(length = 128)
    String name;

    @Column(length = 16)
    String type;

    @Column(columnDefinition = "text")
    String code;

    @Column(columnDefinition = "text")
    String fullName;

    @Transient
    List<String> memberIds;

    @Column(columnDefinition = "text")
    String memberIdsJSON;

    @Column(columnDefinition = "text")
    String memberFullNamesJSON;

    int level;

    boolean isEnabled = true;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSuperiorId() {
        return superiorId;
    }

    public void setSuperiorId(String superiorId) {
        this.superiorId = superiorId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public List<String> getMemberIds() {
        return memberIds;
    }

    public void setMemberIds(List<String> memberIds) {
        this.memberIds = memberIds;
    }

    public String getMemberIdsJSON() {
        return memberIdsJSON;
    }

    public void setMemberIdsJSON(String memberIdsJson) {
        this.memberIdsJSON = memberIdsJson;
    }

    public String getMemberFullNamesJSON() {
        return memberFullNamesJSON;
    }

    public void setMemberFullNamesJSON(String memberFullNamesJSON) {
        this.memberFullNamesJSON = memberFullNamesJSON;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(boolean enabled) {
        isEnabled = enabled;
    }

}