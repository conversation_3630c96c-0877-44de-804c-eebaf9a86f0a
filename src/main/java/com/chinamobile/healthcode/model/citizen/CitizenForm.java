package com.chinamobile.healthcode.model.citizen;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "citizen_forms", indexes = {
        @Index(columnList = "deptId"),
        @Index(columnList = "gridId"),
        @Index(columnList = "name"),
        @Index(columnList = "credentialNo"),
        @Index(columnList = "mp"),
        @Index(columnList = "creatorId"),
        @Index(columnList = "createTime"),
        @Index(columnList = "deptId, mp, actor"),
        @Index(columnList = "gridId, mp, actor"),
        @Index(columnList = "creatorId, createTime"),
        @Index(columnList = "deptFullName")
})
public class CitizenForm extends AbstractEntity {

    @Id
    @Column(length = 18)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 18)
    String deptId;

    @Column(length = 255)
    String deptFullName;

    @Transient
    String deptName;

    @Column(length = 36)
    String gridId;

    @Column(length = 36)
    String profileId;

    @Column(length = 16)
    String name;

    ENUM_ACTOR actor;

    ENUM_STAY stay;

    ENUM_CREDENTIAL_TYPE credentialType;

    @Column(length = 36)
    String credentialNo;

    @Column(length = 11)
    String mp;

    @Transient
    List<String> crowdIds;

    @Column(columnDefinition = "text")
    String crowdIdsJSON;

    @Transient
    String crowdNamesJSON;

    @Column(columnDefinition = "text")
    String workplace;

    @Column(length = 18)
    String subdistrictId;

    @Column(columnDefinition = "text")
    String subdistrictFullName;

    @Column(columnDefinition = "text")
    String address;

    @Column(columnDefinition = "text")
    String memo;

    @Column(length = 32)
    String lat;

    @Column(length = 32)
    String lnt;

    @Column(length = 32)
    String latInWgs;

    @Column(length = 32)
    String lngInWgs;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptFullName() {
        return deptFullName;
    }

    public void setDeptFullName(String deptFullName) {
        this.deptFullName = deptFullName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getGridId() {
        return gridId;
    }

    public void setGridId(String gridId) {
        this.gridId = gridId;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public ENUM_ACTOR getActor() {
        return actor;
    }

    public void setActor(ENUM_ACTOR actor) {
        this.actor = actor;
    }

    public ENUM_STAY getStay() {
        return stay;
    }

    public void setStay(ENUM_STAY stay) {
        this.stay = stay;
    }

    public ENUM_CREDENTIAL_TYPE getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(ENUM_CREDENTIAL_TYPE credentialType) {
        this.credentialType = credentialType;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNum) {
        this.credentialNo = StringUtils.trimWhitespace(credentialNum);
    }

    public String getMp() {
        return mp;
    }

    public void setMp(String mp) {
        this.mp = StringUtils.trimWhitespace(mp);
    }

    public List<String> getCrowdIds() {
        return crowdIds;
    }

    public void setCrowdIds(List<String> crowdIds) {
        this.crowdIds = crowdIds;
    }

    public String getCrowdIdsJSON() {
        return crowdIdsJSON;
    }

    public void setCrowdIdsJSON(String crowdId) {
        this.crowdIdsJSON = crowdId;
    }

    public String getCrowdNamesJSON() {
        return crowdNamesJSON;
    }

    public void setCrowdNamesJSON(String crowdNamesJSON) {
        this.crowdNamesJSON = crowdNamesJSON;
    }

    public String getWorkplace() {
        return workplace;
    }

    public void setWorkplace(String workplace) {
        this.workplace = StringUtils.trimWhitespace(workplace);
    }

    public String getSubdistrictId() {
        return subdistrictId;
    }

    public void setSubdistrictId(String subdistrictId) {
        this.subdistrictId = subdistrictId;
    }

    public String getSubdistrictFullName() {
        return subdistrictFullName;
    }

    public void setSubdistrictFullName(String subdistrictFullName) {
        this.subdistrictFullName = subdistrictFullName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = StringUtils.trimWhitespace(address);
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = StringUtils.trimWhitespace(memo);
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String addressLat) {
        this.lat = addressLat;
    }

    public String getLnt() {
        return lnt;
    }

    public void setLnt(String addressLnt) {
        this.lnt = addressLnt;
    }

    public String getLatInWgs() {
        return latInWgs;
    }

    public void setLatInWgs(String addressLatInWgs) {
        this.latInWgs = addressLatInWgs;
    }

    public String getLngInWgs() {
        return lngInWgs;
    }

    public void setLngInWgs(String addressLngInWgs) {
        this.lngInWgs = addressLngInWgs;
    }

    public enum ENUM_ACTOR {

        本人(0),
        亲属(1),
        朋友(2),
        同事(3),
        其他(4);

        int value;

        ENUM_ACTOR(int value) {
            this.value = value;
        }

        public static ENUM_ACTOR valueOf(int value) {
            if (value == 0) {
                return 本人;
            } else if (value == 1) {
                return 亲属;
            } else if (value == 2) {
                return 朋友;
            } else if (value == 3) {
                return 同事;
            } else if (value == 4) {
                return 其他;
            }
            return null;
        }

        public int value() {
            return this.value;
        }
    }

    public enum ENUM_CREDENTIAL_TYPE {

        身份证(0),
        其它(1);

        int value;

        ENUM_CREDENTIAL_TYPE(int value) {
            this.value = value;
        }

        public static ENUM_CREDENTIAL_TYPE valueOf(int value) {
            if (value == 0) {
                return 身份证;
            } else if (value == 1) {
                return 其它;
            }
            return null;
        }

        public int value() {
            return this.value;
        }
    }

    public enum ENUM_STAY {

        常住(0),
        暂住(1);

        int value;

        ENUM_STAY(int value) {
            this.value = value;
        }

        public static ENUM_STAY valueOf(int value) {
            if (value == 0) {
                return 常住;
            } else if (value == 1) {
                return 暂住;
            }
            return null;
        }

        public int value() {
            return this.value;
        }
    }

}
