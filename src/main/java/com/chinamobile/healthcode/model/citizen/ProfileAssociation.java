package com.chinamobile.healthcode.model.citizen;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;

@Entity
@Table(name = "citizen_profile_associations")
public class ProfileAssociation extends AbstractEntity {

    @Id
    @Column(length = 18)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 11)
    String mp;

    @Column(length = 36)
    String associatedCredentialNo;

    @Transient
    String associatedUsername;

    Profile.ENUM_ACTOR actor;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMp() {
        return mp;
    }

    public void setMp(String mp) {
        this.mp = StringUtils.trimWhitespace(mp);
    }

    public String getAssociatedCredentialNo() {
        return associatedCredentialNo;
    }

    public void setAssociatedCredentialNo(String relativeCredentialNo) {
        this.associatedCredentialNo = StringUtils.trimWhitespace(relativeCredentialNo);
    }

    public String getAssociatedUsername() {
        return associatedUsername;
    }

    public void setAssociatedUsername(String associatedUsername) {
        this.associatedUsername = StringUtils.trimWhitespace(associatedUsername);
    }

    public Profile.ENUM_ACTOR getActor() {
        return actor;
    }

    public void setActor(Profile.ENUM_ACTOR actor) {
        this.actor = actor;
    }

}