package com.chinamobile.healthcode.model.citizen;

import com.chinamobile.sparrow.domain.model.AbstractEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "citizen_grid_managers")
public class GridManager extends AbstractEntity {

    @Id
    @Column(length = 18)
    String gridId;

    @Id
    @Column(length = 18)
    String userId;

    public String getGridId() {
        return gridId;
    }

    public void setGridId(String griIid) {
        this.gridId = griIid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

}