package com.chinamobile.healthcode.model.citizen;

import com.chinamobile.sparrow.domain.model.AbstractEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "citizen_profile_validations")
public class ProfileValidation extends AbstractEntity {

    @Id
    @Column(length = 36)
    String profileId;

    @Column(length = 18)
    String subdistrictId;

    @Column(columnDefinition = "text")
    String subdistrictFullName;

    @Column(length = 128)
    String codes;

    @Column(columnDefinition = "text")
    String descriptions;

    public ProfileValidation() {
    }

    public ProfileValidation(String profileId, String codes, String descriptions, String subdistrictId, String subdistrictFullName) {
        this.profileId = profileId;
        this.codes = codes;
        this.descriptions = descriptions;
        this.subdistrictId = subdistrictId;
        this.subdistrictFullName = subdistrictFullName;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public String getCodes() {
        return codes;
    }

    public void setCodes(String codes) {
        this.codes = codes;
    }

    public String getDescriptions() {
        return descriptions;
    }

    public void setDescriptions(String description) {
        this.descriptions = description;
    }

    public String getSubdistrictId() {
        return subdistrictId;
    }

    public void setSubdistrictId(String subdistrictId) {
        this.subdistrictId = subdistrictId;
    }

    public String getSubdistrictFullName() {
        return subdistrictFullName;
    }

    public void setSubdistrictFullName(String subdistrictFullName) {
        this.subdistrictFullName = subdistrictFullName;
    }

}