package com.chinamobile.healthcode.model.citizen;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "citizen_historical_forms")
public class HistoricalForm extends AbstractEntity {

    @Id
    @Column(length = 18)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 18)
    String formId;

    @Column(length = 18)
    String deptId;

    @Column(columnDefinition = "text")
    String deptFullName;

    @Transient
    String deptName;

    @Column(length = 36)
    String gridId;

    @Column(length = 16)
    String name;

    CitizenForm.ENUM_ACTOR actor;

    CitizenForm.ENUM_STAY stay;

    CitizenForm.ENUM_CREDENTIAL_TYPE credentialType;

    @Column(length = 36)
    String credentialNo;

    @Column(length = 11)
    String mp;

    @Transient
    List<String> crowdIds;

    @Column(columnDefinition = "text")
    String crowdIdsJSON;

    @Transient
    String crowdNamesJSON;

    @Column(columnDefinition = "text")
    String workplace;

    @Column(length = 18)
    String subdistrictId;

    @Column(columnDefinition = "text")
    String subdistrictFullName;

    @Column(columnDefinition = "text")
    String address;

    @Column(columnDefinition = "text")
    String memo;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptFullName() {
        return deptFullName;
    }

    public void setDeptFullName(String deptFullName) {
        this.deptFullName = deptFullName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getGridId() {
        return gridId;
    }

    public void setGridId(String gridId) {
        this.gridId = gridId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public CitizenForm.ENUM_ACTOR getActor() {
        return actor;
    }

    public void setActor(CitizenForm.ENUM_ACTOR actor) {
        this.actor = actor;
    }

    public CitizenForm.ENUM_STAY getStay() {
        return stay;
    }

    public void setStay(CitizenForm.ENUM_STAY stay) {
        this.stay = stay;
    }

    public CitizenForm.ENUM_CREDENTIAL_TYPE getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(CitizenForm.ENUM_CREDENTIAL_TYPE credentialType) {
        this.credentialType = credentialType;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNum) {
        this.credentialNo = credentialNum;
    }

    public String getMp() {
        return mp;
    }

    public void setMp(String mp) {
        this.mp = mp;
    }

    public List<String> getCrowdIds() {
        return crowdIds;
    }

    public void setCrowdIds(List<String> crowdIds) {
        this.crowdIds = crowdIds;
    }

    public String getCrowdIdsJSON() {
        return crowdIdsJSON;
    }

    public void setCrowdIdsJSON(String crowdId) {
        this.crowdIdsJSON = crowdId;
    }

    public String getCrowdNamesJSON() {
        return crowdNamesJSON;
    }

    public void setCrowdNamesJSON(String crowdNamesJSON) {
        this.crowdNamesJSON = crowdNamesJSON;
    }

    public String getWorkplace() {
        return workplace;
    }

    public void setWorkplace(String workplace) {
        this.workplace = workplace;
    }

    public String getSubdistrictId() {
        return subdistrictId;
    }

    public void setSubdistrictId(String subdistrictId) {
        this.subdistrictId = subdistrictId;
    }

    public String getSubdistrictFullName() {
        return subdistrictFullName;
    }

    public void setSubdistrictFullName(String subdistrictFullName) {
        this.subdistrictFullName = subdistrictFullName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

}