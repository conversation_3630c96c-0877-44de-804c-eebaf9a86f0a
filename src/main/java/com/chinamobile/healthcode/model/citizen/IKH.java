package com.chinamobile.healthcode.model.citizen;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "t_inject_info_ikh")
public class IKH {

    @Id
    @Column(length = 36)
    String id;

    @Column(name = "person_name")
    String name;

    @Column(name = "idcard_no")
    String credentialNo;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNo) {
        this.credentialNo = credentialNo;
    }

}