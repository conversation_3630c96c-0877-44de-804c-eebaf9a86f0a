package com.chinamobile.healthcode.model.citizen;

import com.chinamobile.sparrow.domain.model.AbstractEntity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "citizen_injection_profiles")
public class CovidVaccineInoculation extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id;

    @Column(length = 16)
    String name;

    Profile.ENUM_STAY stay;

    Profile.ENUM_CREDENTIAL_TYPE credentialType;

    @Column(columnDefinition = "varchar(36) unique")
    String credentialNo;

    @Column(columnDefinition = "varchar(11)")
    String mp;

    @Transient
    List<String> crowdIds;

    @Column(columnDefinition = "text")
    String crowdIdsJSON;

    @Transient
    String crowdNamesJSON;

    @Column(columnDefinition = "text")
    String workplace;

    @Column(length = 32)
    String district;

    @Column(length = 18)
    String subdistrictId;

    @Column(length = 250)
    String subdistrictFullName;

    @Column(columnDefinition = "text")
    String address;

    @Column(length = 32)
    String addressLat;

    @Column(length = 32)
    String addressLnt;

    Profile.ENUM_ACTOR actor;

    @Column(name = "vaccine_name", length = 250)
    String vaccine;

    @Column(name = "inject_times")
    Integer injectTimes;

    @Column(name = "inject_date")
    Date injectDate;

    Integer age;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Profile.ENUM_STAY getStay() {
        return stay;
    }

    public void setStay(Profile.ENUM_STAY stay) {
        this.stay = stay;
    }

    public Profile.ENUM_CREDENTIAL_TYPE getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(Profile.ENUM_CREDENTIAL_TYPE credentialType) {
        this.credentialType = credentialType;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNo) {
        this.credentialNo = credentialNo;
    }

    public String getMp() {
        return mp;
    }

    public void setMp(String mp) {
        this.mp = mp;
    }

    public List<String> getCrowdIds() {
        return crowdIds;
    }

    public void setCrowdIds(List<String> crowdIds) {
        this.crowdIds = crowdIds;
    }

    public String getCrowdIdsJSON() {
        return crowdIdsJSON;
    }

    public void setCrowdIdsJSON(String crowdIdsJSON) {
        this.crowdIdsJSON = crowdIdsJSON;
    }

    public String getCrowdNamesJSON() {
        return crowdNamesJSON;
    }

    public void setCrowdNamesJSON(String crowdNamesJSON) {
        this.crowdNamesJSON = crowdNamesJSON;
    }

    public String getWorkplace() {
        return workplace;
    }

    public void setWorkplace(String workplace) {
        this.workplace = workplace;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getSubdistrictId() {
        return subdistrictId;
    }

    public void setSubdistrictId(String subdistrictId) {
        this.subdistrictId = subdistrictId;
    }

    public String getSubdistrictFullName() {
        return subdistrictFullName;
    }

    public void setSubdistrictFullName(String subdistrictFullName) {
        this.subdistrictFullName = subdistrictFullName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddressLat() {
        return addressLat;
    }

    public void setAddressLat(String addressLat) {
        this.addressLat = addressLat;
    }

    public String getAddressLnt() {
        return addressLnt;
    }

    public void setAddressLnt(String addressLnt) {
        this.addressLnt = addressLnt;
    }

    public Profile.ENUM_ACTOR getActor() {
        return actor;
    }

    public void setActor(Profile.ENUM_ACTOR actor) {
        this.actor = actor;
    }

    public String getVaccine() {
        return vaccine;
    }

    public void setVaccine(String vaccineName) {
        this.vaccine = vaccineName;
    }

    public Integer getInjectTimes() {
        return injectTimes;
    }

    public void setInjectTimes(Integer injectTimes) {
        this.injectTimes = injectTimes;
    }

    public Date getInjectDate() {
        return injectDate;
    }

    public void setInjectDate(Date injectDate) {
        this.injectDate = injectDate;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

}