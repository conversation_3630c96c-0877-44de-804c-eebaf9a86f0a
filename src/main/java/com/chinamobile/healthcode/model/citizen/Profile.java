package com.chinamobile.healthcode.model.citizen;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "citizen_profiles", indexes = {
        @Index(columnList = "subdistrictFullName"),
        @Index(columnList = "name"),
        @Index(columnList = "credentialNo"),
        @Index(columnList = "mp"),
        @Index(columnList = "creatorId"),
        @Index(columnList = "createTime"),
        @Index(columnList = "mp, actor"),
        @Index(columnList = "creatorId, createTime")
})
public class Profile extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 16)
    String name;

    ENUM_STAY stay;

    ENUM_CREDENTIAL_TYPE credentialType;

    @Column(columnDefinition = "varchar(36) unique")
    String credentialNo;

    @Column(columnDefinition = "varchar(11)")
    String mp;

    Date birthday;

    @Transient
    List<String> crowdIds;

    @Column(columnDefinition = "text")
    String crowdIdsJSON;

    @Transient
    String crowdNamesJSON;

    @Column(columnDefinition = "text")
    String workplace;

    @Column(length = 18)
    String subdistrictId;

    @Column(columnDefinition = "text")
    String subdistrictFullName;

    @Column(columnDefinition = "text")
    String address;

    Double lat;

    Double lng;

    Double latInWgs;

    Double lngInWgs;

    ENUM_ACTOR actor;

    @Column(length = 32)
    String actorOther;

    @Column(length = 36)
    String ownerId;

    ENUM_STATUS status;

    ENUM_VALIDATION validation = ENUM_VALIDATION.待验证;

    @Transient
    String validationDescription;

    @Transient
    boolean isEditable;

    boolean subjectPerson;

    String subjectPersonTypes;

    public Profile(String id, String name, String credentialNo, ENUM_ACTOR actor) {
        this(name, credentialNo, actor);
        this.id = id;
    }

    public Profile(String name, String credentialNo, ENUM_ACTOR actor) {
        this.name = StringUtils.trimWhitespace(name);
        this.credentialType = ENUM_CREDENTIAL_TYPE.身份证;
        this.credentialNo = StringUtils.trimWhitespace(credentialNo);
        this.actor = actor;
        this.validation = ENUM_VALIDATION.待验证;
    }

    public Profile() {

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public ENUM_STAY getStay() {
        return stay;
    }

    public void setStay(ENUM_STAY stay) {
        this.stay = stay;
    }

    public ENUM_CREDENTIAL_TYPE getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(ENUM_CREDENTIAL_TYPE credentialType) {
        this.credentialType = credentialType;
    }

    public String getCredentialNo() {
        return credentialNo;
    }

    public void setCredentialNo(String credentialNum) {
        this.credentialNo = StringUtils.trimWhitespace(credentialNum);
    }

    public String getMp() {
        return mp;
    }

    public void setMp(String mp) {
        this.mp = StringUtils.trimWhitespace(mp);
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public List<String> getCrowdIds() {
        return crowdIds;
    }

    public void setCrowdIds(List<String> crowdIds) {
        this.crowdIds = crowdIds;
    }

    public String getCrowdIdsJSON() {
        return crowdIdsJSON;
    }

    public void setCrowdIdsJSON(String crowdId) {
        this.crowdIdsJSON = crowdId;
    }

    public String getCrowdNamesJSON() {
        return crowdNamesJSON;
    }

    public void setCrowdNamesJSON(String crowdNamesJSON) {
        this.crowdNamesJSON = crowdNamesJSON;
    }

    public String getWorkplace() {
        return workplace;
    }

    public void setWorkplace(String workplace) {
        this.workplace = StringUtils.trimWhitespace(workplace);
    }

    public String getSubdistrictId() {
        return subdistrictId;
    }

    public void setSubdistrictId(String subdistrictId) {
        this.subdistrictId = subdistrictId;
    }

    public String getSubdistrictFullName() {
        return subdistrictFullName;
    }

    public void setSubdistrictFullName(String subdistrictFullName) {
        this.subdistrictFullName = subdistrictFullName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = StringUtils.trimWhitespace(address);
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lnt) {
        this.lng = lnt;
    }

    public Double getLatInWgs() {
        return latInWgs;
    }

    public void setLatInWgs(Double latInWgs) {
        this.latInWgs = latInWgs;
    }

    public Double getLngInWgs() {
        return lngInWgs;
    }

    public void setLngInWgs(Double lngInWgs) {
        this.lngInWgs = lngInWgs;
    }

    public ENUM_ACTOR getActor() {
        return actor;
    }

    public void setActor(ENUM_ACTOR actor) {
        this.actor = actor;
    }

    public String getActorOther() {
        return actorOther;
    }

    public void setActorOther(String actorOther) {
        this.actorOther = StringUtils.trimWhitespace(actorOther);
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public ENUM_STATUS getStatus() {
        return status;
    }

    public void setStatus(ENUM_STATUS status) {
        this.status = status;
    }

    public ENUM_VALIDATION getValidation() {
        return validation;
    }

    public void setValidation(ENUM_VALIDATION validation) {
        this.validation = validation;
    }

    public String getValidationDescription() {
        return validationDescription;
    }

    public void setValidationDescription(String validationDescription) {
        this.validationDescription = validationDescription;
    }

    public boolean getIsEditable() {
        return isEditable;
    }

    public void setIsEditable(boolean editable) {
        isEditable = editable;
    }

    public boolean isSubjectPerson() {
        return subjectPerson;
    }

    public void setSubjectPerson(boolean subjectPerson) {
        this.subjectPerson = subjectPerson;
    }

    public String getSubjectPersonTypes() {
        return subjectPersonTypes;
    }

    public void setSubjectPersonTypes(String subjectPersonTypes) {
        this.subjectPersonTypes = subjectPersonTypes;
    }

    public enum ENUM_CREDENTIAL_TYPE {
        身份证(0), 其它(1), 港澳居民来往内地通行证(2), 护照(3);

        int value;

        ENUM_CREDENTIAL_TYPE(int value) {
            this.value = value;
        }

        public static ENUM_CREDENTIAL_TYPE valueOf(int value) {
            if (value == 0) {
                return 身份证;
            } else if (value == 1) {
                return 其它;
            } else if (value == 2) {
                return 港澳居民来往内地通行证;
            } else if (value == 3) {
                return 护照;
            }

            return null;
        }

        public int value() {
            return this.value;
        }
    }

    public enum ENUM_STAY {
        常住(0), 暂住(1);

        int value;

        ENUM_STAY(int value) {
            this.value = value;
        }

        public static ENUM_STAY valueOf(int value) {
            if (value == 0) {
                return 常住;
            } else if (value == 1) {
                return 暂住;
            }

            return null;
        }

        public int value() {
            return this.value;
        }
    }

    public enum ENUM_ACTOR {

        本人(0),
        亲属(1),
        朋友(2),
        同事(3),
        社区工作人员(5),
        网格员(6),
        其他(4);

        int value;

        ENUM_ACTOR(int value) {
            this.value = value;
        }

        public static ENUM_ACTOR valueOf(int value) {
            if (value == 0) {
                return 本人;
            } else if (value == 1) {
                return 亲属;
            } else if (value == 2) {
                return 朋友;
            } else if (value == 3) {
                return 同事;
            } else if (value == 5) {
                return 社区工作人员;
            } else if (value == 6) {
                return 网格员;
            } else if (value == 4) {
                return 其他;
            }
            return null;
        }

        public int value() {
            return this.value;
        }
    }

    public enum ENUM_VALIDATION {
        待验证(0), 已验证(1), 验证失败(2);

        int value;

        ENUM_VALIDATION(int value) {
            this.value = value;
        }

        public static ENUM_VALIDATION valueOf(int value) {
            if (value == 0) {
                return 待验证;
            } else if (value == 1) {
                return 已验证;
            } else if (value == 2) {
                return 验证失败;
            }

            return null;
        }

        public int value() {
            return this.value;
        }
    }

    public enum ENUM_STATUS {
        在册(0), 外出(1), 失踪(2), 已故(3);

        int value;

        ENUM_STATUS(int value) {
            this.value = value;
        }

        public static ENUM_STATUS valueOf(int value) {
            if (value == 0) {
                return 在册;
            } else if (value == 1) {
                return 外出;
            } else if (value == 2) {
                return 失踪;
            } else if (value == 3) {
                return 已故;
            }

            return null;
        }

        public int value() {
            return this.value;
        }
    }

}
