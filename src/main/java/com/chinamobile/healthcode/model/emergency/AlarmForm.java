package com.chinamobile.healthcode.model.emergency;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 1/30/2024 15:52
 */
@Entity
@Table(name = "emergency_alarm_form")
public class AlarmForm extends AbstractEntity {
    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36)
    String taskId;

    @Column(length = 36)
    String alarmId;

    /**
     * 所属行政区划，应急单元
     */
    @Column(length = 36)
    String regionId;

    @Column(length = 128)
    String regionFullName;

    Boolean confirmed;

    @Transient
    List<String> attachmentIds;

    @Transient
    List<Media> attachments;

    /**
     * 现场图片
     */
    @Column(columnDefinition = "text")
    String attachmentIdsJSON;

    /**
     * 文字备注
     */
    @Column(columnDefinition = "text")
    String memo;

    @Column(columnDefinition = "text")
    String address;

    Double latitudeInGcj;

    Double longitudeInGcj;

    Double latitudeInWgs;

    Double longitudeInWgs;

    @Transient
    Boolean readonly;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getAlarmId() {
        return alarmId;
    }

    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public List<String> getAttachmentIds() {
        return attachmentIds;
    }

    public void setAttachmentIds(List<String> attachmentIds) {
        this.attachmentIds = attachmentIds;
    }

    public List<Media> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Media> attachments) {
        this.attachments = attachments;
    }

    public String getAttachmentIdsJSON() {
        return attachmentIdsJSON;
    }

    public void setAttachmentIdsJSON(String attachmentIdsJSON) {
        this.attachmentIdsJSON = attachmentIdsJSON;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getLatitudeInGcj() {
        return latitudeInGcj;
    }

    public void setLatitudeInGcj(Double latitudeInGcj) {
        this.latitudeInGcj = latitudeInGcj;
    }

    public Double getLongitudeInGcj() {
        return longitudeInGcj;
    }

    public void setLongitudeInGcj(Double longitudeInGcj) {
        this.longitudeInGcj = longitudeInGcj;
    }

    public Double getLatitudeInWgs() {
        return latitudeInWgs;
    }

    public void setLatitudeInWgs(Double latitudeInWgs) {
        this.latitudeInWgs = latitudeInWgs;
    }

    public Double getLongitudeInWgs() {
        return longitudeInWgs;
    }

    public void setLongitudeInWgs(Double longitudeInWgs) {
        this.longitudeInWgs = longitudeInWgs;
    }

    public Boolean getReadonly() {
        return readonly;
    }

    public void setReadonly(Boolean readonly) {
        this.readonly = readonly;
    }
}
