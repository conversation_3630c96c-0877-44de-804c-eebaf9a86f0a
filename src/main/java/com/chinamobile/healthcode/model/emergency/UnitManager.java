package com.chinamobile.healthcode.model.emergency;

import com.chinamobile.sparrow.domain.model.AbstractEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 1/31/2024 9:55
 */
@Entity
@Table(name = "emergency_unit_managers")
public class UnitManager extends AbstractEntity {
    @Id
    @Column(length = 36)
    String regionId;

    @Id
    @Column(length = 36)
    String userId;

    public UnitManager() {
    }

    public UnitManager(String regionId, String userId) {
        this.regionId = regionId;
        this.userId = userId;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
