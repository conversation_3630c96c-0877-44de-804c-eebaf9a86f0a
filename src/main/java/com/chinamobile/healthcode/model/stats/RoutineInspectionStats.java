package com.chinamobile.healthcode.model.stats;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 8/13/2024 15:27
 */

@Entity
@Table(name = "routine_inspection_stats")
public class RoutineInspectionStats {
    @Id
    @Column(name = "id", nullable = false)
    private String id;

    @Column(name = "record_date")
    private Date recordDate;

    @Size(max = 16)
    @Column(name = "category", length = 16)
    private String category;

    @Size(max = 255)
    @Column(name = "type")
    private String type;

    @Column(name = "total")
    private Long total;

    @Column(name = "diff")
    private Long diff;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getDiff() {
        return diff;
    }

    public void setDiff(Long diff) {
        this.diff = diff;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Date getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(Date recordDate) {
        this.recordDate = recordDate;
    }
}
