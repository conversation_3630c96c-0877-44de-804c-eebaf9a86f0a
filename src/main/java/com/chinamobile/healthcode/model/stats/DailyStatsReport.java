package com.chinamobile.healthcode.model.stats;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import java.util.Date;

@Entity
@Table(name = "daily_stats_report")
public class DailyStatsReport {
    @Id
    @Column(name = "id", nullable = false)
    private String id;

    @Column(name = "record_date")
    private Date recordDate;

    @Size(max = 18)
    @Column(name = "region_id", length = 18)
    private String regionId;

    @Size(max = 255)
    @Column(name = "region_full_name")
    private String regionFullName;

    @Column(name = "num_of_login_users")
    private Integer numOfLoginUsers;

    @Size(max = 16)
    @Column(name = "percentage_of_login_users", length = 16)
    private String percentageOfLoginUsers;

    @Column(name = "person_in_total")
    private Integer personInTotal;

    @Column(name = "increment_of_person")
    private Integer incrementOfPerson;

    @Column(name = "place_in_total")
    private Integer placeInTotal;

    @Column(name = "increment_of_place")
    private Integer incrementOfPlace;

    @Column(name = "event_in_total")
    private Integer eventInTotal;

    @Column(name = "increment_of_event")
    private Integer incrementOfEvent;

    @Column(name = "property_in_total")
    private Integer propertyInTotal;

    @Column(name = "increment_of_property")
    private Integer incrementOfProperty;

    @Column(name = "unit_in_total")
    private Integer unitInTotal;

    @Column(name = "increment_of_unit")
    private Integer incrementOfUnit;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(Date recordDate) {
        this.recordDate = recordDate;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

    public Integer getNumOfLoginUsers() {
        return numOfLoginUsers;
    }

    public void setNumOfLoginUsers(Integer numOfLoginUsers) {
        this.numOfLoginUsers = numOfLoginUsers;
    }

    public String getPercentageOfLoginUsers() {
        return percentageOfLoginUsers;
    }

    public void setPercentageOfLoginUsers(String percentageOfLoginUsers) {
        this.percentageOfLoginUsers = percentageOfLoginUsers;
    }

    public Integer getPersonInTotal() {
        return personInTotal;
    }

    public void setPersonInTotal(Integer personInTotal) {
        this.personInTotal = personInTotal;
    }

    public Integer getIncrementOfPerson() {
        return incrementOfPerson;
    }

    public void setIncrementOfPerson(Integer incrementOfPerson) {
        this.incrementOfPerson = incrementOfPerson;
    }

    public Integer getPlaceInTotal() {
        return placeInTotal;
    }

    public void setPlaceInTotal(Integer placeInTotal) {
        this.placeInTotal = placeInTotal;
    }

    public Integer getIncrementOfPlace() {
        return incrementOfPlace;
    }

    public void setIncrementOfPlace(Integer incrementOfPlace) {
        this.incrementOfPlace = incrementOfPlace;
    }

    public Integer getEventInTotal() {
        return eventInTotal;
    }

    public void setEventInTotal(Integer eventInTotal) {
        this.eventInTotal = eventInTotal;
    }

    public Integer getIncrementOfEvent() {
        return incrementOfEvent;
    }

    public void setIncrementOfEvent(Integer incrementOfEvent) {
        this.incrementOfEvent = incrementOfEvent;
    }

    public Integer getPropertyInTotal() {
        return propertyInTotal;
    }

    public void setPropertyInTotal(Integer propertyInTotal) {
        this.propertyInTotal = propertyInTotal;
    }

    public Integer getIncrementOfProperty() {
        return incrementOfProperty;
    }

    public void setIncrementOfProperty(Integer incrementOfProperty) {
        this.incrementOfProperty = incrementOfProperty;
    }

    public Integer getUnitInTotal() {
        return unitInTotal;
    }

    public void setUnitInTotal(Integer unitInTotal) {
        this.unitInTotal = unitInTotal;
    }

    public Integer getIncrementOfUnit() {
        return incrementOfUnit;
    }

    public void setIncrementOfUnit(Integer incrementOfUnit) {
        this.incrementOfUnit = incrementOfUnit;
    }

}
