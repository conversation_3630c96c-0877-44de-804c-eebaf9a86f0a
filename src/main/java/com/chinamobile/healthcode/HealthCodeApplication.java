package com.chinamobile.healthcode;

import com.chinamobile.sparrow.domain.infra.job.QuartzJobScan;
import com.chinamobile.sparrow.domain.infra.sec.shiro.ShiroPermissionScan;
import com.chinamobile.sparrow.domain.infra.sys.MenuScan;
import com.chinamobile.sparrow.springboot.web.autoconfigure.DataSourceAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@EnableAsync
@EnableWebMvc
@EnableWebSocket
@MenuScan
@QuartzJobScan
@ShiroPermissionScan
public class HealthCodeApplication {

    public static void main(String[] args) {
        SpringApplication.run(HealthCodeApplication.class, args);
    }

}