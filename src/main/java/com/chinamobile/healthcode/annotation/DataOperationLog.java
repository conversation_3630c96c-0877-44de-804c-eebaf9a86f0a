package com.chinamobile.healthcode.annotation;

import java.lang.annotation.*;

/**
 * 数据操作日志注解
 * 用于标记需要记录数据操作日志的方法
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataOperationLog {
    
    /**
     * 操作类型
     */
    OperationType type();
    
    /**
     * 操作描述
     */
    String description() default "";
    
    /**
     * 实体类名称
     */
    String entityName() default "";
    
    /**
     * 实体ID参数名（用于获取实体ID）
     */
    String entityIdParam() default "id";
    
    /**
     * 是否记录请求参数
     */
    boolean logParams() default true;
    
    /**
     * 是否记录响应结果
     */
    boolean logResult() default true;
    
    /**
     * 操作类型枚举
     */
    enum OperationType {
        CREATE("新增"),
        UPDATE("修改"),
        DELETE("删除"),
        QUERY("查询");
        
        private final String description;
        
        OperationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
} 