package com.chinamobile.healthcode.service;

import com.chinamobile.sparrow.domain.lang.WebSocketMessagePayload;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class WebSocketClientService {

    final List<Pair<String, WebSocketSession>> clients;
    final Logger logger;

    public WebSocketClientService() {
        this.clients = new ArrayList<>();
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public void registerAlarmClient(String region, WebSocketSession session) {
        if (clients.stream().noneMatch(i -> region.equals(i.getLeft()) && session.getId().equals(i.getRight().getId()))) {
            clients.add(Pair.of(region, session));

            try {
                WebSocketMessagePayload<String> _id = new WebSocketMessagePayload<>();
                _id.setSubject("register_alarm");
                _id.setData(session.getId());

                session.sendMessage(new TextMessage(ConverterUtil.toJson(_id)));
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    public void sendAlarmMessage(String region, String message) {
        String _region = parseRegion(region);
        List<Pair<String, WebSocketSession>> _sessions = clients.stream()
                .filter(i -> Objects.equals(_region, i.getLeft()))
                .filter(i -> i.getRight().isOpen())
                .collect(Collectors.toList());

        _sessions.forEach(i -> {
            try {
                i.getRight().sendMessage(new TextMessage(message));
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
        });
    }

    public void unregisterClient(WebSocketSession session) {
        List<Pair<String, WebSocketSession>> _alarms = clients.stream()
                .filter(i -> session.getId().equals(i.getRight().getId()))
                .collect(Collectors.toList());
        if (!_alarms.isEmpty()) {
            clients.removeAll(_alarms);
        }
    }

    String parseRegion(String region) {
        return DefaultDepartmentRepository.ROOT_DEPARTMENT_NAME.equals(region) ? "汕头市" : region;
    }

}