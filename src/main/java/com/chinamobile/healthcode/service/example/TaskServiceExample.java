package com.chinamobile.healthcode.service.example;

import com.chinamobile.healthcode.annotation.DataOperationLog;
import com.chinamobile.healthcode.model.Task;
import com.chinamobile.healthcode.repository.TaskRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Task服务示例 - 展示如何使用@DataOperationLog注解
 */
@Service
public class TaskServiceExample {

    @Autowired
    private TaskRepository taskRepository;

    /**
     * 新增任务 - 使用CREATE操作类型
     */
    @DataOperationLog(
        type = DataOperationLog.OperationType.CREATE,
        description = "新增任务",
        entityName = "Task",
        entityIdParam = "id"
    )
    @Transactional
    public Result<String> createTask(Task task, User user) {
        // 业务逻辑：创建任务
        Result<String> result = new Result<>();
        
        try {
            // 这里调用实际的保存逻辑
            // taskRepository.save(task, user);
            result.data = task.getId();
        } catch (Exception e) {
            result.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{e.getMessage()});
        }
        
        return result;
    }

    /**
     * 更新任务 - 使用UPDATE操作类型
     */
    @DataOperationLog(
        type = DataOperationLog.OperationType.UPDATE,
        description = "更新任务",
        entityName = "Task",
        entityIdParam = "id"
    )
    @Transactional
    public Result<String> updateTask(Task task, User user) {
        // 业务逻辑：更新任务
        Result<String> result = new Result<>();
        
        try {
            // 这里调用实际的更新逻辑
            // taskRepository.save(task, user);
            result.data = task.getId();
        } catch (Exception e) {
            result.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{e.getMessage()});
        }
        
        return result;
    }

    /**
     * 删除任务 - 使用DELETE操作类型
     */
    @DataOperationLog(
        type = DataOperationLog.OperationType.DELETE,
        description = "删除任务",
        entityName = "Task",
        entityIdParam = "id"
    )
    @Transactional
    public Result<Void> deleteTask(String taskId, User user) {
        // 业务逻辑：删除任务
        Result<Void> result = new Result<>();
        
        try {
            // 这里调用实际的删除逻辑
            // taskRepository.remove(taskId, user);
        } catch (Exception e) {
            result.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{e.getMessage()});
        }
        
        return result;
    }

    /**
     * 查询任务 - 使用QUERY操作类型
     */
    @DataOperationLog(
        type = DataOperationLog.OperationType.QUERY,
        description = "查询任务",
        entityName = "Task",
        entityIdParam = "id",
        logParams = true,
        logResult = false  // 查询结果通常不需要记录
    )
    @Transactional(readOnly = true)
    public Result<Task> getTask(String taskId, User user) {
        // 业务逻辑：查询任务
        Result<Task> result = new Result<>();
        
        try {
            // 这里调用实际的查询逻辑
            // result = taskRepository.get(taskId, user);
        } catch (Exception e) {
            result.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{e.getMessage()});
        }
        
        return result;
    }
} 