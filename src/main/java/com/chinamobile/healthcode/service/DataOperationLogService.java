package com.chinamobile.healthcode.service;

import com.chinamobile.healthcode.model.DataOperationLogEntity;
import com.chinamobile.healthcode.repository.DataOperationLogRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 数据操作日志服务
 */
@Service
public class DataOperationLogService {

    @Autowired
    private DataOperationLogRepository dataOperationLogRepository;
}