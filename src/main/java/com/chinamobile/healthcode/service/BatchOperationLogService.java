package com.chinamobile.healthcode.service;

import com.chinamobile.healthcode.annotation.DataOperationLog;
import com.chinamobile.healthcode.event.DataOperationLogEventPublisher;
import com.chinamobile.healthcode.model.DataOperationLogEntity;
import com.chinamobile.healthcode.util.BatchOperationLogUtil;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量操作日志服务示例
 * 展示如何使用事件驱动方式记录批量操作日志
 */
@Service
public class BatchOperationLogService {

    @Autowired
    private DataOperationLogEventPublisher logEventPublisher;

    /**
     * 批量导入任务示例
     */
    @Transactional
    public Result<BatchImportResult> batchImportTasks(List<Task> tasks, User user) {
        Result<BatchImportResult> result = new Result<>();
        BatchImportResult batchResult = new BatchImportResult();
        
        List<String> successIds = new ArrayList<>();
        List<String> failedIds = new ArrayList<>();
        List<Task> failedTasks = new ArrayList<>();
        
        // 记录批量操作开始日志
        DataOperationLogEntity batchLog = BatchOperationLogUtil.createBatchLog(
            DataOperationLog.OperationType.CREATE,
            "批量导入任务",
            "Task",
            user,
            tasks.size(),
            0,
            0,
            new ArrayList<>()
        );
        
        for (Task task : tasks) {
            try {
                // 这里调用实际的保存逻辑
                // Result<String> saveResult = taskRepository.save(task, user);
                Result<String> saveResult = new Result<>();
                saveResult.data = task.getId(); // 模拟成功
                
                if (saveResult.isOK()) {
                    successIds.add(saveResult.data);
                    
                    // 记录单个操作日志
                    DataOperationLogEntity singleLog = BatchOperationLogUtil.createSingleLog(
                        DataOperationLog.OperationType.CREATE,
                        "导入单个任务",
                        "Task",
                        task.getId(),
                        null,
                        task,
                        user,
                        true,
                        null
                    );
                    logEventPublisher.publishLogEvent(this, singleLog);
                } else {
                    failedIds.add(task.getId());
                    failedTasks.add(task);
                }
            } catch (Exception e) {
                failedIds.add(task.getId());
                failedTasks.add(task);
                
                // 记录错误日志
                DataOperationLogEntity errorLog = BatchOperationLogUtil.createErrorLog(
                    DataOperationLog.OperationType.CREATE,
                    "导入任务失败",
                    "Task",
                    task.getId(),
                    task,
                    user,
                    e
                );
                logEventPublisher.publishErrorLogEvent(this, errorLog);
            }
        }
        
        // 更新批量操作日志结果
        batchLog.setSuccessCount(successIds.size());
        batchLog.setFailedCount(failedIds.size());
        batchLog.setSuccess(failedIds.isEmpty());
        
        // 发布批量操作日志事件
        logEventPublisher.publishBatchLogEvent(this, batchLog);
        
        // 设置返回结果
        batchResult.setSuccessCount(successIds.size());
        batchResult.setFailedCount(failedIds.size());
        batchResult.setSuccessIds(successIds);
        batchResult.setFailedIds(failedIds);
        batchResult.setFailedTasks(failedTasks);
        
        result.data = batchResult;
        return result;
    }

    /**
     * 批量删除任务示例
     */
    @Transactional
    public Result<BatchDeleteResult> batchDeleteTasks(List<String> taskIds, User user) {
        Result<BatchDeleteResult> result = new Result<>();
        BatchDeleteResult batchResult = new BatchDeleteResult();
        
        List<String> successIds = new ArrayList<>();
        List<String> failedIds = new ArrayList<>();
        List<Task> deletedTasks = new ArrayList<>();
        
        // 记录批量操作开始日志
        DataOperationLogEntity batchLog = BatchOperationLogUtil.createBatchLog(
            DataOperationLog.OperationType.DELETE,
            "批量删除任务",
            "Task",
            user,
            taskIds.size(),
            0,
            0,
            taskIds
        );
        
        for (String taskId : taskIds) {
            try {
                // 这里调用实际的删除逻辑
                // Result deleteResult = taskRepository.remove(taskId, user);
                Result deleteResult = new Result(); // 模拟成功
                
                if (deleteResult.isOK()) {
                    successIds.add(taskId);
                    
                    // 记录单个删除日志
                    DataOperationLogEntity singleLog = BatchOperationLogUtil.createSingleLog(
                        DataOperationLog.OperationType.DELETE,
                        "批量删除单个任务",
                        "Task",
                        taskId,
                        null, // 删除前数据，实际使用时需要获取
                        null,
                        user,
                        true,
                        null
                    );
                    logEventPublisher.publishLogEvent(this, singleLog);
                } else {
                    failedIds.add(taskId);
                }
            } catch (Exception e) {
                failedIds.add(taskId);
                
                // 记录错误日志
                DataOperationLogEntity errorLog = BatchOperationLogUtil.createErrorLog(
                    DataOperationLog.OperationType.DELETE,
                    "批量删除失败",
                    "Task",
                    taskId,
                    null,
                    user,
                    e
                );
                logEventPublisher.publishErrorLogEvent(this, errorLog);
            }
        }
        
        // 更新批量操作日志结果
        batchLog.setSuccessCount(successIds.size());
        batchLog.setFailedCount(failedIds.size());
        batchLog.setSuccess(failedIds.isEmpty());
        
        // 发布批量操作日志事件
        logEventPublisher.publishBatchLogEvent(this, batchLog);
        
        // 设置返回结果
        batchResult.setSuccessCount(successIds.size());
        batchResult.setFailedCount(failedIds.size());
        batchResult.setSuccessIds(successIds);
        batchResult.setFailedIds(failedIds);
        batchResult.setDeletedTasks(deletedTasks);
        
        result.data = batchResult;
        return result;
    }

    /**
     * 批量导入结果类
     */
    public static class BatchImportResult {
        private int successCount;
        private int failedCount;
        private List<String> successIds;
        private List<String> failedIds;
        private List<Task> failedTasks;
        
        // getter和setter方法
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public List<String> getSuccessIds() { return successIds; }
        public void setSuccessIds(List<String> successIds) { this.successIds = successIds; }
        
        public List<String> getFailedIds() { return failedIds; }
        public void setFailedIds(List<String> failedIds) { this.failedIds = failedIds; }
        
        public List<Task> getFailedTasks() { return failedTasks; }
        public void setFailedTasks(List<Task> failedTasks) { this.failedTasks = failedTasks; }
    }

    /**
     * 批量删除结果类
     */
    public static class BatchDeleteResult {
        private int successCount;
        private int failedCount;
        private List<String> successIds;
        private List<String> failedIds;
        private List<Task> deletedTasks;
        
        // getter和setter方法
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public List<String> getSuccessIds() { return successIds; }
        public void setSuccessIds(List<String> successIds) { this.successIds = successIds; }
        
        public List<String> getFailedIds() { return failedIds; }
        public void setFailedIds(List<String> failedIds) { this.failedIds = failedIds; }
        
        public List<Task> getDeletedTasks() { return deletedTasks; }
        public void setDeletedTasks(List<Task> deletedTasks) { this.deletedTasks = deletedTasks; }
    }

    // 模拟Task类
    public static class Task {
        private String id;
        private String name;
        
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }
}
