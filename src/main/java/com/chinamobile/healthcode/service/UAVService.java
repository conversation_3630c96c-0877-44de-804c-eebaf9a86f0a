package com.chinamobile.healthcode.service;

import com.chinamobile.healthcode.model.iot.Client;
import com.chinamobile.healthcode.repository.iot.ClientRepository;
import com.chinamobile.sparrow.domain.service.uav.DroneFacade;
import com.chinamobile.sparrow.domain.service.uav.TokenFacade;
import com.chinamobile.sparrow.domain.service.uav.infra.TokenStore;
import okhttp3.ConnectionPool;
import org.jinq.tuples.Tuple3;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

@Component
public class UAVService {

    final String baseUrl;
    final ClientRepository clientRepository;
    final ConnectionPool connectionPool;

    final CopyOnWriteArrayList<Tuple3<String, String, TokenStore>> tokenStores;
    final CopyOnWriteArrayList<Tuple3<String, String, TokenFacade>> tokenFacades;
    final CopyOnWriteArrayList<Tuple3<String, String, DroneFacade>> droneFacades;

    public UAVService(
            @Value(value = "${uav.base-url}") String baseUrl,
            ClientRepository clientRepository,
            ConnectionPool connectionPool
    ) {
        this.baseUrl = baseUrl;
        this.clientRepository = clientRepository;
        this.connectionPool = connectionPool;
        this.tokenStores = new CopyOnWriteArrayList<>();
        this.tokenFacades = new CopyOnWriteArrayList<>();
        this.droneFacades = new CopyOnWriteArrayList<>();

        cache();
    }

    public void cache() {
        List<Client> _clients = clientRepository.find(Collections.singletonList(Client.ENUM_PLATFORM.UAV));

        droneFacades.clear();
        tokenStores.clear();
        tokenFacades.clear();

        tokenFacades.addAll(_clients.stream()
                .map(i -> new Tuple3<>(
                        i.getId(),
                        i.getAppId(),
                        new TokenFacade(baseUrl, i.getAppId(), i.getAppSecret(), connectionPool)
                )).collect(Collectors.toList()));

        tokenStores.addAll(tokenFacades.stream()
                .map(i -> new Tuple3<>(
                        i.getOne(),
                        i.getTwo(),
                        new TokenStore(i.getThree())
                )).collect(Collectors.toList()));

        droneFacades.addAll(tokenStores.stream()
                .map(i -> new Tuple3<>(
                        i.getOne(),
                        i.getTwo(),
                        new DroneFacade(baseUrl, i.getThree(), connectionPool)
                )).collect(Collectors.toList()));
    }

    public TokenStore tokenStore(String clientId) {
        return tokenStores.stream()
                .filter(i -> Objects.equals(i.getOne(), clientId))
                .map(Tuple3::getThree)
                .findFirst().orElse(null);
    }

    public DroneFacade droneFacade(String clientId) {
        return droneFacades.stream()
                .filter(i -> Objects.equals(i.getOne(), clientId))
                .map(Tuple3::getThree)
                .findFirst().orElse(null);
    }

    public List<Tuple3<String, String, DroneFacade>> droneFacades() {
        return droneFacades;
    }

}