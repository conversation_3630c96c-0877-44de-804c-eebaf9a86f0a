package com.chinamobile.healthcode.service.event;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class PublishService {

    final RedisTemplate<String, Object> redisTemplate;

    public PublishService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void publish(String channel, Object message) {
        redisTemplate.convertAndSend(channel, message);
    }

}