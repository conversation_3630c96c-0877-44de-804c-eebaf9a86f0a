package com.chinamobile.healthcode.service.event;

import com.chinamobile.healthcode.service.WebSocketClientService;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
public class SubscribeService extends MessageListenerAdapter {

    final String alarmRingTopic;
    final WebSocketClientService webSocketClientService;

    public SubscribeService(
            @Value(value = "${event.topic.alarm-ring}") String alarmRingTopic,
            WebSocketClientService webSocketClientService
    ) {
        this.alarmRingTopic = alarmRingTopic;
        this.webSocketClientService = webSocketClientService;
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String _json = new String(message.getBody(), StandardCharsets.UTF_8);
        JsonArray _array = ConverterUtil.json2Object(_json, JsonArray.class);

        String _topic = new String(message.getChannel(), StandardCharsets.UTF_8);
        if (alarmRingTopic.equals(_topic)) {
            Pair<String, String> _payload = ConverterUtil.json2Object(_array.get(1).toString(), new TypeToken<Pair<String, String>>() {
            }.getType());
            webSocketClientService.sendAlarmMessage(_payload.getLeft(), _payload.getRight());
        }
    }

}