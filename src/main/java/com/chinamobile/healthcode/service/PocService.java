package com.chinamobile.healthcode.service;

import com.chinamobile.healthcode.model.iot.Client;
import com.chinamobile.healthcode.repository.iot.ClientRepository;
import com.chinamobile.sparrow.domain.service.poc.pro.DeviceFacade;
import com.chinamobile.sparrow.domain.service.poc.pro.LoginFacade;
import com.chinamobile.sparrow.domain.service.poc.pro.UserFacade;
import com.chinamobile.sparrow.domain.service.poc.pro.infra.TokenStore;
import okhttp3.ConnectionPool;
import org.jinq.tuples.Tuple3;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

@Component
public class PocService {

    final String baseUrl;
    final ClientRepository clientRepository;
    final ConnectionPool connectionPool;

    final CopyOnWriteArrayList<Tuple3<String, String, TokenStore>> tokenStores;
    final CopyOnWriteArrayList<Tuple3<String, String, LoginFacade>> loginFacades;
    final CopyOnWriteArrayList<Tuple3<String, String, DeviceFacade>> deviceFacades;
    final CopyOnWriteArrayList<Tuple3<String, String, UserFacade>> userFacades;

    public PocService(
            @Value(value = "${poc-pro.base-url}") String baseUrl,
            ClientRepository clientRepository,
            ConnectionPool connectionPool
    ) {
        this.baseUrl = baseUrl;
        this.clientRepository = clientRepository;
        this.connectionPool = connectionPool;

        this.tokenStores = new CopyOnWriteArrayList<>();
        this.loginFacades = new CopyOnWriteArrayList<>();
        this.deviceFacades = new CopyOnWriteArrayList<>();
        this.userFacades = new CopyOnWriteArrayList<>();

        cache();
    }

    public void cache() {
        List<Client> _clients = clientRepository.find(Collections.singletonList(Client.ENUM_PLATFORM.POC));

        deviceFacades.clear();
        userFacades.clear();
        tokenStores.clear();
        loginFacades.clear();

        loginFacades.addAll(_clients.stream()
                .map(i -> new Tuple3<>(
                        i.getId(),
                        i.getAppId(),
                        new LoginFacade(baseUrl, i.getAppId(), i.getAppSecret(), connectionPool)
                )).collect(Collectors.toList()));

        tokenStores.addAll(loginFacades.stream()
                .map(i -> new Tuple3<>(
                        i.getOne(),
                        i.getTwo(),
                        new TokenStore(i.getThree())
                )).collect(Collectors.toList()));

        deviceFacades.addAll(tokenStores.stream()
                .map(i -> new Tuple3<>(
                        i.getOne(),
                        i.getTwo(),
                        new DeviceFacade(baseUrl, i.getTwo(), i.getThree(), connectionPool)
                )).collect(Collectors.toList()));

        userFacades.addAll(tokenStores.stream()
                .map(i -> new Tuple3<>(
                        i.getOne(),
                        i.getTwo(),
                        new UserFacade(baseUrl, i.getTwo(), i.getThree(), connectionPool)
                )).collect(Collectors.toList()));
    }

    public DeviceFacade deviceFacade(String clientId) {
        return deviceFacades.stream()
                .filter(i -> Objects.equals(i.getOne(), clientId))
                .map(Tuple3::getThree)
                .findFirst().orElse(null);
    }

    public List<Tuple3<String, String, DeviceFacade>> deviceFacades() {
        return deviceFacades;
    }

    public UserFacade userFacade(String clientId) {
        return userFacades.stream()
                .filter(i -> Objects.equals(i.getOne(), clientId))
                .map(Tuple3::getThree)
                .findFirst().orElse(null);
    }

    public List<Tuple3<String, String, UserFacade>> userFacades() {
        return userFacades;
    }

}