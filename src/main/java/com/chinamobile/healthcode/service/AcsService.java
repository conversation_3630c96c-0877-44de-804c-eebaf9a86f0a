package com.chinamobile.healthcode.service;

import com.chinamobile.healthcode.model.iot.Client;
import com.chinamobile.healthcode.repository.iot.ClientRepository;
import com.chinamobile.sparrow.domain.service.acs.AlarmFacade;
import com.chinamobile.sparrow.domain.service.acs.DeviceFacade;
import com.chinamobile.sparrow.domain.service.acs.MediaFacade;
import com.chinamobile.sparrow.domain.service.acs.infra.HttpDigestAuthenticator;
import okhttp3.ConnectionPool;
import org.jinq.tuples.Tuple3;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

@Component
public class AcsService {

    final String baseUrl;
    final ClientRepository clientRepository;
    final ConnectionPool connectionPool;

    final CopyOnWriteArrayList<Tuple3<String, String, HttpDigestAuthenticator>> authenticators;
    final CopyOnWriteArrayList<Tuple3<String, String, AlarmFacade>> alarmFacades;
    final CopyOnWriteArrayList<Tuple3<String, String, DeviceFacade>> deviceFacades;
    final CopyOnWriteArrayList<Tuple3<String, String, MediaFacade>> mediaFacades;

    public final Map<String, String> EVENTS;

    public AcsService(
            @Value(value = "${acs.base-url}") String baseUrl,
            ClientRepository clientRepository,
            ConnectionPool connectionPool
    ) {
        this.baseUrl = baseUrl;
        this.clientRepository = clientRepository;
        this.connectionPool = connectionPool;

        this.authenticators = new CopyOnWriteArrayList<>();
        this.alarmFacades = new CopyOnWriteArrayList<>();
        this.deviceFacades = new CopyOnWriteArrayList<>();
        this.mediaFacades = new CopyOnWriteArrayList<>();

        EVENTS = new HashMap<>();
        EVENTS.put("ALARM_FIRELANE_ZT", "消防占道识别ZT");
        EVENTS.put("ALARM_ADVERTISING_ZT", "违规户外广告识别ZT");
        EVENTS.put("ALARM_TRUCK_TEST", "货车识别TEST");
        EVENTS.put("ALARM_AI_REFUEL_X", "油枪插入油桶算法识别X");
        EVENTS.put("ALARM_OILNUMBER_X", "油桶编码识别X");
        EVENTS.put(" ALARM_AI_TOUYOU_G", "油枪插进油桶识别G");
        EVENTS.put("ALARM_TRUCK_G", "冷库异常车辆停靠识别G");
        EVENTS.put("ABNORMAL_NUMBER_OF_PEOPLE_DETECTION_41811000027", "人数异常检测-ZT");
        EVENTS.put("ABNORMAL_NUMBER_OF_PEOPLE_DETECTION_41812000022", "人数异常检测-HM");
        EVENTS.put("AIR_CONDITIONING_RECOGNITION_41811000003", "空调识别-ZT");
        EVENTS.put("AIR_CONDITIONING_RECOGNITION_41812000045", "空调识别-HM");
        EVENTS.put("AI_MOVE_41811000060", "人员搬货识别ZT");
        EVENTS.put("ALARM_AI_ABNORMAL_HEAD_COUNT_DETECTION", "人数异常检测");
        EVENTS.put("ALARM_AI_ABNORMAL_HEAD_COUNT_DETECTION_G", "人数异常检测G");
        EVENTS.put("ALARM_AI_ABNORMAL_HEAD_COUNT_DETECTION_X", "人数异常检测X");
        EVENTS.put("ALARM_AI_AIR_CONDITIONING_IDENTIFICATION", "空调识别");
        EVENTS.put("ALARM_AI_AIR_CONDITIONING_IDENTIFICATION_G", "空调识别G");
        EVENTS.put("ALARM_AI_AIR_CONDITIONING_IDENTIFICATION_X", "空调识别X");
        EVENTS.put("ALARM_AI_BUSINESS_ON_THE_ROAD_X", "占道经营X");
        EVENTS.put("ALARM_AI_CHEFHAT_NOT_WEAR", "未戴厨师帽告警");
        EVENTS.put("ALARM_AI_CHEFHAT_NOT_WEAR_G", "未戴厨师帽告警G");
        EVENTS.put("ALARM_AI_CHEFHAT_NOT_WEAR_X", "未戴厨师帽告警X");
        EVENTS.put("ALARM_AI_CLIMB_DETECTION", "爬高检测告警");
        EVENTS.put("ALARM_AI_CLOTHES_DISARRAY", "着装不规范告警");
        EVENTS.put("ALARM_AI_COLDCHAIN_CONTAINERNUMBER_IDENTIFICATION", "冷链货柜号识别");
        EVENTS.put("ALARM_AI_COLDCHAIN_CONTAINERNUMBER_IDENTIFICATION_G", "冷链货柜号识别G");
        EVENTS.put("ALARM_AI_COLDCHAIN_CONTAINERNUMBER_IDENTIFICATION_X", "冷链货柜号识别X");
        EVENTS.put("ALARM_AI_CONSTRUCTION_VEHICLE_DETECTION", "工程车辆检测告警");
        EVENTS.put("ALARM_AI_COURSE_DEVIATION_DETECTION", "航道偏离检测");
        EVENTS.put("ALARM_AI_COURSE_DEVIATION_DETECTION_G", "航道偏离检测G");
        EVENTS.put("ALARM_AI_COURSE_DEVIATION_DETECTION_X", "航道偏离检测X");
        EVENTS.put("ALARM_AI_CROWD_DENSITY_GATHERING", "人群密度聚集检测");
        EVENTS.put("ALARM_AI_CROWD_DENSITY_GATHERING_G", "人群密度聚集检测G");
        EVENTS.put("ALARM_AI_CROWD_DENSITY_GATHERING_X", "人群密度聚集检测X");
        EVENTS.put("ALARM_AI_CROWD_MASS", "人群聚集告警");
        EVENTS.put("ALARM_AI_DETENTION_DETECTION", "人员滞留检测");
        EVENTS.put("ALARM_AI_DETENTION_DETECTION_G", "人员滞留检测G");
        EVENTS.put("ALARM_AI_DETENTION_DETECTION_X", "人员滞留检测X");
        EVENTS.put("ALARM_AI_DISORDERLY_PARKING_MOTOR_VEHICLES", "机动车乱停乱放识别");
        EVENTS.put("ALARM_AI_DISORDERLY_PARKING_MOTOR_VEHICLES_G", "机动车乱停乱放识别G");
        EVENTS.put("ALARM_AI_DISORDERLY_PARKING_MOTOR_VEHICLES_X", "机动车乱停乱放识别X");
        EVENTS.put("ALARM_AI_DIVIDED_ATTENTION", "分心检测");
        EVENTS.put("ALARM_AI_DIVIDED_ATTENTION_G", "分心检测G");
        EVENTS.put("ALARM_AI_DIVIDED_ATTENTION_X", "分心检测X");
        EVENTS.put("ALARM_AI_ENGINEERING_VEHICLE_IDENTIFICATION_41812000060", "工程车识别HM");
        EVENTS.put("ALARM_AI_ENGINEERING_VEHICLE_IDENTIFICATION_G", "工程车识别G");
        EVENTS.put("ALARM_AI_ENGINEERING_VEHICLE_IDENTIFICATION_X", "工程车识别X");
        EVENTS.put("ALARM_AI_FACE_DETECTION", "人脸检测");
        EVENTS.put("ALARM_AI_FATIGUE_DETECTION", "疲劳检测");
        EVENTS.put("ALARM_AI_FATIGUE_DETECTION_G", "疲劳检测G");
        EVENTS.put("ALARM_AI_FATIGUE_DETECTION_X", "疲劳检测X");
        EVENTS.put("ALARM_AI_FIGHTING_DETECTION", "打架识别");
        EVENTS.put("ALARM_AI_FIGHTING_DETECTION_G", "打架识别G");
        EVENTS.put("ALARM_AI_FIGHTING_DETECTION_X", "打架识别X");
        EVENTS.put("ALARM_AI_FIRE_DETECTION", "烟火检测告警");
        EVENTS.put("ALARM_AI_FIRE_DETECTION_G", "火警检测G");
        EVENTS.put("ALARM_AI_FIRE_DETECTION_X", "火警检测告警X");
        EVENTS.put("ALARM_AI_FLOTSAM_DETECTION", "漂浮物检测告警");
        EVENTS.put("ALARM_AI_FORBIDDEN_ELEVATOR", "禁止进入电梯告警");
        EVENTS.put("ALARM_AI_FORBIDDEN_ELEVATOR_G", "禁止进入电梯告警G");
        EVENTS.put("ALARM_AI_FORBIDDEN_ELEVATOR_X", "禁止进入电梯告警X");
        EVENTS.put("ALARM_AI_FOREIGN_BODY_DETECTION", "后厨异物检测告警");
        EVENTS.put("ALARM_AI_HELMET_NOT_WEAR", "未佩戴安全帽告警");
        EVENTS.put("ALARM_AI_HELMET_NOT_WEAR_G", "安全帽佩戴检测G");
        EVENTS.put("ALARM_AI_HELMET_NOT_WEAR_X", "安全帽佩戴检测X");
        EVENTS.put("ALARM_AI_HIGHRISK_AREA_DETECTION", "高危区域闯入检测告警");
        EVENTS.put("ALARM_AI_ILLEGAL_BUILDING", "违章建筑告警");
        EVENTS.put("ALARM_AI_ILLEGAL_PERSONS_DETECTION", "异常人员检测告警");
        EVENTS.put("ILLEGAL_SMOKING", "违规吸烟");
        EVENTS.put("ALARM_AI_ILLEGAL_SMOKING_G", "违规吸烟G");
        EVENTS.put("ALARM_AI_ILLEGAL_SMOKING_X", "违规吸烟X");
        EVENTS.put("ALARM_AI_INTRUSION", "入侵检测告警");
        EVENTS.put("ALARM_AI_INTRUSION_DETECTION", "周界入侵检测告警");
        EVENTS.put("ALARM_AI_INTRUSION_DETECTION_G", "周界入侵检测G");
        EVENTS.put("ALARM_AI_INTRUSION_DETECTION_X", "周界入侵检测X");
        EVENTS.put("ALARM_AI_KNIFE_X", "持刀持械X");
        EVENTS.put("ALARM_AI_LEAKAGE_DETECTION", "漏水检测");
        EVENTS.put("ALARM_AI_LEAKAGE_DETECTION_G", "漏水检测G");
        EVENTS.put("ALARM_AI_LEAKAGE_DETECTION_X", "漏水检测X");
        EVENTS.put("ALARM_AI_LEAVE_ATWORK_DETECTION_G", "离岗检测G");
        EVENTS.put("ALARM_AI_LEAVE_ATWORK_DETECTION_X", "离岗检测告警X");
        EVENTS.put("ALARM_AI_LICENSE_PLATE_RECOGNITION", "车牌识别");
        EVENTS.put("ALARM_AI_LICENSE_PLATE_RECOGNITION_G", "车牌识别G");
        EVENTS.put("ALARM_AI_LICENSE_PLATE_RECOGNITION_X", "车牌识别X");
        EVENTS.put("ALARM_AI_MASK_NOT_WEAR", "未戴口罩告警");
        EVENTS.put("ALARM_AI_MASK_NOT_WEAR_G", "未戴口罩告警G");
        EVENTS.put("ALARM_AI_MASK_NOT_WEAR_X", "未戴口罩告警X");
        EVENTS.put("ALARM_AI_MOVE_X", "人员搬货识别X");
        EVENTS.put("ALARM_AI_NIGHTTIME_PERSON_STAY_IDENTIFICATION", "夜间人员逗留识别");
        EVENTS.put("ALARM_AI_NIGHTTIME_PERSON_STAY_IDENTIFICATION_G", "夜间人员逗留识别G");
        EVENTS.put("ALARM_AI_NIGHTTIME_PERSON_STAY_IDENTIFICATION_X", "夜间人员逗留识别X");
        EVENTS.put("NOT_COVERED", "垃圾桶盖未盖");
        EVENTS.put("ALARM_AI_NOT_COVERED_G", "垃圾桶盖未盖G");
        EVENTS.put("ALARM_AI_NOT_COVERED_X", "垃圾桶盖未盖X");
        EVENTS.put("NOT_ WEARING_GLOVES", "未戴手套");
        EVENTS.put("ALARM_AI_NOT_WEARING_GLOVES_G", "未戴手套G");
        EVENTS.put("ALARM_AI_NOT_WEARING_GLOVES_X", " 未戴手套X");
        EVENTS.put("Not wearing tooling", "未穿工装");
        EVENTS.put("ALARM_AI_NOT_WEARING_TOOLING_G", "未穿工装G");
        EVENTS.put("ALARM_AI_NOT_WEARING_TOOLING_X", "未穿工装X");
        EVENTS.put("ALARM_AI_NO_WORK_LICENSE_DETECTION", "非持证上岗人员检测告警");
        EVENTS.put("ALARM_AI_PARKING_VIOLATION_ALARM", "违停告警");
        EVENTS.put("ALARM_AI_PARKING_VIOLATION_ALARM_G", "违停告警G");
        EVENTS.put("ALARM_AI_PARKING_VIOLATION_ALARM_X", "违停告警X");
        EVENTS.put("ALARM_AI_PEOPLE_STREAM", "人流密度检测告警");
        EVENTS.put("ALARM_AI_PERSONNEL_PROXIMITY_DETECTION", "人员靠近检测");
        EVENTS.put("ALARM_AI_PERSONNEL_PROXIMITY_DETECTION_G", "人员靠近检测G");
        EVENTS.put("ALARM_AI_PERSONNEL_PROXIMITY_DETECTION_X", "人员靠近检测X");
        EVENTS.put("ALARM_AI_PERSONS_FALL_DETECTION", "人员跌倒检测告警");
        EVENTS.put("ALARM_AI_PERSONS_FALL_DETECTION_G", "人员摔倒检测G");
        EVENTS.put("ALARM_AI_PERSONS_FALL_DETECTION_X", "人员摔倒检测X");
        EVENTS.put("PHONE_PLAYING", "违规玩手机");
        EVENTS.put("ALARM_AI_PHONE_PLAYING_G", "违规玩手机G");
        EVENTS.put("ALARM_AI_PHONE_PLAYING_X", "违规玩手机X");
        EVENTS.put("ALARM_AI_PIC_QUALITY", "图像质量告警");
        EVENTS.put("ALARM_AI_PROTECTIVE_CLOTHING_G", "未穿防护服G");
        EVENTS.put("ALARM_AI_RAISE_DUST", "扬尘检测告警");
        EVENTS.put("RAT_INFESTATION", "老鼠出没");
        EVENTS.put("ALARM_AI_RAT_INFESTATION_G", "老鼠出没G");
        EVENTS.put("ALARM_AI_RAT_INFESTATION_X", "老鼠出没X");
        EVENTS.put("ALARM_AI_REFLECTIVE_CLOTHING_DETECTION", "反光衣检测");
        EVENTS.put("ALARM_AI_REFLECTIVE_CLOTHING_DETECTION_G", "反光衣检测G");
        EVENTS.put("ALARM_AI_REFLECTIVE_CLOTHING_DETECTION_X", "反光衣检测X");
        EVENTS.put("ALARM_AI_REGIONAL_POPULATION_STATISTICS", "区域人数统计");
        EVENTS.put("ALARM_AI_REGIONAL_POPULATION_STATISTICS_G", "区域人数统计G");
        EVENTS.put("ALARM_AI_REGIONAL_POPULATION_STATISTICS_X", "区域人数统计X");
        EVENTS.put("ALARM_AI_RISK_AREA_LOITERING_DETECTION", "危险区域徘徊检测告警");
        EVENTS.put("ALARM_AI_RISK_AREA_LOITERING_DETECTION_G", "高危区域人员徘徊检测G");
        EVENTS.put("ALARM_AI_RISK_AREA_LOITERING_DETECTION_X", "高危区域人员徘徊检测X");
        EVENTS.put("ALARM_AI_ROAD_ANOMALY_IDENTIFICATION", "道路车辆异常情况识别");
        EVENTS.put("ALARM_AI_ROAD_ANOMALY_IDENTIFICATION_G", "道路车辆异常情况识别G");
        EVENTS.put("ALARM_AI_ROAD_ANOMALY_IDENTIFICATION_X", "道路车辆异常情况识别X");
        EVENTS.put("ALARM_AI_ROAD_USING", "占道告警");
        EVENTS.put("ALARM_AI_RUNNING_RECOGNITION", "奔跑识别");
        EVENTS.put("ALARM_AI_RUNNING_RECOGNITION_G", "奔跑识别G");
        EVENTS.put("ALARM_AI_RUNNING_RECOGNITION_X", "奔跑识别X");
        EVENTS.put("ALARM_AI_SAFETY_BELT", "安全带检测");
        EVENTS.put("ALARM_AI_SAFETY_BELT_G", "安全带检测G");
        EVENTS.put("ALARM_AI_SAFETY_BELT_X", "安全带检测X");
        EVENTS.put("ALARM_AI_SEWAGE", "污水检测告警");
        EVENTS.put("ALARM_AI_SHIP_CAPTURED", "过船抓拍");
        EVENTS.put("ALARM_AI_SHIP_CAPTURED_G", "过船抓拍G");
        EVENTS.put("ALARM_AI_SHIP_CAPTURED_X", "过船抓拍X");
        EVENTS.put("ALARM_AI_SHIP_TYPE_RECOGNITION", "船型识别");
        EVENTS.put("ALARM_AI_SHIP_TYPE_RECOGNITION_G", "船型识别G");
        EVENTS.put("ALARM_AI_SHIP_TYPE_RECOGNITION_X", "船型识别X");
        EVENTS.put("ALARM_AI_SLEEP_ATWORK_DETECTION_G", "睡岗检测G");
        EVENTS.put("ALARM_AI_SLEEP_ATWORK_DETECTION_X", "睡岗检测告警X");
        EVENTS.put("ALARM_AI_SLEEP_LEAVE_ATWORK_DETECTION", "睡岗离岗检测告警");
        EVENTS.put("ALARM_AI_SMOKE_DETECTION", "工业烟雾检测告警");
        EVENTS.put("ALARM_AI_SMOKE_DETECTION_G", "烟雾检测G");
        EVENTS.put("ALARM_AI_SMOKE_DETECTION_X", "烟雾检测告警X");
        EVENTS.put("ALARM_AI_SOLID_WASTE", "固体垃圾检测告警");
        EVENTS.put("ALARM_AI_SPARK_WORKING_IDENTIFICATION", "火星作业识别");
        EVENTS.put("ALARM_AI_SPARK_WORKING_IDENTIFICATION_G", "火星作业识别G");
        EVENTS.put("ALARM_AI_SPARK_WORKING_IDENTIFICATION_X", "火星作业识别X");
        EVENTS.put("ALARM_AI_STATIC_OBJECT_DETECTION", "滞留物检测告警");
        EVENTS.put("ALARM_AI_STATION_INSPECTION", "站场巡检识别");
        EVENTS.put("ALARM_AI_STATION_INSPECTION_G", "站场巡检识别G");
        EVENTS.put("ALARM_AI_STATION_INSPECTION_X", "站场巡检识别X");
        EVENTS.put("ALARM_AI_STRANGER_RECOGNITION", "陌生人识别");
        EVENTS.put("ALARM_AI_STRANGER_RECOGNITION_G", "陌生人识别G");
        EVENTS.put("ALARM_AI_STRANGER_RECOGNITION_X", "陌生人识别X");
        EVENTS.put("ALARM_AI_STRAW_BURNING", "秸秆焚烧告警");
        EVENTS.put("ALARM_AI_STRENUOUS_EXERCISE", "剧烈运动");
        EVENTS.put("ALARM_AI_STRENUOUS_EXERCISE_G", "剧烈运动G");
        EVENTS.put("ALARM_AI_STRENUOUS_EXERCISE_X", "剧烈运动X");
        EVENTS.put("ALARM_AI_TOUYOU_41812000061", " 油枪插进油桶识别HM");
        EVENTS.put("ALARM_AI_TRACK_SAFETY_INSPECTION", "铁路轨道安全巡检识别");
        EVENTS.put("ALARM_AI_TRACK_SAFETY_INSPECTION_G", "铁路轨道安全巡检识别G");
        EVENTS.put("ALARM_AI_TRACK_SAFETY_INSPECTION_X", "铁路轨道安全巡检识别X");
        EVENTS.put("ALARM_AI_UNIVERSAL_TARGET_DETECTION", "通用目标检测（垃圾、杂物）");
        EVENTS.put("ALARM_AI_UNIVERSAL_TARGET_DETECTION_G", "通用目标检测（垃圾、杂物）G");
        EVENTS.put("ALARM_AI_UNIVERSAL_TARGET_DETECTION_X", "通用目标检测（垃圾、杂物）X");
        EVENTS.put("ALARM_AI_VIP_UNRECOGNIZABLE", "VIP客户识别提醒");
        EVENTS.put("ALARM_AI_WATER_LEVEL_DETECTION", "水位检测告警");
        EVENTS.put("ALARM_ALL", "所有告警，不计入告警");
        EVENTS.put("ALARM_ARTIFACT_VIDEO", "人工视频报警");
        EVENTS.put("ALARM_ATTITUDE_ABNORMAL", "姿态异常告警类型");
        EVENTS.put("ALARM_AUDIO_DOWNP", "音频陡降");
        EVENTS.put("ALARM_AUDIO_EXCEPTION", "音频异常侦测");
        EVENTS.put("ALARM_AUDIO_UP", "音频陡升");
        EVENTS.put("ALARM_AUTO_TRACKING", "自动跟踪");
        EVENTS.put("ALARM_BRIGHT_DIAGNOSIS", "增益失衡");
        EVENTS.put("ALARM_CAR_DETECTION", "停车侦测");
        EVENTS.put("ALARM_CLOCK_SYNCHRONIZATION", "时钟同步报警");
        EVENTS.put("ALARM_COLOR_CAST_DIAGNOSIS", "偏色检测");
        EVENTS.put("ALARM_CPU_HIGH", "CPU过高告警");
        EVENTS.put("ALARM_CROWD_ANALYSIS", "人员聚集");
        EVENTS.put("ALARM_CROWD_DENSITY_DETECT", "人群密度");
        EVENTS.put("ALARM_DENSITY", "密度检测报警");
        EVENTS.put("ALARM_DEVICE_ERROR", "设备报警");
        EVENTS.put("ALARM_DEVICE_FAN_ERROR", "设备风扇故障报警");
        EVENTS.put("ALARM_DEVICE_HIGH_TEMPERATURE", "设备高温报警");
        EVENTS.put("ALARM_DEVICE_LOW_TEMPERATURE", "设备低温报警");
        EVENTS.put("ALARM_DEVICE_TAMPER_PROOF", "设备防拆报警");
        EVENTS.put("ALARM_DEV_TEMPE", "温度芯片错误告警");
        EVENTS.put("ALARM_DIRECTIONAL_MOTION", "定向移动检测报警");
        EVENTS.put("ALARM_DISC_PULLOUT", "硬盘移除");
        EVENTS.put("ALARM_DISK_FAULT_FULL_LOAD", "硬盘故障: 磁盘满负荷异常");
        EVENTS.put("ALARM_DISK_FAULT_IO", "硬盘故障: 读写硬盘出错");
        EVENTS.put("ALARM_DISK_FAULT_OFF", "硬盘故障恢复");
        EVENTS.put("ALARM_DISK_FAULT_ON", "硬盘故障");
        EVENTS.put("ALARM_DISK_FAULT_UN_FORMAT", "硬盘故障 : 硬盘未格式化");
        EVENTS.put("ALARM_DISK_FULL", "存储设备磁盘满报警");
        EVENTS.put("ALARM_DISK_FULL_OFF", "磁盘满告警恢复");
        EVENTS.put("ALARM_DISK_FULL_ON", "磁盘满告警");
        EVENTS.put("ALARM_DISTURB", "干扰检测");
        EVENTS.put("ALARM_DOT1X_CA_CERT_EXPIRE", "802.1x CA证书超期告警");
        EVENTS.put("ALARM_DOT1X_CA_CERT_WILL_EXPIRE", "802.1x CA证书即将超期告警");
        EVENTS.put("ALARM_DOT1X_CERT_EXPIRE", "802.1x证书超期告警");
        EVENTS.put("ALARM_DOT1X_CERT_WILL_EXPIRE", "802.1x证书即将超期告警");
        EVENTS.put("ALARM_DURATION_OF_STAY_ZT", "逗留时长分析ZT");
        EVENTS.put("ALARM_DUSTBIN_OVER_FLOW", "垃圾满溢-VPS");
        EVENTS.put("ALARM_ENGINEERING_X", "工程车识别算法X");
        EVENTS.put("ALARM_EXTERNAL_POWER_DOWN", "外部电源掉电告警");
        EVENTS.put("ALARM_E_BIKE_NOT_HAT_X", "电动车未戴头盔识别X");
        EVENTS.put("ALARM_FACE_MASK", "未带口罩告警");
        EVENTS.put("ALARM_FACE_RECOGNITION", "3559人脸识别，不计入告警");
        EVENTS.put("ALARM_FAN_HEC_BLOCKED", "半罩球编码板风扇堵转告警");
        EVENTS.put("ALARM_FAN_PWR_BLOCKED", "半罩球电源板风扇堵转告警");
        EVENTS.put("ALARM_FAST_MOVING", "快速移动报警");
        EVENTS.put("ALARM_FD_DL", "3559人脸检测，不计入告警");
        EVENTS.put("ALARM_FIRE_DETECT", "火点检测");
        EVENTS.put("ALARM_FOG_DETECT", "雾气检测告警");
        EVENTS.put("ALARM_FOG_VISIBILITY", "能见度告警");
        EVENTS.put("ALARM_GARBAGE_EXPOSURE", "垃圾暴露-VPS");
        EVENTS.put("ALARM_GENETEC_USR_LOCK", "GENETEC用户被锁定");
        EVENTS.put("ALARM_HBA_CLIMBING", "攀爬");
        EVENTS.put("ALARM_HBA_FALL_DOWN", "跌倒");
        EVENTS.put("ALARM_HBA_FIGHTING", "打架");
        EVENTS.put("ALARM_HBA_RUNNING", "跑步");
        EVENTS.put("ALARM_HIGH_TOSS_DETECT", "高空抛物检测-VPS");
        EVENTS.put("ALARM_HTTPS_CERT_EXPIRE", "https证书超期告警");
        EVENTS.put("ALARM_HTTPS_CERT_WILL_EXPIRE", "https证书即将超期告警");
        EVENTS.put("ALARM_HUMAN_COUNT", "过线统计");
        EVENTS.put("ALARM_IGNORE", "忽略告警");
        EVENTS.put("ALARM_ILLEGAL_PARKING", "违停球");
        EVENTS.put("ALARM_IMAGE_TOO_DARK", "设备暗图像告警");
        EVENTS.put("ALARM_INTRUSION", "入侵检测报警");
        EVENTS.put("ALARM_INTRUSION_IN", "入侵检测（进入）告警");
        EVENTS.put("ALARM_INTRUSION_OUT", "入侵检测（离开）告警");
        EVENTS.put("ALARM_INVADE", "拌线检测");
        EVENTS.put("ALARM_IPC_DISK_FULL_OFF", "摄像机SD卡满恢复");
        EVENTS.put("ALARM_IPC_DISK_FULL_ON", "摄像机SD卡满告警");
        EVENTS.put("ALARM_IPC_SD_CARD_FAULT_OFF", "摄像机SD卡故障恢复");
        EVENTS.put("ALARM_IPC_SD_CARD_FAULT_ON", "摄像机SD卡故障");
        EVENTS.put("ALARM_IP_ADDRESS_CONFLICT", "IP冲突");
        EVENTS.put("ALARM_ITS_485COIL_EXCEPTION", "its的485线圈异常的告警");
        EVENTS.put("ALARM_ITS_485STROBE_EXCEPTION", "485频闪灯告警");
        EVENTS.put("ALARM_ITS_IOCOIL_EXCEPTION", "its的IO线圈异常的告警");
        EVENTS.put("ALARM_ITS_RADAR_EXCEPTION", "its雷达异常的告警");
        EVENTS.put("ALARM_ITS_REDTOR_EXCEPTION", "its红绿灯检测器异常的告警");
        EVENTS.put("ALARM_LEAVE_DETECT", "离岗检测");
        EVENTS.put("ALARM_LEGACY", "遗留检测");
        EVENTS.put("ALARM_LENS_FAILURE", "镜头PT光耦失效告警");
        EVENTS.put("ALARM_LIGHT_SENSOR_INVALID", "光敏芯片失效告警");
        EVENTS.put("ALARM_LOG_WR", "日志读写告警");
        EVENTS.put("ALARM_MAU_VIDEO_LOST", "视频丢失告警(质量诊断)");
        EVENTS.put("ALARM_MAU_VQD_BLUR", "视频模糊告警");
        EVENTS.put("ALARM_MAU_VQD_BRIGHTNESS", "视频过亮告警");
        EVENTS.put("ALARM_MAU_VQD_CAMERACOVERD", "视频遮挡告警(质量诊断)");
        EVENTS.put("ALARM_MAU_VQD_COLORCAST", "视频色偏告警");
        EVENTS.put("ALARM_MAU_VQD_DARKNESS", "视频过暗告警");
        EVENTS.put("ALARM_MAU_VQD_FRAMEFROZEN", "视频冻结告警");
        EVENTS.put("ALARM_MAU_VQD_SHAKE", "视频抖动告警");
        EVENTS.put("ALARM_MAU_VQD_SNOWNOISE", "视频雪花噪声告警");
        EVENTS.put("ALARM_MAU_VQD_STRIPENOISE", "视频条纹干扰告警");
        EVENTS.put("ALARM_MAX(10000", "预留，不计入告警");
        EVENTS.put("ALARM_MEDIA_INTERRUPT", "媒体流中断");
        EVENTS.put("ALARM_MEM_HIGH", "内存过高告警");
        EVENTS.put("ALARM_MOTORBIKE", "电瓶车告警");
        EVENTS.put("ALARM_MOVING_OBJECT", "运动目标检测报警");
        EVENTS.put("ALARM_NETWORK_INTERRUPT", "网络中断");
        EVENTS.put("ALARM_NON_MOTORIZED_VEHICLE_INTRUSION_ZT", "非机动车闯入识别ZT");
        EVENTS.put("ALARM_NO_SIGNAL", "视频丢失");
        EVENTS.put("ALARM_NO_SIGNAL_EX", "无视频告警");
        EVENTS.put("ALARM_OBJECT_ABANDONED", "物体遗留检测报警");
        EVENTS.put("ALARM_OBJECT_REMOVAL", "物体移走检测报警");
        EVENTS.put("ALARM_OBJECT_WANDER", "徘徊检测报警");
        EVENTS.put("ALARM_OBJECT_WANDER_G", "徘徊检测G");
        EVENTS.put("ALARM_OBJECT_WANDER_X", "徘徊检测X");
        EVENTS.put("ALARM_OFF_SIGNAL", "设备开关量信号-关");
        EVENTS.put("ALARM_ONVIF_USR_LOCK", "ONVIF用户被锁定");
        EVENTS.put("ALARM_ON_SIGNAL", "设备开关量信号-开");
        EVENTS.put("ALARM_OUTOFFOCUS_DETECT", "虚焦检测");
        EVENTS.put("ALARM_PASSENGER_FIOW_STATISTICS_ZT", "客流统计ZT");
        EVENTS.put("ALARM_PATH", "路径检测报警");
        EVENTS.put("ALARM_POWER_SUPPLY_ERROR", "供电故障告警");
        EVENTS.put("ALARM_PRESENCE", "突然出现报警");
        EVENTS.put("ALARM_PTZ_CONFIG_WR", "PTZ云台协议配置告警");
        EVENTS.put("ALARM_PTZ_MOVE", "genetec PTZ 移动");
        EVENTS.put("ALARM_PU_OFFLINE", "前端下线");
        EVENTS.put("ALARM_PU_ONLINE", "前端上线");
        EVENTS.put("ALARM_QUEUE_DETECT", "排队长度");
        EVENTS.put("ALARM_REBOOT", "重启报警");
        EVENTS.put("ALARM_RECORDING_JOB_STATE", "录像任务状态变化");
        EVENTS.put("ALARM_RESET_TIME", "重置时间告警");
        EVENTS.put("ALARM_RETROGRADE", "逆行检测报警");
        EVENTS.put("ALARM_SAFETY_HELMET_DETECTION", "安全帽检测-VPS");
        EVENTS.put("ALARM_SCENE_CHANGE", "场景变更");
        EVENTS.put("ALARM_SDK_PWD_STILL_INITIAL_PWD", "SDK密码未修改");
        EVENTS.put("ALARM_SDK_USR_LOCK", "SDK用户被锁定");
        EVENTS.put("ALARM_SFP_BIAS_HIGH", "光模块偏置电流过高告警");
        EVENTS.put("ALARM_SFP_BIAS_LOW", "光模块偏置电流过低告警");
        EVENTS.put("ALARM_SFP_LASER_TEMP_HIGH", "光模块laser温度过高告警");
        EVENTS.put("ALARM_SFP_LASER_TEMP_LOW", "光模块laser温度过低告警");
        EVENTS.put("ALARM_SFP_NOT_EXIST", "光模块不在位告警");
        EVENTS.put("ALARM_SFP_RX_PWR_HIGH", "光模块接收功率过高告警");
        EVENTS.put("ALARM_SFP_RX_PWR_LOW", "光模块接收功率过低告警");
        EVENTS.put("ALARM_SFP_TEC_CURRENT_HIGH", "光模块tec电流过高告警");
        EVENTS.put("ALARM_SFP_TEC_CURRENT_LOW", "光模块tec电流过低告警");
        EVENTS.put("ALARM_SFP_TEMP_HIGH", "光模块温度过高告警");
        EVENTS.put("ALARM_SFP_TEMP_LOW", "光模块温度过低告警");
        EVENTS.put("ALARM_SFP_TX_PWR_HIGH", "光模块发送功率过高告警");
        EVENTS.put("ALARM_SFP_TX_PWR_LOW", "光模块发送功率过低告警");
        EVENTS.put("ALARM_SFP_VOL_HIGH", "光模块电压过高告警");
        EVENTS.put("ALARM_SFP_VOL_LOW", "光模块电压过低告警");
        EVENTS.put("ALARM_SHIELD", "遮挡告警");
        EVENTS.put("ALARM_YINGWEN_X", "测试002");
        EVENTS.put("ALARM_SLAVE_RTSP_KEEP_ALIVE_FAIL", "从机rtsp保活失败");
        EVENTS.put("ALARM_SLAVE_STREAM_NORMAL", "从机rtsp流正常; 多目(动点)设备上、下线事件");
        EVENTS.put("ALARM_SPEED", "(突然加速)速度检测报警");
        EVENTS.put("ALARM_SSH_OPEN+C98:C100", "SSH被打开");
        EVENTS.put("ALARM_STORAGE_FAIL", "驱动存储器告警");
        EVENTS.put("ALARM_SWIM_IDENTIFY_X", "游泳检测X");
        EVENTS.put("ALARM_SWIM_ZT", "游泳检测ZT");
        EVENTS.put("ALARM_TARGET_COUNT", "目标计数");
        EVENTS.put("ALARM_TEMPERATURE", "设备高温告警");
        EVENTS.put("ALARM_TEMPERATURE_DIFF", "温差告警");
        EVENTS.put("ALARM_TRAFFIC_CONGESTION", "ITS交通拥堵");
        EVENTS.put("ALARM_TRAFFIC_JUNCTION_DETECT", "新交通告警");
        EVENTS.put("ALARM_TRAFFIC_STATISTICS", "流量统计报警");
        EVENTS.put("ALARM_TRIP_LINE", "绊线检测报警");
        EVENTS.put("ALARM_TRUCK_41812000062", "冷库异常车辆停靠识别HM");
        EVENTS.put("ALARM_TRUCK_X", "货车识别X");
        EVENTS.put("ALARM_TYPE_BRUTE_FORCE_CRACKING", "暴力破解告警");
        EVENTS.put("ALARM_TYPE_DI", "告警输入设备告警");
        EVENTS.put("ALARM_TYPE_HIDS_BOTNE ", "系统入侵僵尸网络告警");
        EVENTS.put("ALARM_TYPE_HIDS_ILLACCOUNT ", "系统入侵非法账户告警");
        EVENTS.put("ALARM_TYPE_HIDS_MINER", "系统入侵挖矿告警");
        EVENTS.put("ALARM_TYPE_HIDS_ROOTKIT", "系统入侵Rootkit告警");
        EVENTS.put("ALARM_TYPE_MOVE_DECTION", "移动侦测告警");
        EVENTS.put("ALARM_TYPE_PTOPT_HOR_FAIL", "水平方向光耦失败");
        EVENTS.put("ALARM_TYPE_PTOPT_VER_FAIL", "垂直方向光耦失败");
        EVENTS.put("ALARM_UMBRELLA_SUPPORT_ZT", "违规撑伞识别ZT");
        EVENTS.put("ALARM_UPDATE_ROLLBACK", "升级版本失败回退告警");
        EVENTS.put("ALARM_VE_ABNORMAL_PLATE", "异常车牌");
        EVENTS.put("ALARM_VE_GUARD_BLACK", "车辆黑名单布控");
        EVENTS.put("ALARM_VE_GUARD_WHITE", "车辆白名单布控");
        EVENTS.put("ALARM_VE_ILLEGAL_PARKING", "违章停车");
        EVENTS.put("ALARM_VE_ON_NONE_VEHICLE_LANE", "机占非");
        EVENTS.put("ALARM_VE_ON_VEHICLE_LANE", "非占机");
        EVENTS.put("ALARM_VE_PRESSING_LINE", "压线");
        EVENTS.put("ALARM_VE_REVERSE", "倒车");
        EVENTS.put("ALARM_VE_SHELTER_PLATE", "遮挡车牌");
        EVENTS.put("ALARM_VE_WRONG_DIRECTION", "逆行");
        EVENTS.put("ALARM_VIDEO_ERROR", "视频异常检测报警");
        EVENTS.put("ALARM_VIDEO_GAIN_IMBALANCE_DARK", "亮度诊断");
        EVENTS.put("ALARM_VIDEO_LOSS", "视频丢失");
        EVENTS.put("ALARM_VLPR_TRAFFIC_CONGESTION", "交通拥堵");
        EVENTS.put("ALARM_WEB_PWD_EXPIRE", "WEB密码超期告警");
        EVENTS.put("ALARM_WEB_USR_LOCK", "WEB用户被锁定");
        EVENTS.put("ALARM_WIFI_ALARM", "wifi异常");
        EVENTS.put("ALARM_WIPER_FAILURE_ALARM", "雨刷失败告警");
        EVENTS.put("AREA_HEADCOUNT_41811000020", "区域人数统计-ZT");
        EVENTS.put("AREA_HEADCOUNT_41812000029", "区域人数统计-HM");
        EVENTS.put("CELL_PHONE_PLAYING_RECOGNITION_41812000032", "玩手机识别-HM");
        EVENTS.put("CELL_PHONE_RECOGNITION_41811000030", "玩手机识别-ZT");
        EVENTS.put("CELL_PHONE_USE_DETECTION_41811000041", "手机使用检测-ZT");
        EVENTS.put("CHANNEL_DEVIATION_DETECTION_41812000059", "航道偏离检测-HM");
        EVENTS.put("CHEFS_HAT_DETECTION_41811000024", "厨师帽检测-ZT");
        EVENTS.put("CHEFS_HAT_DETECTION_41812000040", "厨师帽检测-HM");
        EVENTS.put("CHEFS_UNIFORM_DETECTION_41811000023", "厨师服检测-ZT");
        EVENTS.put("CHEFS_UNIFORM_DETECTION_41812000039", "厨师服检测-HM");
        EVENTS.put("CHUDIAN_JINGYING_41811000046", "出店经营（占道经营）ZT");
        EVENTS.put("CHUDIAN_JINGYING_X", "出店经营（占道经营）X");
        EVENTS.put("CIGARETTE_SMOKING_RECOGNITION_41811000005", "抽烟识别-ZT");
        EVENTS.put("COLD_CHAIN_CONTAINER_NUMBER_IDENTIFICATION_41811000034", "冷链货柜号识别-ZT");
        EVENTS.put("COLD_CHAIN_CONTAINER_NUMBER_IDENTIFICATION_41812000036", "冷链货柜号识别-HM");
        EVENTS.put("CROWD_DENSITY_GATHERING_DETECTION_41811000021", "人群密度聚集检测-ZT");
        EVENTS.put("CROWD_DENSITY_GATHERING_DETECTION_41812000030", "人群密度聚集检测-HM");
        EVENTS.put("DISTRACTION_DETECTION_41811000043", "分心检测-ZT");
        EVENTS.put("DOULIU_SHICHANG_X", "逗留时长分析X");
        EVENTS.put("DRUM_IDENTIFICATION_41811000061", "油桶识别ZT");
        EVENTS.put("ENGINEERING_VEHICLE_IDENTIFICATION_41811000059", "工程车识别ZT");
        EVENTS.put("FATIGUE_DETECTION_41811000040", "疲劳检测-ZT");
        EVENTS.put("FIGHTS_AND_BRAWLS_41811000025", "打架斗殴-ZT");
        EVENTS.put("FIGHTS_AND_BRAWLS_41812000025", "打架斗殴-HM");
        EVENTS.put("FIGHT_RECOGNITION_41812000041", "打架识别-HM");
        EVENTS.put("FIRE_DETECTION_41811000017", "火警检测-ZT");
        EVENTS.put("FIRE_DETECTION_41812000009", "平台火警检测-HM");
        EVENTS.put("GENERAL_TARGET_DETECTION_41812000048", "通用目标检测（垃圾、杂物）-HM");
        EVENTS.put("GENERIC_TARGET_DETECTION_41811000004", "通用目标检测（垃圾、杂物）-ZT");
        EVENTS.put("GLOVE_DETECTION_41811000032", "手套检测-ZT");
        EVENTS.put("GLOVE_DETECTION_41812000034", "手套检测-HM");
        EVENTS.put("HIGH_TOSS_DETECT", "高空抛物-IVS");
        EVENTS.put("JIANZHU_FEILIAO_DETECTION_41811000050", "建筑废料堆放识别ZT");
        EVENTS.put("JIANZHU_FEILIAO_DETECTION_X", "建筑废料堆放识别X");
        EVENTS.put("KELIU_TONGJI_X", "客流统计X");
        EVENTS.put("KNIFE_41811000063", "持刀持械-ZT");
        EVENTS.put("LICENSE_PLATE_RECOGNITION_41811000009", "车牌识别-ZT");
        EVENTS.put("LICENSE_PLATE_RECOGNITION_41812000006", "平台车牌识别-HM");
        EVENTS.put("LOITERING_DETECTION_IN_HIGH-RISK_AREAS_41812000011", "平台高危区域人员徘徊检测-HM");
        EVENTS.put("MASK_DETECTION_41811000010", "口罩检测-ZT");
        EVENTS.put("MASK_DETECTION_41812000012", "平台口罩检测-HM");
        EVENTS.put("MOTOR_VEHICLE_DISORDERLY_PARKING_RECOGNITION_41812000044", "机动车乱停乱放识别-HM");
        EVENTS.put("MOTOR_VEHICLE_INDISCRIMINATE_PARKING_RECOGNITION_41811000002", "机动车乱停放识别-ZT");
        EVENTS.put("MOUSE_DETECTION_41812000031", "老鼠检测-HM");
        EVENTS.put("NIGHT-TIME_PERSON_STAY_RECOGNITION_41811000008", "夜间人员逗留识别-ZT");
        EVENTS.put("NIGHT-TIME_STAY_RECOGNITION_41812000005", "平台夜间人员逗留识别-HM");
        EVENTS.put("OFF-DUTY_DETECTION_41811000015", "离岗检测-ZT");
        EVENTS.put("OFF-DUTY_DETECTION_41812000007", "平台离岗检测-HM");
        EVENTS.put("OILNUMBER_41811000062", "油桶编码识别ZT");
        EVENTS.put("PARKING_VIOLATION_ALARM_41811000012", "违停告警-ZT");
        EVENTS.put("PARKING_VIOLATION_WARNING_41812000014", "平台违停告警-HM");
        EVENTS.put("PASSING_SHIP_CAPTURE_41812000057", "过船抓拍-HM");
        EVENTS.put("PEOPLE_PROXIMITY_DETECTION_41811000026", "人员靠近检测-ZT");
        EVENTS.put("PEOPLE_STAY_DETECTION_41811000028", "人员滞留检测-ZT");
        EVENTS.put("PERIMETER_INTRUSION_DETECTION_41811000007", "周界入侵检测-ZT");
        EVENTS.put("PERIMETER_INTRUSION_DETECTION_41812000004", "平台周界入侵检测-HM");
        EVENTS.put("PERSONNEL_FALL_DETECTION_41812000043", "人员摔倒检测-HM");
        EVENTS.put("PERSONNEL_LEAVING_DETECTION_41812000016", "人员离岗检测-HM");
        EVENTS.put("PERSON_APPROACH_DETECTION_41812000021", "人员靠近检测-HM");
        EVENTS.put("PERSON_FALL_DETECTION_41811000013", "跌倒检测（人员摔倒检测）-ZT");
        EVENTS.put("PERSON_FALL_DETECTION_41812000020", "跌倒检测（人员摔倒检测）-HM");
        EVENTS.put("PERSON_RECOGNITION", "人脸识别");
        EVENTS.put("PERSON_STRANDING_DETECTION_41812000023", "人员滞留检测-HM");
        EVENTS.put("PROHIBITION_OF_ELEVATOR_ACCESS_ALARM_41811000038", "禁止进入电梯告警-ZT");
        EVENTS.put("PROTECTIVE_CLOTHING_IDENTIFICATION_41811000033", "防护服识别-ZT");
        EVENTS.put("PROTECTIVE_CLOTHING_IDENTIFICATION_41812000035", "防护服识别-HM");
        EVENTS.put("PU_ALARM_TYPE_BRUTE_FORCE_CRACKING", "暴力破解检测告警");
        EVENTS.put("PU_ALARM_TYPE_HIDS_BOTNE", "系统入侵僵尸网络告警");
        EVENTS.put("PU_ALARM_TYPE_HIDS_ILLACCOUNT", "系统入侵非法账户告警");
        EVENTS.put("PU_ALARM_TYPE_HIDS_MINER", "系统入侵挖矿告警");
        EVENTS.put("PU_ALARM_TYPE_HIDS_ROOTKIT", "系统入侵rootkit告警");
        EVENTS.put("PU_AUTOTRANS_FACEPIC_RES", "断网重连自动发送图片，不计入告警");
        EVENTS.put("PU_LPR_CAPTURE_RES", "车牌抓拍结果，不计入告警");
        EVENTS.put("PU_MANUAL_LPR_CAPTURE_RES", "手动车牌抓拍结果，不计入告警");
        EVENTS.put("RAILROAD_TRACK_SAFETY_INSPECTION_RECOGNITION_41811000045", "铁路轨道安全巡检识别-ZT");
        EVENTS.put("RAILROAD_TRACK_SAFETY_INSPECTION_RECOGNITION_41812000055", "铁路轨道安全巡检识别-HM");
        EVENTS.put("RAT_DETECTION_41811000022", "老鼠检测-ZT");
        EVENTS.put("REFLECTIVE_CLOTHING_DETECTION_41811000035", "反光衣检测-ZT");
        EVENTS.put("REFLECTIVE_CLOTHING_DETECTION_41812000052", "反光衣检测-HM");
        EVENTS.put("ROAD_VEHICLE_ANOMALY_RECOGNITION_41811000044", "道路车辆异常情况识别-ZT");
        EVENTS.put("ROAD_VEHICLE_ANOMALY_RECOGNITION_41812000054", "道路车辆异常情况识别-HM");
        EVENTS.put("RUNNING_IDENTIFICATION_41811000001", "奔跑识别-ZT");
        EVENTS.put("RUNNING_RECOGNITION_41812000042", "奔跑识别-HM");
        EVENTS.put("SAFETY_HELMET_WEARING_DETECTION_41811000014", "安全帽佩戴检测-ZT");
        EVENTS.put("SAFETY_HELMET_WEARING_DETECTION_41812000001", "平台安全帽佩戴检测-HM");
        EVENTS.put("SEAT_BELT_DETECTION_41811000042", "安全带检测-ZT");
        EVENTS.put("SECURITY_SLEEPING_DETECTION_41812000017", "保安睡觉检测-HM");
        EVENTS.put("SHIP_TYPE_RECOGNITION_41812000058", "船型识别-HM");
        EVENTS.put("SHUIWEI_DETECTION_41811000048", "水位检测ZT");
        EVENTS.put("SHUIWEI_DETECTION_X", "水位检测X");
        EVENTS.put("SLEEPING_DETECTION_41811000016", "睡岗检测-ZT");
        EVENTS.put("SLEEPING_DETECTION_41812000008", "平台睡岗检测-HM");
        EVENTS.put("SMOKE_DETECTION_41811000006", "烟雾检测-ZT");
        EVENTS.put("SMOKE_DETECTION_41812000003", "平台烟雾检测-HM");
        EVENTS.put("SMOKING_RECOGNITION_41812000002", "平台抽烟识别-HM");
        EVENTS.put("SPARKS_OPERATION_IDENTIFICATION_41811000018", "火星作业识别-ZT");
        EVENTS.put("SPARK_OPERATION_RECOGNITION_41812000010", "平台火星作业识别-HM");
        EVENTS.put("STATION_INSPECTION_IDENTIFICATION_41811000036", "站场巡检识别-ZT");
        EVENTS.put("STATION_INSPECTION_RECOGNITION_41812000056", "站场巡检识别-HM");
        EVENTS.put("STRANGER_IDENTIFICATION_41812000013", "平台陌生人识别-HM");
        EVENTS.put("STRANGER_RECOGNITION_41811000011", "陌生人识别-ZT");
        EVENTS.put("STREET_DRYING_41811000064", "沿街晾晒ZT");
        EVENTS.put("STRENUOUS_MOVEMENT_41812000024", "剧烈运动-HM");
        EVENTS.put("TRACK_TRACKING_41811000037", "轨迹跟踪-ZT");
        EVENTS.put("TRASH_CAN_UNCOVERED_41811000031", "垃圾桶未盖-ZT");
        EVENTS.put("TRASH_CAN_UNCOVERED_41812000033", "垃圾桶未盖-HM");
        EVENTS.put("TRUCK_41811000058", "货车识别ZT");
        EVENTS.put("VEHICLE_RECOGNITION", "车辆识别");
        EVENTS.put("VIGOROUS_MOVEMENT_41811000029", "剧烈运动-ZT");
        EVENTS.put("WANDERING_DETECTION_41811000019", "徘徊检测-ZT");
        EVENTS.put("WANDERING_DETECTION_41812000026", "徘徊检测-HM");
        EVENTS.put("WATER_LEAKAGE_DETECTION_41811000039", "漏水检测-ZT");
        EVENTS.put("WEIGUI_CHENGSAN_X", "违规撑伞识别X");
        EVENTS.put("WEIGUI_HUWAIGUANGGAO_X", "违规户外广告识别X");
        EVENTS.put("WUPAOCHE_DETECTION_41811000047", "雾炮车识别ZT");
        EVENTS.put("WUPAOCHE_DETECTION_X", "雾炮车识别X");
        EVENTS.put("WUSHUI_PAICHU_DETECTION_41811000049", "污水排出识别ZT");
        EVENTS.put("WUSHUI_PAICHU_DETECTION_X", "污水排出识别X");
        EVENTS.put("XIAOFANG_ZHANDAO_X", "消防占道识别X");
        EVENTS.put("YANJIE_LIANGSHAI_X", "沿街晾晒X");
        EVENTS.put("防疫", "防疫告警");

        cache();
    }

    public void cache() {
        List<Client> _clients = clientRepository.find(Collections.singletonList(Client.ENUM_PLATFORM.ACS));

        mediaFacades.clear();
        deviceFacades.clear();
        alarmFacades.clear();
        authenticators.clear();

        authenticators.addAll(_clients.stream()
                .map(i -> new Tuple3<>(
                        i.getId(),
                        i.getAppId(),
                        new HttpDigestAuthenticator(i.getAppId(), i.getAppSecret())
                ))
                .collect(Collectors.toList()));

        alarmFacades.addAll(authenticators.stream()
                .map(i -> new Tuple3<>(
                        i.getOne(),
                        i.getTwo(),
                        new AlarmFacade(baseUrl, connectionPool, i.getThree())
                ))
                .collect(Collectors.toList()));

        deviceFacades.addAll(authenticators.stream()
                .map(i -> new Tuple3<>(
                        i.getOne(),
                        i.getTwo(),
                        new DeviceFacade(baseUrl, connectionPool, i.getThree())
                ))
                .collect(Collectors.toList()));

        mediaFacades.addAll(authenticators.stream()
                .map(i -> new Tuple3<>(
                        i.getOne(),
                        i.getTwo(),
                        new MediaFacade(baseUrl, connectionPool, i.getThree())
                ))
                .collect(Collectors.toList()));
    }

    public AlarmFacade alarmFacade(String clientId) {
        return alarmFacades.stream()
                .filter(i -> Objects.equals(i.getOne(), clientId))
                .map(Tuple3::getThree)
                .findFirst().orElse(null);
    }

    public List<Tuple3<String, String, AlarmFacade>> alarmFacades() {
        return alarmFacades;
    }

    public DeviceFacade deviceFacade(String clientId) {
        return deviceFacades.stream()
                .filter(i -> Objects.equals(i.getOne(), clientId))
                .map(Tuple3::getThree)
                .findFirst().orElse(null);
    }

    public List<Tuple3<String, String, DeviceFacade>> deviceFacades() {
        return deviceFacades;
    }

    public MediaFacade mediaFacade(String clientId) {
        return mediaFacades.stream()
                .filter(i -> Objects.equals(i.getOne(), clientId))
                .map(Tuple3::getThree)
                .findFirst().orElse(null);
    }

    public List<Tuple3<String, String, MediaFacade>> mediaFacades() {
        return mediaFacades;
    }

}