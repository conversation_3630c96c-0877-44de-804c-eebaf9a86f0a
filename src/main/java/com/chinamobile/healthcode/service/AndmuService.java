package com.chinamobile.healthcode.service;

import com.chinamobile.healthcode.model.iot.Client;
import com.chinamobile.healthcode.repository.iot.ClientRepository;
import com.chinamobile.sparrow.domain.service.andmu.CameraFacade;
import com.chinamobile.sparrow.domain.service.andmu.SmokeDetectorFacade;
import com.chinamobile.sparrow.domain.service.andmu.SubscribeFacade;
import com.chinamobile.sparrow.domain.service.andmu.TokenFacade;
import com.chinamobile.sparrow.domain.service.andmu.infra.TokenStore;
import okhttp3.ConnectionPool;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

@Component
public class AndmuService {

    final String baseUrl;
    final ClientRepository clientRepository;
    final ConnectionPool connectionPool;

    final CopyOnWriteArrayList<Tuple4<String, String, String, TokenStore>> tokenStores;
    final CopyOnWriteArrayList<Tuple4<String, String, String, TokenFacade>> tokenFacades;
    final CopyOnWriteArrayList<Tuple3<String, String, CameraFacade>> cameraFacades;
    final CopyOnWriteArrayList<Tuple3<String, String, SmokeDetectorFacade>> smokeDetectorFacades;
    final CopyOnWriteArrayList<Tuple3<String, String, SubscribeFacade>> subscribeFacades;

    public AndmuService(
            @Value(value = "${andmu.base-url}") String baseUrl,
            ClientRepository clientRepository,
            ConnectionPool connectionPool
    ) {
        this.baseUrl = baseUrl;
        this.clientRepository = clientRepository;
        this.connectionPool = connectionPool;

        this.tokenStores = new CopyOnWriteArrayList<>();
        this.tokenFacades = new CopyOnWriteArrayList<>();
        this.cameraFacades = new CopyOnWriteArrayList<>();
        this.smokeDetectorFacades = new CopyOnWriteArrayList<>();
        this.subscribeFacades = new CopyOnWriteArrayList<>();

        cache();
    }

    public void cache() {
        List<Client> _clients = clientRepository.find(Collections.singletonList(Client.ENUM_PLATFORM.ANDMU));

        subscribeFacades.clear();
        smokeDetectorFacades.clear();
        cameraFacades.clear();
        tokenStores.clear();
        tokenFacades.clear();

        tokenFacades.addAll(_clients.stream()
                .map(i -> new Tuple4<>(
                        i.getId(),
                        i.getAppId(),
                        i.getInfo(),
                        new TokenFacade(baseUrl, i.getAppId(), i.getAppSecret(), i.getInfo(), connectionPool)
                ))
                .collect(Collectors.toList()));

        tokenStores.addAll(tokenFacades.stream().map(i -> new Tuple4<>(
                        i.getOne(),
                        i.getTwo(),
                        i.getThree(),
                        new TokenStore(i.getFour())
                ))
                .collect(Collectors.toList()));

        cameraFacades.addAll(tokenStores.stream()
                .map(i -> new Tuple3<>(
                        i.getOne(),
                        i.getTwo(),
                        new CameraFacade(baseUrl, i.getTwo(), i.getThree(), i.getFour(), connectionPool)
                ))
                .collect(Collectors.toList()));

        smokeDetectorFacades.addAll(_clients.stream()
                .map(i -> new Tuple3<>(
                        i.getId(),
                        i.getAppId(),
                        new SmokeDetectorFacade(baseUrl, i.getAppId(), i.getInfo(), connectionPool)
                ))
                .collect(Collectors.toList()));

        subscribeFacades.addAll(tokenStores.stream()
                .map(i -> new Tuple3<>(
                        i.getOne(),
                        i.getTwo(),
                        new SubscribeFacade(baseUrl, i.getTwo(), i.getThree(), i.getFour(), connectionPool)
                ))
                .collect(Collectors.toList()));
    }

    public CameraFacade cameraFacade(String clientId) {
        return cameraFacades.stream()
                .filter(i -> Objects.equals(i.getOne(), clientId))
                .map(Tuple3::getThree)
                .findFirst().orElse(null);
    }

    public List<Tuple3<String, String, CameraFacade>> cameraFacades() {
        return cameraFacades;
    }

    public SmokeDetectorFacade smokeDetectorFacade(String clientId) {
        return smokeDetectorFacades.stream()
                .filter(i -> Objects.equals(i.getOne(), clientId))
                .map(Tuple3::getThree)
                .findFirst().orElse(null);
    }

    public List<Tuple3<String, String, SmokeDetectorFacade>> smokeDetectorFacades() {
        return smokeDetectorFacades;
    }

    public SubscribeFacade subscribeFacade(String clientId) {
        return subscribeFacades.stream()
                .filter(i -> Objects.equals(i.getOne(), clientId))
                .map(Tuple3::getThree)
                .findFirst().orElse(null);
    }

    public List<SubscribeFacade> subscribeFacades() {
        return subscribeFacades.stream()
                .map(Tuple3::getThree)
                .collect(Collectors.toList());
    }

}