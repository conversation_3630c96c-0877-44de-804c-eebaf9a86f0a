package com.chinamobile.healthcode.service;

import com.chinamobile.healthcode.model.OperationLog;
import com.chinamobile.healthcode.model.OperationLogQueryDto;
import com.chinamobile.healthcode.repository.OperationLogRepository;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 操作日志服务
 */
@Service
public class OperationLogService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OperationLogService.class);
    
    private final OperationLogRepository operationLogRepository;
    private final ObjectMapper objectMapper;

    public OperationLogService(OperationLogRepository operationLogRepository, ObjectMapper objectMapper) {
        this.operationLogRepository = operationLogRepository;
        this.objectMapper = objectMapper;
    }

    /**
     * 记录新增操作日志
     */
    @Transactional
    public void logCreate(String entityType, String entityId, Object entity, User operator, String description) {
        try {
            OperationLog log = createBaseLog(entityType, entityId, OperationLog.OperationType.CREATE, operator, description);
            log.setAfterData(convertToJson(entity));
            log.setResult(OperationLog.OperationResult.SUCCESS);
            operationLogRepository.add(log, operator.getId());
        } catch (Exception e) {
            LOGGER.error("记录新增操作日志失败", e);
        }
    }

    /**
     * 记录修改操作日志
     */
    @Transactional
    public void logUpdate(String entityType, String entityId, Object beforeEntity, Object afterEntity, User operator, String description) {
        try {
            OperationLog log = createBaseLog(entityType, entityId, OperationLog.OperationType.UPDATE, operator, description);
            log.setBeforeData(convertToJson(beforeEntity));
            log.setAfterData(convertToJson(afterEntity));
            log.setResult(OperationLog.OperationResult.SUCCESS);
            operationLogRepository.add(log, operator.getId());
        } catch (Exception e) {
            LOGGER.error("记录修改操作日志失败", e);
        }
    }

    /**
     * 记录删除操作日志
     */
    @Transactional
    public void logDelete(String entityType, String entityId, Object entity, User operator, String description) {
        try {
            OperationLog log = createBaseLog(entityType, entityId, OperationLog.OperationType.DELETE, operator, description);
            log.setBeforeData(convertToJson(entity));
            log.setResult(OperationLog.OperationResult.SUCCESS);
            operationLogRepository.add(log, operator.getId());
        } catch (Exception e) {
            LOGGER.error("记录删除操作日志失败", e);
        }
    }

    /**
     * 记录导入操作日志
     */
    @Transactional
    public void logImport(String entityType, String entityId, Object entity, User operator, String description) {
        try {
            OperationLog log = createBaseLog(entityType, entityId, OperationLog.OperationType.IMPORT, operator, description);
            log.setAfterData(convertToJson(entity));
            log.setResult(OperationLog.OperationResult.SUCCESS);
            operationLogRepository.add(log, operator.getId());
        } catch (Exception e) {
            LOGGER.error("记录导入操作日志失败", e);
        }
    }

    /**
     * 记录操作失败日志
     */
    @Transactional
    public void logFailure(String entityType, String entityId, OperationLog.OperationType operationType, User operator, String description, String errorMessage) {
        try {
            OperationLog log = createBaseLog(entityType, entityId, operationType, operator, description);
            log.setResult(OperationLog.OperationResult.FAILURE);
            log.setErrorMessage(errorMessage);
            operationLogRepository.add(log, operator.getId());
        } catch (Exception e) {
            LOGGER.error("记录操作失败日志失败", e);
        }
    }

    /**
     * 分页查询操作日志
     */
    @Transactional(readOnly = true)
    public Page<OperationLog> queryLogs(OperationLogQueryDto queryDto) {
        return operationLogRepository.findByQueryDto(queryDto);
    }

    /**
     * 根据实体类型和ID查询操作日志
     */
    @Transactional(readOnly = true)
    public List<OperationLog> getLogsByEntity(String entityType, String entityId) {
        return operationLogRepository.findByEntityTypeAndEntityId(entityType, entityId);
    }

    /**
     * 根据操作人ID查询操作日志
     */
    @Transactional(readOnly = true)
    public List<OperationLog> getLogsByOperator(String operatorId) {
        return operationLogRepository.findByOperatorId(operatorId);
    }

    /**
     * 根据部门ID查询操作日志
     */
    @Transactional(readOnly = true)
    public List<OperationLog> getLogsByDepartment(String deptId) {
        return operationLogRepository.findByDeptId(deptId);
    }

    /**
     * 根据时间范围查询操作日志
     */
    @Transactional(readOnly = true)
    public List<OperationLog> getLogsByTimeRange(Date startTime, Date endTime) {
        return operationLogRepository.findByTimeRange(startTime, endTime);
    }

    /**
     * 清理指定时间之前的日志
     */
    @Transactional
    public int cleanLogsBefore(Date date) {
        return operationLogRepository.deleteByOperateTimeBefore(date);
    }

    /**
     * 创建基础日志对象
     */
    private OperationLog createBaseLog(String entityType, String entityId, OperationLog.OperationType operationType, User operator, String description) {
        OperationLog log = new OperationLog();
        log.setEntityType(entityType);
        log.setEntityId(entityId);
        log.setOperationType(operationType);
        log.setDescription(description);
        log.setOperatorId(operator.getId());
        log.setOperatorName(operator.getName());
        log.setDeptId(operator.getDeptId());
        log.setDeptName(operator.getDeptName());
        log.setOperateTime(new Date());
        
        // 获取请求信息
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                log.setIpAddress(getClientIpAddress(request));
                log.setUserAgent(request.getHeader("User-Agent"));
            }
        } catch (Exception e) {
            LOGGER.warn("获取请求信息失败", e);
        }
        
        return log;
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String convertToJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            LOGGER.warn("对象转JSON失败", e);
            return obj.toString();
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
