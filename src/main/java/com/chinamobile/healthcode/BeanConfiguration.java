package com.chinamobile.healthcode;

import cn.binarywang.wx.miniapp.api.WxMaLinkService;
import cn.binarywang.wx.miniapp.api.WxMaQrcodeService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaLinkServiceImpl;
import cn.binarywang.wx.miniapp.api.impl.WxMaQrcodeServiceImpl;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.chinamobile.healthcode.service.event.SubscribeService;
import com.chinamobile.sparrow.domain.infra.sec.shiro.DefaultLoginUtil;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade;
import com.chinamobile.sparrow.domain.util.DefaultHttpUtil;
import okhttp3.ConnectionPool;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import java.net.Proxy;

@Configuration
public class BeanConfiguration {

    @Bean
    public DefaultHttpUtil httpUtil(
            ConnectionPool connectionPool,
            @Autowired(required = false) @Qualifier("defaultProxy") Proxy proxy
    ) {
        return new DefaultHttpUtil(connectionPool, proxy);
    }

    @Bean
    public LoginUtil loginUtil(
            @Value("${sec.rsa.default.private-key}") String rsaPrivateKey,
            @Value("${sec.username}") String username,
            UserRepository userRepository,
            SessionDAO sessionDAO
    ) {
        return new DefaultLoginUtil(rsaPrivateKey, username, userRepository, sessionDAO);
    }


    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(
            @Value(value = "${event.topic.pattern}") String topicPattern,
            SubscribeService subscribeService,
            RedisTemplate<String, Object> redisTemplate
    ) {
        RedisMessageListenerContainer _container = new RedisMessageListenerContainer();
        _container.setConnectionFactory(redisTemplate.getConnectionFactory());
        _container.addMessageListener(subscribeService, new PatternTopic(topicPattern));

        return _container;
    }

    @Bean
    public WxMaConfig wxMaSTFYConfig(
            @Value("${wx.ma.stfy.app-id}") String appId,
            @Value("${wx.ma.stfy.secret}") String secret,
            @Value("${wx.ma.msg-data-format}") String msgDataFormat
    ) {
        WxMaDefaultConfigImpl _config = new WxMaDefaultConfigImpl();
        _config.setAppid(appId);
        _config.setSecret(secret);
        _config.setMsgDataFormat(msgDataFormat);

        return _config;
    }

    @Bean
    public WxMaService wxMaSTFYService(@Qualifier(value = "wxMaSTFYConfig") WxMaConfig wxMaConfig) {
        WxMaServiceImpl _service = new WxMaServiceImpl();
        _service.setWxMaConfig(wxMaConfig);
        return _service;
    }

    @Bean
    public AccessFacade wxMaSTFYAccessFacade(WxMaService wxMaSTFYService) {
        return new AccessFacade(wxMaSTFYService);
    }

    @Bean
    public WxMaConfig wxMaJFMConfig(
            @Value("${wx.ma.jfm.app-id}") String appId,
            @Value("${wx.ma.jfm.secret}") String secret,
            @Value("${wx.ma.msg-data-format}") String msgDataFormat
    ) {
        WxMaDefaultConfigImpl _config = new WxMaDefaultConfigImpl();
        _config.setAppid(appId);
        _config.setSecret(secret);
        _config.setMsgDataFormat(msgDataFormat);

        return _config;
    }

    @Bean
    public WxMaService wxMaJFMService(@Qualifier(value = "wxMaJFMConfig") WxMaConfig wxMaConfig) {
        WxMaServiceImpl _service = new WxMaServiceImpl();
        _service.setWxMaConfig(wxMaConfig);
        return _service;
    }

    @Bean
    public AccessFacade wxMaJFMAccessFacade(WxMaService wxMaJFMService) {
        return new AccessFacade(wxMaJFMService);
    }

    @Bean
    public WxMaLinkService wxMaLinkService(@Qualifier(value = "wxMaJFMService") WxMaService wxMaService) {
        return new WxMaLinkServiceImpl(wxMaService);
    }

    @Bean
    public WxMaQrcodeService wxMaJFMQrcodeService(@Qualifier(value = "wxMaJFMService") WxMaService wxMaService) {
        return new WxMaQrcodeServiceImpl(wxMaService);
    }

}