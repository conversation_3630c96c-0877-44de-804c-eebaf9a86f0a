package com.chinamobile.healthcode;

import com.chinamobile.healthcode.controller.emergency.AlarmWebSocketHandler;
import com.chinamobile.healthcode.controller.iot.InterphoneWebSocketHandler;
import com.chinamobile.sparrow.springboot.web.ShiroHandshakeInterceptorImpl;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
public class DefaultWebSocketConfigurer implements WebSocketConfigurer {

    final ShiroHandshakeInterceptorImpl shiroHandshakeInterceptor;
    final AlarmWebSocketHandler alarmWebSocketHandler;
    final InterphoneWebSocketHandler interphoneWebSocketHandler;

    public DefaultWebSocketConfigurer(
            ShiroHandshakeInterceptorImpl shiroHandshakeInterceptor,
            AlarmWebSocketHandler alarmWebSocketHandler,
            InterphoneWebSocketHandler interphoneWebSocketHandler
    ) {
        this.shiroHandshakeInterceptor = shiroHandshakeInterceptor;
        this.alarmWebSocketHandler = alarmWebSocketHandler;
        this.interphoneWebSocketHandler = interphoneWebSocketHandler;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(alarmWebSocketHandler, "/emergency/alarm/conn")
                .addHandler(interphoneWebSocketHandler, "/iot/device/interphone/find")
                .addInterceptors(shiroHandshakeInterceptor)
                .setAllowedOrigins("*");
    }

}