package com.chinamobile.healthcode.aspect;

import com.chinamobile.healthcode.annotation.DataOperationLog;
import com.chinamobile.healthcode.model.DataOperationLogEntity;
import com.chinamobile.healthcode.repository.DataOperationLogRepository;
import com.chinamobile.healthcode.util.DataOperationLogUtil;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.User;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 数据操作日志切面
 */
@Aspect
@Component
public class DataOperationLogAspect {

    private final DataOperationLogRepository dataOperationLogRepository;
    private final LoginUtil loginUtil;

    public DataOperationLogAspect(DataOperationLogRepository dataOperationLogRepository,
                                  LoginUtil loginUtil) {
        this.dataOperationLogRepository = dataOperationLogRepository;
        this.loginUtil = loginUtil;
    }

    /**
     * 定义切点：所有带有@DataOperationLog注解的方法
     */
    @Pointcut("@annotation(com.chinamobile.healthcode.annotation.DataOperationLog)")
    public void dataOperationLogPointcut() {}

    /**
     * 环绕通知：记录方法执行前后的日志
     */
    @Around("dataOperationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        boolean success = false;
        String errorMessage = null;
        
        // 获取方法签名和注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        DataOperationLog annotation = method.getAnnotation(DataOperationLog.class);
        
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        
        // 获取操作人信息
        String operatorId = getCurrentUserId(args);
        String operatorName = getCurrentUserName(args);
        String operatorDepartmentFullName = getCurrentUserDepartmentFullName(args);
        
        // 获取修改前的数据（用于UPDATE操作）
        Object beforeData = null;
        if (annotation.type() == DataOperationLog.OperationType.UPDATE) {
            beforeData = getBeforeData(args, annotation);
        }
        
        try {
            // 执行原方法
            result = joinPoint.proceed();
            success = true;
        } catch (Exception e) {
            success = false;
            errorMessage = e.getMessage();
            throw e;
        } finally {
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 获取修改后的数据
            Object afterData = null;
            if (annotation.type() == DataOperationLog.OperationType.UPDATE || 
                annotation.type() == DataOperationLog.OperationType.CREATE) {
                afterData = getAfterData(args, result);
            }
            
            // 构建日志实体
            DataOperationLogEntity logEntity = DataOperationLogUtil.buildLogEntity(
                annotation, method, args, result, beforeData, afterData,
                operatorId, operatorName, operatorDepartmentFullName, executionTime, success, errorMessage
            );
            
            // 异步保存日志
            try {
                dataOperationLogRepository.saveLogAsync(logEntity);
            } catch (Exception e) {
                // 日志保存失败不影响主业务流程
                System.err.println("保存操作日志失败: " + e.getMessage());
            }
        }
        
        return result;
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId(Object[] args) {
        String userId;

        try {
            if (args == null || args.length == 0) {
                userId = loginUtil.getUserId();
            } else {
                userId = Arrays.stream(args)
                        .filter(User.class::isInstance)
                        .findFirst()
                        .map(arg -> ((User) arg).getId())
                        .orElse("未知用户id");
            }

        } catch (Exception e) {
            userId = "未知用户id";
        }

        return userId;
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUserName(Object[] args) {
        String username;

        try {
            if (args == null || args.length == 0) {
                username = loginUtil.getUsername();
            } else {
                username = Arrays.stream(args)
                        .filter(User.class::isInstance)
                        .findFirst()
                        .map(arg -> ((User) arg).getName())
                        .orElse("未知用户");
            }
        } catch (Exception e) {
            username = "未知用户";
        }

        return username;
    }

    /**
     * 获取当前用户部门
     */
    private String getCurrentUserDepartmentFullName(Object[] args) {
        String userDepartmentFullName;

        try {
            if (args == null || args.length == 0) {
                userDepartmentFullName = loginUtil.getUser().getDeptFullName();
            } else {
                userDepartmentFullName = Arrays.stream(args)
                        .filter(User.class::isInstance)
                        .findFirst()
                        .map(arg -> ((User) arg).getDeptFullName())
                        .orElse("未知部门");
            }

        } catch (Exception e) {
            userDepartmentFullName = "未知部门";
        }

        return userDepartmentFullName;
    }

    /**
     * 获取修改前的数据
     */
    private Object getBeforeData(Object[] args, DataOperationLog annotation) {
        if (args == null || args.length == 0) {
            return null;
        }
        
        // 查找实体对象
        for (Object arg : args) {
            if (arg != null && isEntityObject(arg)) {
                String entityId = getEntityId(arg);
                if (entityId != null && !entityId.trim().isEmpty()) {
                    // 这里可以通过Repository查询数据库中的原始数据
                    // 为了简化，暂时返回null
                    return null;
                }
            }
        }
        
        return null;
    }

    /**
     * 获取修改后的数据
     */
    private Object getAfterData(Object[] args, Object result) {
        // 优先从方法参数中获取
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                if (arg != null && isEntityObject(arg)) {
                    return arg;
                }
            }
        }
        
        // 如果参数中没有，则从返回值中获取
        if (result != null && isEntityObject(result)) {
            return result;
        }
        
        return null;
    }

    /**
     * 判断是否为实体对象
     */
    private boolean isEntityObject(Object obj) {
        if (obj == null) {
            return false;
        }
        
        try {
            // 检查是否有getId方法
            obj.getClass().getMethod("getId");
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取实体ID
     */
    private String getEntityId(Object entity) {
        if (entity == null) {
            return null;
        }
        
        try {
            Method getIdMethod = entity.getClass().getMethod("getId");
            Object id = getIdMethod.invoke(entity);
            return id != null ? id.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }
} 