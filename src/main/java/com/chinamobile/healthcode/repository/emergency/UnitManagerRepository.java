package com.chinamobile.healthcode.repository.emergency;

import com.chinamobile.healthcode.model.emergency.UnitManager;
import com.chinamobile.healthcode.repository.project.RecordRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 1/31/2024 10:04
 */
@Repository
public class UnitManagerRepository extends AbstractEntityRepository<UnitManager> {
    public static final String UNIT_ADMIN_ROLE = "";
    final DefaultDepartmentRepository departmentRepository;
    final DefaultUserRepository userRepository;
    final RoleRepository roleRepository;
    final LoginUtil loginUtil;

    protected UnitManagerRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                                    @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
                                    DefaultDepartmentRepository departmentRepository,
                                    DefaultUserRepository userRepository,
                                    RoleRepository roleRepository,
                                    LoginUtil loginUtil) {
        super(entityManagerFactory, jinqJPAStreamProvider, UnitManager.class);
        this.departmentRepository = departmentRepository;
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.loginUtil = loginUtil;
    }

    @Transactional(readOnly = true)
    public PagingItems<DefaultDepartment> fuzzyUnits(int count, int index, String regionId, String name) {
        User _user = loginUtil.getUser();
        List<DefaultDepartment> _units = departmentRepository.subordinates(regionId, 5, null);
        _units = _units.stream()
                .filter(Department::getIsEnabled)
                .filter(u -> roleRepository.isUserInRole(_user.getId(), RecordRepository.ADMIN_ROLE) || u.getCode().contains(_user.getDeptCode()))
                .filter(u -> !StringUtils.hasLength(regionId) || !u.getId().equals(regionId))
                .filter(u -> !StringUtils.hasLength(name) || u.getName().contains(name))
                .collect(Collectors.toList());
        // 分页
        PagingItems<DefaultDepartment> _page = new PagingItems<>(count, index);
        _page.total = _units.size();
        _units = _units.stream()
                .skip((long) count * index)
                .limit(count)
                .collect(Collectors.toList());
        _page.items = _units;
        return _page;
    }

    @Transactional(readOnly = true)
    public List<DefaultUser> list(String regionId) {
        List<String> _userIds = stream(UnitManager.class)
                .where(i -> regionId.equals(i.getRegionId()))
                .select(UnitManager::getUserId)
                .toList();

        return CollectionUtils.isEmpty(_userIds) ? new ArrayList<>() : userRepository.query(_userIds, null, null, null, null, true, null);
    }

    public Result save(String regionId, String userId, String actorId) {
        Result _success = new Result<>();

        Result<DefaultDepartment> _department = departmentRepository.get(regionId, true);
        if (_department.isOK()) {
            UnitManager _unitManager = stream(UnitManager.class)
                    .where(i -> i.getRegionId().equals(regionId))
                    .where(i -> i.getUserId().equals(userId))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(_unitManager)) {
                _unitManager = new UnitManager(regionId, userId);
                _unitManager.setCreatorId(actorId);
                _unitManager.setCreateTime(new Date());
                getCurrentSession().save(_unitManager);
            }
        } else {
            _success.pack(_department);
        }
        return _success;
    }

    public Result<Void> delete(String regionId, String userId) {
        Result<Void> _r = new Result<>();
        UnitManager _unitManager = stream(UnitManager.class)
                .where(i -> i.getRegionId().equals(regionId))
                .where(i -> i.getUserId().equals(userId))
                .findFirst()
                .orElse(null);

        if (Objects.isNull(_unitManager)) {
            _r.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        } else {
            getCurrentSession().remove(_unitManager);
        }
        return _r;
    }
}
