package com.chinamobile.healthcode.repository.emergency;

import com.chinamobile.healthcode.model.emergency.AlarmForm;
import com.chinamobile.healthcode.model.emergency.AlarmFormQueryDto;
import com.chinamobile.healthcode.repository.iot.AlarmRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.GpsUtil;
import com.google.common.reflect.TypeToken;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 1/29/2024 17:43
 */
@Repository
public class AlarmFormRepository extends AbstractEntityRepository<AlarmForm> {
    public static final String ADMIN_ROLE = "应急单元管理员";
    final RoleRepository roleRepository;
    final DepartmentRepository departmentRepository;
    final UserRepository userRepository;
    final AbstractMediaRepository mediaRepository;
    final AlarmRepository alarmRepository;

    protected AlarmFormRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                                  @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
                                  RoleRepository roleRepository,
                                  DepartmentRepository departmentRepository,
                                  UserRepository userRepository,
                                  AbstractMediaRepository mediaRepository,
                                  AlarmRepository alarmRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, AlarmForm.class);
        this.roleRepository = roleRepository;
        this.departmentRepository = departmentRepository;
        this.userRepository = userRepository;
        this.mediaRepository = mediaRepository;
        this.alarmRepository = alarmRepository;
    }

    @Transactional(readOnly = true)
    public Result<AlarmForm> get(String id, String alarmId, String taskId, boolean brief, String userId) {
        Result<AlarmForm> _item = new Result<>();

        if (StringUtils.hasLength(id)) {
            _item.data = getCurrentSession().get(AlarmForm.class, id);
        } else if (StringUtils.hasLength(alarmId) || StringUtils.hasLength(taskId)) {
            JinqStream<AlarmForm> _query = stream(AlarmForm.class);
            if (StringUtils.hasLength(alarmId)) {
                _query = _query.where(i -> i.getAlarmId().equals(alarmId));
            }
            if (StringUtils.hasLength(taskId)) {
                _query  = _query.where(i -> i.getTaskId().equals(taskId));
            }
            _item.data = _query.findFirst().orElse(null);
        }

        if (Objects.isNull(_item.data)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{AlarmForm.class.getSimpleName()});
        } else {
            parseAttachments(Collections.singletonList(_item.data), brief);
            parseCreator(Collections.singletonList(_item.data), brief, userId);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<AlarmForm> fuzzy(int count, int index, String alarmId, String taskId, AlarmFormQueryDto alarmFormQueryDto, User user) {
        PagingItems<AlarmForm> _page = new PagingItems<>(count, index);

        if (!StringUtils.hasLength(alarmId) && !StringUtils.hasLength(taskId)) {
            _page.items = new ArrayList<>();
        } else {
            JinqStream<AlarmForm> _query = stream(alarmId, taskId, alarmFormQueryDto, user);

            _query = _query.sortedDescendingBy(AbstractEntity::getCreateTime);

            _page.total = _query.count();

            if (count >= 0 && index >= 0) {
                _query = _query.skip(count * index).limit(count);
            }
            _page.items = _query.toList();

            parseAttachments(_page.items, true);
            parseCreator(_page.items, true, user.getId());
        }

        return _page;
    }

    public Result<String> save(AlarmForm item, User user) {
        Result<String> _id = new Result<>();

        boolean _isNew = false;

        Result<AlarmForm> _item = get(item.getId(), null, null, true, user.getId());

        List<String> _ids = new ArrayList<>();
        if (_item.isOK()) {
            if (_item.data.getReadonly()) {
                _id.setCode(Result.DATA_ACCESS_DENY);
                return _id;
            }
            _ids = StringUtils.hasLength(_item.data.getAttachmentIdsJSON()) ? ConverterUtil.json2Object(_item.data.getAttachmentIdsJSON(), new TypeToken<List<String>>() {
            }.getType()) : new ArrayList<>();

            if (!CollectionUtils.isEmpty(item.getAttachmentIds())) {
                _ids.removeAll(item.getAttachmentIds());
            }

            copyProperties(item, _item.data, new String[]{"id", "alarmId", "taskId", "attachmentIdsJSON"});
        } else {
            _item.data = new AlarmForm();
            copyProperties(item, _item.data, new String[]{"id", "attachmentIdsJSON"});
            _isNew = true;
        }

        _item.data.setAttachmentIdsJSON(CollectionUtils.isEmpty(item.getAttachmentIds()) ? null : ConverterUtil.toJson(item.getAttachmentIds()));

        if (Objects.nonNull(_item.data.getLongitudeInGcj()) && Objects.nonNull(_item.data.getLatitudeInGcj())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.data.getLatitudeInGcj(), _item.data.getLongitudeInGcj());
            _item.data.setLatitudeInWgs(_latLngInWgs[0]);
            _item.data.setLongitudeInWgs(_latLngInWgs[1]);
        }

        Result _success = _isNew ? add(_item.data, user.getId()) : update(_item.data, user.getId());
        if (_success.isOK()) {
            for (String i : _ids) {
                mediaRepository.remove(i, true, user.getId());
            }
        } else {
            return _id.pack(_success);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result<Void> remove(String id, String userId) {
        Result<Void> _success = new Result<>();

        Result<AlarmForm> _item = get(id, null, null,true, userId);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getReadonly()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 删除附件
        if (!CollectionUtils.isEmpty(_item.data.getAttachmentIds())) {
            for (String i : _item.data.getAttachmentIds()) {
                mediaRepository.remove(i, true, userId);
            }
        }

        getCurrentSession().remove(_item.data);

        return _success;
    }

    public JinqStream<AlarmForm> stream(String alarmId, String taskId, AlarmFormQueryDto alarmFormQueryDto, User user) {
        JinqStream<AlarmForm> _query = stream(AlarmForm.class);

        if (StringUtils.hasLength(alarmId)) {
            _query = _query.where(i -> i.getAlarmId() == null || alarmId.equals(i.getAlarmId()));
        }

        if (StringUtils.hasLength(taskId)) {
            _query = _query.where(i -> i.getTaskId() == null || taskId.equals(i.getTaskId()));
        }

        boolean _isAdmin = roleRepository.isUserInRole(user.getId(), ADMIN_ROLE);
        if (!_isAdmin) {
            // 数据权限
            List<Department> _subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);
            // 排除本单位
            _subordinates = _subordinates.stream()
                    .filter(i -> !Objects.equals(i.getId(), user.getDeptId()))
                    .collect(Collectors.toList());

            // 无下级村居单位，视为采集人员
            if (_subordinates.stream().noneMatch(i -> i.getLevel() == 3)) {
                String _userId = user.getId();
                String _deptFullName = user.getDeptFullName();
                _query = _query.where(i -> i.getRegionFullName().contains(_deptFullName)
                        || _userId.equals(i.getCreatorId()));
            } else {
                // 获取网格
                List<String> _deptIds = _subordinates.stream()
                        .filter(i -> i.getLevel() == 4)
                        .map(Department::getId)
                        .collect(Collectors.toList());
                _query = CollectionUtils.isEmpty(_deptIds) ? _query.where(i -> false) : _query.where(i -> i.getRegionId() != null && _deptIds.contains(i.getRegionId()));
            }
        }

        if (StringUtils.hasLength(alarmFormQueryDto.getCreator())) {
            Result<User> _user = userRepository.getBriefByIdOrAccountOrMp(alarmFormQueryDto.getCreator(), true);
            if (_user.isOK()) {
                String _userId = _user.data.getId();
                _query = _query.where(i -> _userId.equals(i.getCreatorId()));
            } else {
                String _creator = alarmFormQueryDto.getCreator();
                _query = _query.where(i -> _creator.equals(i.getCreatorId()));
            }
        }

        if (StringUtils.hasLength(alarmFormQueryDto.getRegionFullName())) {
            String _regionFullName = alarmFormQueryDto.getRegionFullName();
            _query = _query.where(i -> i.getRegionFullName() != null && i.getRegionFullName().contains(_regionFullName));
        }

        if (alarmFormQueryDto.getStartTime() != null) {
            Date _startTime = alarmFormQueryDto.getStartTime();
            _query = _query.where(i -> !i.getCreateTime().before(_startTime));
        }

        if (alarmFormQueryDto.getEndTime() != null) {
            Date _endTime = alarmFormQueryDto.getEndTime();
            _query = _query.where(i -> i.getCreateTime().before(_endTime));
        }

        return _query;
    }

    void parseCreator(List<AlarmForm> items, boolean brief, String userId) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        for (AlarmForm i : items) {
            i.setReadonly(!Objects.equals(i.getCreatorId(), userId));
        }
    }

    void parseAttachments(List<AlarmForm> items, boolean brief) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        for (AlarmForm i : items) {
            i.setAttachmentIds(StringUtils.hasLength(i.getAttachmentIdsJSON()) ? ConverterUtil.json2Object(i.getAttachmentIdsJSON(), new TypeToken<List<String>>() {
            }.getType()) : null);
        }

        if (!brief) {
            List<String> _ids = items.stream()
                    .filter(i -> i.getAttachmentIds() != null)
                    .flatMap(i -> i.getAttachmentIds().stream())
                    .collect(Collectors.toList());

            List<Media> _medias = mediaRepository.query(_ids, null);

            for (AlarmForm i : items) {
                i.setAttachments(CollectionUtils.isEmpty(i.getAttachmentIds()) ? null : _medias.stream()
                        .filter(j -> i.getAttachmentIds().contains(j.getId()))
                        .collect(Collectors.toList()));
            }
        }
    }
}
