package com.chinamobile.healthcode.repository;

import com.chinamobile.healthcode.model.OperationLog;
import com.chinamobile.healthcode.repository.subject.DefaultAbstractEntityRepository;
import com.chinamobile.healthcode.service.OperationLogService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;

/**
 * 带日志功能的Repository基类
 */
public abstract class LoggableAbstractEntityRepository<T extends AbstractEntity> extends DefaultAbstractEntityRepository<T> {
    private final DefaultUserRepository userRepository;
    protected final OperationLogService operationLogService;

    public LoggableAbstractEntityRepository(EntityManagerFactory entityManagerFactory,
                                            JinqJPAStreamProvider jinqJPAStreamProvider,
                                            Class<T> tClass,
                                            Validator validator,
                                            DefaultUserRepository userRepository,
                                            OperationLogService operationLogService) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass, validator);
        this.userRepository = userRepository;
        this.operationLogService = operationLogService;
    }

    /**
     * 获取实体类
     */
    protected Class<T> getEntityClass() {
        // 通过反射获取tClass字段的值
        try {
            java.lang.reflect.Field tClassField = DefaultAbstractEntityRepository.class.getDeclaredField("tClass");
            tClassField.setAccessible(true);
            return (Class<T>) tClassField.get(this);
        } catch (Exception e) {
            // 如果无法获取，返回null
            return null;
        }
    }

    public String getEntityId(T entity) {
        if (entity == null) {
            return null;
        }

        try {
            java.lang.reflect.Method getIdMethod = entity.getClass().getMethod("getId");
            Object id = getIdMethod.invoke(entity);
            return id != null ? id.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 重写add方法，添加日志记录
     */
    @Override
    @Transactional
    public Result<?> add(T entity, String userId) {
        Result<?> result = super.add(entity, userId);

        if (result.isOK()) {
            // 记录新增日志
            User user = getCurrentUser(userId);
            if (user != null) {
                String description = String.format("新增%s", getEntityDisplayName());
                operationLogService.logCreate(
                        entity.getClass().getSimpleName(),
                        getEntityId(entity),
                        entity,
                        user,
                        description
                );
            }
        } else {
            // 记录失败日志
            User user = getCurrentUser(userId);
            if (user != null) {
                String description = String.format("新增%s失败", getEntityDisplayName());
                operationLogService.logFailure(
                        entity.getClass().getSimpleName(),
                        getEntityId(entity),
                        OperationLog.OperationType.CREATE,
                        user,
                        description,
                        result.getCode()
                );
            }
        }

        return result;
    }

    /**
     * 重写update方法，添加日志记录
     */
    @Override
    @Transactional
    public Result<?> update(T entity, String userId) {
        // 获取更新前的数据
        T beforeEntity = null;
        try {
            // 使用HQL查询获取原始数据，避免Session缓存影响
            String entityId = getEntityId(entity);
            if (entityId != null) {
                beforeEntity = getCurrentSession().createQuery(
                    "FROM " + tClass.getSimpleName() + " WHERE id = :id", tClass)
                    .setParameter("id", entityId)
                    .uniqueResult();
            }
        } catch (Exception e) {
            beforeEntity = null;
        }

        Result<?> result = super.update(entity, userId);

        if (result.isOK()) {
            // 记录更新日志
            User user = getCurrentUser(userId);
            if (user != null) {
                String description = String.format("修改%s", getEntityDisplayName());
                operationLogService.logUpdate(
                        entity.getClass().getSimpleName(),
                        getEntityId(entity),
                        beforeEntity,
                        entity,
                        user,
                        description
                );
            }
        } else {
            // 记录失败日志
            User user = getCurrentUser(userId);
            if (user != null) {
                String description = String.format("修改%s失败", getEntityDisplayName());
                operationLogService.logFailure(
                        entity.getClass().getSimpleName(),
                        getEntityId(entity),
                        OperationLog.OperationType.UPDATE,
                        user,
                        description,
                        result.getCode()
                );
            }
        }

        return result;
    }

    /**
     * 重写remove方法，添加日志记录
     */
    @Override
    @Transactional
    public Result<T> remove(String id, User user) {
        // 获取删除前的数据
        T beforeEntity;
        try {
            beforeEntity = getCurrentSession().get(tClass, id);
        } catch (Exception e) {
            beforeEntity = null;
        }

        Result<T> result = super.remove(id, user);

        if (result.isOK() && beforeEntity != null) {
            // 记录删除日志
            String description = String.format("删除%s", getEntityDisplayName());
            operationLogService.logDelete(
                    beforeEntity.getClass().getSimpleName(),
                    id,
                    beforeEntity,
                    user,
                    description
            );
        } else if (!result.isOK()) {
            // 记录失败日志
            String description = String.format("删除%s失败", beforeEntity != null ? getEntityDisplayName() : "数据");
            operationLogService.logFailure(
                    getEntityClass().getSimpleName(),
                    id,
                    OperationLog.OperationType.DELETE,
                    user,
                    description,
                    result.getCode()
            );
        }

        return result;
    }

    /**
     * 获取实体显示名称
     */
    public abstract String getEntityDisplayName();

    /**
     * 获取当前用户
     */
    protected User getCurrentUser(String userId) {
        DefaultUser user;
        try {
            Result<DefaultUser> userResult = userRepository.getBriefByIdOrAccountOrMp(userId, true);
            if (userResult.isOK()) {
                user = userResult.data;
            } else {
                throw new Exception();
            }
        } catch (Exception e) {
            user = new DefaultUser();
            user.setId(userId);
            user.setName("未知用户");
            user.setDeptFullName("未知部门");
        }

        return user;
    }
}
