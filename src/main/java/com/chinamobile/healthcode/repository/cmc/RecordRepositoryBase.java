package com.chinamobile.healthcode.repository.cmc;

import com.chinamobile.healthcode.model.Record;
import com.chinamobile.healthcode.model.subject.UnitDescription;
import com.chinamobile.healthcode.repository.subject.UnitRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractMediaAvailableRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class RecordRepositoryBase<T extends Record> extends AbstractMediaAvailableRepository<T> {

    protected final DefaultDepartmentRepository departmentRepository;
    protected final RoleRepository roleRepository;
    protected final UnitRepository unitRepository;

    public RecordRepositoryBase(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            AbstractMediaRepository mediaRepository,
            Class<T> tClass,
            DefaultDepartmentRepository departmentRepository,
            RoleRepository roleRepository,
            UnitRepository unitRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, tClass);
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.unitRepository = unitRepository;
    }

    @Override
    public List<String> getMediaIds(List<T> items) {
        return items.stream()
                .filter(i -> StringUtils.hasLength(i.getAttachmentKeys()))
                .map(i -> (List<String>) ConverterUtil.json2Object(i.getAttachmentKeys(), new TypeToken<List<String>>() {
                }.getType()))
                .flatMap(i -> i.stream())
                .filter(i -> StringUtils.hasLength(i))
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Result<T> get(String id, boolean brief, String actorId) {
        Result<T> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{tClass.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(tClass, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{tClass.getSimpleName()});
        } else {
            parseAttachments(Collections.singletonList(_item.data), brief);
            parseCreator(Collections.singletonList(_item.data), actorId);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<T> me(int count, int index, String userId) {
        JinqStream<T> _query = stream(tClass).where(i -> userId.equals(i.getCreatorId()))
                .sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<T> _page = new PagingItems<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }
        _page.items = _query.toList();

        parseAttachments(_page.items, true);

        return _page;
    }

    public Result<String> save(T item, User user) throws InstantiationException, IllegalAccessException {
        Result<String> _id = new Result<>();

        boolean _isNew = false;

        Result<T> _item = get(item.getId(), true, user.getId());
        if (_item.isOK()) {
            if (!_item.data.getIsCreator()) {
                _id.setCode(Result.DATA_ACCESS_DENY);
                return _id;
            }
        } else {
            _item.data = tClass.newInstance();

            try {
                Field _field = item.getClass().getDeclaredField("unitId");
                _field.setAccessible(true);
                String _centerId = (String) _field.get(item);

                Result<UnitDescription> _center = unitRepository.get(_centerId);
                if (!_center.isOK()) {
                    return _id.pack(_center);
                }

                _item.data.setRegionId(_center.data.getRegionId());
                _item.data.setRegionFullName(_center.data.getRegionFullName());

                /* Result<Department> _region = departmentRepository.get(item.getRegionId(), true);
                if (!_region.isOK()) {
                    return _id.pack(_region);
                }

                _item.data.setRegionId(item.getRegionId());
                _item.data.setRegionFullName(_region.data.getFullName()); */
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }

            _isNew = true;
        }

        // 保存附件
        _item.data.setAttachmentKeys(CollectionUtils.isEmpty(item.getAttachmentIds()) ? null : ConverterUtil.toJson(item.getAttachmentIds()));

        copyProperties(item, _item.data, new String[]{"id", "regionId", "regionFullName", "attachmentKeys"});

        if (_isNew) {
            super.add(_item.data, user.getId());
        } else {
            super.update(_item.data, user.getId());
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result remove(String id, User user) {
        Result _success = new Result();

        Result<T> _item = get(id, true, user.getId());
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非本人或下级单位
        if (!_item.data.getIsCreator()
                && (Objects.equals(_item.data.getRegionId(), user.getDeptId()) || !departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                .anyMatch(i -> i.getId().equals(_item.data.getRegionId())))) {
            _item.setCode(Result.DATA_ACCESS_DENY);
            return _item;
        }

        super.remove(_item.data, user.getId());

        return _success;
    }

    @Transactional(readOnly = true)
    public List<Tuple4<String, String, String, Long>> statistics(String regionId, User user) {
        List<String> _regions = departmentRepository.subordinatesByUser(regionId, false, user);

        return stream(tClass)
                .leftOuterJoin(
                        (i, source) -> source.stream(Department.class),
                        (i, j) -> i.getRegionFullName().equals(j.getFullName()) || i.getRegionFullName().startsWith(j.getFullName() + Department.NAME_SEPARATOR)
                )
                .where(i -> i.getTwo() != null && _regions.contains(i.getTwo().getFullName()) && i.getTwo().getIsEnabled())
                .group(
                        i -> new Tuple3<>(i.getTwo().getSuperiorId(), i.getTwo().getId(), i.getTwo().getName()),
                        (key, stream) -> stream.select(i -> i.getTwo()).count()
                )
                .map(i -> new Tuple4<>(i.getOne().getOne(), i.getOne().getTwo(), i.getOne().getThree(), i.getTwo()))
                .collect(Collectors.toList());
    }

    public JinqStream<T> stream(String regionFullName, boolean includeSubordinates, User user) {
        JinqStream<T> _query = stream(tClass);

        if (roleRepository.isUserInRole(user.getId(), DefaultRoleRepository.ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _userId = user.getId();
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR;
            _query = includeSubordinates
                    ? _query.where(i -> _deptFullName.equals(i.getRegionFullName()) || i.getRegionFullName().startsWith(_pattern) || _userId.equals(i.getCreatorId()))
                    : _query.where(i -> _deptFullName.equals(i.getRegionFullName()) || _userId.equals(i.getCreatorId()));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR;
            _query = includeSubordinates
                    ? _query.where(i -> regionFullName.equals(i.getRegionFullName()) || i.getRegionFullName().startsWith(_pattern))
                    : _query.where(i -> regionFullName.equals(i.getRegionFullName()));
        }

        return _query;
    }

    void parseAttachments(List<T> items, boolean brief) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        for (T i : items) {
            i.setAttachmentIds(StringUtils.hasLength(i.getAttachmentKeys()) ? ConverterUtil.json2Object(i.getAttachmentKeys(), new TypeToken<List<String>>() {
            }.getType()) : null);
        }

        if (brief) {
            return;
        }

        List<String> _attachmentIds = getMediaIds(items);
        List<Media> _attachments = mediaRepository.query(_attachmentIds, null);

        for (T i : items) {
            List<Media> _temp = CollectionUtils.isEmpty(i.getAttachmentIds())
                    ? null
                    : _attachments.stream()
                    .filter(j -> i.getAttachmentIds().contains(j.getId()))
                    .collect(Collectors.toList());
            i.setAttachments(_temp);
        }
    }

    void parseCreator(List<T> items, String actorId) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        for (T i : items) {
            i.setIsCreator(Objects.equals(i.getCreatorId(), actorId));
        }
    }

}