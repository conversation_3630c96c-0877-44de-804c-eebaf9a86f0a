package com.chinamobile.healthcode.repository.cmc;

import com.chinamobile.healthcode.model.cmc.MassesVisit;
import com.chinamobile.healthcode.model.subject.UnitDescription;
import com.chinamobile.healthcode.repository.subject.UnitRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
public class MassesVisitRepository extends RecordRepositoryBase<MassesVisit> {

    public MassesVisitRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            AbstractMediaRepository mediaRepository,
            DefaultDepartmentRepository departmentRepository,
            DefaultRoleRepository roleRepository,
            UnitRepository unitRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, MassesVisit.class, departmentRepository, roleRepository, unitRepository);
    }

    public Result<MassesVisit> get(String id, String actorId) {
        Result<MassesVisit> _item = super.get(id, false, actorId);
        if (_item.isOK()) {
            parseUnit(Collections.singletonList(_item.data));
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<MassesVisit> fuzzy(int count, int index, String regionFullName, String unitId, boolean includeSubordinates, User user) {
        JinqStream<MassesVisit> _query = stream(regionFullName, includeSubordinates, user)
                .sortedDescendingBy(i -> i.getCreateTime());

        if (StringUtils.hasLength(unitId)) {
            _query = _query.where(i -> unitId.equals(i.getUnitId()));
        }

        PagingItems<MassesVisit> _page = new PagingItems(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }
        _page.items = _query.toList();

        parseAttachments(_page.items, true);
        parseUnit(_page.items);

        return _page;
    }

    @Override
    public PagingItems<MassesVisit> me(int count, int index, String userId) {
        PagingItems<MassesVisit> _page = super.me(count, index, userId);
        parseUnit(_page.items);
        return _page;
    }

    void parseUnit(List<MassesVisit> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        List<String> _unitIds = items.stream()
                .map(i -> i.getUnitId())
                .collect(Collectors.toList());
        List<UnitDescription> _units = unitRepository.queryCenters(_unitIds);

        for (MassesVisit i : items) {
            UnitDescription _unit = _units.stream()
                    .filter(j -> Objects.equals(i.getUnitId(), j.getId()))
                    .findFirst().orElse(null);

            i.setUnitName(Optional.ofNullable(_unit.getName()).orElse(null));
        }
    }

}