package com.chinamobile.healthcode.repository;

import com.chinamobile.healthcode.model.DataOperationLogEntity;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;

/**
 * 数据操作日志Repository
 */
@Repository
public class DataOperationLogRepository extends AbstractEntityRepository<DataOperationLogEntity> {

    public DataOperationLogRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                                      @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider) {
        super(entityManagerFactory, jinqJPAStreamProvider, DataOperationLogEntity.class);
    }

    /**
     * 保存操作日志
     */
    @Transactional
    public void saveLog(DataOperationLogEntity logEntity) {
        if (logEntity != null) {
            add(logEntity, null);
        }
    }

    /**
     * 异步保存操作日志
     */
    @Transactional
    public void saveLogAsync(DataOperationLogEntity logEntity) {
        // 这里可以使用@Async注解或者消息队列来实现异步保存
        saveLog(logEntity);
    }

    /**
     * 根据ID查询操作日志
     */
    @Transactional(readOnly = true)
    public Result<DataOperationLogEntity> findById(String id) {
        Result<DataOperationLogEntity> result = new Result<>();

        if (id == null || id.trim().isEmpty()) {
            result.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{DataOperationLogEntity.class.getSimpleName()});
            return result;
        }

        result.data = stream(DataOperationLogEntity.class)
                .where(log -> log.getId().equals(id))
                .findFirst()
                .orElse(null);

        if (result.data == null) {
            result.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{DataOperationLogEntity.class.getSimpleName()});
        }

        return result;
    }

    /**
     * 删除操作日志
     */
    @Transactional
    public Result<Void> deleteById(String id) {
        Result<Void> result = new Result<>();

        if (id == null || id.trim().isEmpty()) {
            result.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{"DataOperationLogEntity"});
            return result;
        }

        DataOperationLogEntity logEntity = stream(DataOperationLogEntity.class)
                .where(log -> log.getId().equals(id))
                .findFirst()
                .orElse(null);

        if (logEntity == null) {
            result.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{DataOperationLogEntity.class.getSimpleName()});
            return result;
        }

        getCurrentSession().remove(logEntity);
        return result;
    }
} 