package com.chinamobile.healthcode.repository;

import com.chinamobile.healthcode.model.FormTrace;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class FormTraceRepository extends AbstractEntityRepository<FormTrace> {

    final UserRepository userRepository;

    public FormTraceRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository userRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, FormTrace.class);
        this.userRepository = userRepository;
    }

    public Result add(String formId, FormTrace.ENUM_FORM_TYPE formType, String event, String opinion, String address, String actorId) {
        FormTrace _item = new FormTrace();
        _item.setFormId(formId);
        _item.setFormType(formType);
        _item.setEvent(event);
        _item.setOpinion(opinion);
        _item.setAddress(address);

        return super.add(_item, actorId);
    }

    @Transactional(readOnly = true)
    public List<FormTrace> query(String formId, FormTrace.ENUM_FORM_TYPE formType) {
        List<FormTrace> _items = stream(FormTrace.class).where(i -> formId.equals(i.getFormId()) && formType == i.getFormType())
                .sortedBy(i -> i.getCreateTime())
                .toList();

        List<String> _userIds = _items.stream()
                .map(i -> i.getCreatorId())
                .distinct()
                .collect(Collectors.toList());

        List<User> _users = userRepository.query(_userIds, null, null, null, null, null, null);
        for (FormTrace i : _items) {
            User _user = _users.stream()
                    .filter(j -> Objects.equals(i.getCreatorId(), j.getId()))
                    .findFirst().orElse(null);
            i.setCreatorName((_user == null || StringUtils.isEmpty(_user.getName())) ? i.getCreatorId() : _user.getName());
            i.setCreatorMp(_user == null ? null : _user.getMp());
        }

        return _items;
    }

}