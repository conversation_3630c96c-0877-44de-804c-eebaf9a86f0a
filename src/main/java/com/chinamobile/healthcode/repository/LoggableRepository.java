package com.chinamobile.healthcode.repository;

import com.chinamobile.healthcode.model.instability.InstabilityBaseEntity;
import com.chinamobile.healthcode.service.OperationLogService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.User;
import org.springframework.transaction.annotation.Transactional;

/**
 * 带日志功能的Repository基类
 * 继承自InstabilityBaseRepository，自动记录增删改操作日志
 */
public abstract class LoggableRepository<T extends InstabilityBaseEntity> extends com.chinamobile.healthcode.repository.instability.InstabilityBaseRepository<T> {

    protected final OperationLogService operationLogService;

    protected LoggableRepository(
            javax.persistence.EntityManagerFactory entityManagerFactory,
            org.jinq.jpa.JinqJPAStreamProvider jinqJPAStreamProvider,
            Class<T> tClass,
            javax.validation.Validator validator,
            com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository departmentRepository,
            com.chinamobile.sparrow.domain.infra.code.DefaultResultParser resultParser,
            OperationLogService operationLogService
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass, validator, departmentRepository, resultParser);
        this.operationLogService = operationLogService;
    }

    /**
     * 获取实体类
     */
    protected Class<T> getEntityClass() {
        // 通过反射获取tClass字段的值
        try {
            java.lang.reflect.Field tClassField = com.chinamobile.healthcode.repository.instability.InstabilityBaseRepository.class.getDeclaredField("tClass");
            tClassField.setAccessible(true);
            return (Class<T>) tClassField.get(this);
        } catch (Exception e) {
            // 如果无法获取，返回null
            return null;
        }
    }

    /**
     * 重写add方法，添加日志记录
     */
    @Override
    @Transactional
    public Result<?> add(T entity, String userId) {
        Result<?> result = super.add(entity, userId);
        
        if (result.isOK()) {
            // 记录新增日志
            User user = getCurrentUser(userId);
            if (user != null) {
                String description = String.format("新增%s", getEntityDisplayName(entity));
                operationLogService.logCreate(
                        entity.getClass().getSimpleName(),
                        entity.getId(),
                        entity,
                        user,
                        description
                );
            }
        } else {
            // 记录失败日志
            User user = getCurrentUser(userId);
            if (user != null) {
                String description = String.format("新增%s失败", getEntityDisplayName(entity));
                operationLogService.logFailure(
                        entity.getClass().getSimpleName(),
                        entity.getId(),
                        com.chinamobile.healthcode.model.OperationLog.OperationType.CREATE,
                        user,
                        description,
                        result.getCode()
                );
            }
        }
        
        return result;
    }

    /**
     * 重写update方法，添加日志记录
     */
    @Override
    @Transactional
    public Result<?> update(T entity, String userId) {
        // 获取更新前的数据
        T beforeEntity = null;
        try {
            Result<T> beforeResult = getById(entity.getId());
            if (beforeResult.isOK()) {
                beforeEntity = beforeResult.data;
            }
        } catch (Exception e) {
            // 忽略获取更新前数据的错误
        }
        
        Result<?> result = super.update(entity, userId);
        
        if (result.isOK()) {
            // 记录更新日志
            User user = getCurrentUser(userId);
            if (user != null) {
                String description = String.format("修改%s", getEntityDisplayName(entity));
                operationLogService.logUpdate(
                        entity.getClass().getSimpleName(),
                        entity.getId(),
                        beforeEntity,
                        entity,
                        user,
                        description
                );
            }
        } else {
            // 记录失败日志
            User user = getCurrentUser(userId);
            if (user != null) {
                String description = String.format("修改%s失败", getEntityDisplayName(entity));
                operationLogService.logFailure(
                        entity.getClass().getSimpleName(),
                        entity.getId(),
                        com.chinamobile.healthcode.model.OperationLog.OperationType.UPDATE,
                        user,
                        description,
                        result.getCode()
                );
            }
        }
        
        return result;
    }

    /**
     * 重写remove方法，添加日志记录
     */
    @Override
    @Transactional
    public Result<T> remove(String id, User user) {
        // 获取删除前的数据
        T beforeEntity = null;
        try {
            Result<T> beforeResult = getById(id);
            if (beforeResult.isOK()) {
                beforeEntity = beforeResult.data;
            }
        } catch (Exception e) {
            // 忽略获取删除前数据的错误
        }
        
        Result<T> result = super.remove(id, user);
        
        if (result.isOK() && beforeEntity != null) {
            // 记录删除日志
            String description = String.format("删除%s", getEntityDisplayName(beforeEntity));
            operationLogService.logDelete(
                    beforeEntity.getClass().getSimpleName(),
                    beforeEntity.getId(),
                    beforeEntity,
                    user,
                    description
            );
        } else if (!result.isOK()) {
            // 记录失败日志
            String description = String.format("删除%s失败", beforeEntity != null ? getEntityDisplayName(beforeEntity) : "数据");
            operationLogService.logFailure(
                    getEntityClass().getSimpleName(),
                    id,
                    com.chinamobile.healthcode.model.OperationLog.OperationType.DELETE,
                    user,
                    description,
                    result.getCode()
                );
        }
        
        return result;
    }

    /**
     * 重写saveOrUpdate方法，添加日志记录
     */
    @Override
    @Transactional
    public Result<String> saveOrUpdate(T entity, User user) {
        // 获取更新前的数据（如果是更新操作）
        T beforeEntity = null;
        if (entity.getId() != null) {
            try {
                Result<T> beforeResult = getById(entity.getId());
                if (beforeResult.isOK()) {
                    beforeEntity = beforeResult.data;
                }
            } catch (Exception e) {
                // 忽略获取更新前数据的错误
            }
        }
        
        Result<String> result = super.saveOrUpdate(entity, user);
        
        if (result.isOK()) {
            // 记录日志
            if (beforeEntity == null) {
                // 新增操作
                String description = String.format("新增%s", getEntityDisplayName(entity));
                operationLogService.logCreate(
                        entity.getClass().getSimpleName(),
                        entity.getId(),
                        entity,
                        user,
                        description
                );
            } else {
                // 更新操作
                String description = String.format("修改%s", getEntityDisplayName(entity));
                operationLogService.logUpdate(
                        entity.getClass().getSimpleName(),
                        entity.getId(),
                        beforeEntity,
                        entity,
                        user,
                        description
                );
            }
        } else {
            // 记录失败日志
            String operationType = beforeEntity == null ? "新增" : "修改";
            String description = String.format("%s%s失败", operationType, getEntityDisplayName(entity));
            operationLogService.logFailure(
                    entity.getClass().getSimpleName(),
                    entity.getId(),
                    beforeEntity == null ? 
                        com.chinamobile.healthcode.model.OperationLog.OperationType.CREATE :
                        com.chinamobile.healthcode.model.OperationLog.OperationType.UPDATE,
                    user,
                    description,
                    result.getCode()
            );
        }
        
        return result;
    }

    /**
     * 重写importFromExcel方法，添加日志记录
     */
    @Override
    @Transactional(propagation = org.springframework.transaction.annotation.Propagation.NOT_SUPPORTED)
    public Result<String> importFromExcel(
            org.springframework.web.multipart.MultipartFile multipartFile, 
            int sheetNum, 
            int headerRowNum, 
            User user
    ) {
        Result<String> result = super.importFromExcel(multipartFile, sheetNum, headerRowNum, user);
        
        if (result.isOK()) {
            // 记录导入日志
            String description = String.format("导入%s数据", getEntityClass().getSimpleName());
            operationLogService.logImport(
                    getEntityClass().getSimpleName(),
                    null, // 导入操作没有具体的实体ID
                    null, // 导入操作没有具体的实体对象
                    user,
                    description
            );
        } else {
            // 记录失败日志
            String description = String.format("导入%s数据失败", getEntityClass().getSimpleName());
            operationLogService.logFailure(
                    getEntityClass().getSimpleName(),
                    null,
                    com.chinamobile.healthcode.model.OperationLog.OperationType.IMPORT,
                    user,
                    description,
                    result.getCode()
            );
        }
        
        return result;
    }

    /**
     * 获取实体显示名称
     */
    protected String getEntityDisplayName(T entity) {
        if (entity == null) {
            Class<T> entityClass = getEntityClass();
            return entityClass != null ? entityClass.getSimpleName() : "未知实体";
        }
        
        // 尝试获取实体的名称字段
        try {
            if (entity instanceof com.chinamobile.healthcode.model.instability.InstabilityEventInfoBaseEntity) {
                com.chinamobile.healthcode.model.instability.InstabilityEventInfoBaseEntity eventEntity = 
                    (com.chinamobile.healthcode.model.instability.InstabilityEventInfoBaseEntity) entity;
                if (eventEntity.getTitle() != null && !eventEntity.getTitle().trim().isEmpty()) {
                    return eventEntity.getTitle();
                }
            }
            
            // 如果有regionFullName字段，使用它
            if (entity.getRegionFullName() != null && !entity.getRegionFullName().trim().isEmpty()) {
                return entity.getRegionFullName();
            }
            
            // 如果有id字段，使用它
            if (entity.getId() != null) {
                return entity.getId();
            }
        } catch (Exception e) {
            // 忽略获取显示名称的错误
        }
        
        Class<T> entityClass = getEntityClass();
        return entityClass != null ? entityClass.getSimpleName() : "未知实体";
    }

    /**
     * 获取当前用户
     */
    protected User getCurrentUser(String userId) {
        // 这里需要根据实际情况实现获取用户信息的方法
        // 可以注入UserRepository或者通过其他方式获取
        return null; // 子类需要重写此方法
    }
}
