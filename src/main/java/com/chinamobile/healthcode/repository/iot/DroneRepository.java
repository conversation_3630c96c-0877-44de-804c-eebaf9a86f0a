package com.chinamobile.healthcode.repository.iot;

import com.chinamobile.healthcode.model.iot.Drone;
import com.chinamobile.healthcode.model.iot.DroneRoute;
import com.chinamobile.healthcode.service.UAVService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.service.uav.lang.Route;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@Repository
public class DroneRepository extends AbstractEntityRepository<Drone> {

    final DefaultRoleRepository roleRepository;
    final UAVService uavService;

    public DroneRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            DefaultRoleRepository roleRepository,
            UAVService uavService
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Drone.class);
        this.roleRepository = roleRepository;
        this.uavService = uavService;
    }

    @Transactional(readOnly = true)
    public Result<Drone> get(String id) {
        Result<Drone> _item = new Result<>();

        _item.data = getCurrentSession().get(Drone.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Drone.class.getSimpleName()});
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<Drone> find(int count, int index, String regionFullName, Date from, Date to, User user) {
        JinqStream<Drone> _query = stream(regionFullName, from, to, user)
                .leftOuterJoin((i, source) -> source.stream(Drone.class), (i, j) -> j.getId().equals(i.getDroneId()))
                .where(i -> i.getTwo() != null)
                .select(Pair::getTwo)
                .sortedBy(Drone::getName);

        PagingItems<Drone> _page = new PagingItems<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.items = _query.toList();

        return _page;
    }

    @Transactional(readOnly = true)
    public Result<DroneRoute> getRoute(String routeId) {
        Result<DroneRoute> _route = new Result<>();

        _route.data = getCurrentSession().get(DroneRoute.class, routeId);
        if (_route.data == null) {
            _route.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{DroneRoute.class.getSimpleName()});
        }

        return _route;
    }

    @Transactional(readOnly = true)
    public Result<List<Route.Coordinate>> getRawRoute(String routeId) throws IOException {
        Result<List<Route.Coordinate>> _route = new Result<>();

        Drone _record = stream(DroneRoute.class).where(i -> routeId.equals(i.getId()))
                .leftOuterJoin((i, source) -> source.stream(Drone.class), (i, j) -> j.getId().equals(i.getDroneId()))
                .where(i -> i.getTwo() != null)
                .select(Pair::getTwo)
                .findFirst().orElse(null);

        if (_record == null) {
            _route.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{DroneRoute.class.getSimpleName()});
            return _route;
        }

        return uavService.droneFacade(_record.getClientId()).route(routeId);
    }

    @Transactional(readOnly = true)
    public PagingItems<DroneRoute> findRoutes(int count, int index, String droneId, String regionFullName, Date from, Date to, User user) {
        JinqStream<DroneRoute> _query = stream(regionFullName, from, to, user);

        if (StringUtils.hasLength(droneId)) {
            _query = _query.where(i -> droneId.equals(i.getDroneId()));
        }

        PagingItems<DroneRoute> _page = new PagingItems<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.items = _query.toList();

        return _page;
    }

    JinqStream<DroneRoute> stream(String regionFullName, Date from, Date to, User user) {
        JinqStream<DroneRoute> _query = stream(DroneRoute.class);

        if (roleRepository.isUserInRole(user.getId(), DefaultRoleRepository.ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR;
            _query = _query.where(i -> i.getRegionFullName() != null)
                    .where(i -> i.getRegionFullName().equals(_deptFullName) || i.getRegionFullName().startsWith(_pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR;
            _query = _query.where(i -> i.getRegionFullName() != null)
                    .where(i -> i.getRegionFullName().equals(regionFullName) || i.getRegionFullName().startsWith(_pattern));
        }

        if (from != null) {
            _query = _query.where(i -> !from.after(i.getStartTime()));
        }

        if (to != null) {
            _query = _query.where(i -> to.after(i.getEndTime()));
        }

        return _query;
    }

}