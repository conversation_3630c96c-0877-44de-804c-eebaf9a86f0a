package com.chinamobile.healthcode.repository.iot;

import com.chinamobile.healthcode.model.iot.Camera;
import com.chinamobile.healthcode.service.AndmuService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class CameraRepository extends AbstractEntityRepository<Camera> {

    final List<String> subscribeEvents;
    final AndmuService andmuService;

    public CameraRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${andmu.subscribe.camera}") String andmuSubscribeEvents, AndmuService andmuService
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Camera.class);

        this.subscribeEvents = Arrays.stream(andmuSubscribeEvents.split(","))
                .map(StringUtils::trimWhitespace)
                .collect(Collectors.toList());
        this.andmuService = andmuService;
    }

    @Transactional(readOnly = true)
    public Result<Camera> get(String id) {
        Result<Camera> _item = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Camera.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(Camera.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Camera.class.getSimpleName()});
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public List<Camera> find(List<String> ids) {
        return stream(Camera.class).where(i -> ids.contains(i.getId())).toList();
    }

    public void subscribe(List<String> ids) throws IOException {
        Map<String, List<Camera>> _group = find(ids).stream()
                .collect(Collectors.groupingBy(Camera::getClientId));
        _group.forEach((key, value) -> {
            List<String> _ids = value.stream()
                    .map(Camera::getId)
                    .collect(Collectors.toList());
            try {
                andmuService.subscribeFacade(key).subscribe(_ids, subscribeEvents);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

}