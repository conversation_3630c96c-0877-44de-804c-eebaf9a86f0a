package com.chinamobile.healthcode.repository.iot;

import com.chinamobile.healthcode.model.iot.Interphone;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;

@Repository
public class InterphoneRepository extends AbstractEntityRepository<Interphone> {

    final RoleRepository roleRepository;

    public InterphoneRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            RoleRepository roleRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Interphone.class);
        this.roleRepository = roleRepository;
    }

    @Transactional(readOnly = true)
    public Result<Interphone> get(String id) {
        Result<Interphone> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Interphone.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(Interphone.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Interphone.class.getSimpleName()});
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public List<Interphone> find(List<String> ids, Boolean isOnline) {
        JinqStream<Interphone> _query = stream(Interphone.class);

        if (ids != null) {
            _query = _query.where(i -> ids.contains(i.getId()));
        }

        if (isOnline != null) {
            _query = _query.where(i -> isOnline == i.getIsOnline());
        }

        return _query.toList();
    }

    @Transactional(readOnly = true)
    public PagingItems<Interphone> search(int count, int index, String regionFullName, String name, com.chinamobile.sparrow.domain.model.sys.User user) {
        JinqStream<Interphone> _query = stream(regionFullName, name, user);

        PagingItems<Interphone> _page = new PagingItems(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }
        _page.items = _query.toList();

        return _page;
    }

    public JinqStream<Interphone> stream(String regionFullName, String name, com.chinamobile.sparrow.domain.model.sys.User user) {
        JinqStream<Interphone> _query = stream(Interphone.class);

        if (roleRepository.isUserInRole(user.getId(), DefaultRoleRepository.ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> _deptFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        return _query;
    }

}