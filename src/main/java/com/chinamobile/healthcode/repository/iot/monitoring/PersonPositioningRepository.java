package com.chinamobile.healthcode.repository.iot.monitoring;

import com.chinamobile.healthcode.model.iot.PersonPositioning;
import com.chinamobile.healthcode.repository.iot.AlarmRepository;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class PersonPositioningRepository extends AbstractEntityRepository<PersonPositioning> {

    final AlarmRepository alarmRepository;
    final DefaultRoleRepository roleRepository;

    public PersonPositioningRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Lazy AlarmRepository alarmRepository,
            DefaultRoleRepository roleRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, PersonPositioning.class);
        this.alarmRepository = alarmRepository;
        this.roleRepository = roleRepository;
    }

    public List<PersonPositioning> findByAlarmIds(List<String> alarmIds) {
        PersonPositioning.ENUM_SOURCE_TYPE _type = PersonPositioning.ENUM_SOURCE_TYPE.ALARM;
        return stream(PersonPositioning.class).where(i -> _type == i.getSourceType() && alarmIds.contains(i.getSourceId()))
                .toList();
    }

    public List<PersonPositioning> track(String alarmId, String regionFullName, Date from, Date to, User user) {
        JinqStream<PersonPositioning> _query = stream(regionFullName, from, to, user);

        if (StringUtils.hasLength(alarmId)) {
            String _personId = stream(PersonPositioning.class).where(i -> i.getSourceId().equals(alarmId)).select(PersonPositioning::getPersonId)
                    .findFirst().orElse(null);
            if (StringUtils.hasLength(_personId)) {
                _query = _query.where(i -> _personId.equals(i.getPersonId()));
            } else {
                return new ArrayList<>();
            }
        }

        _query = _query.sortedBy(AbstractEntity::getCreateTime);

        return _query.toList();
    }

    JinqStream<PersonPositioning> stream(String regionFullName, Date from, Date to, User user) {
        JinqStream<PersonPositioning> _query = stream(PersonPositioning.class);

        if (roleRepository.isUserInRole(user.getId(), DefaultRoleRepository.ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR;
            _query = _query.where(i -> i.getRegionFullName() != null)
                    .where(i -> i.getRegionFullName().equals(_deptFullName) || i.getRegionFullName().startsWith(_pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR;
            _query = _query.where(i -> i.getRegionFullName() != null)
                    .where(i -> i.getRegionFullName().equals(regionFullName) || i.getRegionFullName().startsWith(_pattern));
        }

        if (from != null) {
            _query = _query.where(i -> !from.after(i.getCreateTime()));
        }

        if (to != null) {
            _query = _query.where(i -> to.after(i.getCreateTime()));
        }

        return _query;
    }

}