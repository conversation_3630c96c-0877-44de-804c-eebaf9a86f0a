package com.chinamobile.healthcode.repository.iot;

import com.chinamobile.healthcode.model.iot.Camera;
import com.chinamobile.healthcode.model.iot.SmokeDetector;
import com.chinamobile.healthcode.service.AndmuService;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class SmokeDetectorRepository extends AbstractEntityRepository<SmokeDetector> {

    final List<String> subscribeEvents;
    final AndmuService andmuService;

    public SmokeDetectorRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${andmu.subscribe.smoke-detector}") String andmuSubscribeEvents,
            AndmuService andmuService
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, SmokeDetector.class);

        this.subscribeEvents = Arrays.stream(andmuSubscribeEvents.split(","))
                .map(StringUtils::trimWhitespace)
                .collect(Collectors.toList());
        this.andmuService = andmuService;
    }

    public Result<SmokeDetector> get(String id) {
        Result<SmokeDetector> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Camera.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(SmokeDetector.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{SmokeDetector.class.getSimpleName()});
        }

        return _item;
    }

    public List<SmokeDetector> find(List<String> ids) {
        return stream(SmokeDetector.class).where(i -> ids.contains(i.getId())).toList();
    }

    public void subscribe(List<String> ids) throws IOException {
        Map<String, List<SmokeDetector>> _group = find(ids).stream()
                .collect(Collectors.groupingBy(SmokeDetector::getClientId));
        _group.forEach((key, value) -> {
            List<String> _ids = value.stream()
                    .map(SmokeDetector::getId)
                    .collect(Collectors.toList());
            try {
                andmuService.subscribeFacade(key).subscribeFireSafety(_ids, subscribeEvents);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

}