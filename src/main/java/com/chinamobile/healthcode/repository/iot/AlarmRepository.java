package com.chinamobile.healthcode.repository.iot;

import com.chinamobile.healthcode.model.Task;
import com.chinamobile.healthcode.model.iot.Alarm;
import com.chinamobile.healthcode.model.iot.Camera;
import com.chinamobile.healthcode.model.iot.Interphone;
import com.chinamobile.healthcode.model.iot.SmokeDetector;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@ErrorCode(module = "061")
public class AlarmRepository extends AbstractEntityRepository<Alarm> {

    final RoleRepository roleRepository;
    final AbstractMediaRepository mediaRepository;
    final CameraRepository cameraRepository;
    final InterphoneRepository interphoneRepository;
    final SmokeDetectorRepository smokeDetectorRepository;

    public AlarmRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            RoleRepository roleRepository,
            AbstractMediaRepository mediaRepository,
            CameraRepository cameraRepository,
            InterphoneRepository interphoneRepository,
            SmokeDetectorRepository smokeDetectorRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Alarm.class);
        this.roleRepository = roleRepository;
        this.mediaRepository = mediaRepository;
        this.cameraRepository = cameraRepository;
        this.interphoneRepository = interphoneRepository;
        this.smokeDetectorRepository = smokeDetectorRepository;
    }

    @Transactional(readOnly = true)
    public Result<Alarm> get(String id, boolean brief) {
        Result<Alarm> _item = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Alarm.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(Alarm.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Alarm.class.getSimpleName()});
        } else {
            parseAttachments(Collections.singletonList(_item.data), brief);
            parseDevice(Collections.singletonList(_item.data), brief);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public List<Alarm> find(List<String> ids) {
        return stream(Alarm.class).where(i -> ids.contains(i.getId())).toList();
    }

    @Transactional(readOnly = true)
    public List<Alarm> find(String type, Date from, Date to) {
        JinqStream<Alarm> _query = stream(Alarm.class);

        if (StringUtils.hasLength(type)) {
            _query = _query.where(i -> type.equals(i.getType()));
        }

        if (from != null) {
            _query = _query.where(i -> !from.after(i.getAlarmTime()));
        }

        if (to != null) {
            _query = _query.where(i -> !to.before(i.getAlarmTime()));
        }

        List<Alarm> _items = _query.toList();
        parseAttachments(_items, true);
        return _items;
    }

    @Transactional(readOnly = true)
    public PagingItems<Alarm> find(int count, int index, String type, String region, Date from, Date to, Task.ENUM_STATUS status, User user) {
        Task.ENUM_TYPE _subject = Task.ENUM_TYPE.设备告警;
        JinqStream<Pair<Alarm, Task>> _temp = stream(type, region, from, to, user)
                .leftOuterJoin((i, source) -> source.stream(Task.class), (i, j) -> i.getId().equals(j.getSubjectId()) && _subject.equals(j.getSubjectType()));

        if (status != null) {
            _temp = _temp.where(i -> status == i.getTwo().getStatus());
        }

        JinqStream<Alarm> _query;
        if (status == Task.ENUM_STATUS.TODO) {
            _query = stream(type, region, from, to, user);
            List<String> _alarmIdList = _temp.select(Pair::getOne).select(Alarm::getId).distinct().toList();
            List<String> _taskAlarmIdList = stream(Task.class).select(Task::getSubjectId).distinct().toList();

            _query = _query.where(i -> _alarmIdList.contains(i.getId()) || !_taskAlarmIdList.contains(i.getId())).distinct();
        } else {
            _query = _temp.select(Pair::getOne).distinct();
        }

        _query = _query.sortedDescendingBy(Alarm::getAlarmTime);

        PagingItems<Alarm> _page = new PagingItems<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.items = _query.toList();

        List<String> _ids = _page.items.stream()
                .map(Alarm::getId)
                .collect(Collectors.toList());
        List<Task> _tasks = stream(Task.class).where(i -> _subject.equals(i.getSubjectType()) && _ids.contains(i.getSubjectId()))
                .toList();

        _page.items.forEach(i -> {
            Task _task = _tasks.stream()
                    .filter(j -> Objects.equals(i.getId(), j.getSubjectId()))
                    .findFirst().orElse(null);
            i.setStatus(_task == null ? Task.ENUM_STATUS.TODO : _task.getStatus());
        });

        return _page;
    }

    @Transactional(readOnly = true)
    public List<Pair<String, Long>> distribution(String region, Date from, Date to, User user) {
        return stream(null, region, from, to, user)
                .group(Alarm::getType, (type, stream) -> stream.count())
                .toList();
    }

    public JinqStream<Alarm> stream(String type, String regionFullName, Date from, Date to, User user) {
        JinqStream<Alarm> _query = stream(Alarm.class);

        if (roleRepository.isUserInRole(user.getId(), DefaultRoleRepository.ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR;
            _query = _query.where(i -> i.getRegionFullName() != null)
                    .where(i -> i.getRegionFullName().equals(_deptFullName) || i.getRegionFullName().startsWith(_pattern));
        }

        if (StringUtils.hasLength(type)) {
            _query = _query.where(i -> i.getType() != null && i.getType().contains(type));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR;
            _query = _query.where(i -> i.getRegionFullName() != null)
                    .where(i -> i.getRegionFullName().equals(regionFullName) || i.getRegionFullName().startsWith(_pattern));
        }

        if (from != null) {
            _query = _query.where(i -> !from.after(i.getAlarmTime()));
        }

        if (to != null) {
            _query = _query.where(i -> to.after(i.getAlarmTime()));
        }

        return _query;
    }

    void parseDevice(List<Alarm> items, boolean brief) {
        if (brief) {
            return;
        }

        items.stream()
                .collect(Collectors.groupingBy(Alarm::getDeviceType))
                .forEach((type, alarms) -> {
                    List<String> _deviceIds = alarms.stream()
                            .map(Alarm::getDeviceId)
                            .collect(Collectors.toList());

                    switch (type) {
                        case CAMERA:
                            List<Camera> _cameras = cameraRepository.find(_deviceIds);
                            alarms.forEach(i -> i.setDevice(_cameras.stream()
                                    .filter(j -> Objects.equals(i.getDeviceId(), j.getId()))
                                    .findFirst().orElse(null)));
                            break;
                        case INTERPHONE:
                            List<Interphone> _interphones = interphoneRepository.find(_deviceIds, null);
                            alarms.forEach(i -> i.setDevice(_interphones.stream()
                                    .filter(j -> Objects.equals(i.getDeviceId(), j.getId()))
                                    .findFirst().orElse(null)));
                            break;
                        case SMOKE_DETECTOR:
                            List<SmokeDetector> _smokeDetectors = smokeDetectorRepository.find(_deviceIds);
                            alarms.forEach(i -> i.setDevice(_smokeDetectors.stream()
                                    .filter(j -> Objects.equals(i.getDeviceId(), j.getId()))
                                    .findFirst().orElse(null)));
                            break;

                    }
                });
    }

    void parseAttachments(List<Alarm> items, boolean brief) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        items.forEach(i -> {
            if (StringUtils.hasLength(i.getAttachmentKeys())) {
                List<String> _attachmentIds = ConverterUtil.json2Object(i.getAttachmentKeys(), new TypeToken<List<String>>() {
                }.getType());
                i.setAttachmentIds(_attachmentIds);
            }
        });

        if (brief) {
            return;
        }

        List<String> _attachmentIds = items.stream()
                .map(Alarm::getAttachmentIds)
                .filter(attachmentIds -> !CollectionUtils.isEmpty(attachmentIds))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        List<Media> _medias = mediaRepository.query(_attachmentIds, null);
        items.forEach(i -> {
            if (CollectionUtils.isEmpty(i.getAttachmentIds())) {
                return;
            }

            i.setAttachments(_medias.stream()
                    .filter(j -> i.getAttachmentIds().contains(j.getId()))
                    .collect(Collectors.toList()));
        });
    }

}