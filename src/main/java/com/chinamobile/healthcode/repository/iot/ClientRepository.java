package com.chinamobile.healthcode.repository.iot;

import com.chinamobile.healthcode.model.iot.Client;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;

@Repository
public class ClientRepository extends AbstractEntityRepository<Client> {

    public ClientRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Client.class);
    }

    public List<Client> find(List<Client.ENUM_PLATFORM> platforms) {
        JinqStream<Client> _query = stream(Client.class);

        if (!CollectionUtils.isEmpty(platforms)) {
            _query = _query.where(c -> platforms.contains(c.getPlatform()));
        }

        return _query.toList();
    }

}