package com.chinamobile.healthcode.repository.iot.monitoring;

import com.chinamobile.healthcode.model.iot.Person;
import com.chinamobile.healthcode.model.iot.PersonPositioning;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.stream.Collectors;

@Repository(value = "iotMonitoringPersonRepository")
public class PersonRepository extends AbstractEntityRepository<Person> {

    final PersonPositioningRepository personPositioningRepository;

    public PersonRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Lazy PersonPositioningRepository personPositioningRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Person.class);
        this.personPositioningRepository = personPositioningRepository;
    }

    public List<Person> findByAlarmIds(List<String> alarmIds) {
        List<String> _personIds = personPositioningRepository.findByAlarmIds(alarmIds).stream()
                .map(PersonPositioning::getPersonId)
                .filter(StringUtils::hasLength)
                .collect(Collectors.toList());

        return stream(Person.class).where(i -> _personIds.contains(i.getId())).toList();
    }

}