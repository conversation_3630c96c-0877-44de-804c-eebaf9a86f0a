package com.chinamobile.healthcode.repository.migrant;

import com.chinamobile.healthcode.model.FormTrace;
import com.chinamobile.healthcode.model.migrant.MigrantForm;
import com.chinamobile.sparrow.domain.infra.orm.jing.MySqlFunctions;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

@Component(value = "migrantStatisticService")
public class StatisticService extends AbstractJinqRepository {

    final FormRepository formRepository;

    public StatisticService(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, FormRepository formRepository) throws NoSuchMethodException {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.formRepository = formRepository;
    }

    /**
     * 新增工单
     *
     * @param begin
     * @param end
     * @param user
     * @return
     */
    @Transactional(readOnly = true)
    public List<Pair<String, Long>> dnf(Date begin, Date end, User user) {
        List<Pair<String, Long>> _query = scope(begin, end, user)
                .group(i -> MySqlFunctions.dateFormat(i.getCreateTime(), "%Y-%m-%d"), (key, value) -> value.count())
                .toList();

        List<Pair<String, Long>> _nums = _query.stream()
                .sorted(Comparator.comparing(i -> DateUtil.from(i.getOne(), "yyyy-MM-dd")))
                .collect(Collectors.toList());

        List<Pair<String, Long>> _counts = new ArrayList<>();
        for (Date i = begin; i.before(end); i = DateUtil.addDays(i, 1)) {
            String _i = DateUtil.toString(i, "yyyy-MM-dd");
            Pair<String, Long> _num = _nums.stream()
                    .filter(j -> Objects.equals(_i, j.getOne()))
                    .findFirst().orElse(null);
            _counts.add(_num == null ? new Pair<>(_i, 0L) : new Pair<>(_num.getOne(), _num.getTwo().longValue()));
        }

        return _counts;
    }

    @Transactional(readOnly = true)
    public List<Pair<String, List<Pair<String, Long>>>> process(Date begin, Date end, User user) {
        FormTrace.ENUM_FORM_TYPE _formType = FormTrace.ENUM_FORM_TYPE.抵返汕人员报备;

        List<Pair<String, Long>> _groups = scope(begin, end, user)
                .leftOuterJoin((i, session) -> session.stream(FormTrace.class), (i, trace) -> i.getId().equals(trace.getFormId()) && _formType == trace.getFormType())
                .where(i -> i.getTwo() != null)
                .group(i -> MySqlFunctions.dateFormat(i.getTwo().getCreateTime(), "%Y-%m-%d") + ":" + i.getTwo().getEvent(), (key, value) -> value.count())
                .sortedBy(i -> i.getOne())
                .toList();
        Map<String, List<Pair<String, Long>>> _dailyGroups = _groups.stream()
                .collect(Collectors.groupingBy(i -> i.getOne().split(":")[0]));

        List<Pair<String, List<Pair<String, Long>>>> _counts = new ArrayList<>();
        for (Map.Entry<String, List<Pair<String, Long>>> i : _dailyGroups.entrySet()) {
            _counts.add(new Pair<>(i.getKey(), i.getValue().stream()
                    .map(j -> new Pair<>(j.getOne().split(":")[1], j.getTwo()))
                    .sorted(Comparator.comparing(j -> j.getOne()))
                    .collect(Collectors.toList())));
        }

        return _counts.stream()
                .sorted(Comparator.comparing(i -> DateUtil.from(i.getOne(), "yyyy-MM-dd")))
                .collect(Collectors.toList());
    }

    JinqStream<MigrantForm> scope(Date begin, Date end, User user) {
        JinqStream<MigrantForm> _query;
        // 全局范围
        if (user == null) {
            _query = stream(MigrantForm.class);

            if (begin != null) {
                _query = _query.where(i -> !i.getCreateTime().before(begin));
            }

            if (end != null) {
                _query = _query.where(i -> i.getCreateTime().before(end));
            }
        }
        // 个人权限范围
        else {
            _query = formRepository.stream(null, null, null, null, null, null, null, begin, end, user);
        }

        return _query;
    }

}