package com.chinamobile.healthcode.repository.migrant;

import com.chinamobile.healthcode.model.FormTrace;
import com.chinamobile.healthcode.model.migrant.MigrantForm;
import com.chinamobile.healthcode.repository.FormTraceRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.POIUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Repository(value = "migrantFormRepository")
public class FormRepository extends AbstractEntityRepository<MigrantForm> {

    public static final String ADMIN_ROLE = "抵（返）汕报备-管理员";
    public static final String DISTRICT_STAFF_ROLE = "区县工作人员";
    public static final String SUBDISTRICT_STAFF_ROLE = "街道工作人员";
    public static final String COMMUNITY_STAFF_ROLE = "社区工作人员";

    final String redisKey;
    final DepartmentRepository departmentRepository;
    final DefaultRoleRepository roleRepository;
    final DictionaryRepository dictionaryRepository;
    final FormTraceRepository formTraceRepository;
    final RedisTemplate<String, Object> redisTemplate;

    public FormRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, @Value(value = "${migrant.redis-key.tip}") String redisKey, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository, DictionaryRepository dictionaryRepository, FormTraceRepository formTraceRepository, RedisTemplate<String, Object> redisTemplate) {
        super(entityManagerFactory, jinqJPAStreamProvider, MigrantForm.class);

        this.redisKey = redisKey;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.formTraceRepository = formTraceRepository;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 提单
     *
     * @param item
     * @param actorId
     * @return
     */
    @Override
    public Result add(MigrantForm item, String actorId) {
        item.setStatus(MigrantForm.ENUM_STATUS.DRAFT);

        super.add(item, actorId);

        // 记录历史
        formTraceRepository.add(item.getId(), FormTrace.ENUM_FORM_TYPE.抵返汕人员报备, "提交", null, null, actorId);

        return new Result();
    }

    /**
     * 提单人修改
     *
     * @param item
     * @param actorId
     * @return
     */
    @Override
    public Result update(MigrantForm item, String actorId) {
        Result _success = new Result();

        Result<MigrantForm> _item = get(item.getId());
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (!Objects.equals(actorId, _item.data.getCreatorId())) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 草稿或退回时可修改
        if (MigrantForm.ENUM_STATUS.DRAFT != _item.data.getStatus() && MigrantForm.ENUM_STATUS.DENIED != _item.data.getStatus()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        copyProperties(item, _item.data, new String[]{"id"});

        // 重置状态为草稿
        _item.data.setStatus(MigrantForm.ENUM_STATUS.DRAFT);

        super.update(_item.data, actorId);

        // 记录历史
        formTraceRepository.add(item.getId(), FormTrace.ENUM_FORM_TYPE.抵返汕人员报备, "修改", null, null, actorId);

        return _success;
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<MigrantForm> sort(JinqStream<MigrantForm> query, BiFunction<JinqStream<MigrantForm>, JinqStream.CollectComparable<MigrantForm, V>, JinqStream<MigrantForm>> compare, String field) {
        switch (field) {
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            default:
                return query;
        }
    }

    @Transactional(readOnly = true)
    public Result<MigrantForm> get(String id) {
        Result<MigrantForm> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{MigrantForm.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(MigrantForm.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<MigrantForm> queryByUserId(int count, int index, String mp, Date beginTime, Date endTime, String userId) {
        JinqStream<MigrantForm> _query = stream(MigrantForm.class).where(i -> userId.equals(i.getCreatorId()));

        if (StringUtils.hasLength(mp)) {
            _query = _query.where(i -> i.getMp() != null && i.getMp().contains(mp));
        }

        if (beginTime != null) {
            _query = _query.where(i -> !i.getCreateTime().before(beginTime));
        }

        if (endTime != null) {
            _query = _query.where(i -> !i.getCreateTime().after(endTime));
        }

        _query = _query.sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<MigrantForm> _page = new PagingItems(count, index);
        _page.total = _query.count();
        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }

        _page.items = _query.toList();
        for (MigrantForm i : _page.items) {
            i.setEditable(MigrantForm.ENUM_STATUS.DRAFT == i.getStatus() || MigrantForm.ENUM_STATUS.DENIED == i.getStatus());
        }

        return _page;
    }

    @Transactional(readOnly = true)
    public PagingItems<MigrantForm> fuzzy(int count, int index, List<SortField> sortFields, String name, String credentialNo, String mp, String destinationRegionFullName, MigrantForm.ENUM_STATUS status, Date departureBeginTime, Date departureEndTime, Date beginTime, Date endTime, User user) {
        JinqStream<MigrantForm> _query = stream(name, credentialNo, mp, destinationRegionFullName, status, departureBeginTime, departureEndTime, beginTime, endTime, user);

        if (CollectionUtils.isEmpty(sortFields)) {
            _query = _query.sortedDescendingBy(i -> i.getCreateTime());
        } else {
            _query = sort(_query, sortFields);
        }

        PagingItems<MigrantForm> _page = new PagingItems(count, index);
        _page.total = _query.count();
        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }

        _page.items = _query.toList();
        return _page;
    }

    @Transactional(readOnly = true)
    public String export(String name, String credentialNo, String mp, String destinationRegionFullName, MigrantForm.ENUM_STATUS status, Date departureBeginTime, Date departureEndTime, Date beginTime, Date endTime, User actor) throws IOException {
        List<MigrantForm> _items = fuzzy(-1, -1, null, name, credentialNo, mp, destinationRegionFullName, status, departureBeginTime, departureEndTime, beginTime, endTime, actor).items;

        return POIUtil.exportToWorkbookBase64String((book) -> {
            Sheet _sheet = book.createSheet();

            // 设置标题行
            Row _row = _sheet.createRow(0);
            _row.createCell(0).setCellValue("姓名");
            _row.createCell(1).setCellValue("手机号");
            _row.createCell(2).setCellValue("证件类型");
            _row.createCell(3).setCellValue("证件号码");
            _row.createCell(4).setCellValue("出发地");
            _row.createCell(5).setCellValue("目的地行政区划");
            _row.createCell(6).setCellValue("抵汕地址");
            _row.createCell(7).setCellValue("负责社区/村居");
            _row.createCell(8).setCellValue("抵汕时间");
            _row.createCell(9).setCellValue("途径城市");
            _row.createCell(10).setCellValue("使用交通工具");
            _row.createCell(11).setCellValue("最后一次核酸日期");
            _row.createCell(12).setCellValue("其它说明事项");
            _row.createCell(13).setCellValue("状态");
            _row.createCell(14).setCellValue("创建时间");

            boolean _requireMasked = !roleRepository.isUserInRoleCached(actor.getId(), FormRepository.ADMIN_ROLE);

            for (int i = 0; i < _items.size(); i++) {
                _row = _sheet.createRow(i + 1);
                _row.createCell(0).setCellValue(_items.get(i).getName());
                _row.createCell(1).setCellValue(_items.get(i).getMp());
                _row.createCell(2).setCellValue(_items.get(i).getCredentialType().name());

                String _credentialNo = _items.get(i).getCredentialNo();
                // 脱敏
                if (_requireMasked) {
                    if (_credentialNo != null && _credentialNo.length() > 16) {
                        _credentialNo = _credentialNo.substring(0, 2) + "**************" + _credentialNo.substring(16);
                    }
                }
                _row.createCell(3).setCellValue(_credentialNo);

                _row.createCell(4).setCellValue(_items.get(i).getDeparture());
                _row.createCell(5).setCellValue(_items.get(i).getDestinationRegionFullName());
                _row.createCell(6).setCellValue(_items.get(i).getAddress());
                _row.createCell(7).setCellValue(_items.get(i).getDestinationDeptFullNameDispatched());
                _row.createCell(8).setCellValue(_items.get(i).getDepartureDate() == null ? "" : _items.get(i).getDepartureDate().toString());
                _row.createCell(9).setCellValue(_items.get(i).getPath());
                _row.createCell(10).setCellValue(_items.get(i).getTransport());
                _row.createCell(11).setCellValue(_items.get(i).getLastNucleicAcidDate() == null ? "" : _items.get(i).getLastNucleicAcidDate().toString());
                _row.createCell(12).setCellValue(_items.get(i).getMemo());

                if (_items.get(i).getStatus() == null) {
                    _row.createCell(13).setCellValue("");
                } else {
                    switch (_items.get(i).getStatus()) {
                        case DRAFT:
                            _row.createCell(13).setCellValue("未审核");
                            break;
                        case DISPATCHED:
                            _row.createCell(13).setCellValue("已指派");
                            break;
                        case DENIED:
                            _row.createCell(13).setCellValue("已退回");
                            break;
                        case RECEIVED:
                            _row.createCell(13).setCellValue("已接收");
                            break;
                        case CLOSED:
                            _row.createCell(13).setCellValue("已关闭");
                            break;
                    }
                }

                _row.createCell(14).setCellValue(DateUtil.toString(_items.get(i).getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            }
        }, "xlsx");
    }

    public JinqStream<MigrantForm> stream(String name, String credentialNo, String mp, String destinationRegionFullName, MigrantForm.ENUM_STATUS status, Date departureBeginTime, Date departureEndTime, Date beginTime, Date endTime, User user) {
        JinqStream<MigrantForm> _query = jinqJPAStreamProvider.streamAll(getCurrentSession(), MigrantForm.class);

        // 管理员
        if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        }
        // 上级单位可以查阅所有下级单位
        else {
            List<String> _deptIds = new ArrayList<>();

            List<Department> _subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);
            if (!CollectionUtils.isEmpty(_subordinates)) {
                _deptIds.addAll(_subordinates.stream()
                        .map(i -> i.getId())
                        .collect(Collectors.toList()));
            }

            _query = CollectionUtils.isEmpty(_deptIds) ? _query.where(i -> false) : _query.where(i -> (i.getDestinationDeptIdDispatched() != null && _deptIds.contains(i.getDestinationDeptIdDispatched())) || (i.getDestinationDeptIdDispatched() == null && i.getDestinationRegionId() != null && _deptIds.contains(i.getDestinationRegionId())));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (StringUtils.hasLength(credentialNo)) {
            _query = _query.where(i -> i.getCredentialNo() != null && i.getCredentialNo().contains(credentialNo));
        }

        if (StringUtils.hasLength(mp)) {
            _query = _query.where(i -> i.getMp() != null && i.getMp().contains(mp));
        }

        if (StringUtils.hasLength(destinationRegionFullName)) {
            _query = _query.where(i -> i.getDestinationRegionFullName() != null && i.getDestinationRegionFullName().contains(destinationRegionFullName));
        }

        if (status != null) {
            _query = _query.where(i -> status == i.getStatus());
        }

        if (departureBeginTime != null) {
            _query = _query.where(i -> !i.getDepartureDate().before(departureBeginTime));
        }

        if (departureEndTime != null) {
            _query = _query.where(i -> i.getDepartureDate().before(departureEndTime));
        }

        if (beginTime != null) {
            _query = _query.where(i -> !i.getCreateTime().before(beginTime));
        }

        if (endTime != null) {
            _query = _query.where(i -> i.getCreateTime().before(endTime));
        }

        return _query;
    }

    /**
     * 街道指派
     *
     * @param ids
     * @param deptId
     * @param deptFullName
     * @param opinion
     * @param actorId
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Result dispatch(List<String> ids, String deptId, String deptFullName, String opinion, String actorId) {
        Result _success = new Result();

        if (CollectionUtils.isEmpty(ids)) {
            return _success;
        }

        List<MigrantForm> _items = stream(MigrantForm.class).where(i -> ids.contains(i.getId())).toList();
        for (MigrantForm i : _items) {
            // 已接收、已拒收、已关闭则不允许指派
            if (MigrantForm.ENUM_STATUS.RECEIVED == i.getStatus() || MigrantForm.ENUM_STATUS.DENIED == i.getStatus() || MigrantForm.ENUM_STATUS.CLOSED == i.getStatus()) {
                continue;
            }

            i.setDestinationDeptIdDispatched(deptId);
            i.setDestinationDeptFullNameDispatched(deptFullName);
            i.setStatus(MigrantForm.ENUM_STATUS.DISPATCHED);

            _success = super.update(i, actorId);
            if (!_success.isOK()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

                break;
            }

            // 记录历史
            formTraceRepository.add(i.getId(), FormTrace.ENUM_FORM_TYPE.抵返汕人员报备, "任务指派", opinion, deptFullName, actorId);
        }

        return _success;
    }

    /**
     * 任务退回
     *
     * @param id
     * @param actor
     * @return
     */
    public Result reject(String id, String opinion, User actor) {
        Result _success = new Result();

        Result<MigrantForm> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非下级单位则不能处理
        if (!belongTo(_item.data, actor.getDeptId())) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 非指派则不能退回
        if (MigrantForm.ENUM_STATUS.DISPATCHED != _item.data.getStatus()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 重置指派单位
        _item.data.setDestinationDeptIdDispatched(null);
        _item.data.setDestinationDeptFullNameDispatched(null);

        // 重置状态
        _item.data.setStatus(MigrantForm.ENUM_STATUS.DRAFT);

        super.update(_item.data, actor.getId());

        // 记录历史
        formTraceRepository.add(id, FormTrace.ENUM_FORM_TYPE.抵返汕人员报备, "任务退回", opinion, actor.getDeptId(), actor.getId());

        return _success;
    }


    /**
     * 拒收
     *
     * @param id
     * @param opinion
     * @param actor
     * @return
     */
    public Result refuse(String id, String opinion, User actor) {
        Result _success = new Result();

        Result<MigrantForm> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非下级单位则不能处理
        if (!belongTo(_item.data, actor.getDeptId())) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 非草稿、任务退回则不能拒收
        if (MigrantForm.ENUM_STATUS.DRAFT != _item.data.getStatus() && MigrantForm.ENUM_STATUS.REJECTED != _item.data.getStatus()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        _item.data.setStatus(MigrantForm.ENUM_STATUS.DENIED);
        super.update(_item.data, actor.getId());

        // 记录历史
        formTraceRepository.add(id, FormTrace.ENUM_FORM_TYPE.抵返汕人员报备, "退回", opinion, null, actor.getId());

        return _success;
    }

    /**
     * 任务关闭
     *
     * @param id
     * @param opinion
     * @param actor
     * @return
     */
    public Result close(String id, String opinion, User actor) {
        Result _success = new Result();

        Result<MigrantForm> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非下级单位则不能处理
        if (!belongTo(_item.data, actor.getDeptId())) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 已拒收、已关闭则不能关闭
        if (MigrantForm.ENUM_STATUS.DENIED == _item.data.getStatus() || MigrantForm.ENUM_STATUS.CLOSED == _item.data.getStatus()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        _item.data.setOpinion(opinion);
        _item.data.setStatus(MigrantForm.ENUM_STATUS.CLOSED);
        super.update(_item.data, actor.getId());

        // 记录历史
        formTraceRepository.add(id, FormTrace.ENUM_FORM_TYPE.抵返汕人员报备, "关闭", opinion, null, actor.getId());

        return _success;
    }

    @Transactional(readOnly = true)
    public String tip() {
        Object _val = redisTemplate.opsForValue().get(redisKey);
        if (_val == null) {
            Result<Dictionary> _item = dictionaryRepository.get("抵（返）汕报备登记", "温馨提示", true);
            String _str = _item.isOK() ? _item.data.getVal() : "因疫情防控需要，请所有抵（返）汕人员按要求报备。所有市外抵（返）汕货车司乘人员在抵（返）汕前需提前24小时向目的地作业场所主动报备车辆车牌号、计划抵汕时间、近7天旅居史，以及全部司乘人员姓名、身份证号、电话号码等情况，抵汕后迅速落实核酸检测“落地检”和“单人单管”。如不如实报备，造成疫情传播风险的，将依法追究法律责任。";
            redisTemplate.opsForValue().set(redisKey, _str);

            return _str;
        }

        return _val.toString();
    }

    boolean belongTo(MigrantForm item, String deptId) {
        // 优先判断指派地，再判断目的地
        String _deptId = StringUtils.isEmpty(item.getDestinationDeptIdDispatched()) ? item.getDestinationRegionId() : item.getDestinationDeptIdDispatched();

        List<Department> _subordinates = departmentRepository.subordinates(deptId, null, null);
        return _subordinates.stream().anyMatch(i -> Objects.equals(i.getId(), _deptId));
    }

}