package com.chinamobile.healthcode.repository.citizen;

import com.chinamobile.healthcode.model.citizen.ProfileValidation;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import org.apache.curator.shaded.com.google.common.reflect.TypeToken;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Repository
public class ProfileValidationRepository extends AbstractEntityRepository<ProfileValidation> {

    public static final List<String> INVALID_IDENTITY_NO_ERROR = Arrays.asList("018", "021");

    final DepartmentRepository departmentRepository;
    final DefaultRoleRepository roleRepository;

    public ProfileValidationRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, ProfileValidation.class);
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
    }

    @Transactional(readOnly = true)
    public List<ProfileValidation> query(List<String> profileIds) {
        return CollectionUtils.isEmpty(profileIds) ? new ArrayList<>() : stream(ProfileValidation.class)
                .where(i -> profileIds.contains(i.getProfileId()))
                .toList();
    }

    public void save(ProfileValidation item) {
        item.setCreateTime(new Date());

        getCurrentSession().saveOrUpdate(item);
    }

    @Transactional(readOnly = true)
    public List<String> getErrors(String profileId) {
        String _json = StringUtils.hasLength(profileId) ? stream(ProfileValidation.class)
                .where(i -> i.getProfileId().equals(profileId))
                .select(i -> i.getCodes())
                .findFirst().orElse(null) : null;
        return StringUtils.hasLength(_json) ? ConverterUtil.json2Object(_json, new TypeToken<List<String>>() {
        }.getType()) : new ArrayList<>();
    }

    @Transactional(readOnly = true)
    public long count(User user) {
        JinqStream<ProfileValidation> _query = jinqJPAStreamProvider.streamAll(getCurrentSession(), ProfileValidation.class);

        if (roleRepository.isUserInRoleCached(user.getId(), ProfileRepository.ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            Result<Department> _department = departmentRepository.get(user.getDeptId(), true);
            if (_department.isOK()) {
                String _deptFullName = _department.data.getFullName();
                _query = _query.where(i -> i.getSubdistrictFullName().equals(_deptFullName) || i.getSubdistrictFullName().startsWith(_deptFullName + "/"));
            } else {
                _query = _query.where(i -> false);
            }
        }

        return _query.count();
    }

    public void truncate() {
        getCurrentSession().createSQLQuery("TRUNCATE TABLE citizen_profile_validations;").executeUpdate();
    }

}