package com.chinamobile.healthcode.repository.citizen;

import com.chinamobile.healthcode.model.citizen.Profile;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.orm.jing.MySqlFunctions;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Repository
@Transactional(readOnly = true)
@ErrorCode(module = "051")
public class ProfileStatisticRepository extends AbstractJinqRepository {

    final List<String> focusCrowdIds;

    final ProfileRepository profileRepository;
    final DefaultDepartmentRepository departmentRepository;
    final DefaultRoleRepository roleRepository;
    final DictionaryRepository dictionaryRepository;
    final ThreadPoolExecutor threadPoolExecutor;

    public ProfileStatisticRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                                      @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
                                      @Value(value = "${citizen.statistic.crowds}") String focusCrowdIds,
                                      ProfileRepository profileRepository,
                                      DepartmentRepository departmentRepository,
                                      DefaultRoleRepository roleRepository,
                                      DictionaryRepository dictionaryRepository,
                                      ThreadPoolExecutor threadPoolExecutor) {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.focusCrowdIds = StringUtils.hasLength(focusCrowdIds) ? Arrays.asList(StringUtils.trimAllWhitespace(focusCrowdIds).split("[,，]")) : new ArrayList<>();
        this.profileRepository = profileRepository;
        this.departmentRepository = (DefaultDepartmentRepository) departmentRepository;
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.threadPoolExecutor = threadPoolExecutor;
    }

    /**
     * @param rootDeptId
     * @param user
     * @return 上级部门id、部门id、部门名称、人口
     */
    public List<Tuple4<String, String, String, Long>> thenTF(String rootDeptId, String crowdId, User user) {
        String _rootDeptId;
        if (StringUtils.hasLength(rootDeptId)) {
            if (roleRepository.isUserInRoleCached(user.getId(), ProfileRepository.ADMIN_ROLE)) {
                _rootDeptId = rootDeptId;
            } else {
                _rootDeptId = departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                        .anyMatch(i -> Objects.equals(rootDeptId, ((Department) i).getId())) ? rootDeptId : user.getDeptId();
            }
        } else {
            _rootDeptId = roleRepository.isUserInRoleCached(user.getId(), ProfileRepository.ADMIN_ROLE) ? null : user.getDeptId();
        }

        return tf(_rootDeptId, crowdId, user);
    }

    /**
     * @param rootDeptId
     * @param user
     * @return 上级部门id、部门id、部门名称、人口
     */
    public List<Tuple4<String, String, String, Long>> tf(String rootDeptId, String crowdId, User user) {
        JinqStream<Profile> _query = user == null ? stream(Profile.class) : profileRepository.stream(null, null, null, null, null, crowdId, null, null, null, null, null, null, null, user);
        List<String> _excludedRegionCodeList = dictionaryRepository
                .fuzzy("排除名单", "首页统计行政区域", true, null)
                .stream()
                .map(Dictionary::getVal)
                .collect(Collectors.toList());
        List<DefaultDepartment> _subordinates = departmentRepository.subordinates(rootDeptId, null, null);

        Department _root;
        int _temp;
        if (StringUtils.hasLength(rootDeptId)) {
            _root = _subordinates.stream()
                    .filter(i -> Objects.equals(i.getId(), rootDeptId))
                    .findFirst().orElse(null);

            _temp = _root.getLevel() + 1;
        } else {
            _temp = 1;
        }

        int _level = _temp;
        List<String> _titles = _level == 1 ? Arrays.asList("金平区", "龙湖区", "濠江区", "澄海区", "潮阳区", "潮南区", "南澳县") : _subordinates.stream()
                .filter(i -> i.getLevel() == _level)
                .filter(i -> !_excludedRegionCodeList.contains(i.getCode()))
                .map(i -> i.getFullName()).collect(Collectors.toList());

        _query = _query.where(i -> _titles.contains(MySqlFunctions.substringIndex(i.getSubdistrictFullName(), "/", _level)));
        List<Pair<String, Long>> _counts = _query.group(i -> MySqlFunctions.substringIndex(i.getSubdistrictFullName(), "/", _level), (key, value) -> value.count())
                .toList();

        return _titles.stream()
                .map(i -> {
                    Department _dept = _subordinates.stream()
                            .filter(j -> Objects.equals(i, j.getFullName()))
                            .findFirst().orElse(null);

                    return new Tuple4<>(_dept == null ? null : _dept.getSuperiorId(), _dept == null ? null : _dept.getId(), i, _counts.stream()
                            .filter(j -> i.equals(j.getOne()))
                            .map(j -> j.getTwo())
                            .findAny().orElse(0L));
                }).collect(Collectors.toList());
    }

    /**
     * @param rootDeptId
     * @param user
     * @return 上级部门id、部门id、部门名称、人口
     */
    public List<Tuple4<String, String, String, Long>> tfCrowds(String rootDeptId, User user) {
        JinqStream<Profile> _query = user == null ? stream(Profile.class) : profileRepository.stream(null, null, null, null, null, null, null, null, null, null, null, null, null, user);

        List<DefaultDepartment> _subordinates = departmentRepository.subordinates(rootDeptId, null, null);

        Department _root;
        int _temp;
        if (StringUtils.hasLength(rootDeptId)) {
            _root = _subordinates.stream()
                    .filter(i -> Objects.equals(i.getId(), rootDeptId))
                    .findFirst().orElse(null);

            _temp = _root.getLevel() + 1;
        } else {
            _temp = 1;
        }

        int _level = _temp;
        List<String> _titles = _level == 1 ? Arrays.asList("金平区", "龙湖区", "濠江区", "澄海区", "潮阳区", "潮南区", "南澳县") : _subordinates.stream()
                .filter(i -> i.getLevel() == _level)
                .map(i -> i.getFullName()).collect(Collectors.toList());

        _query = _query.where(i -> _titles.contains(MySqlFunctions.substringIndex(i.getSubdistrictFullName(), "/", _level)));
        List<Pair<String, Long>> _counts = _query.group(i -> MySqlFunctions.substringIndex(i.getSubdistrictFullName(), "/", _level), (key, value) -> value.count())
                .toList();

        return _titles.stream()
                .map(i -> {
                    Department _dept = _subordinates.stream()
                            .filter(j -> Objects.equals(i, j.getFullName()))
                            .findFirst().orElse(null);

                    return new Tuple4<>(_dept == null ? null : _dept.getSuperiorId(), _dept == null ? null : _dept.getId(), i, _counts.stream()
                            .filter(j -> i.equals(j.getOne()))
                            .map(j -> j.getTwo())
                            .findAny().orElse(0L));
                }).collect(Collectors.toList());
    }

    public Result<PopulationDTO> population(String divisionCode) {
        Result<PopulationDTO> _population = new Result<>();

        Result<DefaultDepartment> _department = departmentRepository.getByDivisionCode(divisionCode);
        if (!_department.isOK()) {
            _population.setCode(Result.ENUM_ERROR.P, 16);
            return _population;
        }

        CompletionService<Object> _service = new ExecutorCompletionService<>(threadPoolExecutor);

        Future<Object> _total = _service.submit(() -> {
            try (Session session = openSession()) {
                return profileRepository.stream(_department.data.getFullName(), session).count();
            }
        });

        Future<Object> _stay = _service.submit(() -> {
            try (Session session = openSession()) {
                List<Pair<Profile.ENUM_STAY, Long>> _query = profileRepository.stream(_department.data.getFullName(), session)
                        .group(i -> i.getStay(), (stay, stream) -> stream.count())
                        .toList();
                Long _prp = _query.stream()
                        .filter(i -> Profile.ENUM_STAY.常住 == i.getOne())
                        .map(i -> i.getTwo())
                        .findFirst().orElse(0L);
                Long _trp = _query.stream()
                        .filter(i -> Profile.ENUM_STAY.暂住 == i.getOne())
                        .map(i -> i.getTwo())
                        .findFirst().orElse(0L);
                return new Pair<>(_prp, _trp);
            }
        });

        Future<Object> _ageing = _service.submit(() -> {
            try (Session session = openSession()) {
                NativeQuery _query = session.createSQLQuery("SELECT COUNT(*) FROM citizen_profiles WHERE (subdistrictFullName = ? OR subdistrictFullName LIKE ?) and (length(credentialNo) = 17 or length(credentialNo) = 18) and (substr(credentialNo, 7, 4) + 0 <= ?)");
                _query.setParameter(1, _department.data.getFullName());
                _query.setParameter(2, _department.data.getFullName() + "/%");
                _query.setParameter(3, new Date().getYear() + 1900 - 60);

                return ((BigInteger) _query.getSingleResult()).longValue();
            }
        });

        _population.data = new PopulationDTO();
        try {
            _population.data.setTotal((Long) _total.get());
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
        try {
            Pair<Long, Long> _nums = (Pair<Long, Long>) _stay.get();
            _population.data.setPrp(_nums.getOne());
            _population.data.setTrp(_nums.getTwo());
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
        try {
            _population.data.setAgeing((Long) _ageing.get());
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }

        return _population;
    }

    public Result<List<org.springframework.data.util.Pair<String, Long>>> crowds(String divisionCode) {
        Result<List<org.springframework.data.util.Pair<String, Long>>> _counts = new Result<>();

        Result<DefaultDepartment> _department = departmentRepository.getByDivisionCode(divisionCode);
        if (!_department.isOK()) {
            _counts.setCode(Result.ENUM_ERROR.P, 16);
            return _counts;
        }

        List<Dictionary> _dictionary = profileRepository.crowds().stream()
                .filter(i -> focusCrowdIds.contains(i.getVal()))
                .collect(Collectors.toList());

        CompletionService<Object> _service = new ExecutorCompletionService<>(threadPoolExecutor);
        List<Future> _futures = new ArrayList<>();

        for (String i : focusCrowdIds) {
            Future<Object> _future = _service.submit(() -> {
                String _title = _dictionary.stream()
                        .filter(j -> Objects.equals(i, j.getVal()))
                        .map(j -> j.getName())
                        .findFirst().orElse(i);

                try (Session session = openSession()) {
                    NativeQuery _query = session.createSQLQuery("SELECT COUNT(*) FROM citizen_profiles WHERE (subdistrictFullName = ? OR subdistrictFullName LIKE ?) AND crowdIdsJSON LIKE ?");
                    _query.setParameter(1, _department.data.getFullName());
                    _query.setParameter(2, _department.data.getFullName() + "/%");
                    _query.setParameter(3, "%\"" + i + "\"%");

                    return org.springframework.data.util.Pair.of(_title, ((BigInteger) _query.getSingleResult()).longValue());
                }
            });

            _futures.add(_future);
        }

        _counts.data = new ArrayList<>();
        for (Future i : _futures) {
            try {
                _counts.data.add((org.springframework.data.util.Pair<String, Long>) i.get());
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        }
        return _counts;
    }

    public static class PopulationDTO {

        // 总人口
        long total;

        // 常住人口
        long prp;

        // 暂住人口
        long trp;

        // 老龄人口
        long ageing;

        public long getTotal() {
            return total;
        }

        public void setTotal(long total) {
            this.total = total;
        }

        public long getPrp() {
            return prp;
        }

        public void setPrp(long prp) {
            this.prp = prp;
        }

        public long getTrp() {
            return trp;
        }

        public void setTrp(long trp) {
            this.trp = trp;
        }

        public long getAgeing() {
            return ageing;
        }

        public void setAgeing(long ageing) {
            this.ageing = ageing;
        }

    }

}
