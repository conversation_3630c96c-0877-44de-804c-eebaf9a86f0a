package com.chinamobile.healthcode.repository.citizen;

import com.chinamobile.healthcode.model.citizen.CovidVaccineInoculation;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.POIUtil;
import com.google.common.reflect.TypeToken;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Repository
public class CovidVaccineInoculationRepository extends AbstractEntityRepository<CovidVaccineInoculation> {

    public static final String ADMIN_ROLE = "金凤码-管理员";

    final DepartmentRepository departmentRepository;
    final DictionaryRepository dictionaryRepository;
    final DefaultRoleRepository roleRepository;

    public CovidVaccineInoculationRepository(@Qualifier(value = "vaccineSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "vaccineJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, DepartmentRepository departmentRepository, DictionaryRepository dictionaryRepository, DefaultRoleRepository roleRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, CovidVaccineInoculation.class);

        this.departmentRepository = departmentRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.roleRepository = roleRepository;
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<CovidVaccineInoculation> sort(JinqStream<CovidVaccineInoculation> query, BiFunction<JinqStream<CovidVaccineInoculation>, JinqStream.CollectComparable<CovidVaccineInoculation, V>, JinqStream<CovidVaccineInoculation>> compare, String field) {
        switch (field) {
            case "injectDate":
                return compare.apply(query, i -> (V) i.getInjectDate());
            default:
                return query;
        }
    }

    @Transactional(readOnly = true)
    public PagingItems<CovidVaccineInoculation> fuzzy(int count, int index, List<SortField> sortFields, String name, String credentialNo, String mp, String subdistrictFullName, String crowdId, Integer ageMin, Integer ageMax, Integer injectTimes, Date beginTime, Date endTime, User user) {
        JinqStream<CovidVaccineInoculation> _query = stream(name, credentialNo, mp, subdistrictFullName, crowdId, ageMin, ageMax, injectTimes, beginTime, endTime, user);

        if (CollectionUtils.isEmpty(sortFields)) {
            _query = _query.sortedDescendingBy(i -> i.getCreateTime());
        } else {
            _query = sort(_query, sortFields);
        }

        PagingItems<CovidVaccineInoculation> _page = new PagingItems(count, index);
        _page.total = _query.count();
        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }

        _page.items = _query.toList();
        parseCrowdName(_page.items);
        return _page;
    }

    @Transactional(readOnly = true)
    public List<CovidVaccineInoculation> query(List<String> credentialNos) {
        return CollectionUtils.isEmpty(credentialNos) ? new ArrayList<>() : stream(CovidVaccineInoculation.class)
                .where(i -> credentialNos.contains(i.getCredentialNo()))
                .distinct()
                .toList();
    }

    @Transactional(readOnly = true)
    public String export(String name, String credentialNo, String mp, String subdistrictFullName, String crowdId, Integer ageMin, Integer ageMax, Integer injectTimes, Date beginTime, Date endTime, User user) throws IOException {
        List<CovidVaccineInoculation> _items = fuzzy(-1, -1, null, name, credentialNo, mp, subdistrictFullName, crowdId, ageMin, ageMax, injectTimes, beginTime, endTime, user).items;

        return POIUtil.exportToWorkbookBase64String((book) -> {
            Sheet _sheet = book.createSheet();

            // 设置标题行
            Row _row = _sheet.createRow(0);
            _row.createCell(0).setCellValue("姓名");
            _row.createCell(1).setCellValue("填报类型");
            _row.createCell(2).setCellValue("证件类型");
            _row.createCell(3).setCellValue("证件号码");
            _row.createCell(4).setCellValue("手机号码");
            _row.createCell(5).setCellValue("年龄");
            _row.createCell(6).setCellValue("特殊人群");
            _row.createCell(7).setCellValue("工作单位/车牌号码");
            _row.createCell(8).setCellValue("居住类型");
            _row.createCell(9).setCellValue("居住行政区划");
            _row.createCell(10).setCellValue("详细地址");
            _row.createCell(11).setCellValue("接种疫苗");
            _row.createCell(12).setCellValue("接种针次");
            _row.createCell(13).setCellValue("接种时间");

            boolean _requireMasked = !roleRepository.isUserInRoleCached(user.getId(), FormRepository.ADMIN_ROLE);

            for (int i = 0; i < _items.size(); i++) {
                _row = _sheet.createRow(i + 1);
                _row.createCell(0).setCellValue(_items.get(i).getName());
                _row.createCell(1).setCellValue(_items.get(i).getActor().name());
                _row.createCell(2).setCellValue(_items.get(i).getCredentialType().name());

                String _credentialNo = _items.get(i).getCredentialNo();
                // 脱敏
                if (_requireMasked) {
                    if (_credentialNo != null && _credentialNo.length() > 16) {
                        _credentialNo = _credentialNo.substring(0, 2) + "**************" + _credentialNo.substring(16);
                    }
                }
                _row.createCell(3).setCellValue(_credentialNo);

                _row.createCell(4).setCellValue(_items.get(i).getMp());
                _row.createCell(5).setCellValue(_items.get(i).getAge());

                String _json = _items.get(i).getCrowdNamesJSON();
                if (StringUtils.hasLength(_json)) {
                    List<String> _names = ConverterUtil.json2Object(_json, new TypeToken<List<String>>() {
                    }.getType());
                    String _str = String.join(", ", _names);
                    _row.createCell(6).setCellValue(_str);
                } else {
                    _row.createCell(6).setCellValue("");
                }

                _row.createCell(7).setCellValue(_items.get(i).getWorkplace());
                _row.createCell(8).setCellValue(_items.get(i).getStay().name());
                _row.createCell(9).setCellValue(_items.get(i).getSubdistrictFullName());
                _row.createCell(10).setCellValue(_items.get(i).getAddress());
                _row.createCell(11).setCellValue(_items.get(i).getVaccine());
                _row.createCell(12).setCellValue(_items.get(i).getInjectTimes());
                _row.createCell(13).setCellValue(_items.get(i).getInjectDate() == null ? "" : _items.get(i).getInjectDate().toString());
            }
        }, "xlsx");
    }

    public JinqStream<CovidVaccineInoculation> stream(String name, String credentialNo, String mp, String subdistrictFullName, String crowdId, Integer ageMin, Integer ageMax, Integer injectTimes, Date beginTime, Date endTime, User user) {
        JinqStream<CovidVaccineInoculation> _query = jinqJPAStreamProvider.streamAll(getCurrentSession(), CovidVaccineInoculation.class);

        if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            Result<Department> _department = departmentRepository.get(user.getDeptId(), true);
            if (_department.isOK()) {
                String _deptFullName = _department.data.getFullName();
                _query = _query.where(i -> i.getSubdistrictFullName().equals(_deptFullName) || i.getSubdistrictFullName().startsWith(_deptFullName + "/"));
            } else {
                _query = _query.where(i -> false);
            }
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (StringUtils.hasLength(credentialNo)) {
            _query = _query.where(i -> i.getCredentialNo() != null && i.getCredentialNo().contains(credentialNo));
        }

        if (StringUtils.hasLength(mp)) {
            _query = _query.where(i -> i.getMp() != null && i.getMp().contains(mp));
        }

        if (StringUtils.hasLength(subdistrictFullName)) {
            _query = _query.where(i -> i.getSubdistrictFullName() != null && i.getSubdistrictFullName().contains(subdistrictFullName));
        }

        if (StringUtils.hasLength(crowdId)) {
            String _crowdQuery = "[]".equals(crowdId) ? crowdId : String.format("\"%s\"", crowdId);
            _query = _query.where(i -> i.getCrowdIdsJSON() != null && i.getCrowdIdsJSON().contains(_crowdQuery));
        }

        if (ageMin != null) {
            _query = _query.where(i -> i.getAge() != null && i.getAge() >= ageMin);
        }

        if (ageMax != null) {
            _query = _query.where(i -> i.getAge() != null && i.getAge() <= ageMax);
        }

        if (injectTimes != null) {
            _query = _query.where(i -> i.getInjectTimes() == injectTimes);
        }

        if (beginTime != null) {
            _query = _query.where(i -> !i.getInjectDate().before(beginTime));
        }

        if (endTime != null) {
            _query = _query.where(i -> i.getInjectDate().before(endTime));
        }

        return _query;
    }

    @Transactional(readOnly = true)
    public List<Dictionary> crowds() {
        return dictionaryRepository.fuzzy("金凤码-特殊人群", null, null, null);
    }

    void parseCrowdName(List<CovidVaccineInoculation> items) {
        Map<String, String> _crowds = crowds().stream().collect(Collectors.toMap(Dictionary::getVal, Dictionary::getName));

        for (CovidVaccineInoculation i : items) {
            if (StringUtils.isEmpty(i.getCrowdIdsJSON())) {
                continue;
            }

            List<String> _ids = ConverterUtil.json2Object(i.getCrowdIdsJSON(), new TypeToken<List<String>>() {
            }.getType());
            i.setCrowdNamesJSON(ConverterUtil.toJson(_ids.stream()
                    .map(_crowds::get)
                    .collect(Collectors.toList())));
        }
    }

}