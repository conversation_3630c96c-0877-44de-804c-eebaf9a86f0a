package com.chinamobile.healthcode.repository.citizen;

import com.chinamobile.healthcode.model.citizen.Profile;
import com.chinamobile.healthcode.model.citizen.ProfileAssociation;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
@ErrorCode(module = "052")
public class ProfileAssociationRepository extends AbstractEntityRepository<ProfileAssociation> {

    final ProfileRepository profileRepository;
    final DepartmentRepository departmentRepository;

    public ProfileAssociationRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, ProfileRepository profileRepository, DepartmentRepository departmentRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, ProfileAssociation.class);
        this.profileRepository = profileRepository;
        this.departmentRepository = departmentRepository;
    }

    public Result<Boolean> add(ProfileAssociation item, User user) {
        Result<Boolean> _isOwner = new Result<>();

        if (StringUtils.isEmpty(item.getMp()) || StringUtils.isEmpty(item.getAssociatedCredentialNo())) {
            _isOwner.setCode(Result.ENUM_ERROR.P, 1);
            return _isOwner;
        }

        // 过滤重复关联
        String _mp = item.getMp();
        String _credentialNo = item.getAssociatedCredentialNo();
        if (stream(ProfileAssociation.class).where(i -> _mp.equals(i.getMp()) && _credentialNo.equals(i.getAssociatedCredentialNo()))
                .findFirst().isPresent()) {
            _isOwner.setCode(Result.ENUM_ERROR.P, 2);
            return _isOwner;
        }

        Profile.ENUM_ACTOR _actor = Profile.ENUM_ACTOR.本人;
        if (item.getActor() == _actor) {
            ProfileAssociation _item = stream(ProfileAssociation.class).where(i -> _mp.equals(i.getMp()) && _actor == i.getActor())
                    .findFirst().orElse(null);
            if (_item != null) {
                _isOwner.setCode(Result.ENUM_ERROR.P, 3, new Object[]{_item.getAssociatedCredentialNo()});
                return _isOwner;
            }
        }

        Result<Profile> _associated = profileRepository.getBriefByCredentialNo(_credentialNo);
        if (_associated.isOK()) {
            // 本人或未设置所有人，则设置所有人
            if (item.getActor() == _actor || StringUtils.isEmpty(_associated.data.getOwnerId())) {
                _associated.data.setOwnerId(user.getId());
                _associated.data.setActor(item.getActor());

                Result _success = profileRepository.update(_associated.data, user.getId());
                if (!_success.isOK()) {
                    return _isOwner.pack(_success);
                }
            }
        } else {
            // 证件号码不存在则新增
            _associated.data = new Profile(item.getAssociatedUsername(), item.getAssociatedCredentialNo(), item.getActor());
            _associated.data.setOwnerId(user.getId());

            Result _success = profileRepository.add(_associated.data, user.getId());
            if (!_success.isOK()) {
                return _isOwner.pack(_success);
            }
        }

        Result _success = super.add(item, user.getId());
        if (!_success.isOK()) {
            return _isOwner.pack(_success);
        }

        _isOwner.data = Objects.equals(_associated.data.getOwnerId(), user.getId());
        return _isOwner;
    }

    public Result update(String credentialNo, Profile.ENUM_ACTOR actor, User user) {
        Result _success = new Result();

        String _mp = user.getMp();
        ProfileAssociation _item = stream(ProfileAssociation.class).where(i -> _mp.equals(i.getMp()) && credentialNo.equals(i.getAssociatedCredentialNo()))
                .findFirst().orElse(null);
        if (_item == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND);
            return _success;
        }

        if (!Objects.equals(_item.getMp(), user.getMp())) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 设置为本人，需判断唯一性
        Profile.ENUM_ACTOR _actor = Profile.ENUM_ACTOR.本人;
        if (actor == _actor) {
            ProfileAssociation _item2 = stream(ProfileAssociation.class).where(i -> _mp.equals(i.getMp()) && _actor == i.getActor())
                    .findFirst().orElse(null);
            if ((_item == null && _item2 != null) || (_item != null && _item2 != null && !Objects.equals(_item.getId(), _item2.getId()))) {
                _success.setCode(Result.ENUM_ERROR.P, 3, new Object[]{_item.getAssociatedCredentialNo()});
                return _success;
            }
        }

        _item.setActor(actor);
        return super.update(_item, user.getId());
    }

    public Result<String> updateProfile(Profile profile, User user) {
        Result<String> _profileId = new Result<>();

        Result<Profile> _profile = profileRepository.getBrief(profile.getId());
        if (!_profile.isOK()) {
            return _profileId.pack(_profile);
        }

        // 非所有人不允许修改
        if (!Objects.equals(user.getId(), _profile.data.getOwnerId())) {
            _profileId.setCode(Result.DATA_ACCESS_DENY);
            return _profileId;
        }

        String _mp = user.getMp();
        String _credentialNo = profile.getCredentialNo();
        Profile.ENUM_ACTOR _actor = Profile.ENUM_ACTOR.本人;
        ProfileAssociation _item = stream(ProfileAssociation.class).where(i -> _mp.equals(i.getMp()) && _credentialNo.equals(i.getAssociatedCredentialNo()))
                .findFirst().orElse(null);
        // 设置为本人，需判断唯一性
        if (profile.getActor() == _actor) {
            ProfileAssociation _item2 = stream(ProfileAssociation.class).where(i -> _mp.equals(i.getMp()) && _actor == i.getActor())
                    .findFirst().orElse(null);
            if ((_item == null && _item2 != null) || (_item != null && _item2 != null && !Objects.equals(_item.getId(), _item2.getId()))) {
                _profileId.setCode(Result.ENUM_ERROR.P, 3, new Object[]{_item.getAssociatedCredentialNo()});
                return _profileId;
            }
        }

        // 如果不存在关联，则新增
        if (_item == null) {
            _item = new ProfileAssociation();
            _item.setMp(user.getMp());
            _item.setAssociatedCredentialNo(profile.getCredentialNo());
            _item.setActor(profile.getActor());

            Result _success = super.add(_item, user.getId());
            if (!_success.isOK()) {
                return _profileId.pack(_success);
            }
        }
        // 如果存在关联，则修改
        else {
            _item.setActor(profile.getActor());

            Result _success = super.update(_item, user.getId());
            if (!_success.isOK()) {
                return _profileId.pack(_success);
            }
        }

        BeanUtils.copyProperties(profile, _profile.data, "id", "credentialNo", "ownerId");

        // 设置网格名称
        String _subdistrictFullName = null;
        if (StringUtils.hasLength(profile.getSubdistrictId())) {
            Result<Department> _dept = departmentRepository.get(profile.getSubdistrictId(), true);
            if (_dept.isOK()) {
                _subdistrictFullName = _dept.data.getFullName();
            }
        }
        _profile.data.setSubdistrictFullName(_subdistrictFullName);

        // 设置特殊人群
        if (profile.getCrowdIds() != null) {
            _profile.data.setCrowdIdsJSON(ConverterUtil.toJson(profile.getCrowdIds()));
        }

        Result _success = profileRepository.update(_profile.data, user.getId());
        if (!_success.isOK()) {
            return _profileId.pack(_success);
        }

        _profileId.data = _profile.data.getId();
        return _profileId;
    }

    public Result remove(String credentialNo, User user) {
        Result _success = new Result<>();

        Result<Profile> _profile = profileRepository.getBriefByCredentialNo(credentialNo);
        if (!_profile.isOK()) {
            return _success.pack(_profile);
        }

        // 删除维护关系
        if (Objects.equals(user.getId(), _profile.data.getOwnerId())) {
            _profile.data.setOwnerId(null);

            _success = profileRepository.update(_profile.data, user.getId());
            if (!_success.isOK()) {
                return _success;
            }
        }

        // 删除关联关系
        String _mp = user.getMp();
        ProfileAssociation _item = stream(ProfileAssociation.class).where(i -> i.getMp().equals(_mp) && i.getAssociatedCredentialNo().equals(credentialNo))
                .findFirst().orElse(null);
        if (_item != null) {
            getCurrentSession().remove(_item);
        }

        return _success;
    }

    @Transactional(readOnly = true)
    public Result<Profile> getProfile(User user) {
        Result<Profile> _profile = new Result<>();

        String _mp = user.getMp();
        Profile.ENUM_ACTOR _actor = Profile.ENUM_ACTOR.本人;
        if (StringUtils.hasLength(_mp)) {
            _profile.data = stream(ProfileAssociation.class).where(i -> _mp.equals(i.getMp()) && _actor == i.getActor())
                    .leftOuterJoin((i, session) -> session.stream(Profile.class), (i, profile) -> i.getAssociatedCredentialNo().equals(profile.getCredentialNo()))
                    .where(i -> i.getTwo() != null)
                    .select(i -> i.getTwo())
                    .findFirst().orElse(null);
        }

        if (_profile.data == null) {
            _profile.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        }

        return _profile;
    }

    @Transactional(readOnly = true)
    public List<Profile> queryProfiles(boolean includeOneself, User user) {
        Profile.ENUM_ACTOR _actor = Profile.ENUM_ACTOR.本人;

        List<Profile> _profiles = new ArrayList<>();

        String _mp = user.getMp();
        if (StringUtils.hasLength(_mp)) {
            JinqStream<Pair<ProfileAssociation, Profile>> _relations = stream(ProfileAssociation.class).where(i -> _mp.equals(i.getMp()))
                    .where(i -> includeOneself == true || _actor != i.getActor())
                    .leftOuterJoin((i, session) -> session.stream(Profile.class), (i, profile) -> i.getAssociatedCredentialNo().equals(profile.getCredentialNo()))
                    .where(i -> i.getTwo() != null);

            _profiles = _relations.toList().stream()
                    .map(i -> {
                        if (Objects.equals(i.getTwo().getOwnerId(), user.getId())) {
                            i.getTwo().setActor(i.getOne().getActor());
                            i.getTwo().setIsEditable(true);
                            return i.getTwo();
                        } else {
                            return new Profile(i.getTwo().getId(), i.getTwo().getName(), i.getTwo().getCredentialNo(), i.getOne().getActor());
                        }
                    })
                    .collect(Collectors.toList());
        }

        _profiles = _profiles.stream()
                .sorted(Comparator.comparing(i -> i.getCredentialNo()))
                .collect(Collectors.toList());

        for (Profile i : _profiles) {
            i.setIsEditable(Objects.equals(i.getOwnerId(), user.getId()));
        }

        profileRepository.parseCrowdName(_profiles);

        return _profiles;
    }

}