package com.chinamobile.healthcode.repository.citizen;

import com.chinamobile.healthcode.model.citizen.CitizenForm;
import com.chinamobile.sparrow.domain.infra.orm.jing.MySqlFunctions;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

@Component(value = "citizenStatisticService")
public class StatisticService extends AbstractJinqRepository {

    final FormRepository formRepository;
    final DefaultRoleRepository roleRepository;
    final DepartmentRepository departmentRepository;

    public StatisticService(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, FormRepository formRepository, DefaultRoleRepository roleRepository, DepartmentRepository departmentRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider);

        this.formRepository = formRepository;
        this.roleRepository = roleRepository;
        this.departmentRepository = departmentRepository;
    }

    /**
     * 每日新增工单
     *
     * @param begin
     * @param end
     * @param user
     * @return
     */
    @Transactional(readOnly = true)
    public List<Pair<String, Long>> dnf(Date begin, Date end, User user) {
        List<Pair<String, Long>> _query = scope(begin, end, user)
                .group(i -> MySqlFunctions.dateFormat(i.getCreateTime(), "%Y-%m-%d"), (key, value) -> value.count())
                .toList();

        List<Pair<String, Long>> _nums = _query.stream()
                .sorted(Comparator.comparing(i -> DateUtil.from(i.getOne(), "yyyy-MM-dd")))
                .collect(Collectors.toList());

        List<Pair<String, Long>> _counts = new ArrayList<>();
        for (Date i = begin; i.before(end); i = DateUtil.addDays(i, 1)) {
            String _i = DateUtil.toString(i, "yyyy-MM-dd");
            Pair<String, Long> _num = _nums.stream()
                    .filter(j -> Objects.equals(_i, j.getOne()))
                    .findFirst().orElse(null);
            _counts.add(_num == null ? new Pair<>(_i, 0L) : new Pair<>(_num.getOne(), _num.getTwo().longValue()));
        }

        return _counts;
    }

    /**
     * 单位累计工单
     *
     * @param user
     * @return
     */
    @Transactional(readOnly = true)
    public List<Pair<String, Long>> tf(User user) {
        List<Pair<String, Long>> _counts;

        JinqStream<CitizenForm> _query = scope(null, null, user);

        int _index;
        List<String> _regionList;
        List<Department> _departmentList = departmentRepository.subordinates(user.getDeptId(), null, null);
        if (roleRepository.isUserInRoleCached(user.getId(), FormRepository.ADMIN_ROLE)) {
            _regionList = Arrays.asList("金平区", "龙湖区", "潮阳区", "潮南区", "澄海区", "濠江区", "南澳县");
            _index = 1;
        } else if (roleRepository.isUserInRoleCached(user.getId(), com.chinamobile.healthcode.repository.migrant.FormRepository.DISTRICT_STAFF_ROLE)) {
            _regionList = _departmentList.stream().filter(dept -> dept.getLevel() == 2).map(Department::getFullName).collect(Collectors.toList());
            _index = 2;
        } else if (roleRepository.isUserInRoleCached(user.getId(), com.chinamobile.healthcode.repository.migrant.FormRepository.SUBDISTRICT_STAFF_ROLE)) {
            _regionList = _departmentList.stream().filter(dept -> dept.getLevel() == 3).map(Department::getFullName).collect(Collectors.toList());
            _index = 3;
        } else {
            _regionList = _departmentList.stream().filter(dept -> dept.getLevel() == 4).map(Department::getFullName).collect(Collectors.toList());
            _index = 4;
        }

        _query = _query.where(i -> _regionList.contains(MySqlFunctions.substringIndex(i.getDeptFullName(), "/", _index)));
        _counts = _query.group(i -> MySqlFunctions.substringIndex(i.getDeptFullName(), "/", _index), (key, value) -> value.count())
                .toList();

        return _regionList.stream().map(region ->
                new Pair<>(region, _counts.stream().filter(pair -> pair.getOne().equals(region)).map(Pair::getTwo).findAny().orElse(0L))
        ).collect(Collectors.toList());
    }

    JinqStream<CitizenForm> scope(Date begin, Date end, User user) {
        JinqStream<CitizenForm> _query;
        // 全局范围
        if (user == null) {
            _query = stream(CitizenForm.class);

            if (begin != null) {
                _query = _query.where(i -> !i.getCreateTime().before(begin));
            }

            if (end != null) {
                _query = _query.where(i -> i.getCreateTime().before(end));
            }
        }
        // 个人权限范围
        else {
            _query = formRepository.stream(null, null, null, null, null, null, null, begin, end, user);
        }

        return _query;
    }

}