package com.chinamobile.healthcode.repository.citizen;

import com.chinamobile.healthcode.model.citizen.IKH;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.List;

@Repository
@Transactional(readOnly = true)
public class IKHRepository extends AbstractJinqRepository {

    public IKHRepository(@Qualifier(value = "healthCommissionSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "healthCommissionJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider) {
        super(entityManagerFactory, jinqJPAStreamProvider);
    }

    public List<IKH> query(List<String> credentialNos) {
        return CollectionUtils.isEmpty(credentialNos) ? new ArrayList<>() : stream(IKH.class)
                .where(i -> credentialNos.contains(i.getCredentialNo()))
                .distinct()
                .toList();
    }

}