package com.chinamobile.healthcode.repository.citizen;

import cn.binarywang.wx.miniapp.api.WxMaQrcodeService;
import com.chinamobile.healthcode.model.citizen.Grid;
import com.chinamobile.healthcode.model.citizen.GridManager;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.QRCodeUtil;
import com.google.common.reflect.TypeToken;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import javax.persistence.EntityManagerFactory;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class GridRepository extends AbstractEntityRepository<Grid> {

    public static final String GRID_ADMIN_ROLE = "金凤码-网格管理员";

    static final int QRCODE_DEFAULT_SIZE = 300;
    static final int LOGO_DEFAULT_SIZE = 100;

    final String qrCodeContentTemplate;
    final String qrCodeLogoPath;
    final String qrcodeEnvVersion;
    final UserRepository userRepository;
    final DepartmentRepository<Department> departmentRepository;
    final WxMaQrcodeService wxMaQrcodeService;

    public GridRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, @Value(value = "${citizen.qr-code-content-template}") String qrCodeContentTemplate, @Value(value = "${citizen.qr-code-logo-path}") String qrCodeLogoPath, @Value(value = "${wx.ma.jfm.qrcode-env-version}") String qrcodeEnvVersion, UserRepository userRepository, DepartmentRepository departmentRepository, WxMaQrcodeService wxMaQrcodeService) {
        super(entityManagerFactory, jinqJPAStreamProvider, Grid.class);

        this.qrCodeContentTemplate = qrCodeContentTemplate;
        this.qrCodeLogoPath = qrCodeLogoPath;
        this.qrcodeEnvVersion = qrcodeEnvVersion;
        this.userRepository = userRepository;
        this.departmentRepository = departmentRepository;
        this.wxMaQrcodeService = wxMaQrcodeService;
    }

    @Override
    public Result add(Grid item, String actorId) {
        Result<String> _id = new Result<>();

        // 设置成员id
        List<Department> _members = pruning(item.getMemberIds());
        List<String> _memberIds = _members.stream()
                .map(i -> i.getId())
                .collect(Collectors.toList());
        item.setMemberIdsJSON(ConverterUtil.toJson(_memberIds));
        List<String> _memberFullNames = _members.stream()
                .map(i -> i.getFullName())
                .collect(Collectors.toList());
        item.setMemberFullNamesJSON(ConverterUtil.toJson(_memberFullNames));

        item.setCode(item.getId());
        item.setFullName(item.getName());

        Result _success = super.add(item, actorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = item.getId();
        return _id;
    }

    @Override
    public Result update(Grid item, String actorId) {
        Result<String> _id = new Result<>();

        Result<Grid> _item = get(item.getId());
        if (!_item.isOK()) {
            return _id.pack(_item);
        }

        // 修改成员
        if (!Objects.equals(item.getMemberIdsJSON(), _item.data.getMemberIdsJSON())) {
            List<Department> _members = pruning(item.getMemberIds());
            List<String> _memberIds = _members.stream()
                    .map(i -> i.getId())
                    .collect(Collectors.toList());
            item.setMemberIdsJSON(ConverterUtil.toJson(_memberIds));
            List<String> _memberFullNames = _members.stream()
                    .map(i -> i.getFullName())
                    .collect(Collectors.toList());
            item.setMemberFullNamesJSON(ConverterUtil.toJson(_memberFullNames));
        }

        copyProperties(item, _item.data, new String[]{"id", "code", "fullName", "memberIdsJson"});
        _item.data.setFullName(_item.data.getName());

        Result _success = super.update(_item.data, actorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = item.getId();
        return _id;
    }

    @Transactional(readOnly = true)
    public Result<Grid> get(String id) {
        Result<Grid> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Grid.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(Grid.class, id);
        if (_item.data == null || !_item.data.getIsEnabled()) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<Grid> fuzzy(int count, int index, String name, String type, String member) {
        JinqStream<Grid> _query = stream(Grid.class).where(i -> i.getIsEnabled());

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (StringUtils.hasLength(type)) {
            _query = _query.where(i -> i.getType() != null && i.getType().contains(type));
        }

        if (StringUtils.hasLength(member)) {
            _query = _query.where(i -> i.getMemberFullNamesJSON() != null && i.getMemberFullNamesJSON().contains(member));
        }

        _query = _query.sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<Grid> _page = new PagingItems(count, index);
        _page.total = _query.count();
        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }

        _page.items = _query.toList();
        return _page;
    }

    @Transactional(readOnly = true)
    public List<Grid> query() {
        return stream(Grid.class).where(i -> i.getIsEnabled()).toList();
    }

    public Result remove(String id, String actorId) {
        Result _success = new Result<>();

        Result<Grid> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        List<GridManager> _relations = stream(GridManager.class)
                .where(i -> id.equals(i.getGridId()))
                .toList();
        for (GridManager i : _relations) {
            getCurrentSession().remove(i);
        }

        _item.data.setIsEnabled(false);
        return super.update(_item.data, actorId);
    }

    @Transactional(readOnly = true)
    public List<User> getUsers(String id) {
        List<String> _userIds = stream(GridManager.class).where(i -> id.equals(i.getGridId()))
                .select(i -> i.getUserId())
                .toList();

        return CollectionUtils.isEmpty(_userIds) ? new ArrayList<>() : userRepository.query(_userIds, null, null, null, null, true, null);
    }

    public Result addUser(String id, String userId, String actorId) {
        Result _success = new Result();

        Result<Grid> _grid = get(id);
        if (!_grid.isOK()) {
            return _success.pack(_grid);
        }

        GridManager _manager = stream(GridManager.class).where(i -> i.getGridId().equals(id) && i.getUserId().equals(userId))
                .findFirst().orElse(null);
        if (_manager == null) {
            _manager = new GridManager();
            _manager.setGridId(id);
            _manager.setUserId(userId);
            _manager.setCreatorId(actorId);
            _manager.setCreateTime(new Date());

            getCurrentSession().save(_manager);
        }

        return _success;
    }

    public Result removeUser(String id, String userId) {
        Result _success = new Result();

        GridManager _manager = stream(GridManager.class)
                .where(i -> i.getGridId().equals(id) && i.getUserId().equals(userId))
                .findFirst().orElse(null);
        if (_manager == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND);
            return _success;
        }

        getCurrentSession().remove(_manager);

        return _success;
    }

    @Transactional(readOnly = true)
    public Pair<List<Grid>, List<Department>> getUserScope(User user) {
        // 获取虚拟网格
        String _userId = user.getId();
        List<Grid> _grids = stream(GridManager.class)
                .where(i -> _userId.equals(i.getUserId()))
                .leftOuterJoin((i, session) -> session.stream(Grid.class), (i, grid) -> grid.getId().equals(i.getGridId()) && grid.getIsEnabled())
                .where(i -> i.getTwo() != null)
                .select(i -> i.getTwo())
                .toList();

        // 获取虚拟网格下属部门
        List<String> _deptIds = new ArrayList<>();
        for (String i : _grids.stream()
                .map(i -> i.getMemberIdsJSON())
                .collect(Collectors.toList())) {
            List<String> _ids = ConverterUtil.json2Object(i, new TypeToken<List<String>>() {
            }.getType());
            if (!CollectionUtils.isEmpty(_ids)) {
                _deptIds.addAll(_ids);
            }
        }

        // 获取行政网格下属部门
        _deptIds.add(user.getDeptId());

        List<Department> _departments = pruning(_deptIds);

        return new Pair<>(_grids.stream()
                .distinct()
                .collect(Collectors.toList()), _departments);
    }

    /**
     * 获取普通二维码
     *
     * @param content
     * @param desc
     * @return
     * @throws Exception
     */
    public File getQRCode(String content, String desc) throws Exception {
        String _content = String.format(qrCodeContentTemplate, content);

        File _logo = ResourceUtils.getFile(qrCodeLogoPath);

        return getQRCode(_content, _logo, desc, QRCODE_DEFAULT_SIZE, QRCODE_DEFAULT_SIZE, LOGO_DEFAULT_SIZE, LOGO_DEFAULT_SIZE);
    }

    /**
     * 获取小程序二维码
     *
     * @param type
     * @param id
     * @param desc
     * @param page
     * @return
     * @throws Exception
     */
    public File getWxMaQRCode(String type, String id, String desc, String page) throws Exception {
        String _scene = type + "&" + id;

        byte[] _bytes = wxMaQrcodeService.createWxaCodeUnlimitBytes(_scene, page, false, qrcodeEnvVersion, 430, true, null, false);
        File _file = File.createTempFile(UUID.randomUUID().toString(), ".png");
        FileCopyUtils.copy(_bytes, _file);

        BufferedImage _image = ImageIO.read(_file);

        // 插入文字
        QRCodeUtil.pressText(_image, desc, new Color(217, 60, 55), new Font("微软雅黑", Font.BOLD, 24));

        ImageIO.write(_image, "png", _file);

        return _file;
    }

    List<Department> pruning(List<String> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return new ArrayList<>();
        }

        List<Department> _departments = departmentRepository.query(departmentIds, null, null, true, null).stream()
                .sorted(Comparator.comparing(i -> i.getLevel()))
                .collect(Collectors.toList());
        int _root = _departments.stream()
                .min(Comparator.comparing(Department::getLevel))
                .map(i -> i.getLevel()).orElse(1);
        int _leaf = _departments.stream()
                .max(Comparator.comparing(Department::getLevel))
                .map(i -> i.getLevel()).orElse(_root);

        List<Department> _pruning = new ArrayList<>();
        for (int i = _root; i <= _leaf; i++) {
            int _i = i;
            _departments.stream()
                    .filter(j -> _i == j.getLevel())
                    .forEach(j -> {
                        for (Department k : _pruning) {
                            if (j.getCode().startsWith(k.getCode() + ".")) {
                                return;
                            }
                        }

                        _pruning.add(j);
                    });
        }

        return _pruning;
    }

    File getQRCode(String content, File logo, String desc, int qrCodeWidth, int qrCodeHeight, int imageWidth, int imageHeight) throws Exception {
        // 创建二维码
        BufferedImage _image = QRCodeUtil.createImage(content, qrCodeWidth, qrCodeHeight, 24);

        // 插入图片
        QRCodeUtil.pressImage(_image, logo, imageWidth, imageHeight);

        // 插入文字
        QRCodeUtil.pressText(_image, desc, new Color(14, 144, 210), new Font("微软雅黑", Font.BOLD, 24));

        File _file = File.createTempFile(UUID.randomUUID().toString(), ".png");
        ImageIO.write(_image, "png", _file);

        return _file;
    }

}
