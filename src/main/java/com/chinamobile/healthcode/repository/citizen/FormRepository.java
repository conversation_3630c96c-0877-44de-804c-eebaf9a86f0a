package com.chinamobile.healthcode.repository.citizen;

import com.chinamobile.healthcode.model.FormTrace;
import com.chinamobile.healthcode.model.citizen.CitizenForm;
import com.chinamobile.healthcode.model.citizen.Grid;
import com.chinamobile.healthcode.model.citizen.HistoricalForm;
import com.chinamobile.healthcode.repository.FormTraceRepository;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.GpsUtil;
import com.chinamobile.sparrow.domain.util.POIUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.reflect.TypeToken;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.hibernate.Transaction;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Repository(value = "citizenFormRepository")
@ErrorCode(module = "053")
public class FormRepository extends AbstractEntityRepository<CitizenForm> {

    public static final String ADMIN_ROLE = "金凤码-管理员";

    final GridRepository gridRepository;
    final FormTraceRepository formTraceRepository;
    final DepartmentRepository departmentRepository;
    final DictionaryRepository dictionaryRepository;
    final DefaultRoleRepository roleRepository;

    final DefaultResultParser resultParser;

    public FormRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, GridRepository gridRepository, FormTraceRepository formTraceRepository, DepartmentRepository departmentRepository, DictionaryRepository dictionaryRepository, DefaultRoleRepository roleRepository, DefaultResultParser resultParser) {
        super(entityManagerFactory, jinqJPAStreamProvider, CitizenForm.class);

        this.gridRepository = gridRepository;
        this.formTraceRepository = formTraceRepository;
        this.departmentRepository = departmentRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.roleRepository = roleRepository;
        this.resultParser = resultParser;
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<CitizenForm> sort(JinqStream<CitizenForm> query, BiFunction<JinqStream<CitizenForm>, JinqStream.CollectComparable<CitizenForm, V>, JinqStream<CitizenForm>> compare, String field) {
        switch (field) {
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            default:
                return query;
        }
    }

    @Transactional(readOnly = true)
    public Result<CitizenForm> get(String id) {
        Result<CitizenForm> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{CitizenForm.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(CitizenForm.class, id);
        if (_item == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        }
        parseCrowdName(ImmutableList.of(_item.data));

        return _item;
    }

    @Transactional(readOnly = true)
    public Result<CitizenForm> getByUserId(String deptId, String mp) {
        Result<CitizenForm> _item = new Result<>();

        CitizenForm.ENUM_ACTOR _actor = CitizenForm.ENUM_ACTOR.本人;
        _item.data = stream(CitizenForm.class).where(i -> deptId.equals(i.getDeptId()) || deptId.equals(i.getGridId()))
                .where(i -> mp.equals(i.getMp()) && _actor == i.getActor())
                .findFirst().orElse(null);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public CitizenForm getByUser(String deptId, String mp) {
        // 获取本人关联表单
        List<CitizenForm> _items = stream(CitizenForm.class)
                .where(i -> mp.equals(i.getMp()))
                .sortedDescendingBy(i -> i.getCreateTime())
                .toList();

        // 本人已在本地打卡
        CitizenForm.ENUM_ACTOR _actor = CitizenForm.ENUM_ACTOR.本人;
        CitizenForm _item = _items.stream()
                .filter(i -> deptId.equals(i.getDeptId()) && _actor == i.getActor())
                .findFirst().orElse(null);
        if (_item != null) {
            return _item;
        }

        // 本人已在他地打卡
        _item = _items.stream()
                .filter(i -> _actor == i.getActor())
                .findFirst().orElse(null);
        if (_item != null) {
            return _item;
        }

        // 他人已打卡
        return _items.stream()
                .filter(i -> mp.equals(i.getMp()))
                .findFirst().orElse(null);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Result<String> add(CitizenForm item, User user) {
        Result<String> _id = new Result<>();

        String _deptId = StringUtils.hasLength(item.getDeptId()) ? item.getDeptId() : item.getGridId();
        Result<CitizenForm> _item = getByUserId(_deptId, user.getMp());
        if (_item.isOK()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST, new Object[]{CitizenForm.class.getSimpleName()});
            return _id;
        }

        // 设置网格名称
        String _deptFullName = null;
        if (StringUtils.hasLength(item.getDeptId())) {
            Result<Department> _dept = departmentRepository.get(item.getDeptId(), true);
            if (_dept.isOK()) {
                _deptFullName = _dept.data.getFullName();
            }
        } else {
            Result<Grid> _grid = gridRepository.get(item.getGridId());
            if (_grid.isOK()) {
                _deptFullName = _grid.data.getFullName();
            }
        }
        item.setDeptFullName(_deptFullName);

        // 设置特殊人群
        if (item.getCrowdIds() != null) {
            item.setCrowdIdsJSON(ConverterUtil.toJson(item.getCrowdIds()));
        }

        item.setActor(CitizenForm.ENUM_ACTOR.本人);

        if (Objects.nonNull(item.getLnt()) && Objects.nonNull(item.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(Double.parseDouble(item.getLat()), Double.parseDouble(item.getLnt()));
            item.setLatInWgs(String.valueOf(_latLngInWgs[0]));
            item.setLngInWgs(String.valueOf(_latLngInWgs[1]));
        }

        Result _success = super.add(item, user.getId());
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        Result<String> _traceId = formTraceRepository.add(item.getId(), FormTrace.ENUM_FORM_TYPE.金凤码, "本人打卡", _deptId, _deptFullName, user.getId());
        if (!_traceId.isOK()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            return _id.pack(_traceId);
        }

        _id.data = item.getId();
        return _id;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Result<String> addByAgent(CitizenForm item, String userId) {
        Result<String> _id = new Result<>();

        // 设置网格名称
        String _deptFullName = null;
        if (StringUtils.hasLength(item.getDeptId())) {
            Result<Department> _dept = departmentRepository.get(item.getDeptId(), true);
            if (_dept.isOK()) {
                _deptFullName = _dept.data.getFullName();
            }
        } else {
            Result<Grid> _grid = gridRepository.get(item.getGridId());
            if (_grid.isOK()) {
                _deptFullName = _grid.data.getFullName();
            }
        }
        item.setDeptFullName(_deptFullName);

        // 设置特殊人群
        if (item.getCrowdIds() != null) {
            item.setCrowdIdsJSON(ConverterUtil.toJson(item.getCrowdIds()));
        }

        if (CitizenForm.ENUM_ACTOR.本人 == item.getActor()) {
            _id.setCode(Result.DATA_ACCESS_DENY);
            return _id;
        }

        if (Objects.nonNull(item.getLnt()) && Objects.nonNull(item.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(Double.parseDouble(item.getLat()), Double.parseDouble(item.getLnt()));
            item.setLatInWgs(String.valueOf(_latLngInWgs[0]));
            item.setLngInWgs(String.valueOf(_latLngInWgs[1]));
        }

        Result _success = super.add(item, userId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        String _deptId = StringUtils.hasLength(item.getDeptId()) ? item.getDeptId() : item.getGridId();
        Result<String> _traceId = formTraceRepository.add(item.getId(), FormTrace.ENUM_FORM_TYPE.金凤码, "他人打卡", _deptId, _deptFullName, userId);
        if (!_traceId.isOK()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            return _id.pack(_traceId);
        }

        _id.data = item.getId();
        return _id;
    }

    public Result<String> update(CitizenForm item, String userId) {
        Result<String> _id = new Result<>();

        Result<CitizenForm> _item = get(item.getId());
        if (!_item.isOK()) {
            return _id.pack(_item);
        }

        // 保存为历史记录
        HistoricalForm _historical = new HistoricalForm();
        BeanUtils.copyProperties(_item.data, _historical, "id");
        _historical.setFormId(_item.data.getId());
        getCurrentSession().save(_historical);

        copyProperties(item, _item.data, new String[]{"id", "deptId", "gridId", "deptFullName"});
        if (Objects.nonNull(_item.data.getLnt()) && Objects.nonNull(_item.data.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(Double.parseDouble(_item.data.getLat()), Double.parseDouble(_item.data.getLnt()));
            _item.data.setLatInWgs(String.valueOf(_latLngInWgs[0]));
            _item.data.setLngInWgs(String.valueOf(_latLngInWgs[1]));
        }

        // 设置特殊人群
        if (item.getCrowdIds() != null) {
            _item.data.setCrowdIdsJSON(ConverterUtil.toJson(item.getCrowdIds()));
        }

        Result _success = super.update(_item.data, userId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    @Transactional(readOnly = true)
    public PagingItems<CitizenForm> queryByUserId(int count, int index, Date beginTime, Date endTime, String userId) {
        JinqStream<CitizenForm> _query = stream(CitizenForm.class).where(i -> userId.equals(i.getCreatorId()));

        if (beginTime != null) {
            _query = _query.where(i -> !i.getCreateTime().before(beginTime));
        }

        if (endTime != null) {
            _query = _query.where(i -> i.getCreateTime().before(endTime));
        }

        _query = _query.sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<CitizenForm> _page = new PagingItems(count, index);
        _page.total = _query.count();
        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }

        _page.items = _query.toList();
        parseCrowdName(_page.items);
        return _page;
    }

    @Transactional(readOnly = true)
    public PagingItems<CitizenForm> fuzzy(int count, int index, List<SortField> sortFields, String grid, Boolean gridType, String name, String credentialNo, String mp, String subdistrictFullName, String crowdId, Date beginTime, Date endTime, User user) {
        JinqStream<CitizenForm> _query = stream(grid, gridType, name, credentialNo, mp, subdistrictFullName, crowdId, beginTime, endTime, user);

        if (CollectionUtils.isEmpty(sortFields)) {
            _query = _query.sortedDescendingBy(i -> i.getCreateTime());
        } else {
            _query = sort(_query, sortFields);
        }

        PagingItems<CitizenForm> _page = new PagingItems(count, index);
        _page.total = _query.count();
        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }

        _page.items = _query.toList();
        parseCrowdName(_page.items);
        return _page;
    }

    public Result remove(String id, String actorId) {
        Result _success = new Result();

        if (StringUtils.isEmpty(id)) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{CitizenForm.class.getSimpleName()});
            return _success;
        }

        CitizenForm _item = getCurrentSession().get(CitizenForm.class, id);
        if (_item == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND);
            return _success;
        }

        if (!Objects.equals(_item.getCreatorId(), actorId)) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        getCurrentSession().remove(_item);

        return _success;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<String> importFromExcel(File file, String actorId) throws Exception {
        List<Department> _departments = departmentRepository.query(null, null, null, true, null);
        List<Grid> _grids = gridRepository.query();
        Map<String, String> _crowds = crowds().stream()
                .collect(Collectors.toMap(Dictionary::getName, Dictionary::getVal));

        return readExcel((header, lines, results) -> {
            for (int i = 0; i < lines.size(); i++) {
                if (!results.get(i).isOK()) {
                    continue;
                }

                String[] _line = lines.get(i);

                CitizenForm _item = new CitizenForm();

                String _name = _line[header.get("打卡网格")];
                Department _department;
                Grid _grid = null;
                _department = _departments.stream()
                        .filter(j -> Objects.equals(_name, j.getFullName()))
                        .findFirst().orElse(null);
                if (_department == null) {
                    _grid = _grids.stream()
                            .filter(j -> Objects.equals(_name, j.getFullName()))
                            .findFirst().orElse(null);
                }
                // 未匹配网格
                if (_department == null && _grid == null) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 3);
                    results.set(i, _success);

                    continue;
                }
                if (_department != null) {
                    _item.setDeptId(_department.getId());
                    _item.setDeptFullName(_department.getFullName());
                } else {
                    _item.setGridId(_grid.getId());
                    _item.setDeptFullName(_grid.getFullName());
                }

                String _text = _line[header.get("姓名")];
                _item.setName(_text);

                try {
                    _text = _line[header.get("填报类型")];
                    CitizenForm.ENUM_ACTOR _actor = CitizenForm.ENUM_ACTOR.valueOf(_text);
                    _item.setActor(_actor);
                }
                // 填报类型不匹配
                catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 4);
                    results.set(i, _success);

                    continue;
                }

                try {
                    _text = _line[header.get("证件类型")];
                    CitizenForm.ENUM_CREDENTIAL_TYPE _credentialType = CitizenForm.ENUM_CREDENTIAL_TYPE.valueOf(_text);
                    _item.setCredentialType(_credentialType);
                }
                // 证件类型不匹配
                catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 5);
                    results.set(i, _success);

                    continue;
                }

                _text = _line[header.get("证件号码")];
                _item.setCredentialNo(_text);

                _text = _line[header.get("手机号码")];
                _item.setMp(_text);

                // 特殊人群
                _text = _line[header.get("特殊人群")];
                if (StringUtils.isEmpty(_text)) {
                    _item.setCrowdIdsJSON(ConverterUtil.toJson(new String[0]));
                } else {
                    String[] _array = _text.split("[;,，]");
                    List<String> _ids = Stream.of(_array).map(j -> _crowds.get(j.trim()))
                            .collect(Collectors.toList());
                    if (_ids.contains(null)) {
                        Result _success = new Result();
                        _success.setCode(Result.ENUM_ERROR.P, 6);
                        results.set(i, _success);

                        continue;
                    }

                    _item.setCrowdIdsJSON(ConverterUtil.toJson(_ids));
                }

                _text = _line[header.get("工作单位")];
                _item.setWorkplace(_text);

                try {
                    _text = _line[header.get("居住类型")];
                    CitizenForm.ENUM_STAY _stay = CitizenForm.ENUM_STAY.valueOf(_text);
                    _item.setStay(_stay);
                }
                // 居住类型不匹配
                catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 7);
                    results.set(i, _success);

                    continue;
                }

                String _name2 = _line[header.get("居住地行政区划")];
                _department = _departments.stream()
                        .filter(j -> Objects.equals(_name2, j.getFullName()))
                        .findFirst().orElse(null);
                // 居住地不匹配
                if (_department == null) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 8);
                    results.set(i, _success);

                    continue;
                }
                _item.setSubdistrictId(_department.getId());
                _item.setSubdistrictFullName(_department.getFullName());

                _text = _line[header.get("居住地详细地址")];
                _item.setAddress(_text);

                _text = _line[header.get("备注")];
                _item.setMemo(_text);

                Transaction _transaction = getCurrentSession().beginTransaction();
                try {
                    add(_item, actorId);

                    _transaction.commit();
                } catch (Throwable e) {
                    e.printStackTrace();

                    Result _error = resultParser.fromException(e, false);
                    if (_error == null) {
                        _error = new Result();
                        _error.setCode(Result.ENUM_ERROR.P, 9, new Object[]{e.getMessage()});
                    }
                    results.set(i, _error);

                    _transaction.rollback();
                }
            }
        }, file, 0, 1);
    }

    @Transactional(readOnly = true)
    public String export(String grid, Boolean gridType, String name, String credentialNo, String mp, String subdistrictFullName, String crowdId, Date beginTime, Date endTime, User user) throws IOException {
        List<CitizenForm> _items = fuzzy(-1, -1, null, grid, gridType, name, credentialNo, mp, subdistrictFullName, crowdId, beginTime, endTime, user).items;

        return POIUtil.exportToWorkbookBase64String((book) -> {
            Sheet _sheet = book.createSheet();

            // 设置标题行
            Row _row = _sheet.createRow(0);
            _row.createCell(0).setCellValue("网格");
            _row.createCell(1).setCellValue("姓名");
            _row.createCell(2).setCellValue("填报类型");
            _row.createCell(3).setCellValue("证件类型");
            _row.createCell(4).setCellValue("证件号码");
            _row.createCell(5).setCellValue("手机号码");
            _row.createCell(6).setCellValue("特殊人群");
            _row.createCell(7).setCellValue("工作单位/车牌号码");
            _row.createCell(8).setCellValue("居住类型");
            _row.createCell(9).setCellValue("居住行政区划");
            _row.createCell(10).setCellValue("详细地址");
            _row.createCell(11).setCellValue("备注");
            _row.createCell(12).setCellValue("采集时间");

            boolean _requireMasked = !roleRepository.isUserInRoleCached(user.getId(), FormRepository.ADMIN_ROLE);

            for (int i = 0; i < _items.size(); i++) {
                _row = _sheet.createRow(i + 1);
                _row.createCell(0).setCellValue(_items.get(i).getDeptFullName());
                _row.createCell(1).setCellValue(_items.get(i).getName());
                _row.createCell(2).setCellValue(_items.get(i).getActor().name());
                _row.createCell(3).setCellValue(_items.get(i).getCredentialType().name());

                String _credentialNo = _items.get(i).getCredentialNo();
                // 脱敏
                if (_requireMasked) {
                    if (_credentialNo != null && _credentialNo.length() > 16) {
                        _credentialNo = _credentialNo.substring(0, 2) + "**************" + _credentialNo.substring(16);
                    }
                }
                _row.createCell(4).setCellValue(_credentialNo);

                _row.createCell(5).setCellValue(_items.get(i).getMp());

                String _json = _items.get(i).getCrowdNamesJSON();
                if (StringUtils.hasLength(_json)) {
                    List<String> _names = ConverterUtil.json2Object(_json, new TypeToken<List<String>>() {
                    }.getType());
                    String _str = String.join(", ", _names);
                    _row.createCell(6).setCellValue(_str);
                } else {
                    _row.createCell(6).setCellValue("");
                }

                _row.createCell(7).setCellValue(_items.get(i).getWorkplace());
                _row.createCell(8).setCellValue(_items.get(i).getStay().name());
                _row.createCell(9).setCellValue(_items.get(i).getSubdistrictFullName());
                _row.createCell(10).setCellValue(_items.get(i).getAddress());
                _row.createCell(11).setCellValue(_items.get(i).getMemo());
                _row.createCell(12).setCellValue(_items.get(i).getCreateTime() == null ? "" : _items.get(i).getCreateTime().toString());
            }
        }, "xlsx");
    }

    public JinqStream<CitizenForm> stream(String grid, Boolean gridType, String name, String credentialNo, String mp, String subdistrictFullName, String crowdId, Date beginTime, Date endTime, User user) {
        JinqStream<CitizenForm> _query = jinqJPAStreamProvider.streamAll(getCurrentSession(), CitizenForm.class);

        if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            Pair<List<Grid>, List<Department>> _scope = gridRepository.getUserScope(user);
            List<String> _gridIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(_scope.getOne())) {
                _gridIds = _scope.getOne().stream()
                        .map(i -> i.getId())
                        .collect(Collectors.toList());
            }

            List<String> _deptIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(_scope.getTwo())) {
                List<Department> _departments = departmentRepository.query(null, null, null, true, null);
                List<String> _fullNames = _scope.getTwo().stream()
                        .map(i -> i.getFullName())
                        .collect(Collectors.toList());
                for (String i : _fullNames) {
                    List<String> _ids = _departments.stream()
                            .filter(j -> j.getFullName().equals(i) || j.getFullName().startsWith(i + "/"))
                            .map(j -> j.getId())
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(_ids)) {
                        _deptIds.addAll(_ids);
                    }
                }
            }

            List<String> _gridIds2 = _gridIds;
            List<String> _deptIds2 = _deptIds;
            if (_gridIds2.size() > 0) {
                if (_deptIds2.size() > 0) {
                    _query = _query.where(i -> _gridIds2.contains(i.getGridId()) || _deptIds2.contains(i.getDeptId()));
                } else {
                    _query = _query.where(i -> _gridIds2.contains(i.getGridId()));
                }
            } else if (_deptIds2.size() > 0) {
                _query = _query.where(i -> _deptIds2.contains(i.getDeptId()));
            } else {
                _query = _query.where(i -> false);
            }
        }

        if (StringUtils.hasLength(grid)) {
            _query = _query.where(i -> i.getDeptFullName() != null && i.getDeptFullName().contains(grid));
        }

        if (gridType != null) {
            _query = gridType ? _query.where(i -> i.getGridId() != null) : _query.where(i -> i.getDeptId() != null);
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (StringUtils.hasLength(credentialNo)) {
            _query = _query.where(i -> i.getCredentialNo() != null && i.getCredentialNo().contains(credentialNo));
        }

        if (StringUtils.hasLength(mp)) {
            _query = _query.where(i -> i.getMp() != null && i.getMp().contains(mp));
        }

        if (StringUtils.hasLength(subdistrictFullName)) {
            _query = _query.where(i -> i.getSubdistrictFullName() != null && i.getSubdistrictFullName().contains(subdistrictFullName));
        }

        if (StringUtils.hasLength(crowdId)) {
            String _crowdQuery = "[]".equals(crowdId) ? crowdId : String.format("\"%s\"", crowdId);
            _query = _query.where(i -> i.getCrowdIdsJSON() != null && i.getCrowdIdsJSON().contains(_crowdQuery));
        }

        if (beginTime != null) {
            _query = _query.where(i -> !i.getCreateTime().before(beginTime));
        }

        if (endTime != null) {
            _query = _query.where(i -> i.getCreateTime().before(endTime));
        }

        return _query;
    }

    @Transactional(readOnly = true)
    public List<Dictionary> crowds() {
        return dictionaryRepository.fuzzy("金凤码-特殊人群", null, true, null);
    }

    void parseCrowdName(List<CitizenForm> items) {
        Map<String, String> _crowds = crowds().stream().collect(Collectors.toMap(Dictionary::getVal, Dictionary::getName));

        for (CitizenForm i : items) {
            if (StringUtils.isEmpty(i.getCrowdIdsJSON())) {
                continue;
            }

            List<String> _ids = ConverterUtil.json2Object(i.getCrowdIdsJSON(), new TypeToken<List<String>>() {
            }.getType());
            i.setCrowdNamesJSON(ConverterUtil.toJson(_ids.stream()
                    .map(_crowds::get)
                    .collect(Collectors.toList())));
        }
    }

}
