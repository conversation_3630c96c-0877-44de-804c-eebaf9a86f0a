package com.chinamobile.healthcode.repository.citizen;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import org.jinq.tuples.Pair;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "profile-validation", jobGroup = "default", triggerName = "profile-validation", triggerGroup = "default", triggerCron = "0 0 0 * * ?", triggerOnStart = false)
public class ValidationJob extends QuartzJobBean {

    final int threads;
    final int size;
    final ProfileRepository profileRepository;
    final ProfileValidationRepository profileValidationRepository;
    final DepartmentRepository departmentRepository;
    final ThreadPoolExecutor threadPoolExecutor;
    final Logger logger;

    public ValidationJob(@Value(value = "${citizen.profile.validation.threads}") int threads, @Value(value = "${citizen.profile.validation.size}") int size, ProfileRepository profileRepository, ProfileValidationRepository profileValidationRepository, DepartmentRepository departmentRepository, ThreadPoolExecutor threadPoolExecutor) {
        this.threads = threads;
        this.size = size;
        this.profileRepository = profileRepository;
        this.profileValidationRepository = profileValidationRepository;
        this.departmentRepository = departmentRepository;
        this.threadPoolExecutor = threadPoolExecutor;
        this.logger = LoggerFactory.getLogger(ValidationJob.class);
    }

    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        logger.info("作业开始");

        try {
            List<Department> _departments = departmentRepository.query(null, null, null, true, null);

            // 清空验证数据
            long _total = profileRepository.total();
            int _step = 1000000;
            for (int i = 0; i < _total; i += _step) {
                profileRepository.resetValidation(_step, i);
            }
            profileValidationRepository.truncate();

            CompletionService<Pair<Long, Long>> _service = new ExecutorCompletionService<>(threadPoolExecutor);
            List<Future<Pair<Long, Long>>> _futures = new ArrayList<>();
            for (int i = 0; i < threads; i++) {
                int no = i;
                _futures.add(_service.submit(() -> validate(no, _departments)));
            }

            long _success = 0, _fail = 0;
            for (Future<Pair<Long, Long>> i : _futures) {
                Pair<Long, Long> _counter = i.get();
                _success += _counter.getOne();
                _fail += _counter.getTwo();
            }

            this.logger.info(String.format("已校验%s项记录，其中%s项失败", _success, _fail));
        } catch (Throwable e) {
            this.logger.info("作业执行时程序异常", e);
        } finally {
            this.logger.info("作业结束");
        }
    }

    protected Pair<Long, Long> validate(int no, List<Department> departments) {
        long _success = 0, _fail = 0, _index = 0;

        while (true) {
            int _realIndex = (int) (no + _index * threads);

            try {
                Pair<Integer, Integer> _dto = profileRepository.validate(size, _realIndex, departments);
                _success += _dto.getOne();
                _fail += _dto.getTwo();

                // 已遍历所有数据
                if (_dto.getOne() + _dto.getTwo() < size) {
                    break;
                }
            } catch (Throwable e) {
                _fail += size;

                this.logger.info(String.format("任务批次%s执行失败", _realIndex + 1), e);
            }

            _index++;
        }

        return new Pair<>(_success, _fail);
    }

}