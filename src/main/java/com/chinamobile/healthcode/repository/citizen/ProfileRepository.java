package com.chinamobile.healthcode.repository.citizen;

import com.chinamobile.healthcode.model.citizen.Profile;
import com.chinamobile.healthcode.model.citizen.ProfileValidation;
import com.chinamobile.healthcode.model.subject.PersonDescription;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.GpsUtil;
import com.chinamobile.sparrow.domain.util.POIUtil;
import com.google.common.reflect.TypeToken;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.query.NativeQuery;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Repository
@ErrorCode(module = "051")
public class ProfileRepository extends AbstractEntityRepository<Profile> {

    public static final String ADMIN_ROLE = "个人信息收集-管理员";

    final String NAME_PATTERN;
    final String IDENTITY_NO_PATTERN;
    final String CONTACT_PATTERN;
    final String MP_PATTERN;

    final ProfileValidationRepository profileValidationRepository;
    final IKHRepository ikhRepository;
    final DefaultDepartmentRepository departmentRepository;
    final DictionaryRepository dictionaryRepository;
    final DefaultRoleRepository roleRepository;

    final DefaultResultParser resultParser;

    public ProfileRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, ProfileValidationRepository profileValidationRepository, IKHRepository ikhRepository, DefaultDepartmentRepository departmentRepository, DictionaryRepository dictionaryRepository, DefaultRoleRepository roleRepository, DefaultResultParser resultParser) {
        super(entityManagerFactory, jinqJPAStreamProvider, Profile.class);

        this.profileValidationRepository = profileValidationRepository;
        this.ikhRepository = ikhRepository;
        this.departmentRepository = departmentRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.roleRepository = roleRepository;
        this.resultParser = resultParser;

        String _val = dictionaryRepository.getVal("校验规则", "姓名", true);
        NAME_PATTERN = StringUtils.hasLength(_val) ? _val : "^[A-Za-z\\u4e00-\\u9fa5]+$";

        _val = dictionaryRepository.getVal("校验规则", "身份证号码", true);
        IDENTITY_NO_PATTERN = StringUtils.hasLength(_val) ? _val : "^(\\d{17}[\\dxX]|\\d{15})$";

        _val = dictionaryRepository.getVal("校验规则", "联系方式", true);
        CONTACT_PATTERN = StringUtils.hasLength(_val) ? _val : "^[0,2-9]\\d{0,10}$";

        _val = dictionaryRepository.getVal("校验规则", "手机号码", true);
        MP_PATTERN = StringUtils.hasLength(_val) ? _val : "^1\\d{10}$";
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<Profile> sort(JinqStream<Profile> query, BiFunction<JinqStream<Profile>, JinqStream.CollectComparable<Profile, V>, JinqStream<Profile>> compare, String field) {
        switch (field) {
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            case "validation":
                return compare.apply(query, i -> (V) i.getValidation());
            default:
                return query;
        }
    }

    @Transactional(readOnly = true)
    public Result<Profile> getBrief(String id) {
        Result<Profile> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Profile.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(Profile.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Profile.class.getSimpleName()});
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public Result<Profile> getBriefByCredentialNo(String credentialNo) {
        Result<Profile> _item = new Result<>();

        if (StringUtils.isEmpty(credentialNo)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Profile.class.getSimpleName()});
            return _item;
        }

        _item.data = stream(Profile.class).where(i -> i.getCredentialNo().equals(credentialNo))
                .findFirst().orElse(null);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public Result<Profile> get(String id) {
        Result<Profile> _item = getBrief(id);
        if (!_item.isOK()) {
            return _item;
        }

        parseCrowdName(Collections.singletonList(_item.data));

        return _item;
    }

    public Result<String> add(Profile item, User user) {
        Result<String> _id = new Result<>();

        if (item.getActor() == Profile.ENUM_ACTOR.本人) {
            _id.setCode(Result.ENUM_ERROR.P, 15);
            return _id;
        }

        Result<Profile> _item = getBriefByCredentialNo(item.getCredentialNo());
        if (_item.isOK()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST, new Object[]{Profile.class.getSimpleName()});
            return _id;
        }

        // 校验数据
        List<Department> _subdistrict = new ArrayList<>();
        List<Result> _validations = validate(item, _subdistrict);
        if (!CollectionUtils.isEmpty(_validations)) {
            return _id.pack(_validations.get(0));
        } else if (!CollectionUtils.isEmpty(_subdistrict)){
            // 设置居住地
            item.setSubdistrictFullName(_subdistrict.get(0).getFullName());
        }

        // 手机号码已被占用
        /*String _mp = item.getMp();
        if (StringUtils.hasLength(_mp) && stream(Profile.class).where(i -> _mp.equals(i.getMp()))
                .findFirst().isPresent()) {
            _id.setCode(Result.ENUM_ERROR.P, 14);
            return _id;
        }*/

        // 设置生日
        if (Profile.ENUM_CREDENTIAL_TYPE.身份证 == item.getCredentialType() && item.getCredentialNo().length() == 18) {
            String _str = item.getCredentialNo().substring(6, 14);
            try {
                Date _birthday = DateUtil.from(_str, "yyyyMMdd");
                _item.data.setBirthday(_birthday);
            } catch (Throwable e) {
            }
        }

        // 设置特殊人群
        if (item.getCrowdIds() != null) {
            item.setCrowdIdsJSON(ConverterUtil.toJson(item.getCrowdIds()));
        }

        if (Profile.ENUM_ACTOR.其他 != item.getActor()) {
            item.setActorOther(null);
        }

        if (Objects.nonNull(item.getLng()) && Objects.nonNull(item.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(item.getLat(), item.getLng());
            item.setLatInWgs(_latLngInWgs[0]);
            item.setLngInWgs(_latLngInWgs[1]);
        }

        // 同步重点人员信息
        synchronizeSubjectPersonInfo(item);

        Result _success = super.add(item, user.getId());
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = item.getId();
        return _id;
    }

    public Result<String> update(Profile item, Boolean isCredentialNoInvalid, User user) {
        Result<String> _id = new Result<>();

        if (item.getActor() == Profile.ENUM_ACTOR.本人) {
            _id.setCode(Result.ENUM_ERROR.P, 15);
            return _id;
        }

        Result<Profile> _item = getBrief(item.getId());
        if (!_item.isOK()) {
            return _id.pack(_item);
        }

        // 本人已确认且非本人修改
        /*if (Profile.ENUM_ACTOR.本人 == _item.data.getActor() && !Objects.equals(user.getMp(), _item.data.getMp())) {
            _id.setCode(Result.DATA_ACCESS_DENY);
            return _id;
        }*/

        // 证件号是否验证失败
        boolean _isCredentialNoInvalid = isCredentialNoInvalid == null ? isCredentialNoInvalid(item.getId()) : isCredentialNoInvalid;
        if (Profile.ENUM_CREDENTIAL_TYPE.身份证 == item.getCredentialType()) {
            if (_isCredentialNoInvalid) {
                String _identity = item.getId();
                String _credentialNo = item.getCredentialNo();

                // 身份证号已被占用
                if (isCredentialNoExist(_identity, _credentialNo)) {
                    _id.setCode(Result.ENUM_ERROR.P, 4);
                    return _id;
                }
            }
            // 还原身份证号码
            else {
                item.setCredentialNo(_item.data.getCredentialNo());
            }
        }

        // 校验数据
        List<Department> _subdistrict = new ArrayList<>();
        List<Result> _validations = validate(item, _subdistrict);
        if (!CollectionUtils.isEmpty(_validations)) {
            return _id.pack(_validations.get(0));
        }
        if (_subdistrict.size() > 0) {
            item.setSubdistrictFullName(_subdistrict.get(0).getFullName());
        }

        /*String _mp = item.getMp();
        if (StringUtils.hasLength(_mp) && stream(Profile.class).where(i -> !i.getId().equals(_identity) && _mp.equals(i.getMp()))
                .findFirst().isPresent()) {
            _id.setCode(Result.ENUM_ERROR.P, 14);
        }*/

        String[] _ignored = _isCredentialNoInvalid ? new String[]{"id", "birthday"} : new String[]{"id", "birthday", "credentialNo"};
        copyProperties(item, _item.data, _ignored);

        // 设置生日
        if (Profile.ENUM_CREDENTIAL_TYPE.身份证 == item.getCredentialType() && item.getCredentialNo().length() == 18) {
            String _str = item.getCredentialNo().substring(6, 14);
            try {
                Date _birthday = DateUtil.from(_str, "yyyyMMdd");
                _item.data.setBirthday(_birthday);
            } catch (Throwable e) {
            }
        }

        // 设置特殊人群
        if (item.getCrowdIds() != null) {
            _item.data.setCrowdIdsJSON(ConverterUtil.toJson(item.getCrowdIds()));
        }

        if (Profile.ENUM_ACTOR.其他 != item.getActor()) {
            _item.data.setActorOther(null);
        }

        _item.data.setValidation(Profile.ENUM_VALIDATION.待验证);

        if (Objects.nonNull(_item.data.getLng()) && Objects.nonNull(_item.data.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.data.getLat(), _item.data.getLng());
            _item.data.setLatInWgs(_latLngInWgs[0]);
            _item.data.setLngInWgs(_latLngInWgs[1]);
        }

        // 同步重点人员信息
        synchronizeSubjectPersonInfo(item);

        Result _success = super.update(_item.data, user.getId());
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Result<String> razeThenUpdate(Profile item, User user) {
        boolean _isCredentialNoInvalid = isCredentialNoInvalid(item.getId());
        // 如果身份证号码验证失败，则需删除其他同号码记录
        if (_isCredentialNoInvalid && Profile.ENUM_CREDENTIAL_TYPE.身份证 == item.getCredentialType()) {
            String _identifier = item.getId();
            JinqStream<Profile> _query = stream(Profile.class).where(i -> _identifier == null || !_identifier.equals(i.getId()));

            String _credentialNo = item.getCredentialNo();
            List<Profile> _items = _query.where(i -> i.getCredentialNo().equals(_credentialNo)).toList();
            for (Profile i : _items) {
                getCurrentSession().remove(i);
            }

            // 发送sql，但不提交
            getCurrentSession().flush();
        }

        Result<String> _id = update(item, _isCredentialNoInvalid, user);
        // 更新失败则回滚
        if (!_id.isOK()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

        return _id;
    }

    @Transactional(readOnly = true)
    public PagingItems<Profile> fuzzy(int count, int index, List<SortField> sortFields, String name, String credentialNo, String mp, String subdistrictFullName, Profile.ENUM_STAY stay, String crowdId, Integer minAge, Integer maxAge, Date beginTime, Date endTime, Profile.ENUM_STATUS status, Profile.ENUM_VALIDATION validation, String error, User user) {
        JinqStream<Profile> _query = stream(name, credentialNo, mp, subdistrictFullName, stay, crowdId, minAge, maxAge, beginTime, endTime, status, validation, error, user);

        if (CollectionUtils.isEmpty(sortFields)) {
            _query = _query.sortedDescendingBy(i -> i.getCreateTime());
        } else {
            _query = sort(_query, sortFields);
        }

        PagingItems<Profile> _page = new PagingItems(count, index);
        _page.total = _query.count();
        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }

        _page.items = _query.toList();
        parseCrowdName(_page.items);
        return _page;
    }

    public Result remove(String id, User actor) {
        Result _success = new Result();

        Result<Profile> _item = getBrief(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非下级单位
        if (!departmentRepository.subordinates(actor.getDeptId(), null, null).stream()
                .anyMatch(i -> i.getId().equals(_item.data.getSubdistrictId()))) {
            _item.setCode(Result.DATA_ACCESS_DENY);
            return _item;
        }

        getCurrentSession().remove(_item.data);

        return _success;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<String> importFromExcel(File file, User user) throws Exception {
        // 读取下级单位
        List<DefaultDepartment> _subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);

        // 读取部门
        List<DefaultDepartment> _departments = departmentRepository.query(null, null, null, true, null);

        // 读取特殊人群
        Map<String, String> _crowds = crowds().stream()
                .collect(Collectors.toMap(Dictionary::getName, Dictionary::getVal));

        int _max = 1000;

        return readExcel((header, lines, results) -> {
            // 读取个人资料
            List<String> _credentialNos = lines.stream()
                    .filter(i -> StringUtils.hasLength(i[3]))
                    .map(i -> i[3])
                    .collect(Collectors.toList());
            List<Profile> _profiles = stream(Profile.class).where(i -> _credentialNos.contains(i.getCredentialNo())).toList();

            for (int i = 0; i < lines.size(); i++) {
                if (!results.get(i).isOK()) {
                    continue;
                }

                String[] _line = lines.get(i);

                Profile _item = new Profile();

                String _text = _line[header.get("姓名")];
                _item.setName(_text);

                Profile.ENUM_ACTOR _actor;
                try {
                    _text = _line[header.get("填报类型")];
                    _actor = Profile.ENUM_ACTOR.valueOf(_text);
                }
                // 填报类型不匹配
                catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 5);
                    results.set(i, _success);

                    continue;
                }
                if (_actor == Profile.ENUM_ACTOR.本人) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 15);
                    results.set(i, _success);

                    continue;
                }
                _item.setActor(_actor);

                try {
                    _text = _line[header.get("证件类型")];
                    _item.setCredentialType(Profile.ENUM_CREDENTIAL_TYPE.valueOf(_text));
                }
                // 证件类型不匹配
                catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 6);
                    results.set(i, _success);

                    continue;
                }

                _text = _line[header.get("证件号码")];
                _item.setCredentialNo(_text);

                _text = _line[header.get("手机号码")];
                _item.setMp(_text);

                // 特殊人群
                _text = _line[header.get("特殊人群")];
                if (StringUtils.isEmpty(_text)) {
                    _item.setCrowdIdsJSON(ConverterUtil.toJson(new String[0]));
                } else {
                    String[] _array = _text.split("[,，;；]");
                    List<String> _ids = Stream.of(_array).map(j -> _crowds.get(j.trim()))
                            .collect(Collectors.toList());
                    if (_ids.contains(null)) {
                        Result _success = new Result();
                        _success.setCode(Result.ENUM_ERROR.P, 8);
                        results.set(i, _success);

                        continue;
                    }

                    _item.setCrowdIdsJSON(ConverterUtil.toJson(_ids));
                }

                _text = _line[header.get("工作单位")];
                _item.setWorkplace(_text);

                try {
                    _text = _line[header.get("居住类型")];
                    _item.setStay(Profile.ENUM_STAY.valueOf(_text));
                }
                // 居住类型不匹配
                catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 9);
                    results.set(i, _success);

                    continue;
                }

                String _text2 = _line[header.get("居住地行政区划")];
                Department _department = _departments.stream()
                        .filter(j -> Objects.equals(_text2, j.getFullName()))
                        .findFirst().orElse(null);
                if (_department == null) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 10);
                    results.set(i, _success);

                    continue;
                }
                List<Result> _validations = validate(_item, Collections.singletonList(_department));
                if (!CollectionUtils.isEmpty(_validations)) {
                    Result _success = new Result();
                    _success.pack(_validations.get(0));
                    results.set(i, _success);

                    continue;
                }

                // 设置生日
                if (Profile.ENUM_CREDENTIAL_TYPE.身份证 == _item.getCredentialType()) {
                    String _temp = _item.getCredentialNo().substring(6, 14);
                    try {
                        Date _birthday = DateUtil.from(_temp, "yyyyMMdd");
                        _item.setBirthday(_birthday);
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }

                // 非下级单位
                if (!_subordinates.stream().anyMatch(j -> Objects.equals(j.getId(), _department.getId()))) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 11);
                    results.set(i, _success);

                    continue;
                }
                _item.setSubdistrictId(_department.getId());
                _item.setSubdistrictFullName(_department.getFullName());

                _text = _line[header.get("居住地详细地址")];
                _item.setAddress(_text);

                try {
                    _text = _line[header.get("纬度")];
                    _item.setLat(StringUtils.hasLength(_text) ? Double.valueOf(_text) : null);
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 22);
                    results.set(i, _success);

                    continue;
                }

                try {
                    _text = _line[header.get("经度")];
                    _item.setLng(StringUtils.hasLength(_text) ? Double.valueOf(_text) : null);
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 22);
                    results.set(i, _success);

                    continue;
                }

                if (Objects.nonNull(_item.getLng()) && Objects.nonNull(_item.getLat())) {
                    double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.getLat(), _item.getLng());
                    _item.setLatInWgs(_latLngInWgs[0]);
                    _item.setLngInWgs(_latLngInWgs[1]);
                }

                try {
                    _text = _line[header.get("人员状态")];
                    _item.setStatus(Profile.ENUM_STATUS.valueOf(_text));
                }
                // 人员状态不匹配
                catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 23);
                    results.set(i, _success);

                    continue;
                }

                // 设置验证状态
                _item.setValidation(Profile.ENUM_VALIDATION.待验证);

                 // 同步重点人员信息
                synchronizeSubjectPersonInfo(_item);

                Transaction _transaction = getCurrentSession().beginTransaction();
                try {
                    Profile _profile = _profiles.stream()
                            .filter(j -> Objects.equals(j.getCredentialNo(), _item.getCredentialNo()))
                            .findFirst().orElse(null);
                    if (_profile == null) {
                        _item.setCreatorId(user.getId());
                        _item.setCreateTime(new Date());

                        getCurrentSession().save(_item);
                    } else {
                        _profile.setMaintainerId(user.getId());
                        _profile.setMaintainTime(new Date());
                        copyProperties(_item, _profile, new String[]{"id"});

                        getCurrentSession().update(_profile);
                    }

                    _transaction.commit();
                } catch (Throwable e) {
                    e.printStackTrace();

                    Result _error = resultParser.fromException(e, false);
                    if (_error == null) {
                        _error = new Result();
                        _error.setCode(Result.ENUM_ERROR.P, 13, new Object[]{e.getMessage()});
                    }
                    results.set(i, _error);

                    _transaction.rollback();
                }
            }

        }, file, 0, 1);
    }

    @Transactional(readOnly = true)
    public String export(String name, String credentialNo, String mp, String subdistrictFullName, Profile.ENUM_STAY stay, String crowdId, Integer minAge, Integer maxAge, Date beginTime, Date endTime, Profile.ENUM_STATUS status, Profile.ENUM_VALIDATION validation, String error, User user) throws IOException {
        List<Profile> _items = fuzzy(-1, -1, null, name, credentialNo, mp, subdistrictFullName, stay, crowdId, minAge, maxAge, beginTime, endTime, status, validation, error, user).items;

        return POIUtil.exportToWorkbookBase64String((book) -> {
            Sheet _sheet = book.createSheet();

            // 设置标题行
            Row _row = _sheet.createRow(0);
            _row.createCell(0).setCellValue("姓名");
            _row.createCell(1).setCellValue("填报类型");
            _row.createCell(2).setCellValue("证件类型");
            _row.createCell(3).setCellValue("证件号码");
            _row.createCell(4).setCellValue("手机号码");
            _row.createCell(5).setCellValue("特殊人群");
            _row.createCell(6).setCellValue("工作单位/车牌号码");
            _row.createCell(7).setCellValue("居住类型");
            _row.createCell(8).setCellValue("居住行政区划");
            _row.createCell(9).setCellValue("详细地址");
            _row.createCell(10).setCellValue("人员状态");
            _row.createCell(11).setCellValue("验证状态");
            _row.createCell(12).setCellValue("采集时间");

            boolean _requireMasked = !roleRepository.isUserInRoleCached(user.getId(), ProfileRepository.ADMIN_ROLE);

            for (int i = 0; i < _items.size(); i++) {
                _row = _sheet.createRow(i + 1);
                _row.createCell(0).setCellValue(_items.get(i).getName());
                _row.createCell(1).setCellValue(_items.get(i).getActor().name());
                _row.createCell(2).setCellValue(_items.get(i).getCredentialType().name());

                String _credentialNo = _items.get(i).getCredentialNo();
                // 脱敏
                if (_requireMasked) {
                    if (_credentialNo != null && _credentialNo.length() > 16) {
                        _credentialNo = _credentialNo.substring(0, 2) + "**************" + _credentialNo.substring(16);
                    }
                }
                _row.createCell(3).setCellValue(_credentialNo);

                _row.createCell(4).setCellValue(_items.get(i).getMp());

                String _json = _items.get(i).getCrowdNamesJSON();
                if (StringUtils.hasLength(_json)) {
                    List<String> _names = ConverterUtil.json2Object(_json, new TypeToken<List<String>>() {
                    }.getType());
                    String _str = String.join(", ", _names);
                    _row.createCell(5).setCellValue(_str);
                } else {
                    _row.createCell(5).setCellValue("");
                }

                _row.createCell(6).setCellValue(_items.get(i).getWorkplace());
                _row.createCell(7).setCellValue(_items.get(i).getStay().name());
                _row.createCell(8).setCellValue(_items.get(i).getSubdistrictFullName());
                _row.createCell(9).setCellValue(_items.get(i).getAddress());
                _row.createCell(10).setCellValue(_items.get(i).getStatus().name());
                _row.createCell(11).setCellValue(_items.get(i).getValidation().name());
                _row.createCell(12).setCellValue(_items.get(i).getCreateTime() == null ? "" : _items.get(i).getCreateTime().toString());
            }
        }, "xlsx");
    }

    @Transactional(readOnly = true)
    public boolean isCredentialNoExist(String id, String credentialNo) {
        String _credentialNo = StringUtils.trimWhitespace(credentialNo);
        return stream(Profile.class).where(i -> !i.getId().equals(id) && i.getCredentialNo().equals(_credentialNo))
                .findFirst().isPresent();
    }

    @Transactional(readOnly = true)
    public boolean isCredentialNoInvalid(String id) {
        List<String> _errors = profileValidationRepository.getErrors(id);
        _errors.retainAll(ProfileValidationRepository.INVALID_IDENTITY_NO_ERROR);
        return _errors.size() > 0;
    }


    @Transactional(readOnly = true)
    public long total() {
        return stream(Profile.class).count();
    }

    public void resetValidation(int count, int offset) {
        NativeQuery _query = getCurrentSession().createSQLQuery("UPDATE citizen_profiles SET validation = 0 WHERE id in (SELECT * FROM (SELECT id FROM citizen_profiles LIMIT ?, ?) AS temp)");
        _query.setParameter(1, offset);
        _query.setParameter(2, count);
        _query.executeUpdate();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Pair<Integer, Integer> validate(int count, int index, List<Department> departments) {
        // 获取个人信息
        List<Profile> _items = stream(Profile.class)
                .sortedBy(i -> i.getCreateTime())
                .skip(count * index)
                .limit(count)
                .toList();

        // 获取疫苗接种信息
        List<String> _keys = _items.stream()
                .map(i -> i.getCredentialNo())
                .collect(Collectors.toList());
        List<Pair<String, String>> _credentialNos = CollectionUtils.isEmpty(_keys) ? new ArrayList<>() : ikhRepository.query(_keys).stream()
                .map(i -> new Pair<>(i.getCredentialNo(), i.getName()))
                .collect(Collectors.toList());

        int _success = 0, _fail = 0;
        boolean _valid;
        for (Profile i : _items) {
            _success++;

            try {
                _valid = true;

                if (Profile.ENUM_STATUS.在册 == i.getStatus() || (Profile.ENUM_STATUS.外出 == i.getStatus() && Profile.ENUM_STAY.常住 == i.getStay())) {
                    List<Result> _errors = new ArrayList<>();

                    Department _subdistrict = departments.stream()
                            .filter(j -> Objects.equals(i.getSubdistrictId(), j.getId()))
                            .findFirst().orElse(null);
                    if (_subdistrict == null) {
                        _valid = false;

                        Result _resp = new Result();
                        _resp.setCode(Result.ENUM_ERROR.P, 10);
                        _errors.add(_resp);
                    }

                    // 字段验证
                    List<Result> _validations = validate(i, Collections.singletonList(_subdistrict));
                    if (!CollectionUtils.isEmpty(_validations)) {
                        _valid = false;

                        _errors.addAll(_validations);
                    }

                    // 真实性验证
                    String _name = _credentialNos.stream()
                            .filter(j -> Objects.equals(i.getCredentialNo(), j.getOne()))
                            .map(j -> j.getTwo())
                            .findFirst().orElse(null);
                    if (StringUtils.hasLength(_name) && !Objects.equals(i.getName(), _name)) {
                        _valid = false;

                        Result _resp = new Result();
                        _resp.setCode(Result.ENUM_ERROR.P, 21);
                        _errors.add(_resp);
                    }

                    // 保存验证结果
                    if (_errors.size() > 0) {
                        String _codes = ConverterUtil.toJson(_errors.stream()
                                .map(j -> j.getCode().split("_")[3])
                                .collect(Collectors.toList()));
                        String _descriptions = ConverterUtil.toJson(_errors.stream()
                                .map(j -> j.message)
                                .collect(Collectors.toList()));

                        ProfileValidation _temp = new ProfileValidation(i.getId(), _codes, _descriptions, i.getSubdistrictId(), i.getSubdistrictFullName());

                        profileValidationRepository.save(_temp);
                    }
                }

                i.setValidation(_valid ? Profile.ENUM_VALIDATION.已验证 : Profile.ENUM_VALIDATION.验证失败);
                update(i, null);
            } catch (Throwable e) {
                _success--;

                _fail++;
            }
        }

        return new Pair<>(_success, _fail);
    }

    public JinqStream<Profile> stream(String name, String credentialNo, String mp, String subdistrictFullName, Profile.ENUM_STAY stay, String crowdId, Integer minAge, Integer maxAge, Date beginTime, Date endTime, Profile.ENUM_STATUS status, Profile.ENUM_VALIDATION validation, String error, User user) {
        JinqStream<Profile> _query = jinqJPAStreamProvider.streamAll(getCurrentSession(), Profile.class);

        if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = _deptFullName + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> _deptFullName.equals(i.getSubdistrictFullName()) || JPQL.like(i.getSubdistrictFullName(), _pattern));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (StringUtils.hasLength(credentialNo)) {
            _query = _query.where(i -> i.getCredentialNo() != null && i.getCredentialNo().contains(credentialNo));
        }

        if (StringUtils.hasLength(mp)) {
            _query = _query.where(i -> i.getMp() != null && i.getMp().contains(mp));
        }

        if (StringUtils.hasLength(subdistrictFullName)) {
            String _pattern = subdistrictFullName + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> subdistrictFullName.equals(i.getSubdistrictFullName()) || JPQL.like(i.getSubdistrictFullName(), _pattern));
        }

        if (stay != null) {
            _query = _query.where(i -> stay == i.getStay());
        }

        if (StringUtils.hasLength(crowdId)) {
            String _crowdQuery = "[]".equals(crowdId) ? crowdId : String.format("\"%s\"", crowdId);
            _query = _query.where(i -> i.getCrowdIdsJSON() != null && i.getCrowdIdsJSON().contains(_crowdQuery));
        }

        if (maxAge != null) {
            Date _birthday = DateUtils.truncate(DateUtil.addYears(new Date(), -maxAge), Calendar.DATE);
            _query = _query.where(i -> !i.getBirthday().before(_birthday));
        }

        if (minAge != null) {
            Date _birthday = DateUtils.truncate(DateUtil.addDays(DateUtil.addYears(new Date(), -minAge), 1), Calendar.DATE);
            _query = _query.where(i -> i.getBirthday().before(_birthday));
        }

        if (beginTime != null) {
            _query = _query.where(i -> !i.getCreateTime().before(beginTime));
        }

        if (endTime != null) {
            _query = _query.where(i -> i.getCreateTime().before(endTime));
        }

        if (status != null) {
            _query = _query.where(i -> status.equals(i.getStatus()));
        }

        if (validation != null) {
            _query = _query.where(i -> i.getValidation() == validation);
        }

        if (StringUtils.hasLength(error)) {
            String _error = "\"" + error + "\"";
            _query = _query.leftOuterJoin((i, source) -> source.stream(ProfileValidation.class), (i, j) -> i.getId().equals(j.getProfileId()))
                    .where(i -> i.getTwo() != null && i.getTwo().getCodes() != null && i.getTwo().getCodes().contains(_error))
                    .select(i -> i.getOne());
        }

        return _query;
    }

    @Transactional(readOnly = true)
    public List<Dictionary> crowds() {
        return dictionaryRepository.fuzzy("金凤码-特殊人群", null, true, null);
    }

    @Transactional(readOnly = true)
    public JinqStream<Profile> stream(String superiorDeptFullName, Session session) {
        return (session == null ? stream(Profile.class) : stream(session, Profile.class)).where(i -> i.getSubdistrictFullName().equals(superiorDeptFullName) || i.getSubdistrictFullName().startsWith(superiorDeptFullName + "/"));
    }

    List<Result> validate(Profile item, List<Department> subdistrict) {
        List<Result> _resp = new ArrayList<>();

        if (Profile.ENUM_STATUS.在册 == item.getStatus() || (Profile.ENUM_STATUS.外出 == item.getStatus() && Profile.ENUM_STAY.常住 == item.getStay())) {

            // 校验名称
            if (StringUtils.isEmpty(item.getName()) || !item.getName().matches(NAME_PATTERN)) {
                Result _success = new Result();
                _success.setCode(Result.ENUM_ERROR.P, 17);
                _resp.add(_success);
            }

            // 校验证件号码
            if (StringUtils.isEmpty(item.getCredentialNo()) || (Profile.ENUM_CREDENTIAL_TYPE.身份证 == item.getCredentialType() && !item.getCredentialNo().matches(IDENTITY_NO_PATTERN))) {
                Result _success = new Result();
                _success.setCode(Result.ENUM_ERROR.P, 18);
                _resp.add(_success);
            }

            // 校验手机号码
            if (StringUtils.isEmpty(item.getMp()) || (!item.getMp().matches(CONTACT_PATTERN) && !item.getMp().matches(MP_PATTERN))) {
                Result _success = new Result();
                _success.setCode(Result.ENUM_ERROR.P, 19);
                _resp.add(_success);
            }

            // 校验居住地
            if (StringUtils.isEmpty(item.getSubdistrictId()) && subdistrict.isEmpty()) {
                Result _success = new Result();
                _success.setCode(Result.ENUM_ERROR.P, 20);
                _resp.add(_success);
            } else {
                if (subdistrict.isEmpty()) {
                    subdistrict.add(departmentRepository.get(item.getSubdistrictId(), true).data);
                }
                Department _subdistrict = subdistrict.get(0);

                if (_subdistrict == null || _subdistrict.getLevel() != 4) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 20);
                    _resp.add(_success);
                }
            }
        }

        return _resp;
    }

    void parseCrowdName(List<Profile> items) {
        Map<String, String> _crowds = crowds().stream()
                .collect(Collectors.toMap(Dictionary::getVal, Dictionary::getName));

        for (Profile i : items) {
            if (StringUtils.isEmpty(i.getCrowdIdsJSON())) {
                continue;
            }

            List<String> _ids = ConverterUtil.json2Object(i.getCrowdIdsJSON(), new TypeToken<List<String>>() {
            }.getType());
            i.setCrowdNamesJSON(ConverterUtil.toJson(_ids.stream()
                    .map(_crowds::get)
                    .collect(Collectors.toList())));
        }
    }

    void synchronizeSubjectPersonInfo(Profile profile) {
        if (profile != null && StringUtils.hasLength(profile.getCredentialNo())) {
            String _credentialNo = profile.getCredentialNo();
            String _subjectPersonTypes = stream(PersonDescription.class)
                    .where(p -> p.getCredentialNo().equals(_credentialNo))
                    .sorted(Comparator.comparing(PersonDescription::getType, Comparator.comparing(Enum::ordinal)))
                    .map(p -> p.getType().getDisplayName())
                    .collect(Collectors.joining("，"));
            profile.setSubjectPerson(StringUtils.hasLength(_subjectPersonTypes));
            profile.setSubjectPersonTypes(StringUtils.hasLength(_subjectPersonTypes) ? _subjectPersonTypes : null);
        }
    }

}
