package com.chinamobile.healthcode.repository.stats;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

/**
 * <AUTHOR>
 * @date 8/19/2024 18:07
 */
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "statistic-report", jobGroup = "default", triggerName = "statistic-report", triggerGroup = "default", triggerCron = "0 0 0 * * ?", triggerOnStart = false)
public class StatisticReportJob extends QuartzJobBean {
    private final Logger logger;
    private final DailyStatsReportRepository dailyStatsReportRepository;
    private final RoutineInspectionStatsRepository routineInspectionStatsRepository;

    public StatisticReportJob(DailyStatsReportRepository dailyStatsReportRepository,
                              RoutineInspectionStatsRepository routineInspectionStatsRepository) {
        this.logger = LoggerFactory.getLogger(StatisticReportJob.class);
        this.dailyStatsReportRepository = dailyStatsReportRepository;
        this.routineInspectionStatsRepository = routineInspectionStatsRepository;
    }

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        logger.info("作业开始");

        try {
            dailyStatsReportRepository.executeJob();
            logger.info("更新数据日报表成功");

            routineInspectionStatsRepository.executeJob();
            logger.info("更新日常巡查表成功");
        } catch (Throwable e) {
            this.logger.info("作业执行时程序异常", e);
        } finally {
            this.logger.info("作业结束");
        }
    }
}
