package com.chinamobile.healthcode.repository.stats;

import com.chinamobile.healthcode.model.stats.RoutineInspectionStats;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.util.POIUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import javax.persistence.ParameterMode;
import javax.persistence.StoredProcedureQuery;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 8/13/2024 15:49
 */
@Repository
public class RoutineInspectionStatsRepository extends AbstractJinqRepository {
    public RoutineInspectionStatsRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider) {
        super(entityManagerFactory, jinqJPAStreamProvider);
    }

    @Transactional(readOnly = true)
    public PagingItems<RoutineInspectionStats> fuzzy(int count, int index, Date recordDate) {
        JinqStream<RoutineInspectionStats> _query = stream(RoutineInspectionStats.class)
                .where(ris -> ris.getRecordDate() == recordDate)
                .sortedBy(RoutineInspectionStats::getId);

        PagingItems<RoutineInspectionStats> _page = new PagingItems<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.items = _query.toList();

        return _page;
    }

    @Transactional(readOnly = true)
    public List<RoutineInspectionStats> fuzzy(Date recordDate) {
        return fuzzy(-1, -1, recordDate).items;
    }

    @Transactional(readOnly = true)
    public String export(Date recordDate) throws IOException {
        List<RoutineInspectionStats> _items = fuzzy(-1, -1, recordDate).items;

        return POIUtil.exportToWorkbookBase64String((book) -> {
            Sheet _sheet = book.createSheet();

            // 设置标题行
            Row _row = _sheet.createRow(0);
            _row.createCell(0).setCellValue("类型");
            _row.createCell(1).setCellValue("类别");
            _row.createCell(2).setCellValue("数量");
            _row.createCell(3).setCellValue("增量");

            for (int i = 0; i < _items.size(); i++) {
                _row = _sheet.createRow(i + 1);
                _row.createCell(0).setCellValue(_items.get(i).getCategory());
                _row.createCell(1).setCellValue(_items.get(i).getType());
                _row.createCell(2).setCellValue(_items.get(i).getTotal());
                _row.createCell(3).setCellValue(_items.get(i).getDiff());
            }
        }, "xlsx");
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean executeJob() {
        StoredProcedureQuery _procedureQuery = getCurrentSession().createStoredProcedureQuery("routine_inspection_job");
        _procedureQuery.registerStoredProcedureParameter(0, String.class, ParameterMode.IN);
        _procedureQuery.setParameter(0, "");
        return _procedureQuery.execute();
    }
}
