package com.chinamobile.healthcode.repository.stats;

import com.chinamobile.healthcode.model.citizen.Grid;
import com.chinamobile.healthcode.model.stats.DailyStatsReport;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.POIUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import javax.persistence.ParameterMode;
import javax.persistence.StoredProcedureQuery;
import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 8/13/2024 15:49
 */
@Repository
public class DailyStatsReportRepository extends AbstractJinqRepository {
    private final DepartmentRepository<Department> departmentRepository;

    public DailyStatsReportRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, DepartmentRepository<Department> departmentRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.departmentRepository = departmentRepository;
    }

    @Transactional(readOnly = true)
    public PagingItems<DailyStatsReport> fuzzy(int count, int index, String rootDepartmentId, Date recordDate, User user) {
        PagingItems<DailyStatsReport> _page = new PagingItems<>(count, index);

        String _rootDepartmentFullName;
        if (StringUtils.isBlank(rootDepartmentId)) {
            _rootDepartmentFullName = user.getDeptFullName();
        } else {
            Result<Department> _deptResult = departmentRepository.get(rootDepartmentId, true);
            if (_deptResult.isOK()) {
                _rootDepartmentFullName = _deptResult.data.getFullName();
            } else {
                _page.items = Collections.emptyList();
                _rootDepartmentFullName = null;
            }
        }

        if (StringUtils.isNotBlank(_rootDepartmentFullName)) {
            JinqStream<DailyStatsReport> _query = stream(DailyStatsReport.class)
                    .leftOuterJoin((dsr, session) -> session.stream(Department.class),
                            (dsr, d) -> dsr.getRegionId().equals(d.getId()))
                    .where(pair -> pair.getOne().getRecordDate() == recordDate)
                    .where(pair -> _rootDepartmentFullName.equals(DefaultDepartmentRepository.ROOT_DEPARTMENT_NAME)
                            || pair.getOne().getRegionFullName().contains(_rootDepartmentFullName))
                    .sortedBy(pair -> pair.getTwo().getSeq())
                    .select(Pair::getOne);
            _page.total = _query.count();

            if (count >= 0 && index >= 0) {
                _query = _query.skip((long) count * index).limit(count);
            }
            _page.items = _query.toList();
        }

        return _page;
    }

    @Transactional(readOnly = true)
    public List<DailyStatsReport> fuzzy(String rootDepartmentId, Date recordDate, User user) {
        return fuzzy(-1, -1, rootDepartmentId, recordDate, user).items;
    }

    @Transactional(readOnly = true)
    public String export(String rootDepartmentId, Date recordDate, User user) throws IOException {
        List<DailyStatsReport> _items = fuzzy(-1, -1, rootDepartmentId, recordDate, user).items;

        return POIUtil.exportToWorkbookBase64String((book) -> {
            Sheet _sheet = book.createSheet();

            // 设置标题行
            Row _row = _sheet.createRow(0);
            _row.createCell(0).setCellValue("行政区域");
            _row.createCell(1).setCellValue("当日登录数");
            _row.createCell(2).setCellValue("登录占比率");
            _row.createCell(3).setCellValue("重点人员-总数");
            _row.createCell(4).setCellValue("重点人员-当日增量");
            _row.createCell(5).setCellValue("重点场所-总数");
            _row.createCell(6).setCellValue("重点场所-当日增量");
            _row.createCell(7).setCellValue("重点事件-总数");
            _row.createCell(8).setCellValue("重点事件-当日增量");
            _row.createCell(9).setCellValue("重点设施-总数");
            _row.createCell(10).setCellValue("重点设施-当日增量");
            _row.createCell(11).setCellValue("重点单位-总数");
            _row.createCell(12).setCellValue("重点单位-当日增量");

            for (int i = 0; i < _items.size(); i++) {
                _row = _sheet.createRow(i + 1);
                _row.createCell(0).setCellValue(_items.get(i).getRegionFullName());
                _row.createCell(1).setCellValue(_items.get(i).getNumOfLoginUsers());
                _row.createCell(2).setCellValue(_items.get(i).getPercentageOfLoginUsers());
                _row.createCell(3).setCellValue(_items.get(i).getPersonInTotal());
                _row.createCell(4).setCellValue(_items.get(i).getIncrementOfPerson());
                _row.createCell(5).setCellValue(_items.get(i).getPlaceInTotal());
                _row.createCell(6).setCellValue(_items.get(i).getIncrementOfPlace());
                _row.createCell(7).setCellValue(_items.get(i).getEventInTotal());
                _row.createCell(8).setCellValue(_items.get(i).getIncrementOfEvent());
                _row.createCell(9).setCellValue(_items.get(i).getPropertyInTotal());
                _row.createCell(10).setCellValue(_items.get(i).getIncrementOfProperty());
                _row.createCell(11).setCellValue(_items.get(i).getUnitInTotal());
                _row.createCell(12).setCellValue(_items.get(i).getIncrementOfUnit());
            }
        }, "xlsx");
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean executeJob() {
        StoredProcedureQuery _procedureQuery = getCurrentSession().createStoredProcedureQuery("daily_stats_report_job");
        _procedureQuery.registerStoredProcedureParameter(0, String.class, ParameterMode.IN);
        _procedureQuery.setParameter(0, "");
        return _procedureQuery.execute();
    }
}
