package com.chinamobile.healthcode.repository;

import com.chinamobile.healthcode.model.OperationLog;
import com.chinamobile.healthcode.model.OperationLogQueryDto;
import com.chinamobile.healthcode.repository.subject.DefaultAbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 操作日志Repository
 */
@Repository
public class OperationLogRepository extends DefaultAbstractEntityRepository<OperationLog> {

    public OperationLogRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            Validator validator
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, OperationLog.class, validator);
    }

    /**
     * 分页查询操作日志
     */
    public Page<OperationLog> findByQueryDto(OperationLogQueryDto queryDto) {
        Pageable pageable = PageRequest.of(queryDto.getPage() - 1, queryDto.getSize());
        
        List<OperationLog> logs = stream(OperationLog.class)
                .where(log -> queryDto.getEntityType() == null || log.getEntityType().equals(queryDto.getEntityType()))
                .where(log -> queryDto.getEntityId() == null || log.getEntityId().equals(queryDto.getEntityId()))
                .where(log -> queryDto.getOperationType() == null || log.getOperationType().equals(queryDto.getOperationType()))
                .where(log -> queryDto.getOperatorId() == null || log.getOperatorId().equals(queryDto.getOperatorId()))
                .where(log -> !StringUtils.hasText(queryDto.getOperatorName()) || log.getOperatorName().contains(queryDto.getOperatorName()))
                .where(log -> queryDto.getDeptId() == null || log.getDeptId().equals(queryDto.getDeptId()))
                .where(log -> queryDto.getResult() == null || log.getResult().equals(queryDto.getResult()))
                .where(log -> queryDto.getStartTime() == null || log.getOperateTime().after(queryDto.getStartTime()))
                .where(log -> queryDto.getEndTime() == null || log.getOperateTime().before(queryDto.getEndTime()))
                .sortedBy(log -> log.getOperateTime())
                .skip(pageable.getOffset())
                .limit(pageable.getPageSize())
                .toList();

        long total = stream(OperationLog.class)
                .where(log -> queryDto.getEntityType() == null || log.getEntityType().equals(queryDto.getEntityType()))
                .where(log -> queryDto.getEntityId() == null || log.getEntityId().equals(queryDto.getEntityId()))
                .where(log -> queryDto.getOperationType() == null || log.getOperationType().equals(queryDto.getOperationType()))
                .where(log -> queryDto.getOperatorId() == null || log.getOperatorId().equals(queryDto.getOperatorId()))
                .where(log -> !StringUtils.hasText(queryDto.getOperatorName()) || log.getOperatorName().contains(queryDto.getOperatorName()))
                .where(log -> queryDto.getDeptId() == null || log.getDeptId().equals(queryDto.getDeptId()))
                .where(log -> queryDto.getResult() == null || log.getResult().equals(queryDto.getResult()))
                .where(log -> queryDto.getStartTime() == null || log.getOperateTime().after(queryDto.getStartTime()))
                .where(log -> queryDto.getEndTime() == null || log.getOperateTime().before(queryDto.getEndTime()))
                .count();

        return new PageImpl<>(logs, pageable, total);
    }

    /**
     * 根据实体类型和ID查询操作日志
     */
    public List<OperationLog> findByEntityTypeAndEntityId(String entityType, String entityId) {
        return stream(OperationLog.class)
                .where(log -> log.getEntityType().equals(entityType))
                .where(log -> log.getEntityId().equals(entityId))
                .sortedBy(log -> log.getOperateTime())
                .toList();
    }

    /**
     * 根据操作人ID查询操作日志
     */
    public List<OperationLog> findByOperatorId(String operatorId) {
        return stream(OperationLog.class)
                .where(log -> log.getOperatorId().equals(operatorId))
                .sortedBy(log -> log.getOperateTime())
                .toList();
    }

    /**
     * 根据部门ID查询操作日志
     */
    public List<OperationLog> findByDeptId(String deptId) {
        return stream(OperationLog.class)
                .where(log -> log.getDeptId().equals(deptId))
                .sortedBy(log -> log.getOperateTime())
                .toList();
    }

    /**
     * 根据时间范围查询操作日志
     */
    public List<OperationLog> findByTimeRange(Date startTime, Date endTime) {
        return stream(OperationLog.class)
                .where(log -> log.getOperateTime().after(startTime))
                .where(log -> log.getOperateTime().before(endTime))
                .sortedBy(log -> log.getOperateTime())
                .toList();
    }

    /**
     * 清理指定时间之前的日志
     */
    public int deleteByOperateTimeBefore(Date date) {
        List<OperationLog> logsToDelete = stream(OperationLog.class)
                .where(log -> log.getOperateTime().before(date))
                .toList();
        
        logsToDelete.forEach(log -> getCurrentSession().remove(log));
        getCurrentSession().flush();
        
        return logsToDelete.size();
    }
}
