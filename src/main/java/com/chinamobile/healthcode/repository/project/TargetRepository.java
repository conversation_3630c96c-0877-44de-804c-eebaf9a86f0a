package com.chinamobile.healthcode.repository.project;

import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.healthcode.model.project.Record;
import com.chinamobile.healthcode.model.project.Target;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.orm.jing.MySqlFunctions;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
@ErrorCode(module = "61")
public class TargetRepository extends AbstractEntityRepository<Target> {

    final RecordRepository recordRepository;
    final DefaultUserRepository userRepository;
    final DepartmentRepository departmentRepository;
    final DefaultRoleRepository roleRepository;

    public TargetRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, RecordRepository recordRepository, DefaultUserRepository userRepository, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, Target.class);
        this.recordRepository = recordRepository;
        this.userRepository = userRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
    }

    @Transactional(readOnly = true)
    public Result<Target> get(String id) {
        Result<Target> _item = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Target.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(Target.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Form.class.getSimpleName()});
        }

        return _item;
    }

    /**
     * @param projectId
     * @param user
     * @return 任务是否已签收、是否签收人员，是否村居管理员、目标值
     */
    @Transactional(readOnly = true)
    public Tuple4<Boolean, Boolean, Boolean, Target> getByProjectId(String projectId, User user) {
        boolean _isAdmin = roleRepository.isUserInRole(user.getId(), RecordRepository.COMMUNITY_ADMIN_ROLE);

        String _regionId = user.getDeptId();
        Target _item = stream(Target.class).where(i -> projectId.equals(i.getProjectId()))
                .where(i -> _regionId.equals(i.getRegionId()))
                .findFirst().orElse(null);
        return new Tuple4<>(_item != null, _item != null && Objects.equals(_item.getCreatorId(), user.getId()), _isAdmin, _item);
    }

    public Result<String> save(Target item, User user) {
        Result<String> _id = new Result<>();

        boolean _isNew = false;

        Result<Target> _item = get(item.getId());
        if (_item.isOK()) {
            if (!Objects.equals(_item.data.getCreatorId(), user.getId())) {
                _id.setCode(Result.DATA_ACCESS_DENY);
                return _id;
            }

            _item.data.setMin(item.getMin());
        } else {
            _isNew = true;

            _item.data = new Target();
            _item.data.setRegionId(user.getDeptId());
            _item.data.setRegionFullName(user.getDeptFullName());

            copyProperties(item, _item.data, new String[]{"id", "regionId", "regionFullName"});
        }

        if (_isNew) {
            super.add(_item.data, user.getId());
        } else {
            super.update(_item.data, user.getId());
        }

        _id.data = _item.data.getId();
        return _id;
    }

    /**
     * 任务是否达标
     *
     * @param taskId
     * @return
     */
    @Transactional(readOnly = true)
    public Tuple3<Long, Long, Boolean> qualify(String taskId) {
        Target _item = stream(Target.class).where(i -> taskId.equals(i.getTaskId()) && i.getMin() != null)
                .findFirst().orElse(null);
        if (_item == null) {
            return new Tuple3<>(null, null, false);
        }

        Result<DefaultUser> _admin = userRepository.getByIdOrAccountOrMp(_item.getCreatorId(), null);
        if (!_admin.isOK()) {
            return new Tuple3<>(_item.getMin(), null, false);
        }

        String _projectId = _item.getProjectId();
        String _deptFullName = _admin.data.getDeptFullName();
        String _pattern = String.format("%s/", _deptFullName);
        Long _count = stream(Form.class)
                .where(i -> _projectId.equals(i.getProjectId()))
                .where(i -> i.getRegionFullName().equals(_deptFullName) || i.getRegionFullName().startsWith(_pattern))
                .count();
        return new Tuple3<>(_item.getMin(), _count, _count >= _item.getMin());
    }

    /**
     * @param rootDeptId
     * @param user
     * @return 上级部门id、部门id、部门名称、表单数
     */
    @Transactional(readOnly = true)
    public List<Tuple4<String, String, String, Long>> thenTF(String projectId, String rootDeptId, User user) {
        String _userId = user.getId();
        boolean _isAdmin = roleRepository.isUserInRole(_userId, RecordRepository.ADMIN_ROLE);
        boolean _isCreator = stream(Record.class).where(i -> projectId.equals(i.getId()) && _userId.equals(i.getCreatorId()))
                .findFirst().isPresent();

        String _rootDeptId;
        if (StringUtils.hasLength(rootDeptId)) {
            if (_isAdmin || _isCreator) {
                _rootDeptId = rootDeptId;
            } else {
                List<Department> _subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);

                _rootDeptId = _subordinates.stream()
                        .anyMatch(i -> Objects.equals(rootDeptId, i.getId())) ? rootDeptId : user.getDeptId();
            }
        } else {
            _rootDeptId = _isAdmin || _isCreator ? null : user.getDeptId();
        }

        return tf(projectId, _rootDeptId, user);
    }

    /**
     * @param rootDeptId
     * @param user
     * @return 上级部门id、部门id、部门名称、表单数
     */
    @Transactional(readOnly = true)
    public List<Tuple4<String, String, String, Long>> tf(String projectId, String rootDeptId, User user) {
        JinqStream<Target> _query = stream(Target.class).where(i -> projectId.equals(i.getProjectId()));

        List<Department> _subordinates = departmentRepository.subordinates(rootDeptId, null, null);

        Department _root;
        int _temp;
        if (StringUtils.hasLength(rootDeptId)) {
            _root = _subordinates.stream()
                    .filter(i -> Objects.equals(i.getId(), rootDeptId))
                    .findFirst().orElse(null);

            //_temp = _root.getLevel() + 1;
            _temp = _root.getLevel() == 3 ? 3 : _root.getLevel() + 1;
        } else {
            _temp = 1;
        }

        int _level = _temp;
        List<String> _titles = _level == 1 ? Arrays.asList("金平区", "龙湖区", "濠江区", "澄海区", "潮阳区", "潮南区", "南澳县") : _subordinates.stream()
                .filter(i -> i.getLevel() == _level)
                .map(i -> i.getFullName()).collect(Collectors.toList());

        _query = _query.where(i -> _titles.contains(MySqlFunctions.substringIndex(i.getRegionFullName(), "/", _level)));
        List<Pair<String, Long>> _counts = _query
                .where(i -> i.getMin() != null)
                .group(i -> MySqlFunctions.substringIndex(i.getRegionFullName(), "/", _level), (key, value) -> value.sumLong(i -> i.getMin()))
                .toList();

        return _titles.stream()
                .map(i -> {
                    Department _dept = _subordinates.stream()
                            .filter(j -> Objects.equals(i, j.getFullName()))
                            .findFirst().orElse(null);

                    return new Tuple4<>(_dept == null ? null : _dept.getSuperiorId(), _dept == null ? null : _dept.getId(), i, _counts.stream()
                            .filter(j -> i.equals(j.getOne()))
                            .map(j -> j.getTwo())
                            .findFirst().orElse(null));
                }).collect(Collectors.toList());
    }

}