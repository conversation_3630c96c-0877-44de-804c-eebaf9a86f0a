package com.chinamobile.healthcode.repository.project;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.springframework.scheduling.quartz.QuartzJobBean;

@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "project-urge", jobGroup = "project", triggerName = "project-urge", triggerGroup = "project", triggerCron = "0 0 9 * * * ?", triggerOnStart = false)
public class UrgeJob extends QuartzJobBean {

    final RecordRepository recordRepository;
    final Logger logger;

    public UrgeJob(RecordRepository recordRepository, Logger logger) {
        this.recordRepository = recordRepository;
        this.logger = logger;
    }

    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        logger.info("作业开始");

        try {
            recordRepository.urgeRange(null);
        } catch (Throwable e) {
            this.logger.info("作业执行时程序异常", e);
        } finally {
            this.logger.info("作业结束");
        }
    }

}