package com.chinamobile.healthcode.repository.project;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

/**
 * <AUTHOR>
 * @date 9/28/2023 10:41
 */
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "project-subject-sync", jobGroup = "default", triggerName = "project-subject-sync", triggerGroup = "default", triggerCron = "0 0 * * * ?", triggerOnStart = false)
public class ProjectJob extends QuartzJobBean {
    final Logger logger;
    final RecordRepository recordRepository;
    final FormRepository formRepository;

    public ProjectJob(RecordRepository recordRepository, FormRepository formRepository) {
        this.logger = LoggerFactory.getLogger(ProjectJob.class);
        this.recordRepository = recordRepository;
        this.formRepository = formRepository;
    }

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        logger.info("作业开始");

        try {
            recordRepository.syncForms();
            logger.info(String.format("同步专项工作数据成功"));
        } catch (Throwable e) {
            this.logger.info("作业执行时程序异常", e);
        } finally {
            this.logger.info("作业结束");
        }
    }

}