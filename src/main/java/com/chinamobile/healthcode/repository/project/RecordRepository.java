package com.chinamobile.healthcode.repository.project;

import com.chinamobile.healthcode.model.Task;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.healthcode.model.project.FormQueryDto;
import com.chinamobile.healthcode.model.project.Record;
import com.chinamobile.healthcode.model.project.Target;
import com.chinamobile.healthcode.model.subject.ProjectDescription;
import com.chinamobile.healthcode.repository.TaskRepository;
import com.chinamobile.healthcode.repository.subject.ProjectRepository;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.orm.jing.MySqlFunctions;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.sms.SentSmsRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import org.apache.commons.io.FileUtils;
import org.hibernate.Transaction;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManagerFactory;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@ErrorCode(module = "060")
public class RecordRepository extends AbstractEntityRepository<Record> {

    public static final String ADMIN_ROLE = "专项工作管理员";
    public static final String LEADER_ROLE = "专项工作领导";

    public static final String COMMUNITY_ADMIN_ROLE = "村居管理员";

    final String templateId;
    final int remindInAdvance;
    final String sign;
    final int max = 5000;

    final FormRepository formRepository;
    final TaskRepository taskRepository;
    final DefaultUserRepository userRepository;
    final DefaultDepartmentRepository departmentRepository;
    final RoleRepository roleRepository;
    final SentSmsRepository sentSmsRepository;
    final AbstractMediaRepository mediaRepository;

    final ApplicationContext applicationContext;

    private LocalDateTime preExecTime;

    public RecordRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, @Value(value = "${project.sms-template-id}") String templateId, @Value(value = "${project.remind-in-advance}") int remindInAdvance, @Value(value = "${mas.sms.sign}") String sign, FormRepository formRepository, TaskRepository taskRepository, DefaultUserRepository userRepository, DefaultDepartmentRepository departmentRepository, RoleRepository roleRepository, SentSmsRepository sentSmsRepository, AbstractMediaRepository mediaRepository, ApplicationContext applicationContext) {
        super(entityManagerFactory, jinqJPAStreamProvider, Record.class);
        this.templateId = templateId;
        this.remindInAdvance = remindInAdvance;
        this.sign = sign;
        this.formRepository = formRepository;
        this.taskRepository = taskRepository;
        this.userRepository = userRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.sentSmsRepository = sentSmsRepository;
        this.mediaRepository = mediaRepository;
        this.applicationContext = applicationContext;
        preExecTime = LocalDateTime.now().minusHours(1);
    }

    @Transactional(readOnly = true)
    public Result<Record> get(String id, boolean brief, String userId) {
        Result<Record> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Record.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(Record.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Record.class.getSimpleName()});
        } else {
            parseAssignee(Collections.singletonList(_item.data), brief);
            parseCreator(Collections.singletonList(_item.data), brief, userId);
            parseLegends(Collections.singletonList(_item.data), brief);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<Record> fuzzy(int count, int index, String title, Record.ENUM_STATUS status, User user) {
        JinqStream<Record> _query = stream(Record.class);

        if (StringUtils.hasLength(title)) {
            _query = _query.where(i -> i.getTitle().contains(title));
        }

        if (status != null) {
            if (Record.ENUM_STATUS.OVERTIME == status) {
                Record.ENUM_STATUS _status = Record.ENUM_STATUS.PUBLISHED;
                Date _now = new Date();
                _query = _query.where(i -> _status == i.getStatus() && i.getDeadline() != null && i.getDeadline().before(_now));
            } else {
                _query = _query.where(i -> status == i.getStatus());
            }
        }

        if (!roleRepository.isUserInRole(user.getId(), ADMIN_ROLE) && user != null) {
            // 获取下级单位负责工作
            List<String> _ids = taskRepository.stream(Task.ENUM_TYPE.专项工作, null, user.getDeptFullName(), null)
                    .select(i -> i.getSubjectId())
                    .distinct()
                    .toList();

            String _userId = user.getId();
            _query = _ids.size() > 0 ? _query.where(i -> _ids.contains(i.getId()) || _userId.equals(i.getCreatorId())) : _query.where(i -> _userId.equals(i.getCreatorId()));
        }

        _query = _query.sortedBy(i -> i.getTitle())
                .sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<Record> _page = new PagingItems(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }
        _page.items = _query.toList();

        parseAssignee(_page.items, true);
        parseCreator(_page.items, true, user.getId());

        return _page;
    }

    @Transactional(readOnly = true)
    public List<Record> query(List<String> ids) {
        return stream(Record.class).where(i -> ids.contains(i.getId()))
                .toList();
    }

    public Result<String> save(Record item, String userId, List<String> fields) throws NoSuchFieldException, IllegalAccessException {
        Result<String> _id = new Result<>();
        List<String> _deletedLegendIds = new ArrayList<>();

        boolean _isNew = false, _delay = false;

        Result<Record> _item = get(item.getId(), true, userId);
        if (_item.isOK()) {
            if (_item.data.getReadonly()) {
                _id.setCode(Result.DATA_ACCESS_DENY);
                return _id;
            }
            _deletedLegendIds = StringUtils.hasLength(_item.data.getLegendIdsJSON())
                    ? ConverterUtil.json2Object(_item.data.getLegendIdsJSON(), new TypeToken<List<String>>() {
            }.getType())
                    : new ArrayList<>();
            if (!CollectionUtils.isEmpty(item.getLegendIds())) {
                _deletedLegendIds.removeAll(item.getLegendIds());
            }

            // 截止日期延后，则重置所有个人任务
            if (item.getDeadline() != null && item.getDeadline().after(_item.data.getDeadline())) {
                taskRepository.retractChildren(item.getId(), null, userId);
            }
        } else {
            _isNew = true;

            _item.data = new Record();
        }

        if (!CollectionUtils.isEmpty(item.getAssigneeIds())) {
            item.setAssigneeIdsJSON(ConverterUtil.toJson(item.getAssigneeIds()));
        }
        _item.data.setLegendIdsJSON(CollectionUtils.isEmpty(item.getLegendIds()) ? null : ConverterUtil.toJson(item.getLegendIds()));

        if (CollectionUtils.isEmpty(fields)) {
            copyProperties(item, _item.data, new String[]{"id", "legendIdsJSON"});
        } else {
            for (String i : fields) {
                Field _field = Record.class.getDeclaredField(i);
                _field.setAccessible(true);
                _field.set(_item.data, _field.get(item));
            }
        }

        Result _result = _isNew ? super.add(_item.data, userId) : super.update(_item.data, userId);
        if (_result.isOK()) {
            _deletedLegendIds.forEach(i -> mediaRepository.remove(i, true, userId));
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result<String> saveWithFiles(Record item, String userId, List<String> fields, MultipartFile[] legendFiles) throws NoSuchFieldException, IllegalAccessException, IOException {
        List<String> _legendIds = new ArrayList<>();

        if (legendFiles != null) {
            for (MultipartFile _lf : legendFiles) {
                File _file = new File(FileUtils.getTempDirectory() + File.separator + _lf.getOriginalFilename());
                _lf.transferTo(_file);
                Result<Media> _media = mediaRepository.add(null, _file, true, userId);
                if (_media.isOK()) {
                    _legendIds.add(_media.data.getId());
                }
            }
        }
        if (CollectionUtils.isEmpty(item.getLegendIds())) {
            item.setLegendIds(_legendIds);
        } else {
            item.getLegendIds().addAll(_legendIds);
        }
        return save(item, userId, fields);
    }

    public Result remove(String id, String userId) {
        Result _success = new Result();

        Result<Record> _item = get(id, true, userId);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getReadonly() || _item.data.getStatus() != Record.ENUM_STATUS.DRAFT) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        getCurrentSession().remove(_item.data);

        return _success;
    }

    @Transactional(readOnly = true)
    public Result<JsonArray> getDefinition(String id) {
        Result<JsonArray> _definition = new Result<>();

        Result<Record> _item = get(id, true, null);
        if (!_item.isOK()) {
            return _definition.pack(_item);
        }

        _definition.data = StringUtils.hasLength(_item.data.getFormDefinition()) ? ConverterUtil.json2Object(_item.data.getFormDefinition(), JsonArray.class) : null;
        return _definition;
    }

    public Result launch(String id, User user) {
        Result _success = new Result();

        Result<Record> _item = get(id, true, user.getId());
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getStatus() != Record.ENUM_STATUS.DRAFT) {
            _success.setCode(Result.ENUM_ERROR.B, 1, new Object[]{_item.data.getStatus()});
        }

        List<String> _assigneeIds = StringUtils.hasLength(_item.data.getAssigneeIdsJSON()) ? ConverterUtil.json2Object(_item.data.getAssigneeIdsJSON(), new TypeToken<List<String>>() {
        }.getType()) : null;
        if (CollectionUtils.isEmpty(_assigneeIds)) {
            _success.setCode(Result.ENUM_ERROR.B, 3);
        }

        // 创建任务
        addTasks(id, null, _assigneeIds, user.getId());

        // 初始化数据
        if (_item.data.isInitialized()) {
            initForms(_item.data, user);
        }

        _item.data.setStatus(Record.ENUM_STATUS.PUBLISHED);
        return super.update(_item.data, user.getId());
    }

    public Result close(String id, String userId) {
        Result _success = new Result();

        Result<Record> _item = get(id, true, userId);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getStatus() == Record.ENUM_STATUS.CLOSED) {
            _success.setCode(Result.ENUM_ERROR.B, 2, new Object[]{_item.data.getStatus()});
        }

        // 关闭所有任务
        taskRepository.closeBySubjectId(id, userId);

        _item.data.setStatus(Record.ENUM_STATUS.CLOSED);
        return super.update(_item.data, userId);
    }

    public long addTasks(String id, String previousTaskId, List<String> departmentIds, String userId) {
        long _count = 0;

        // 新增组任务
        List<DefaultDepartment> _departments = departmentRepository.query(departmentIds, null, null, true, null);
        List<Department> _assignees = _departments.stream()
                .filter(i -> i.getLevel() < 3)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(_assignees)) {
            _count += taskRepository.addGroupTasks(previousTaskId, Task.ENUM_TYPE.专项工作, id, _assignees, userId);
        }

        _assignees = _departments.stream()
                .filter(i -> i.getLevel() == 3)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(_assignees)) {
            // 新增个人任务
            List<String> _departmentIds = _assignees.stream()
                    .map(i -> i.getId())
                    .collect(Collectors.toList());
            List<DefaultUser> _users = userRepository.query(null, _departmentIds, null, null, null, true, null);
            if (!CollectionUtils.isEmpty(_users)) {
                for (DefaultUser i : _users) {
                    Department _department = _assignees.stream()
                            .filter(j -> Objects.equals(i.getDeptId(), j.getId()))
                            .findFirst().orElse(null);
                    i.setDeptName(_department.getName());
                    i.setDeptFullName(_department.getFullName());
                }

                _count += taskRepository.addUserTasks(previousTaskId, Task.ENUM_TYPE.专项工作, id, _users, userId);
            }
        }

        // 刷新数据到数据库
        getCurrentSession().flush();

        taskRepository.urge(() -> {
            return taskRepository.querySubsequent(id, previousTaskId, Collections.singletonList(Task.ENUM_STATUS.TODO));
        }, templateId, sign, userId);

        return _count;
    }

    /**
     * 完成率TopN
     *
     * @param topN
     * @param user
     * @return
     */
    @Transactional(readOnly = true)
    public List<Tuple3<String, Long, Long>> statTaskComplete(Integer topN, User user) {
        Task.ENUM_STATUS _done = Task.ENUM_STATUS.DONE;
        Task.ENUM_STATUS _closed = Task.ENUM_STATUS.CLOSED;

        Map<String, Long> _completed, _total;
        List<Pair<String, Task.ENUM_STATUS>> _tasks = scopedTasks(null, user);
        if (_tasks == null) {
            List<Pair<Pair<String, Task.ENUM_STATUS>, Long>> _counts = taskRepository.stream(Task.ENUM_TYPE.专项工作, null, null, null)
                    .group(i -> new Pair<>(i.getSubjectId(), i.getStatus()), (i, stream) -> stream.count()).collect(Collectors.toList());

            _completed = _counts.stream()
                    .filter(i -> _done == i.getOne().getTwo() || _closed == i.getOne().getTwo())
                    .collect(Collectors.groupingBy(i -> i.getOne().getOne(), Collectors.summingLong(i -> i.getTwo())));

            _total = _counts.stream()
                    .collect(Collectors.groupingBy(i -> i.getOne().getOne(), Collectors.summingLong(i -> i.getTwo())));
        } else {
            _completed = _tasks.stream()
                    .filter(i -> _done == i.getTwo() || _closed == i.getTwo())
                    .collect(Collectors.groupingBy(i -> i.getOne(), Collectors.counting()));

            _total = _tasks.stream()
                    .collect(Collectors.groupingBy(i -> i.getOne(), Collectors.counting()));
        }

        List<Tuple3<String, Long, Long>> _pc = new ArrayList<>();
        for (Map.Entry<String, Long> i : _total.entrySet()) {
            Long _count = _completed.getOrDefault(i.getKey(), 0L);
            _pc.add(new Tuple3<>(i.getKey(), _count, i.getValue()));
        }

        List<String> _ids = _total.keySet().stream()
                .collect(Collectors.toList());
        List<Pair<String, String>> _titles = stream(Record.class).where(i -> _ids.contains(i.getId()))
                .select(i -> new Pair<>(i.getId(), i.getTitle()))
                .toList();

        List<Tuple3<String, Long, Long>> _pc2 = new ArrayList<>();
        for (Tuple3<String, Long, Long> i : _pc) {
            String _title = _titles.stream()
                    .filter(j -> Objects.equals(i.getOne(), j.getOne()))
                    .map(j -> j.getTwo())
                    .findFirst().orElse(null);

            _pc2.add(new Tuple3<>(_title, i.getTwo(), i.getThree()));
        }

        _pc2 = _pc2.stream()
                .sorted(Comparator.comparing(i -> i.getTwo() == 0L ? 0D : (i.getThree().doubleValue() / i.getTwo())))
                .collect(Collectors.toList());

        if (topN != null && topN < _pc2.size()) {
            _pc2 = _pc2.subList(0, topN - 1);
        }

        return _pc2;
    }

    /**
     * 统计已读任务
     *
     * @param id
     * @param rootDeptId
     * @param user
     * @return
     */
    @Transactional(readOnly = true)
    public List<Tuple4<String, String, String, Long>> drillDownTaskRead(String id, String rootDeptId, User user) {
        Tuple4<JinqStream<Task>, List<DefaultDepartment>, Integer, List<String>> _dto = tf(id, rootDeptId, user);

        int _level = _dto.getThree();
        List<Pair<String, Long>> _counts = _dto.getOne()
                .where(i -> i.getReadTime() != null)
                .group(i -> MySqlFunctions.substringIndex(i.getDeptFullName(), "/", _level), (key, value) -> value.count())
                .toList();

        return _dto.getFour().stream()
                .map(i -> {
                    Department _dept = _dto.getTwo().stream()
                            .filter(j -> Objects.equals(i, j.getFullName()))
                            .findFirst().orElse(null);

                    return new Tuple4<>(_dept == null ? null : _dept.getSuperiorId(), _dept == null ? null : _dept.getId(), i, _counts.stream()
                            .filter(j -> i.equals(j.getOne()))
                            .map(j -> j.getTwo())
                            .findAny().orElse(0L));
                }).collect(Collectors.toList());
    }

    /**
     * 统计所有任务
     *
     * @param id
     * @param rootDeptId
     * @param user
     * @return
     */
    @Transactional(readOnly = true)
    public List<Tuple4<String, String, String, Long>> drillDownTaskAll(String id, String rootDeptId, User user) {
        Tuple4<JinqStream<Task>, List<DefaultDepartment>, Integer, List<String>> _dto = tf(id, rootDeptId, user);

        int _level = _dto.getThree();
        List<Pair<String, Long>> _counts = _dto.getOne()
                .group(i -> MySqlFunctions.substringIndex(i.getDeptFullName(), "/", _level), (key, value) -> value.count())
                .toList();

        return _dto.getFour().stream()
                .map(i -> {
                    Department _dept = _dto.getTwo().stream()
                            .filter(j -> Objects.equals(i, j.getFullName()))
                            .findFirst().orElse(null);

                    return new Tuple4<>(_dept == null ? null : _dept.getSuperiorId(), _dept == null ? null : _dept.getId(), i, _counts.stream()
                            .filter(j -> i.equals(j.getOne()))
                            .map(j -> j.getTwo())
                            .findAny().orElse(0L));
                }).collect(Collectors.toList());
    }

    /**
     * 统计目标值
     *
     * @param id
     * @param rootDeptId
     * @param user
     * @return
     */
    /* @Transactional(readOnly = true)
    public List<Tuple4<String, String, String, Long>> drillDownTaskTarget(String id, String rootDeptId, User user) {
        Tuple4<JinqStream<Task>, List<Department>, Integer, List<String>> _dto = tf(id, rootDeptId, user);

        int _level = _dto.getThree();
        List<Pair<String, Long>> _counts = _dto.getOne()
                .where(i -> i.getTarget() != null)
                .group(i -> MySqlFunctions.substringIndex(i.getDeptFullName(), "/", _level), (key, value) -> value.sumLong(i -> i.getTarget()))
                .toList();

        return _dto.getFour().stream()
                .map(i -> {
                    Department _dept = _dto.getTwo().stream()
                            .filter(j -> Objects.equals(i, j.getFullName()))
                            .findFirst().orElse(null);

                    return new Tuple4<>(_dept == null ? null : _dept.getSuperiorId(), _dept == null ? null : _dept.getId(), i, _counts.stream()
                            .filter(j -> i.equals(j.getOne()))
                            .map(j -> j.getTwo())
                            .findFirst().orElse(0L));
                }).collect(Collectors.toList());
    } */
    public void urge(String id, String userId) {
        taskRepository.urge(() -> {
            Result<Record> _item = get(id, true, userId);
            if (!_item.isOK()) {
                return new ArrayList<>();
            }

            Task.ENUM_STATUS _todo = Task.ENUM_STATUS.TODO;
            return taskRepository.stream(Task.ENUM_TYPE.专项工作, Collections.singletonList(id), null, null)
                    .where(i -> _todo == i.getStatus())
                    .toList();
        }, templateId, sign, userId);
    }

    public void urgeRange(String userId) {
        taskRepository.urge(() -> {
            Date _deadline = DateUtil.addHours(DateUtil.getDate(new Date()), remindInAdvance);
            Record.ENUM_STATUS _published = Record.ENUM_STATUS.PUBLISHED;
            List<String> _ids = stream(Record.class).where(i -> i.getDeadline() != null && !i.getDeadline().after(_deadline))
                    .where(i -> _published == i.getStatus())
                    .select(i -> i.getId())
                    .toList();

            Task.ENUM_STATUS _todo = Task.ENUM_STATUS.TODO;
            return taskRepository.stream(Task.ENUM_TYPE.专项工作, _ids, null, null)
                    .where(i -> _todo == i.getStatus())
                    .toList();
        }, templateId, sign, userId);
    }

    /**
     * @param rootDeptId
     * @param user
     * @return 任务、下级单位、级别、标题
     */
    protected Tuple4<JinqStream<Task>, List<DefaultDepartment>, Integer, List<String>> tf(String id, String rootDeptId, User user) {
        String _userId = user.getId();
        boolean _isAdmin = roleRepository.isUserInRole(_userId, ADMIN_ROLE);
        boolean _isCreator = stream(Record.class).where(i -> id.equals(i.getId()) && _userId.equals(i.getCreatorId()))
                .findFirst().isPresent();
        boolean _onlyMe = false;
        boolean _isCommunityAdmin = roleRepository.isUserInRole(user.getId(), RecordRepository.COMMUNITY_ADMIN_ROLE);
        boolean _isLeader = roleRepository.isUserInRole(user.getId(), RecordRepository.LEADER_ROLE);

        String _rootDeptId;
        if (StringUtils.hasLength(rootDeptId)) {
            if (_isAdmin || _isCreator) {
                _rootDeptId = rootDeptId;
            } else {
                List<DefaultDepartment> _subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);

                _rootDeptId = _subordinates.stream()
                        .anyMatch(i -> Objects.equals(rootDeptId, i.getId())) ? rootDeptId : user.getDeptId();

                // 村居用户，只允许查看自己的任务
                _onlyMe = _subordinates.stream()
                        .filter(i -> Objects.equals(i.getId(), user.getDeptId())).map(i -> i.getLevel())
                        .findFirst().get() == 3;
            }
        } else {
            _rootDeptId = _isAdmin || _isCreator ? null : user.getDeptId();
        }

        JinqStream<Task> _query = taskRepository.stream(Task.ENUM_TYPE.专项工作, Collections.singletonList(id), null, null);

        if (_onlyMe) {
            if (_isCommunityAdmin || _isLeader) {
                String _deptId = user.getDeptId();
                _query = _query.where(i -> _deptId.equals(i.getDeptId()));
            } else {
                Task.ENUM_ASSIGNEE _assignee = Task.ENUM_ASSIGNEE.USER;
                _query = _query.where(i -> _assignee == i.getAssignee() && _userId.equals(i.getAssigneeId()));
            }
        }

        List<DefaultDepartment> _subordinates = departmentRepository.subordinates(_rootDeptId, null, null);

        Department _root;
        int _temp;
        if (StringUtils.hasLength(_rootDeptId)) {
            _root = _subordinates.stream()
                    .filter(i -> Objects.equals(i.getId(), _rootDeptId))
                    .findFirst().orElse(null);
            if (_root.getLevel() == 3) {
                if (_isCommunityAdmin || _isLeader) {
                    _query = _query.where(i -> _rootDeptId.equals(i.getDeptId()));
                } else {
                    _query = _query.where(i -> _userId.equals(i.getAssigneeId()));
                }

                _temp = 3;
            } else {
                _temp = _root.getLevel() + 1;
            }
        } else {
            _temp = 1;
        }

        int _level = _temp;
        List<String> _titles = _level == 1 ? Arrays.asList("金平区", "龙湖区", "濠江区", "澄海区", "潮阳区", "潮南区", "南澳县") : _subordinates.stream()
                .filter(i -> i.getLevel() == _level)
                .map(i -> i.getFullName()).collect(Collectors.toList());

        _query = _query.where(i -> _titles.contains(MySqlFunctions.substringIndex(i.getDeptFullName(), "/", _level)));

        return new Tuple4<>(_query, _subordinates, _level, _titles);
    }

    protected List<Pair<String, Task.ENUM_STATUS>> scopedTasks(String id, User user) {
        List<Pair<String, Task.ENUM_STATUS>> _tasks;

        if (roleRepository.isUserInRole(user.getId(), ADMIN_ROLE)) {
            // 获取全部任务
            _tasks = StringUtils.hasLength(id) ? taskRepository.stream(null, Collections.singletonList(id), null, null)
                    .select(i -> new Pair<>(i.getSubjectId(), i.getStatus()))
                    .toList() : null;
        } else {
            // 获取本人创建的专项工作
            String _userId = user.getId();
            JinqStream<Record> _query = stream(Record.class).where(i -> _userId.equals(i.getCreatorId()));
            if (StringUtils.hasLength(id)) {
                _query = _query.where(i -> id.equals(i.getId()));
            }
            List<String> _ids = _query.select(i -> i.getId()).toList();

            // 获取本人创始的任务
            List<Tuple3<String, String, Task.ENUM_STATUS>> _temp = CollectionUtils.isEmpty(_ids) ? new ArrayList<>() : taskRepository.stream(Task.ENUM_TYPE.专项工作, _ids, null, null)
                    .select(i -> new Tuple3<>(i.getId(), i.getSubjectId(), i.getStatus()))
                    .toList();
            _tasks = _temp.stream()
                    .map(i -> new Pair<>(i.getTwo(), i.getThree()))
                    .collect(Collectors.toList());

            // 获取本人可见的任务
            JinqStream<Task> _query2 = taskRepository.stream(Task.ENUM_TYPE.专项工作, null, user.getDeptFullName(), null);

            List<String> _taskIds = _temp
                    .stream().map(i -> i.getOne())
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(_taskIds)) {
                _query2 = _query2.where(i -> !_taskIds.contains(i.getId()));
            }

            _tasks.addAll(_query2.select(i -> new Pair<>(i.getSubjectId(), i.getStatus()))
                    .toList());
        }

        return _tasks;
    }

    protected void parseAssignee(List<Record> items, boolean brief) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        for (Record i : items) {
            if (StringUtils.hasLength(i.getAssigneeIdsJSON())) {
                i.setAssigneeIds(ConverterUtil.json2Object(i.getAssigneeIdsJSON(), new TypeToken<List<String>>() {
                }.getType()));
            }
        }

        if (!brief) {
            List<String> _ids = items.stream().filter(i -> !CollectionUtils.isEmpty(i.getAssigneeIds()))
                    .map(i -> i.getAssigneeIds())
                    .flatMap(i -> i.stream())
                    .collect(Collectors.toList());
            List<DefaultDepartment> _departments = CollectionUtils.isEmpty(_ids) ? new ArrayList<>() : departmentRepository.query(_ids, null, null, true, null);
            for (Record i : items) {
                if (CollectionUtils.isEmpty(i.getAssigneeIds())) {
                    continue;
                }

                List<Department> _assignees = _departments.stream()
                        .filter(j -> i.getAssigneeIds().contains(j.getId()))
                        .collect(Collectors.toList());
                i.setAssignees(_assignees);
            }
        }
    }

    protected void parseCreator(List<Record> items, boolean brief, String userId) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        for (Record i : items) {
            i.setReadonly(!Objects.equals(i.getCreatorId(), userId));
        }
    }

    protected void parseLegends(List<Record> items, boolean brief) {
        for (Record i : items) {
            i.setLegendIds(StringUtils.hasLength(i.getLegendIdsJSON()) ? ConverterUtil.json2Object(i.getLegendIdsJSON(), new TypeToken<List<String>>() {
            }.getType()) : null);
        }

        if (!brief) {
            List<String> _ids = items.stream()
                    .filter(i -> i.getLegendIds() != null)
                    .flatMap(i -> i.getLegendIds().stream())
                    .collect(Collectors.toList());

            List<Media> _medias = mediaRepository.query(_ids, null);

            for (Record i : items) {
                i.setLegends(CollectionUtils.isEmpty(i.getLegendIds()) ? null : _medias.stream()
                        .filter(j -> i.getLegendIds().contains(j.getId()))
                        .collect(Collectors.toList()));
            }
        }
    }

    public void initForms(Record record, User user) {
        Map<String, ProjectRepository> _repoMap = applicationContext.getBeansOfType(ProjectRepository.class);
        ProjectRepository _repo = _repoMap.entrySet().stream()
                .filter(e -> e.getValue().getProjectType().equals(record.getType()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(null);

        if (_repo != null) {
            List<ProjectDescription> _desc = _repo.fuzzyProjectDescription(record.getSubtype(), user);
            _desc.stream().map(ProjectDescription::toForm)
                    .forEach(f -> {
                        f.setProjectId(record.getId());
                        formRepository.add(f, null);
                    });
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void syncForms() {
        Date _preExecTime = Date.from(preExecTime.atZone(ZoneId.systemDefault()).toInstant());

        Map<String, ProjectRepository> _repoMap = applicationContext.getBeansOfType(ProjectRepository.class);
        Record.ENUM_STATUS _publishedStatus = Record.ENUM_STATUS.PUBLISHED;
        Record.ENUM_STATUS _closedStatus = Record.ENUM_STATUS.CLOSED;
        Record.ENUM_STATUS _overtimeStatus = Record.ENUM_STATUS.OVERTIME;

        // 专项任务列表
        List<Record> _records = stream(Record.class)
                .where(r -> (r.getStatus() == _publishedStatus && (r.getDeadline() == null || r.getDeadline() != null && r.getDeadline().after(_preExecTime)))
                        || (r.getStatus() == _closedStatus && r.getMaintainTime() != null && r.getMaintainTime().after(_preExecTime))
                        || (r.getStatus() == _overtimeStatus && r.getDeadline() != null && r.getDeadline().after(_preExecTime))
                )
//                .where(r -> r.getId().equals("462476926330601472"))
                .collect(Collectors.toList());

        _records.forEach(r -> {
            try {
                // 专项任务相应类型的repository
                ProjectRepository _repo = _repoMap.entrySet().stream()
                        .filter(e -> e.getValue().getProjectType().equals(r.getType()))
                        .map(Map.Entry::getValue)
                        .findFirst()
                        .orElse(null);

                if (_repo != null) {
                    // 专项任务采集表单列表
                    formRepository.stream(Collections.singletonList(r.getId()), null)
                            // 创建人不为空，新增或修改初始化数据
                            .filter(f -> f.getCreatorId() != null)
                            .forEach(f -> {
                                ProjectDescription _desc = _repo.fromForm(r.getSubtype(), f);
                                if (_desc != null) {
                                    Transaction _transaction = getCurrentSession().beginTransaction();
                                    try {
                                        if (StringUtils.hasLength(f.getRefId())) {
                                            // 初始化数据被更新
                                            Optional.ofNullable(f.getRegionId()).ifPresent(_desc::setRegionId);
                                            Optional.ofNullable(f.getRegionFullName()).ifPresent(_desc::setRegionFullName);
                                            Optional.ofNullable(f.getLng()).ifPresent(_desc::setLng);
                                            Optional.ofNullable(f.getLat()).ifPresent(_desc::setLat);
                                            Optional.ofNullable(f.getLngInWgs()).ifPresent(_desc::setLngInWgs);
                                            Optional.ofNullable(f.getLatInWgs()).ifPresent(_desc::setLatInWgs);
                                            _repo.update(_desc, f.getMaintainerId());
                                        } else {
                                            // 新增记录
                                            _desc.setRegionId(f.getRegionId());
                                            _desc.setRegionFullName(f.getRegionFullName());
                                            _desc.setLng(f.getLng());
                                            _desc.setLat(f.getLat());
                                            _desc.setLngInWgs(f.getLngInWgs());
                                            _desc.setLatInWgs(f.getLatInWgs());
                                            Result<Void> _r = _repo.add(_desc, f.getCreatorId());
                                            if (_r.isOK()) {
                                                f.setRefId(_desc.getId());
                                            }
                                        }
                                        formRepository.update(f, f.getMaintainerId());
                                        _transaction.commit();
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        _transaction.rollback();
                                    }
                                }
                            });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        preExecTime = LocalDateTime.now();
    }

    /**
     * @param user 当前用户信息
     * @return 任务信息、个人提交数、任务目标值
     */
    public Result<List<Tuple3<Task, Long, Long>>> myStats(User user) {
        Result<List<Tuple3<Task, Long, Long>>> _result = new Result<>();
        String _userDeptId = user.getDeptId();

        List<Task> _taskList = taskRepository.fuzzy(-1, -1, null, null, user).items;
        List<String> _projectIdList = _taskList.stream().map(Task::getSubjectId).collect(Collectors.toList());
        List<Target> _targetList = stream(Target.class)
                .where(t -> _projectIdList.contains(t.getProjectId()))
                .where(t -> t.getRegionId().equals(_userDeptId))
                .toList();
        List<Form> _formList = formRepository.stream(null, null, new FormQueryDto(), user)
                .where(f -> _projectIdList.contains(f.getProjectId()))
                .toList();

        Task.ENUM_ASSIGNEE _assigneeUser = Task.ENUM_ASSIGNEE.USER;
        _result.data = _taskList.stream()
                .filter(t -> t.getAssignee() == _assigneeUser)
                .map(t -> {
                    String _projectId = t.getSubjectId();
                    String _taskId = t.getId();
                    long _count = _formList.stream()
                            .filter(f -> f.getProjectId().equals(_projectId))
                            .count();
                    Long _targetNum = _targetList.stream()
                            .filter(target -> target.getProjectId().equals(_projectId))
                            .findFirst()
                            .map(Target::getMin)
                            .orElse(null);

                    return new Tuple3<>(t, _count, _targetNum);
                })
                .collect(Collectors.toList());
        return _result;
    }
}
