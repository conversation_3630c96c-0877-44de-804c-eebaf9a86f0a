package com.chinamobile.healthcode.repository.project;

import com.chinamobile.healthcode.model.Task;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.healthcode.model.project.FormQueryDto;
import com.chinamobile.healthcode.model.project.Record;
import com.chinamobile.healthcode.repository.TaskRepository;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.orm.jing.MySqlFunctions;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.GpsUtil;
import com.google.common.reflect.TypeToken;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
@ErrorCode(module = "060")
public class FormRepository extends AbstractEntityRepository<Form> {

    final RecordRepository recordRepository;
    final TaskRepository taskRepository;
    final UserRepository userRepository;
    final DepartmentRepository departmentRepository;
    final RoleRepository roleRepository;
    final AbstractMediaRepository mediaRepository;

    public FormRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, @Lazy RecordRepository recordRepository, @Lazy TaskRepository taskRepository, UserRepository userRepository, DepartmentRepository departmentRepository, RoleRepository roleRepository, AbstractMediaRepository mediaRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, Form.class);
        this.recordRepository = recordRepository;
        this.taskRepository = taskRepository;
        this.userRepository = userRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.mediaRepository = mediaRepository;
    }

    @Transactional(readOnly = true)
    public Result<Form> get(String id, String userId) {
        Result<Form> _item = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Form.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(Form.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Form.class.getSimpleName()});
        } else {
            parseCreator(Collections.singletonList(_item.data), true, userId);
            parseAttachments(Collections.singletonList(_item.data), true);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<Form> fuzzy(int count, int index, String projectId, String taskId, FormQueryDto formQueryDto, User user) {
        PagingItems<Form> _page = new PagingItems(count, index);

        if (!StringUtils.hasLength(projectId) && !StringUtils.hasLength(taskId)) {
            _page.items = new ArrayList<>();
        } else {
            JinqStream<Form> _query = stream(projectId, taskId, formQueryDto, user);

            _query = _query.sortedDescendingBy(p -> p.getCreateTime());

            _page.total = _query.count();

            if (count >= 0 && index >= 0) {
                _query = _query.skip(count * index).limit(count);
            }
            _page.items = _query.toList();

            parseCreator(_page.items, true, user.getId());

            parseAttachments(_page.items, true);
        }

        return _page;
    }

    public Result<String> save(Form item, User user) {
        Result<String> _id = new Result<>();

        boolean _isNew = false;

        Result<Form> _item = get(item.getId(), user.getId());

        List<String> _ids = new ArrayList<>();
        if (_item.isOK()) {
            if (_item.data.getReadOnly()) {
                _id.setCode(Result.DATA_ACCESS_DENY);
                return _id;
            }
            _ids = StringUtils.hasLength(_item.data.getAttachmentIdsJSON()) ? ConverterUtil.json2Object(_item.data.getAttachmentIdsJSON(), new TypeToken<List<String>>() {
            }.getType()) : new ArrayList<>();

            if (!CollectionUtils.isEmpty(item.getAttachmentIds())) {
                _ids.removeAll(item.getAttachmentIds());
            }

            copyProperties(item, _item.data, new String[]{"id", "projectId", "taskId", "refId", "attachmentIdsJSON"});

            // 初始化数据被更新
            if (StringUtils.hasLength(_item.data.getRefId())
                    && _item.data.getTaskId() == null
                    && _item.data.getCreatorId() == null) {
                _item.data.setCreatorId(user.getId());
                Task _task = taskRepository.getBySubjectIdAndAssigneeId(_item.data.getProjectId(), user);
                if (_task != null) {
                    _item.data.setTaskId(_task.getId());
                }
            }
        } else {
            Result<Record> _r = recordRepository.get(item.getProjectId(), false, user.getId());
            if (!_r.isOK()) {
                return _id.pack(_r);
            }

            long _formsNum = stream(Collections.singletonList(item.getProjectId()), Collections.singletonList(item.getTaskId()))
                    .count();
            if (_r.data.getQuota() != null && _formsNum >= _r.data.getQuota()) {
                _id.setCode(Result.ENUM_ERROR.B, 4);
                return _id;
            }

            _item.data = new Form();
            copyProperties(item, _item.data, new String[]{"id", "attachmentIdsJSON"});
            _isNew = true;
        }

        _item.data.setAttachmentIdsJSON(CollectionUtils.isEmpty(item.getAttachmentIds()) ? null : ConverterUtil.toJson(item.getAttachmentIds()));

        if (Objects.nonNull(_item.data.getLng()) && Objects.nonNull(_item.data.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.data.getLat(), _item.data.getLng());
            _item.data.setLatInWgs(_latLngInWgs[0]);
            _item.data.setLngInWgs(_latLngInWgs[1]);
        }

        Result _success = _isNew ? add(_item.data, user.getId()) : update(_item.data, user.getId());
        if (_success.isOK()) {
            for (String i : _ids) {
                mediaRepository.remove(i, true, user.getId());
            }
        } else {
            return _id.pack(_success);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result<Void> remove(String id, String userId) {
        Result<Void> _success = new Result<>();

        Result<Form> _item = get(id, userId);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getReadOnly()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 删除附件
        if (!CollectionUtils.isEmpty(_item.data.getAttachmentIds())) {
            for (String i : _item.data.getAttachmentIds()) {
                mediaRepository.remove(i, true, userId);
            }
        }

        getCurrentSession().remove(_item.data);

        return _success;
    }

    public JinqStream<Form> stream(String projectId, String taskId, FormQueryDto formQueryDto, User user) {
        JinqStream<Form> _query = stream(Form.class);

        if (StringUtils.hasLength(projectId)) {
            _query = _query.where(i -> projectId.equals(i.getProjectId()));
        }

        if (StringUtils.hasLength(taskId)) {
            _query = _query.where(i -> i.getTaskId() == null || taskId.equals(i.getTaskId()));
        }

        boolean _isAdmin = roleRepository.isUserInRole(user.getId(), RecordRepository.ADMIN_ROLE);
        boolean _isCommunityAdmin = roleRepository.isUserInRole(user.getId(), RecordRepository.COMMUNITY_ADMIN_ROLE);
        boolean _isLeader = roleRepository.isUserInRole(user.getId(), RecordRepository.LEADER_ROLE);
        boolean _isCreator = false;
        if (StringUtils.hasLength(projectId)) {
            Result<Record> _record = recordRepository.get(projectId, true, user.getId());
            _isCreator = _record.isOK() && !_record.data.getReadonly();
        }
        if (!_isAdmin && !_isCreator) {
            // 数据权限
            List<Department> _subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);
            // 排除本单位
            _subordinates = _subordinates.stream()
                    .filter(i -> !Objects.equals(i.getId(), user.getDeptId()))
                    .collect(Collectors.toList());

            // 无下级村居单位，视为采集人员
            if (!_isCommunityAdmin && !_isLeader && !_subordinates.stream().anyMatch(i -> i.getLevel() == 3)) {
                String _userId = user.getId();
                String _deptFullName = user.getDeptFullName();
                // creatorId为null为初始化数据
                _query = _query.where(i -> (i.getCreatorId() == null && i.getRegionFullName().contains(_deptFullName))
                        || _userId.equals(i.getCreatorId()));
            } else {
                // 获取网格
                List<String> _deptIds = _subordinates.stream()
                        .filter(i -> i.getLevel() == 4)
                        .map(i -> i.getId())
                        .collect(Collectors.toList());
                _query = CollectionUtils.isEmpty(_deptIds) ? _query.where(i -> false) : _query.where(i -> i.getRegionId() != null && _deptIds.contains(i.getRegionId()));
            }
        }

        if (StringUtils.hasLength(formQueryDto.getCreator())) {
            Result<User> _user = userRepository.getBriefByIdOrAccountOrMp(formQueryDto.getCreator(), true);
            if (_user.isOK()) {
                String _userId = _user.data.getId();
                _query = _query.where(i -> _userId.equals(i.getCreatorId()));
            } else {
                String _creator = formQueryDto.getCreator();
                _query = _query.where(i -> _creator.equals(i.getCreatorId()));
            }
        }

        if (StringUtils.hasLength(formQueryDto.getRegionFullName())) {
            String _regionFullName = formQueryDto.getRegionFullName();
            _query = _query.where(i -> i.getRegionFullName() != null && i.getRegionFullName().contains(_regionFullName));
        }

        if (StringUtils.hasLength(formQueryDto.getKeyword())) {
            String _keyword = formQueryDto.getKeyword();
            _query = _query.where(i ->
                    i.getPersonName().contains(_keyword)
                            || i.getResidentialAddress().contains(_keyword)
                            || i.getGuardianName().contains(_keyword)
                            || i.getRectifyTimeLimit().contains(_keyword)
                            || i.getEventName().contains(_keyword)
                            || i.getEventAddress().contains(_keyword)
                            || i.getSiteName().contains(_keyword)
                            || i.getDetailedAddress().contains(_keyword)
                            || i.getInstallationName().contains(_keyword)
                            || i.getInstallationAddress().contains(_keyword)
                            || i.getUnitName().contains(_keyword)
                            || i.getUnitAddress().contains(_keyword)
                            || i.getDescription().contains(_keyword)
                            || i.getAddress().contains(_keyword)
            );
        }

        if (formQueryDto.getStartTime() != null) {
            Date _startTime = formQueryDto.getStartTime();
            _query = _query.where(i -> !i.getCreateTime().before(_startTime));
        }

        if (formQueryDto.getEndTime() != null) {
            Date _endTime = formQueryDto.getEndTime();
            _query = _query.where(i -> i.getCreateTime().before(_endTime));
        }

        return _query;
    }

    public JinqStream<Form> stream(List<String> projectIds, List<String> taskIds) {
        JinqStream<Form> _query = stream(Form.class);

        if (projectIds != null) {
            _query = _query.where(i -> projectIds.contains(i.getProjectId()));
        }

        if (taskIds != null) {
            _query = _query.where(i -> taskIds.contains(i.getTaskId()));
        }

        return _query;
    }

    /**
     * @param rootDeptId
     * @param user
     * @return 上级部门id、部门id、部门名称、表单数
     */
    @Transactional(readOnly = true)
    public List<Tuple4<String, String, String, Long>> thenTF(String projectId, String rootDeptId, User user) {
        String _userId = user.getId();
        boolean _isAdmin = roleRepository.isUserInRole(_userId, RecordRepository.ADMIN_ROLE);
        boolean _isCreator = stream(Record.class).where(i -> projectId.equals(i.getId()) && _userId.equals(i.getCreatorId()))
                .findFirst().isPresent();

        String _rootDeptId;
        if (StringUtils.hasLength(rootDeptId)) {
            if (_isAdmin || _isCreator) {
                _rootDeptId = rootDeptId;
            } else {
                List<Department> _subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);

                _rootDeptId = _subordinates.stream()
                        .anyMatch(i -> Objects.equals(rootDeptId, i.getId())) ? rootDeptId : user.getDeptId();
            }
        } else {
            _rootDeptId = _isAdmin || _isCreator ? null : user.getDeptId();
        }

        return tf(projectId, _rootDeptId, user);
    }

    /**
     * @param rootDeptId
     * @param user
     * @return 上级部门id、部门id、部门名称、表单数
     */
    @Transactional(readOnly = true)
    public List<Tuple4<String, String, String, Long>> tf(String projectId, String rootDeptId, User user) {
        JinqStream<Form> _query = user == null ? stream(Form.class).where(i -> projectId.equals(i.getProjectId())) : stream(projectId, null, new FormQueryDto(), user);

        List<Department> _subordinates = departmentRepository.subordinates(rootDeptId, null, null);

        Department _root;
        int _temp;
        if (StringUtils.hasLength(rootDeptId)) {
            _root = _subordinates.stream()
                    .filter(i -> Objects.equals(i.getId(), rootDeptId))
                    .findFirst().orElse(null);

            _temp = _root.getLevel() + 1;
        } else {
            _temp = 1;
        }

        int _level = _temp;
        List<String> _titles = _level == 1 ? Arrays.asList("金平区", "龙湖区", "濠江区", "澄海区", "潮阳区", "潮南区", "南澳县") : _subordinates.stream()
                .filter(i -> i.getLevel() == _level)
                .map(i -> i.getFullName()).collect(Collectors.toList());

        _query = _query.where(i -> _titles.contains(MySqlFunctions.substringIndex(i.getRegionFullName(), "/", _level)));
        List<Pair<String, Long>> _counts = _query.group(i -> MySqlFunctions.substringIndex(i.getRegionFullName(), "/", _level), (key, value) -> value.count())
                .toList();

        return _titles.stream()
                .map(i -> {
                    Department _dept = _subordinates.stream()
                            .filter(j -> Objects.equals(i, j.getFullName()))
                            .findFirst().orElse(null);

                    return new Tuple4<>(_dept == null ? null : _dept.getSuperiorId(), _dept == null ? null : _dept.getId(), i, _counts.stream()
                            .filter(j -> i.equals(j.getOne()))
                            .map(j -> j.getTwo())
                            .findAny().orElse(0L));
                }).collect(Collectors.toList());
    }

    public Long countInitForms(String projectId, User user) {
        String _departmentFullName = user.getDeptFullName();
        return stream(Form.class)
                .where(f -> projectId.equals(f.getProjectId()))
                .where(f -> f.getRefId() != null)
                .where(f -> f.getRegionFullName().contains(_departmentFullName))
                .count();
    }

    void parseCreator(List<Form> items, boolean brief, String userId) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        for (Form i : items) {
            // creatorId为null时初始化数据可读
            i.setReadOnly(!Objects.isNull(i.getCreatorId()) && !Objects.equals(i.getCreatorId(), userId));
        }
    }

    void parseAttachments(List<Form> items, boolean brief) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        for (Form i : items) {
            i.setAttachmentIds(StringUtils.hasLength(i.getAttachmentIdsJSON()) ? ConverterUtil.json2Object(i.getAttachmentIdsJSON(), new TypeToken<List<String>>() {
            }.getType()) : null);
        }

        if (!brief) {
            List<String> _ids = items.stream()
                    .filter(i -> i.getAttachmentIds() != null)
                    .flatMap(i -> i.getAttachmentIds().stream())
                    .collect(Collectors.toList());

            List<Media> _medias = mediaRepository.query(_ids, null);

            for (Form i : items) {
                i.setAttachments(CollectionUtils.isEmpty(i.getAttachmentIds()) ? null : _medias.stream()
                        .filter(j -> i.getAttachmentIds().contains(j.getId()))
                        .collect(Collectors.toList()));
            }
        }
    }

}
