package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.instability.BasicInfo;
import com.chinamobile.healthcode.repository.LoggableAbstractEntityRepository;
import com.chinamobile.healthcode.service.OperationLogService;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.util.List;
import java.util.Map;

/**
 * 带日志功能的基础信息Repository示例
 * 继承自LoggableRepository，自动记录所有增删改操作日志
 */
@Repository
public class LoggableBasicInfoAbstractEntityRepository extends LoggableAbstractEntityRepository<BasicInfo> {

    private final DefaultUserRepository userRepository;

    public LoggableBasicInfoAbstractEntityRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            Validator validator,
            DefaultDepartmentRepository departmentRepository,
            DefaultResultParser resultParser,
            OperationLogService operationLogService,
            DefaultUserRepository userRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, BasicInfo.class, validator, departmentRepository, resultParser, operationLogService);
        this.userRepository = userRepository;
    }

    /**
     * 重写getCurrentUser方法，实现获取用户信息
     */
    @Override
    protected User getCurrentUser(String userId) {
        try {
            // 使用DefaultUserRepository的getBriefByIdOrAccountOrMp方法获取用户信息
            Result<DefaultUser> userResult = userRepository.getBriefByIdOrAccountOrMp(userId, null);
            if (userResult.isOK()) {
                return userResult.data;
            }
        } catch (Exception e) {
            // 如果无法获取用户信息，返回null
        }
        return null;
    }

    /**
     * 实现抽象方法excelRowHandler
     */
    @Override
    public Result<BasicInfo> excelRowHandler(List<DefaultDepartment> subordinates, List<DefaultDepartment> departments, Map<String, Integer> header, String[] rowData, User user) throws IllegalArgumentException {
        // 这里实现具体的Excel行处理逻辑
        // 由于这是一个示例，我们返回一个简单的实现
        Result<BasicInfo> result = new Result<>();
        try {
            BasicInfo basicInfo = new BasicInfo();
            // 设置基本信息
            basicInfo.setRegionId(rowData[header.get("行政区域ID")]);
            basicInfo.setRegionFullName(rowData[header.get("行政区域")]);
            // 设置其他字段...
            
            result.data = basicInfo;
        } catch (Exception e) {
            result.setCode(Result.ENUM_ERROR.P, 1, new Object[]{e.getMessage()});
        }
        return result;
    }
}
