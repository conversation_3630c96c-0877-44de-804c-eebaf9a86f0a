package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.Task;
import com.chinamobile.healthcode.model.instability.InstabilityNotification;
import com.chinamobile.healthcode.model.subject.PersonDescription;
import com.chinamobile.healthcode.repository.TaskRepository;
import com.chinamobile.healthcode.repository.subject.PlaceRepository;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class InstabilityNotificationRepository extends AbstractEntityRepository<InstabilityNotification> {
    private final DefaultDepartmentRepository departmentRepository;
    private final DefaultUserRepository userRepository;
    private final DictionaryRepository dictionaryRepository;
    private final TaskRepository taskRepository;
    private final ComparisonRepository comparisonRepository;

    public InstabilityNotificationRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                                             @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
                                             DefaultDepartmentRepository departmentRepository,
                                             DefaultUserRepository userRepository,
                                             DictionaryRepository dictionaryRepository,
                                             TaskRepository taskRepository,
                                             ComparisonRepository comparisonRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, InstabilityNotification.class);
        this.departmentRepository = departmentRepository;
        this.userRepository = userRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.taskRepository = taskRepository;
        this.comparisonRepository = comparisonRepository;
    }

    @Transactional(readOnly = true)
    public List<InstabilityNotification> listByIds(List<String> ids) {
        return stream(InstabilityNotification.class)
                .where(i -> ids.contains(i.getId()))
                .toList();
    }

    public void addRecordingTasks() {
        List<String> excludedAccountIdList = this.dictionaryRepository.fuzzy("排除名单", "统计账号", true, null)
                .stream()
                .map(Dictionary::getVal)
                .collect(Collectors.toList());
        // 通知镇街一级
        List<DefaultDepartment> departmentList = departmentRepository.query(null, null, null,true, null)
                .stream().filter(d -> d.getLevel() == 2 || d.getLevel() == 3)
                .collect(Collectors.toList());
        List<String> departmentIdList = departmentList.stream().map(DefaultDepartment::getId).collect(Collectors.toList());
        List<DefaultUser> userList = userRepository.query(null, departmentIdList, null, null, null, true, null)
                .stream()
                .filter(u -> !excludedAccountIdList.contains(u.getId()))
                .collect(Collectors.toList());

        // 创建任务通知镇街一级账号
        for (DefaultDepartment department : departmentList) {
            List<DefaultUser> departmentUserList = userList.stream()
                    .filter(u -> u.getDeptId().equals(department.getId()))
                    .collect(Collectors.toList());

            if (!departmentUserList.isEmpty()) {
                InstabilityNotification notification = new InstabilityNotification();
                notification.setTitle("重点事件");
                notification.setContent("请完善9大类重点事件中的相关信息，如：事件概述、事发地点、事发时间等。");
                notification.setNotificationTime(DateUtil.getDate(new Date()));
                add(notification, null);
                taskRepository.addUserTasks(null, Task.ENUM_TYPE.村社专项管理, notification.getId(), departmentUserList, null);
            }
        }
    }

    public void addComparisonWarningTasks() {
        List<String> excludedAccountIdList = this.dictionaryRepository.fuzzy("排除名单", "统计账号", true, null)
                .stream()
                .map(Dictionary::getVal)
                .collect(Collectors.toList());
        // 通知区县、镇街一级
        List<DefaultDepartment> departmentList = departmentRepository.query(null, null, null,true, null)
                .stream().filter(d -> d.getLevel() <= 3)
                .collect(Collectors.toList());
        List<String> departmentIdList = departmentList.stream().map(DefaultDepartment::getId).collect(Collectors.toList());
        List<DefaultUser> userList = userRepository.query(null, departmentIdList, null, null, null, true, null)
                .stream()
                .filter(u -> !excludedAccountIdList.contains(u.getId()))
                .collect(Collectors.toList());

        List<String> villageFullNameList = departmentRepository.query(null, null, null,true, null)
                .stream().filter(d -> d.getLevel() == 3)
                .map(DefaultDepartment::getFullName)
                .collect(Collectors.toList());

        // 重点人员
        List<PersonDescription.ENUM_TYPE> personTypeList = Arrays.asList(PersonDescription.ENUM_TYPE.values());
        String personQueryFields = personTypeList.stream().map(PersonDescription.ENUM_TYPE::getDisplayName)
                .map(n -> "`村社区管理-" + n + "` - `专题管理-" + n + "` AS `" + n + "`")
                .collect(Collectors.joining(", "));

        // 从视图查询数据
        List<Object[]> personResultList = getCurrentSession().createNativeQuery(
                        "SELECT `村/社区`, " + personQueryFields + " FROM subject_instability_personnel_difference")
                .getResultList();

        for (DefaultDepartment department: departmentList) {
            List<Object[]> personResult = personResultList.stream()
                    .filter(o -> villageFullNameList.contains(o[0].toString()))
                    .filter(o -> o[0].toString().contains(department.getFullName()))
                    .collect(Collectors.toList());
            for (Object[] villagePersonDiff: personResult) {
                long totalDiff = Arrays.stream(villagePersonDiff).skip(1)
                        .map(Object::toString)
                        .mapToLong(Long::parseLong)
                        .sum();

                List<DefaultUser> notificationUserList = userList.stream()
                        .filter(u -> u.getDeptId().equals(department.getId()))
                        .collect(Collectors.toList());
                if (totalDiff > 0 && !notificationUserList.isEmpty()) {
                    InstabilityNotification notification = new InstabilityNotification();
                    notification.setTitle("重点人员");
                    String content = "请核对" + villagePersonDiff[0] + "重点人员数量，需补录";
                    for (int i = 1; i < villagePersonDiff.length; i++) {
                        if (Long.parseLong(villagePersonDiff[i].toString()) > 0) {
                            content = content.concat(villagePersonDiff[i] + "位" + personTypeList.get(i - 1).getDisplayName() + "，");
                        }
                    }
                    content = content.concat( "请补充相关重点人员信息。");
                    notification.setContent(content);
                    notification.setNotificationTime(DateUtil.getDate(new Date()));
                    add(notification, null);
                    taskRepository.addUserTasks(null, Task.ENUM_TYPE.综治专题管理, notification.getId(), notificationUserList, null);
                }
            }
        }

        // 重点场所
        List<Dictionary> placeTypeDic = dictionaryRepository.fuzzy(PlaceRepository.TYPE_DICT_NAME, null, true, null);

        // 获取所有或指定类别
        List<String> placeTypeList = placeTypeDic.stream()
                .map(Dictionary::getName)
                .collect(Collectors.toList());

        String placeTypeMappingJson = dictionaryRepository.getVal("重点场所", "村社区管理类型映射", true);
        JsonObject placeTypeMapping = ConverterUtil.json2Object(placeTypeMappingJson, JsonObject.class);

        // 构造SQL字段
        String placeQueryFields = placeTypeList.stream()
                .map(placeTypeMapping::get)
                .filter(Objects::nonNull)
                .map(JsonElement::getAsString)
                .map(n -> "`村社区管理-" + n + "` - `专题管理-" + n + "` AS `" + n + "`")
                .collect(Collectors.joining(", "));


        // 从视图查询数据
        List<Object[]> placeResultList = getCurrentSession().createNativeQuery(
                        "SELECT `村/社区`, " + placeQueryFields + " FROM subject_instability_place_difference")
                .getResultList();

        for (DefaultDepartment department: departmentList) {
            List<Object[]> placeResult = placeResultList.stream()
                    .filter(o -> villageFullNameList.contains(o[0].toString()))
                    .filter(o -> o[0].toString().contains(department.getFullName()))
                    .collect(Collectors.toList());
            for (Object[] villagePlaceDiff: placeResult) {
                long totalDiff = Arrays.stream(villagePlaceDiff).skip(1)
                        .map(Object::toString)
                        .mapToLong(Long::parseLong)
                        .sum();

                List<DefaultUser> notificationUserList = userList.stream()
                        .filter(u -> u.getDeptId().equals(department.getId()))
                        .collect(Collectors.toList());
                if (totalDiff > 0 && !notificationUserList.isEmpty()) {
                    InstabilityNotification notification = new InstabilityNotification();
                    notification.setTitle("重点场所");
                    String content = "请核对" + villagePlaceDiff[0] + "重点场所数量，需补录";
                    for (int i = 1; i < villagePlaceDiff.length; i++) {
                        if (Long.parseLong(villagePlaceDiff[i].toString()) > 0) {
                            content = content.concat(villagePlaceDiff[i] + "个" + placeTypeList.get(i - 1) + "，");
                        }
                    }
                    content = content.concat( "请补充相关重点场所信息。");
                    notification.setContent(content);
                    notification.setNotificationTime(DateUtil.getDate(new Date()));
                    add(notification, null);
                    taskRepository.addUserTasks(null, Task.ENUM_TYPE.综治专题管理, notification.getId(), notificationUserList, null);
                }
            }
        }

        // 重点事件
        String eventTypeMappingJson = dictionaryRepository.getVal("重点事件", "村社区管理类型映射", true);
        JsonObject eventTypeMapping = ConverterUtil.json2Object(eventTypeMappingJson, JsonObject.class);
        List<String> eventTypeList = new ArrayList<>(eventTypeMapping.keySet());
        String eventQueryFields = eventTypeList.stream()
                .map(n -> "`村社区管理-" + n + "` - `专题管理-" + n + "` AS `" + n + "`")
                .collect(Collectors.joining(", "));

        // 从视图查询数据
        List<Object[]> eventResultList = getCurrentSession().createNativeQuery(
                        "SELECT `村/社区`, " + eventQueryFields + " FROM subject_instability_event_difference")
                .getResultList();
        for (DefaultDepartment department: departmentList) {
            List<Object[]> eventResult = eventResultList.stream()
                    .filter(o -> villageFullNameList.contains(o[0].toString()))
                    .filter(o -> o[0].toString().contains(department.getFullName()))
                    .collect(Collectors.toList());
            for (Object[] villageEventDiff: eventResult) {
                long totalDiff = Arrays.stream(villageEventDiff).skip(1)
                        .map(Object::toString)
                        .mapToLong(Long::parseLong)
                        .sum();

                List<DefaultUser> notificationUserList = userList.stream()
                        .filter(u -> u.getDeptId().equals(department.getId()))
                        .collect(Collectors.toList());
                if (totalDiff > 0 && !notificationUserList.isEmpty()) {
                    InstabilityNotification notification = new InstabilityNotification();
                    notification.setTitle("重点事件");
                    String content = "请核对" + villageEventDiff[0] + "重点事件数量，需补录";
                    for (int i = 1; i < villageEventDiff.length; i++) {
                        if (Long.parseLong(villageEventDiff[i].toString()) > 0) {
                            content = content.concat(villageEventDiff[i] + "件" + eventTypeList.get(i - 1) + "，");
                        }
                    }
                    content = content.concat( "请补充相关重点事件信息。");
                    notification.setContent(content);
                    notification.setNotificationTime(DateUtil.getDate(new Date()));
                    add(notification, null);
                    taskRepository.addUserTasks(null, Task.ENUM_TYPE.综治专题管理, notification.getId(), notificationUserList, null);
                }
            }
        }
    }
} 