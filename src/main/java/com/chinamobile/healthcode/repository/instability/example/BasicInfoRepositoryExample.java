package com.chinamobile.healthcode.repository.instability.example;

import com.chinamobile.healthcode.model.instability.BasicInfo;
import com.chinamobile.healthcode.repository.instability.InstabilityBaseRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.User;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * BasicInfoRepository使用示例
 * 展示如何使用InstabilityBaseRepository中的手动日志记录功能
 */
@Repository
public class BasicInfoRepositoryExample extends InstabilityBaseRepository<BasicInfo> {

    public BasicInfoRepositoryExample(EntityManagerFactory entityManagerFactory,
                                    org.jinq.jpa.JinqJPAStreamProvider jinqJPAStreamProvider,
                                    Validator validator,
                                    com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository departmentRepository,
                                    com.chinamobile.sparrow.domain.infra.code.DefaultResultParser resultParser) {
        super(entityManagerFactory, jinqJPAStreamProvider, BasicInfo.class, validator, departmentRepository, resultParser);
    }

    /**
     * 实现抽象方法
     */
    @Override
    public Result<BasicInfo> excelRowHandler(List<DefaultDepartment> subordinates, 
                                           List<DefaultDepartment> departments, 
                                           Map<String, Integer> header, 
                                           String[] rowData, 
                                           User user) throws IllegalArgumentException {
        // 这里实现具体的Excel行处理逻辑
        // 暂时返回空实现
        return new Result<>();
    }

    /**
     * 新增村社区基础信息
     * 会自动记录新增操作日志
     */
    @Transactional
    public Result<String> createBasicInfo(BasicInfo basicInfo, User user) {
        // 调用父类的add方法，会自动记录日志
        Result result = add(basicInfo, user.getId());
        
        if (result.isOK()) {
            // 业务逻辑：创建成功后的处理
            LOGGER.info("成功创建村社区基础信息，ID: {}", basicInfo.getId());
        } else {
            LOGGER.error("创建村社区基础信息失败: {}", result.message);
        }
        
        return result;
    }

    /**
     * 更新村社区基础信息
     * 会自动记录更新操作日志（包括更新前后的数据）
     */
    @Transactional
    public Result<String> updateBasicInfo(BasicInfo basicInfo, User user) {
        // 调用父类的update方法，会自动记录日志
        Result result = update(basicInfo, user.getId());
        
        if (result.isOK()) {
            // 业务逻辑：更新成功后的处理
            LOGGER.info("成功更新村社区基础信息，ID: {}", basicInfo.getId());
        } else {
            LOGGER.error("更新村社区基础信息失败: {}", result.message);
        }
        
        return result;
    }

    /**
     * 批量导入村社区基础信息
     * 每个记录都会自动记录日志
     */
    @Transactional
    public Result<BatchImportResult> batchImportBasicInfo(List<BasicInfo> basicInfoList, User user) {
        BatchImportResult result = new BatchImportResult();
        List<String> successIds = new ArrayList<>();
        List<String> failedIds = new ArrayList<>();
        
        for (BasicInfo basicInfo : basicInfoList) {
            try {
                // 检查是否已存在
                if (isDuplicate(basicInfo)) {
                    // 更新现有记录
                    Result updateResult = update(basicInfo, user.getId());
                    if (updateResult.isOK()) {
                        successIds.add(basicInfo.getId());
                    } else {
                        failedIds.add(basicInfo.getId());
                    }
                } else {
                    // 新增记录
                    Result addResult = add(basicInfo, user.getId());
                    if (addResult.isOK()) {
                        successIds.add(basicInfo.getId());
                    } else {
                        failedIds.add(basicInfo.getId());
                    }
                }
            } catch (Exception e) {
                LOGGER.error("处理村社区基础信息失败: {}", e.getMessage(), e);
                failedIds.add(basicInfo.getId());
            }
        }
        
        result.setSuccessCount(successIds.size());
        result.setFailedCount(failedIds.size());
        result.setSuccessIds(successIds);
        result.setFailedIds(failedIds);
        
        LOGGER.info("批量导入完成，成功: {}, 失败: {}", successIds.size(), failedIds.size());
        
        Result<BatchImportResult> finalResult = new Result<>();
        finalResult.data = result;
        return finalResult;
    }

    /**
     * 批量导入结果类
     */
    public static class BatchImportResult {
        private int successCount;
        private int failedCount;
        private List<String> successIds;
        private List<String> failedIds;
        
        // getter和setter方法
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public List<String> getSuccessIds() { return successIds; }
        public void setSuccessIds(List<String> successIds) { this.successIds = successIds; }
        
        public List<String> getFailedIds() { return failedIds; }
        public void setFailedIds(List<String> failedIds) { this.failedIds = failedIds; }
    }
}
