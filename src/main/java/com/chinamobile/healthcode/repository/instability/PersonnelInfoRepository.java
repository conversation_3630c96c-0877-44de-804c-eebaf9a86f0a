package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.instability.PersonnelInfo;
import com.chinamobile.healthcode.service.OperationLogService;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Repository
public class PersonnelInfoRepository extends InstabilityBaseRepository<PersonnelInfo> {
    private final DefaultRoleRepository roleRepository;

    public PersonnelInfoRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            Validator validator,
            DefaultUserRepository userRepository,
            OperationLogService operationLogService,
            DefaultDepartmentRepository departmentRepository,
            DefaultRoleRepository roleRepository,
            DefaultResultParser resultParser

    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, PersonnelInfo.class, validator, userRepository, operationLogService, departmentRepository, resultParser);
        this.roleRepository = roleRepository;
    }

    @Transactional(readOnly = true)
    public PagingItems<PersonnelInfo> fuzzy(
            int count,
            int index,
            String regionFullName,
            User user
    ) {
        PagingItems<PersonnelInfo> paging = new PagingItems<>(count, index);
        JinqStream<PersonnelInfo> stream = stream(PersonnelInfo.class);

        if (!roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE_NAME)) {
            String userDepartmentName = user.getDeptFullName();
            String pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            stream = stream.where(i -> userDepartmentName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            stream = stream.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), pattern));
        }

        paging.total = stream.count();

        if (count >= 0 && index >= 0) {
            stream = stream.skip((long) count * index).limit(count);
        }
        paging.items = stream.toList();

        return paging;
    }

    @Override
    public Result<PersonnelInfo> excelRowHandler(List<DefaultDepartment> subordinates, List<DefaultDepartment> departments, Map<String, Integer> header, String[] rowData, User user) {
        Result<PersonnelInfo> importResult = new Result<>();
        PersonnelInfo personnelInfo = new PersonnelInfo();

        String regionFullName = rowData[header.get("行政区域")];
        Department region = departments.stream()
                .filter(j -> Objects.equals(regionFullName, j.getFullName()))
                .findFirst().orElse(null);
        if (region == null) {
            importResult.setCode(Result.ENUM_ERROR.P, 3);
            return importResult;
        } else if (region.getLevel() != 3) {
            importResult.setCode(Result.ENUM_ERROR.P, 9, new Object[]{"请选择村、社区一级行政区域"});
            return importResult;
        }
        personnelInfo.setRegionId(region.getId());
        personnelInfo.setRegionFullName(region.getFullName());

        String text = rowData[header.get("政治安全重点人员数")];
        personnelInfo.setNumOfPoliticalSecurityKeyPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("律师重点人员数")];
        personnelInfo.setNumOfLawyerKeyPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("邪教人员总数")];
        personnelInfo.setNumOfCultMembers(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("邪教人员-法轮功人员数")];
        personnelInfo.setNumOfFaLunGongMembers(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("邪教人员-全能神人员数")];
        personnelInfo.setNumOfAlmightyGodMembers(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("邪教人员-其他邪教人员数")];
        personnelInfo.setNumOfOtherCultMembers(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("信访重点人员总数")];
        personnelInfo.setNumOfPetitionKeyPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("信访重点人员-“三跨三分离”人员数")];
        personnelInfo.setNumOfThreeCrossThreeSeparationPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("网络重点人员数")];
        personnelInfo.setNumOfOnlineKeyPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("涉众金融投资受损人员数")];
        personnelInfo.setNumOfFinanciallyAffectedPersons(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("军队退役人员总数")];
        personnelInfo.setNumOfMilitaryVeterans(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("军队退役人员-涉访重点人员数")];
        personnelInfo.setNumOfPetitionRelatedKeyPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("疫苗“受害”人员数")];
        personnelInfo.setNumOfVaccineVictims(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("“三新”从业人员数")];
        personnelInfo.setNumOfThreeNewPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("严重精神障碍患者总数")];
        personnelInfo.setNumOfSevereMentalDisorderPatients(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("严重精神障碍患者-危险性评估三级以上精神障碍患者数")];
        personnelInfo.setNumOfHighRiskMentalDisorderPatients(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("刑满释放安置帮教人员总数")];
        personnelInfo.setNumOfReleasedPrisoners(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("刑满释放安置帮教人员-重点帮教人员数")];
        personnelInfo.setNumOfKeyEducationAndHelpPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("社区矫正人员总数")];
        personnelInfo.setNumOfCommunityCorrectionPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("社区矫正人员-重点社矫人员数")];
        personnelInfo.setNumOfKeyCommunityCorrectionPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("吸毒人员总数")];
        personnelInfo.setNumOfDrugAddicts(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("吸毒人员-社戒社康人员数")];
        personnelInfo.setNumOfCommunityDrugRehabilitations(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("“八类”重点未成年人数")];
        personnelInfo.setNumOfEightCategoriesKeyMinors(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("“三失一偏”人员数")];
        personnelInfo.setNumOfThreeLossesOneBiasPersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("低保在册人员数")];
        personnelInfo.setNumOfRegisteredLowIncomePersonnel(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        importResult.data = personnelInfo;

        return importResult;
    }
}
