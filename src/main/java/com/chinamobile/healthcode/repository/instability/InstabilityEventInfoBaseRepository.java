package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.instability.EventCommitteesDisciplinaryInfo;
import com.chinamobile.healthcode.model.instability.InstabilityEventInfoBaseEntity;
import com.chinamobile.healthcode.model.subject.EventDescription;
import com.chinamobile.healthcode.repository.subject.EventRepository;
import com.chinamobile.healthcode.service.OperationLogService;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.util.Optional;

/**
 * 村（社）专项管理重点事件情况基础实体类
 */
public abstract class InstabilityEventInfoBaseRepository<T extends InstabilityEventInfoBaseEntity> extends InstabilityBaseRepository<T> {

    protected final DefaultRoleRepository roleRepository;
    protected final EventRepository eventRepository;
    private static final Logger LOGGER = LoggerFactory.getLogger(InstabilityEventInfoBaseRepository.class);

    protected InstabilityEventInfoBaseRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            Class<T> tClass,
            Validator validator,
            DefaultUserRepository userRepository,
            OperationLogService operationLogService,
            DefaultDepartmentRepository departmentRepository,
            DefaultResultParser resultParser,
            DefaultRoleRepository roleRepository,
            EventRepository eventRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass, validator, userRepository, operationLogService, departmentRepository, resultParser);
        this.roleRepository = roleRepository;
        this.eventRepository = eventRepository;
    }

    public JinqStream<T> stream(String regionFullName, String category, String type, String title, String overview, User user) {
        JinqStream<T> stream = stream(tClass);

        if (!roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE_NAME)) {
            String userDepartmentName = user.getDeptFullName();
            String pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            stream = stream.where(i -> userDepartmentName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            stream = stream.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), pattern));
        }

        if (StringUtils.hasLength(category)) {
            stream = stream.where(i -> i.getCategory().equals(category));
        }

        if (StringUtils.hasLength(type)) {
            stream = stream.where(i -> i.getType().equals(type));
        }

        if (StringUtils.hasLength(title)) {
            stream = stream.where(i -> i.getTitle() != null && i.getTitle().contains(title));
        }

        if (StringUtils.hasLength(overview)) {
            stream = stream.where(i -> i.getOverview().contains(overview));
        }

        return stream;
    }

    @Transactional(readOnly = true)
    public PagingItems<T> fuzzy(
            int count,
            int index,
            String regionFullName,
            String category,
            String type,
            String title,
            String overview,
            User user
    ) {
        PagingItems<T> paging = new PagingItems<>(count, index);
        JinqStream<T> stream = stream(regionFullName, category, type, title, overview, user);

        paging.total = stream.count();

        if (count >= 0 && index >= 0) {
            stream = stream.skip((long) count * index).limit(count);
        }
        paging.items = stream.toList();

        return paging;
    }

    @Override
    public Result<Void> validateBeforeSaveOrUpdate(T t) {
        Result<Void> validResult = new Result<>();

        Result<DefaultDepartment> deptResult = departmentRepository.get(t.getRegionId(), true);
        if (!deptResult.isOK() || deptResult.data.getLevel() < 3 || deptResult.data.getLevel() > 4) {
            validResult.setCode(Result.ENUM_ERROR.P, 9, new Object[]{"请选择村、社区或网格一级行政区域"});
        }

        return validResult;
    }

    @Override
    public Result<String> saveOrUpdate(T t, User actor) {
        Result<String> result = super.saveOrUpdate(t, actor);

        if (result.isOK()) {
            // 同步到重点事件
            try {
                syncToEventDescription(t, actor);
            } catch (Exception e) {
                LOGGER.error("同步到重点事件失败", e);
            }
        }

        return result;
    }

    @Override
    public T getByUniqueColumns(T eventInfo) {
        String regionId = eventInfo.getRegionId();
        String overview = eventInfo.getOverview();
        return stream(tClass)
                .where(i -> i.getRegionId().equals(regionId))
                .where(i -> i.getOverview().equals(overview))
                .findFirst()
                .orElse(null);
    }

    @Override
    protected void afterImport(T t, User actor) {
        syncToEventDescription(t, actor);
    }

    private void syncToEventDescription(T t, User actor) {
        String regionId = t.getRegionId();
        String overview = t.getOverview();
        // 查找是否存在相同regionId和overview的重点事件
        EventDescription existingEvent = stream(EventDescription.class)
                .where(e -> e.getRegionId().equals(regionId) && e.getDescription().equals(overview))
                .findFirst()
                .orElse(null);

        if (existingEvent != null) {
            // 更新现有事件
            existingEvent.setRegionId(t.getRegionId());
            existingEvent.setRegionFullName(departmentRepository.get(t.getRegionId(), true).data.getFullName());
            existingEvent.setTitle(t.getTitle());
            existingEvent.setType(t.getCategory());
            existingEvent.setSubtype(t.getType());
            existingEvent.setAddress(Optional.ofNullable(t.getAddress()).orElse(""));
            existingEvent.setLng(t.getLngInGcj());
            existingEvent.setLat(t.getLatInGcj());
            existingEvent.setLngInWgs(t.getLngInWgs());
            existingEvent.setLatInWgs(t.getLatInWgs());
            existingEvent.setBeginTime(t.getIncidentTime());

            eventRepository.update(existingEvent, actor.getId());
        } else {
            // 创建新事件
            EventDescription newEvent = new EventDescription();
            newEvent.setRegionId(t.getRegionId());
            newEvent.setRegionFullName(departmentRepository.get(t.getRegionId(), true).data.getFullName());
            newEvent.setTitle(t.getTitle());
            newEvent.setType(t.getCategory());
            newEvent.setSubtype(t.getType());
            newEvent.setDescription(t.getOverview());
            newEvent.setAddress(Optional.ofNullable(t.getAddress()).orElse(""));
            newEvent.setLng(t.getLngInGcj());
            newEvent.setLat(t.getLatInGcj());
            newEvent.setLngInWgs(t.getLngInWgs());
            newEvent.setLatInWgs(t.getLatInWgs());
            newEvent.setBeginTime(t.getIncidentTime());

            eventRepository.add(newEvent, actor.getId());
        }
    }
}
