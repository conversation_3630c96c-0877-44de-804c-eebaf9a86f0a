package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.healthcode.model.instability.PlaceInfo;
import com.chinamobile.healthcode.service.OperationLogService;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Repository
public class PlaceInfoRepository extends InstabilityBaseRepository<PlaceInfo> {
    private final DefaultRoleRepository roleRepository;

    public PlaceInfoRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            Validator validator,
            DefaultUserRepository userRepository,
            OperationLogService operationLogService,
            DefaultDepartmentRepository departmentRepository,
            DefaultRoleRepository roleRepository,
            DefaultResultParser resultParser

    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, PlaceInfo.class, validator, userRepository, operationLogService, departmentRepository, resultParser);
        this.roleRepository = roleRepository;
    }

    @Transactional(readOnly = true)
    public PagingItems<PlaceInfo> fuzzy(
            int count,
            int index,
            String regionFullName,
            User user
    ) {
        PagingItems<PlaceInfo> paging = new PagingItems<>(count, index);
        JinqStream<PlaceInfo> stream = stream(PlaceInfo.class);

        if (!roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE_NAME)) {
            String userDepartmentName = user.getDeptFullName();
            String pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            stream = stream.where(i -> userDepartmentName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            stream = stream.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), pattern));
        }

        paging.total = stream.count();

        if (count >= 0 && index >= 0) {
            stream = stream.skip((long) count * index).limit(count);
        }
        paging.items = stream.toList();

        return paging;
    }

    @Override
    public Result<PlaceInfo> excelRowHandler(List<DefaultDepartment> subordinates, List<DefaultDepartment> departments, Map<String, Integer> header, String[] rowData, User user) {
        Result<PlaceInfo> importResult = new Result<>();
        PlaceInfo placeInfo = new PlaceInfo();

        String regionFullName = rowData[header.get("行政区域")];
        Department region = departments.stream()
                .filter(j -> Objects.equals(regionFullName, j.getFullName()))
                .findFirst().orElse(null);
        if (region == null) {
            importResult.setCode(Result.ENUM_ERROR.P, 3);
            return importResult;
        } else if (region.getLevel() != 3) {
            importResult.setCode(Result.ENUM_ERROR.P, 9, new Object[]{"请选择村、社区一级行政区域"});
            return importResult;
        }
        placeInfo.setRegionId(region.getId());
        placeInfo.setRegionFullName(region.getFullName());

        String text = rowData[header.get("党政机关数")];
        placeInfo.setNumOfGovernmentAgencies(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("繁华商圈总数")];
        placeInfo.setNumOfBusinessAreas(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("繁华商圈-步行街数")];
        placeInfo.setNumOfPedestrianStreets(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("繁华商圈-大型商场数")];
        placeInfo.setNumOfShoppingMalls(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("繁华商圈-城市综合体数")];
        placeInfo.setNumOfUrbanComplexes(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("车站数")];
        placeInfo.setNumOfStations(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("码头数")];
        placeInfo.setNumOfDocks(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("广场数")];
        placeInfo.setNumOfSquares(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("标志性建筑物数")];
        placeInfo.setNumOfLandmarkBuildings(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("天桥数")];
        placeInfo.setNumOfOverpasses(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("公共电子显示屏数")];
        placeInfo.setNumOfLedDisplays(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("文体场馆数")];
        placeInfo.setNumOfCulturalAndSportsVenues(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("旅游景区数")];
        placeInfo.setNumOfTouristAttractions(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("学校总数")];
        placeInfo.setNumOfSchools(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("学校-大学（大专）数")];
        placeInfo.setNumOfCollegesAndUniversities(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("学校-中学数")];
        placeInfo.setNumOfMiddleSchools(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("学校-小学数")];
        placeInfo.setNumOfPrimarySchools(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("学校-幼儿园数")];
        placeInfo.setNumOfKindergartens(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("医疗机构总数")];
        placeInfo.setNumOfMedicalInstitutions(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("医疗机构-医院数")];
        placeInfo.setNumOfHospitals(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("医疗机构-卫生站数")];
        placeInfo.setNumOfHealthStations(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("信访接待场所数")];
        placeInfo.setNumOfPetitionReceptionPlaces(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("宗教场所总数")];
        placeInfo.setNumOfReligiousPlaces(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("宗教场所-私人宗教聚集点数")];
        placeInfo.setNumOfPrivateReligiousPlaces(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("休闲娱乐场所总数")];
        placeInfo.setNumOfEntertainmentPlaces(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("休闲娱乐场所-酒店数")];
        placeInfo.setNumOfHotels(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("休闲娱乐场所-KTV数")];
        placeInfo.setNumOfKtv(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("休闲娱乐场所-酒吧数")];
        placeInfo.setNumOfBars(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("休闲娱乐场所-歌舞厅数")];
        placeInfo.setNumOfDanceHalls(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("休闲娱乐场所-沐足按摩数")];
        placeInfo.setNumOfMassages(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("休闲娱乐场所-棋牌室数")];
        placeInfo.setNumOfChessAndCardRooms(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        text = rowData[header.get("休闲娱乐场所-茶座数")];
        placeInfo.setNumOfTeaHouses(Optional.ofNullable(text).map(Double::parseDouble).map(Double::intValue).orElse(0));

        importResult.data = placeInfo;

        return importResult;
    }
}
