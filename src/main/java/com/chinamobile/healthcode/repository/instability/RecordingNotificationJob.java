package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

/**
 * <AUTHOR>
 */
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "recording-notification", jobGroup = "instability", triggerName = "recording-notification", triggerGroup = "instability", triggerCron = "0 0 0 * * ?", triggerOnStart = false)
public class RecordingNotificationJob extends QuartzJobBean {
    private final Logger logger = LoggerFactory.getLogger(RecordingNotificationJob.class);
    private final InstabilityNotificationRepository instabilityNotificationRepository;

    public RecordingNotificationJob(InstabilityNotificationRepository instabilityNotificationRepository) {
        this.instabilityNotificationRepository = instabilityNotificationRepository;
    }

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        logger.info("作业开始");
        instabilityNotificationRepository.addRecordingTasks();
        this.logger.info("作业结束");
    }
}
