package com.chinamobile.healthcode.repository.instability;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

/**
 * <AUTHOR>
 */
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "comparison-warning", jobGroup = "instability", triggerName = "comparison-warning", triggerGroup = "instability", triggerCron = "0 0 0 ? * MON", triggerOnStart = false)
public class ComparisonWarningJob extends QuartzJobBean {
    private final Logger logger = LoggerFactory.getLogger(ComparisonWarningJob.class);
    private final InstabilityNotificationRepository instabilityNotificationRepository;

    public ComparisonWarningJob(InstabilityNotificationRepository instabilityNotificationRepository) {
        this.instabilityNotificationRepository = instabilityNotificationRepository;
    }

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        logger.info("作业开始");
        instabilityNotificationRepository.addComparisonWarningTasks();
        this.logger.info("作业结束");
    }
}
