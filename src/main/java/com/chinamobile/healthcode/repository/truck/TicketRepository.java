package com.chinamobile.healthcode.repository.truck;

import com.chinamobile.healthcode.model.FormTrace;
import com.chinamobile.healthcode.model.truck.TruckEnterRegistrationPassenger;
import com.chinamobile.healthcode.model.truck.TruckEnterRegistrationTicket;
import com.chinamobile.healthcode.repository.FormTraceRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.POIUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Repository
public class TicketRepository extends AbstractEntityRepository<TruckEnterRegistrationTicket> {

    public static final String ADMIN_SENIOR_ROLE = "货运司机报备-高级管理员";
    public static final String ADMIN_ROLE = "货运司机报备-管理员";

    public static final String DISTRICT_STAFF_ROLE = "区县工作人员";
    public static final String SUBDISTRICT_STAFF_ROLE = "街道工作人员";
    public static final String COMMUNITY_STAFF_ROLE = "社区工作人员";
    public static final String STATION_STAFF_ROLE = "爱心站工作人员";
    public static final String ZONE_DIRECTOR_ROLE = "货主";

    public static final String SECURITY_GUARD_ROLE = "园区安保人员";

    final int exp;
    final String qrCodeTemplate;

    final PassengerRepository passengerRepository;
    final FormTraceRepository formTraceRepository;
    final DefaultUserRepository userRepository;
    final DepartmentRepository departmentRepository;
    final DefaultRoleRepository roleRepository;

    public TicketRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, @Value(value = "${truck.exp}") int exp, @Value(value = "${truck.qr-code-template}") String qrCodeTemplate, PassengerRepository passengerRepository, FormTraceRepository formTraceRepository, DefaultUserRepository userRepository, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, TruckEnterRegistrationTicket.class);

        this.exp = exp;
        this.qrCodeTemplate = qrCodeTemplate;
        this.passengerRepository = passengerRepository;
        this.formTraceRepository = formTraceRepository;
        this.userRepository = userRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<TruckEnterRegistrationTicket> sort(JinqStream<TruckEnterRegistrationTicket> query, BiFunction<JinqStream<TruckEnterRegistrationTicket>, JinqStream.CollectComparable<TruckEnterRegistrationTicket, V>, JinqStream<TruckEnterRegistrationTicket>> compare, String field) {
        switch (field) {
            case "examineTime":
                return compare.apply(query, i -> (V) i.getExamineTime());
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            default:
                return query;
        }
    }

    /**
     * 获取表单
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    public Result<TruckEnterRegistrationTicket> get(String id) {
        Result<TruckEnterRegistrationTicket> _item = new Result<>();

        if (org.springframework.util.StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{TruckEnterRegistrationTicket.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(TruckEnterRegistrationTicket.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        }

        return _item;
    }

    /**
     * 获取表单明细
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    public Result<TruckEnterRegistrationTicket> getWithPassengers(String id) {
        Result<TruckEnterRegistrationTicket> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{TruckEnterRegistrationTicket.class.getSimpleName()});
            return _item;
        }

        List<Pair<TruckEnterRegistrationTicket, TruckEnterRegistrationPassenger>> _relations = stream(TruckEnterRegistrationTicket.class).where(i -> id.equals(i.getId()))
                .leftOuterJoin((i, session) -> session.stream(TruckEnterRegistrationPassenger.class), (i, passenger) -> i.getId().equals(passenger.getTicketId()))
                .toList();

        if (CollectionUtils.isEmpty(_relations)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
            return _item;
        }

        _item.data = _relations.stream().map(i -> i.getOne())
                .findFirst().orElse(null);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
            return _item;
        }

        List<TruckEnterRegistrationPassenger> _passengers = _relations.stream()
                .filter(i -> i.getTwo() != null)
                .map(i -> i.getTwo())
                .sorted(Comparator.comparing(i -> i.getCreateTime()))
                .collect(Collectors.toList());
        _item.data.setPassengerList(_passengers);

        return _item;
    }


    @Transactional(readOnly = true)
    public Result<TruckEnterRegistrationTicket> getWithPassengersAndEditable(String id, User user) {
        Result<TruckEnterRegistrationTicket> _item = getWithPassengers(id);
        if (!_item.isOK()) {
            return _item;
        }

        _item.data = editable(_item.data, user);

        return _item;
    }

    @Transactional(readOnly = true)
    public Result<TruckEnterRegistrationTicket> getWithPassengersAndScanable(String id, User user) {
        Result<TruckEnterRegistrationTicket> _item = getWithPassengers(id);

        _item.data = scanable(_item.data, user);

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<TruckEnterRegistrationTicket> queryByUser(int count, int index, User user) {
        JinqStream<TruckEnterRegistrationTicket> _query = stream(TruckEnterRegistrationTicket.class);

        String _userId = user.getId();
        _query = _query.where(i -> _userId.equals(i.getCreatorId()));

        _query = _query.sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<TruckEnterRegistrationTicket> _page = new PagingItems(count, index);
        _page.total = _query.count();
        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }

        _page.items = _query.toList();

        return _page;
    }

    @Transactional(readOnly = true)
    public PagingItems<TruckEnterRegistrationTicket> fuzzyRelative(int count, int index, User user) {
        String _mp = user.getMp();

        // 作为乘客的报备单
        List<String> _ticketIds = stream(TruckEnterRegistrationPassenger.class)
                .where(i -> _mp.equals(i.getMobileNumber()))
                .select(i -> i.getTicketId())
                .toList();

        // 合并作为司机的报备单
        JinqStream<TruckEnterRegistrationTicket> _query = CollectionUtils.isEmpty(_ticketIds) ? stream(TruckEnterRegistrationTicket.class).where(i -> _mp.equals(i.getDriverMobileNumber())) : stream(TruckEnterRegistrationTicket.class).where(i -> _ticketIds.contains(i.getId()) || _mp.equals(i.getDriverMobileNumber()));

        // 过滤有效期
        Date _min = DateUtil.addDays(new Date(), -exp);
        _query = _query.where(i -> i.getExamineTime() == null || !i.getExamineTime().before(_min));

        _query = _query.sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<TruckEnterRegistrationTicket> _page = new PagingItems(count, index);
        _page.total = _query.count();
        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }

        _page.items = _query.toList();

        return _page;
    }

    @Transactional(readOnly = true)
    public PagingItems<TruckEnterRegistrationTicket> fuzzy(int count, int index, List<SortField> sortFields, String driverName, String driverMp, String licensePlateNumber, String departure, String originRegionFullName, String originCompanyName, String originContactName, String examinerAddress, TruckEnterRegistrationTicket.ENUM_STATUS state, Date departureBeginTime, Date departureEndTime, Date beginTime, Date endTime, User user) {
        JinqStream<TruckEnterRegistrationTicket> _query = stream(driverName, driverMp, licensePlateNumber, departure, originRegionFullName, originCompanyName, originContactName, examinerAddress, state, departureBeginTime, departureEndTime, beginTime, endTime, user);

        if (CollectionUtils.isEmpty(sortFields)) {
            _query = _query.sortedDescendingBy(i -> i.getCreateTime());
        } else {
            _query = sort(_query, sortFields);
        }

        PagingItems<TruckEnterRegistrationTicket> _page = new PagingItems(count, index);
        _page.total = _query.count();
        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }

        _page.items = _query.toList();

        return _page;
    }

    @Transactional(readOnly = true)
    public String export(String driverName, String driverMp, String licensePlateNumber, String departure, String originRegionFullName, String originCompanyName, String originContactName, String examinerAddress, TruckEnterRegistrationTicket.ENUM_STATUS state, Date departureBeginTime, Date departureEndTime, Date beginTime, Date endTime, User user) throws IOException {
        List<TruckEnterRegistrationTicket> _items = fuzzy(-1, -1, null, driverName, driverMp, licensePlateNumber, departure, originRegionFullName, originCompanyName, originContactName, examinerAddress, state, departureBeginTime, departureEndTime, beginTime, endTime, user).items;

        return POIUtil.exportToWorkbookBase64String((book) -> {
            Sheet _sheet = book.createSheet();

            // 设置标题行
            Row _row = _sheet.createRow(0);
            _row.createCell(0).setCellValue("公司名称");
            _row.createCell(1).setCellValue("联系人名称");
            _row.createCell(2).setCellValue("联系人手机号");
            _row.createCell(3).setCellValue("司机姓名");
            _row.createCell(4).setCellValue("手机号");
            _row.createCell(5).setCellValue("车牌号");
            _row.createCell(6).setCellValue("出发地");
            _row.createCell(7).setCellValue("到访行政区划");
            _row.createCell(8).setCellValue("货主注册地");
            _row.createCell(9).setCellValue("指派社区/村居");
            _row.createCell(10).setCellValue("计划到达时间");
            _row.createCell(11).setCellValue("状态");
            _row.createCell(12).setCellValue("创建时间");
            _row.createCell(13).setCellValue("爱心站");
            _row.createCell(14).setCellValue("现场确认人员");
            _row.createCell(15).setCellValue("现场确认时间");

            // 显示身份证号码
            if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_SENIOR_ROLE)) {
                _row.createCell(16).setCellValue("司机身份证号码");
            }

            for (int i = 0; i < _items.size(); i++) {
                _row = _sheet.createRow(i + 1);
                _row.createCell(0).setCellValue(_items.get(i).getOriginCompanyName());
                _row.createCell(1).setCellValue(_items.get(i).getOriginContactName());
                _row.createCell(2).setCellValue(_items.get(i).getOriginContactMobileNumber());
                _row.createCell(3).setCellValue(_items.get(i).getDriverName());
                _row.createCell(4).setCellValue(_items.get(i).getDriverMobileNumber());
                _row.createCell(5).setCellValue(_items.get(i).getLicensePlateNumber());
                _row.createCell(6).setCellValue(_items.get(i).getFromLocation());
                _row.createCell(7).setCellValue(_items.get(i).getOriginRegionFullName());
                _row.createCell(8).setCellValue(_items.get(i).getDestinationRegionFullName());
                _row.createCell(9).setCellValue(_items.get(i).getDispatchedCommunityFullName());
                _row.createCell(10).setCellValue(_items.get(i).getEta() == null ? "" : _items.get(i).getEta().toString());

                if (_items.get(i).getState() == null) {
                    _row.createCell(11).setCellValue("");
                } else {
                    switch (_items.get(i).getState()) {
                        case DRAFT:
                            _row.createCell(11).setCellValue("未审核");
                            break;
                        case ZONE:
                            _row.createCell(11).setCellValue("园区确认");
                            break;
                        case STREET:
                            _row.createCell(11).setCellValue("街道确认");
                            break;
                        case DISPATCHED:
                            _row.createCell(11).setCellValue("任务指派");
                            break;
                        case REJECTED:
                            _row.createCell(11).setCellValue("任务退回");
                            break;
                        case PASSED:
                            _row.createCell(11).setCellValue("现场确认");
                            break;
                        case DENIED:
                            _row.createCell(11).setCellValue("退回");
                            break;
                    }
                }

                _row.createCell(12).setCellValue(_items.get(i).getCreateTime() == null ? "" : _items.get(i).getCreateTime().toString());
                _row.createCell(13).setCellValue(_items.get(i).getExaminerAddress());
                _row.createCell(14).setCellValue(_items.get(i).getExaminerName());
                _row.createCell(15).setCellValue(_items.get(i).getExamineTime() == null ? "" : _items.get(i).getExamineTime().toString());

                // 显示身份证号码
                if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_SENIOR_ROLE)) {
                    _row.createCell(16).setCellValue(_items.get(i).getDriverIdentityCardNumber() == null ? "" : _items.get(i).getDriverIdentityCardNumber());
                }
            }
        }, "xlsx");
    }

    public JinqStream<TruckEnterRegistrationTicket> stream(String driverName, String driverMp, String licensePlateNumber, String departure, String originRegionFullName, String originCompanyName, String originContactName, String examinerAddress, TruckEnterRegistrationTicket.ENUM_STATUS state, Date departureBeginTime, Date departureEndTime, Date beginTime, Date endTime, User user) {
        JinqStream<TruckEnterRegistrationTicket> _query = jinqJPAStreamProvider.streamAll(getCurrentSession(), TruckEnterRegistrationTicket.class);

        String _userId = user.getId(), _mp = user.getMp();

        // 如果是管理员
        if (roleRepository.isUserInRoleCached(_userId, ADMIN_ROLE) || roleRepository.isUserInRoleCached(_userId, ADMIN_SENIOR_ROLE)) {
            _query = _query.where(i -> true);
        }
        // 如果是货主
        else if (roleRepository.isUserInRoleCached(_userId, ZONE_DIRECTOR_ROLE)) {
            _query = org.springframework.util.StringUtils.isEmpty(_mp) ? _query.where(i -> _userId.equals(i.getCreatorId())) : _query.where(i -> _mp.equals(i.getOriginContactMobileNumber()) || _userId.equals(i.getCreatorId()));
        }
        // 上级单位可以查看所有下级单位
        else {
            List<String> _subordinateIds = new ArrayList<>();
            List<Department> _subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);
            if (!CollectionUtils.isEmpty(_subordinates)) {
                _subordinateIds.addAll(_subordinates.stream()
                        .map(i -> i.getId())
                        .collect(Collectors.toList()));
            }

            _query = CollectionUtils.isEmpty(_subordinateIds) ? _query.where(i -> false) : _query.where(i -> (i.getDispatchedCommunityId() != null && _subordinateIds.contains(i.getDispatchedCommunityId())) || (i.getDispatchedCommunityId() == null && i.getDestinationRegionId() != null && _subordinateIds.contains(i.getDestinationRegionId())) || (i.getDispatchedCommunityId() == null && i.getDestinationRegionId() == null && i.getOriginRegionId() != null && _subordinateIds.contains(i.getOriginRegionId())));
        }

        if (org.springframework.util.StringUtils.hasLength(driverName)) {
            _query = _query.where(i -> i.getDriverName() != null && i.getDriverName().contains(driverName));
        }

        if (org.springframework.util.StringUtils.hasLength(driverMp)) {
            _query = _query.where(i -> i.getDriverMobileNumber() != null && i.getDriverMobileNumber().contains(driverMp));
        }

        if (org.springframework.util.StringUtils.hasLength(licensePlateNumber)) {
            _query = _query.where(i -> i.getLicensePlateNumber() != null && i.getLicensePlateNumber().contains(licensePlateNumber));
        }

        if (org.springframework.util.StringUtils.hasLength(departure)) {
            _query = _query.where(i -> i.getFromLocation() != null && i.getFromLocation().contains(departure));
        }

        if (org.springframework.util.StringUtils.hasLength(originRegionFullName)) {
            _query = _query.where(i -> i.getOriginRegionFullName() != null && i.getOriginRegionFullName().contains(originRegionFullName));
        }

        if (org.springframework.util.StringUtils.hasLength(originCompanyName)) {
            _query = _query.where(i -> i.getOriginCompanyName() != null && i.getOriginCompanyName().contains(originCompanyName));
        }

        if (org.springframework.util.StringUtils.hasLength(originContactName)) {
            _query = _query.where(i -> i.getOriginContactName() != null && i.getOriginContactName().contains(originContactName));
        }

        if (org.springframework.util.StringUtils.hasLength(examinerAddress)) {
            _query = _query.where(i -> i.getExaminerAddress() != null && i.getExaminerAddress().contains(examinerAddress));
        }

        if (state != null) {
            _query = _query.where(i -> state == i.getState());
        }

        if (departureBeginTime != null) {
            _query = _query.where(i -> !i.getEta().before(departureBeginTime));
        }

        if (departureEndTime != null) {
            _query = _query.where(i -> i.getEta().before(departureEndTime));
        }

        if (beginTime != null) {
            _query = _query.where(i -> !i.getCreateTime().before(beginTime));
        }

        if (endTime != null) {
            _query = _query.where(i -> i.getCreateTime().before(endTime));
        }

        return _query;
    }

    /**
     * 提交单据
     *
     * @param dto
     * @param actor
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Result submit(TruckEnterRegistrationTicketDto dto, User actor) {
        Result _success = new Result();

        if (StringUtils.isAnyBlank(dto.getOriginRegionId(), dto.getOriginCompanyName(), dto.getOriginContactName(), dto.getOriginContactMobileNumber())) {
            _success.setCode("104_B_001");
        }

        // 获取货主归属的街道
        String _subdistrictId = null, _subdistrictFullName = null;
        try {
            Result<DefaultUser> _staff = userRepository.getByIdOrAccountOrMp(dto.getOriginContactMobileNumber(), true);
            if (_staff.isOK()) {
                _subdistrictId = _staff.data.getDeptId();

                _subdistrictFullName = _staff.data.getAddress();
                int _index = _subdistrictFullName.lastIndexOf("/");
                _subdistrictFullName = _subdistrictFullName.substring(0, _index);
            }
        } catch (Throwable e) {
        }

        TruckEnterRegistrationTicket _item = new TruckEnterRegistrationTicket();

        // 修正街道
        if (org.springframework.util.StringUtils.hasLength(_subdistrictId) && org.springframework.util.StringUtils.hasLength(_subdistrictFullName)) {
            BeanUtils.copyProperties(dto, _item, "id", "destinationRegionId", "destinationRegionFullName");
            _item.setDestinationRegionId(_subdistrictId);
            _item.setDestinationRegionFullName(_subdistrictFullName);
        }
        // 复制街道
        else {
            BeanUtils.copyProperties(dto, _item, "id");
        }

        _item.setState(roleRepository.isUserInRoleCached(actor.getId(), ZONE_DIRECTOR_ROLE) ? TruckEnterRegistrationTicket.ENUM_STATUS.ZONE : TruckEnterRegistrationTicket.ENUM_STATUS.DRAFT);

        _success = add(_item, actor.getId());
        if (!_success.isOK()) {
            return _success;
        }

        for (TruckEnterRegistrationPassenger i : _item.getPassengerList()) {
            i.setTicketId(_item.getId());
            _success = passengerRepository.add(i, actor.getId());
            if (!_success.isOK()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

                return _success;
            }
        }

        // 记录历史
        formTraceRepository.add(_item.getId(), FormTrace.ENUM_FORM_TYPE.货运司机报备, "提交", null, null, actor.getId());

        _success.data = _item;
        return _success;
    }

    /**
     * 修改单据
     *
     * @param dto
     * @param actor
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Result update(TruckEnterRegistrationTicketDto dto, User actor) {
        Result _success = new Result();

        Result<TruckEnterRegistrationTicket> _origin = get(dto.getId());
        if (!_origin.isOK()) {
            return _success.pack(_origin);
        }

        // 必须本人提交，且被退回的报备单才支持修改
        if (!Objects.equals(actor.getId(), _origin.data.getCreatorId()) || TruckEnterRegistrationTicket.ENUM_STATUS.DENIED != _origin.data.getState()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 获取货主归属的街道
        String _subdistrictId = null, _subdistrictFullName = null;
        try {
            Result<DefaultUser> _staff = userRepository.getByIdOrAccountOrMp(dto.getOriginContactMobileNumber(), true);
            if (_staff.isOK()) {
                _subdistrictId = _staff.data.getDeptId();

                _subdistrictFullName = _staff.data.getAddress();
                int _index = _subdistrictFullName.lastIndexOf("/");
                _subdistrictFullName = _subdistrictFullName.substring(0, _index);
            }
        } catch (Throwable e) {
        }

        // 修正街道
        if (org.springframework.util.StringUtils.hasLength(_subdistrictId) && org.springframework.util.StringUtils.hasLength(_subdistrictFullName)) {
            BeanUtils.copyProperties(dto, _origin.data, "id", "destinationRegionId", "destinationRegionFullName");
            _origin.data.setDestinationRegionId(_subdistrictId);
            _origin.data.setDestinationRegionFullName(_subdistrictFullName);
        }
        // 复制街道
        else {
            BeanUtils.copyProperties(dto, _origin.data, "id");
        }

        _origin.data.setState(roleRepository.isUserInRoleCached(actor.getId(), ZONE_DIRECTOR_ROLE) ? TruckEnterRegistrationTicket.ENUM_STATUS.ZONE : TruckEnterRegistrationTicket.ENUM_STATUS.DRAFT);

        _success = update(_origin.data, actor.getId());
        if (!_success.isOK()) {
            return _success;
        }

        // 删除旧的乘客
        passengerRepository.removeRangeByTicketId(dto.getId());

        // 新增乘客
        for (TruckEnterRegistrationPassenger i : dto.getPassengerList()) {
            i.setTicketId(dto.getId());
            _success = passengerRepository.add(i, actor.getId());
            if (!_success.isOK()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

                break;
            }
        }

        // 记录历史
        formTraceRepository.add(_origin.data.getId(), FormTrace.ENUM_FORM_TYPE.货运司机报备, "修改", null, null, actor.getId());

        return _success;
    }

    /**
     * 园区确认
     *
     * @param id
     * @param actor
     * @return
     */
    public Result approve(String id, String opinion, User actor) {
        Result _success = new Result();

        Result<TruckEnterRegistrationTicket> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getState() == TruckEnterRegistrationTicket.ENUM_STATUS.DRAFT) {// 需货主确认
            if (roleRepository.isUserInRoleCached(actor.getId(), ZONE_DIRECTOR_ROLE)) {
                // 非本人负责
                if (!belongTo(_item.data, actor)) {
                    _success.setCode(Result.DATA_ACCESS_DENY);
                    return _success;
                }

                _item.data.setState(TruckEnterRegistrationTicket.ENUM_STATUS.ZONE);
                _success = update(_item.data, actor.getId());

                // 记录历史
                formTraceRepository.add(id, FormTrace.ENUM_FORM_TYPE.货运司机报备, "货主确认", opinion, null, actor.getId());

                return _success;
            }
        }
        _success.setCode(Result.DATA_ACCESS_DENY);

        return _success;
    }

    /**
     * 拒收
     *
     * @param id
     * @param opinion
     * @param actor
     * @return
     */
    public Result refuse(String id, String opinion, User actor) {
        Result _success = new Result();

        Result<TruckEnterRegistrationTicket> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (!belongTo(_item.data, actor)) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        switch (_item.data.getState()) {
            case DRAFT:
                // 货主拒收
                if (roleRepository.isUserInRoleCached(actor.getId(), ZONE_DIRECTOR_ROLE)) {
                    _item.data.setState(TruckEnterRegistrationTicket.ENUM_STATUS.DENIED);
                    _success = update(_item.data, actor.getId());

                    // 记录历史
                    formTraceRepository.add(id, FormTrace.ENUM_FORM_TYPE.货运司机报备, "货主退回", opinion, null, actor.getId());

                    break;
                }
                // 其它情况同街道拒收
            case ZONE:
            case REJECTED:
                if (roleRepository.isUserInRoleCached(actor.getId(), SUBDISTRICT_STAFF_ROLE)) {
                    _item.data.setState(TruckEnterRegistrationTicket.ENUM_STATUS.DENIED);
                    _success = update(_item.data, actor.getId());

                    // 记录历史
                    formTraceRepository.add(id, FormTrace.ENUM_FORM_TYPE.货运司机报备, "街道退回", opinion, null, actor.getId());

                    break;
                }
            default:
                _success.setCode(Result.DATA_ACCESS_DENY);
                break;
        }

        return _success;
    }

    /**
     * 街道指派
     *
     * @param ids
     * @param deptId
     * @param deptFullName
     * @param opinion
     * @param actor
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Result dispatch(List<String> ids, String deptId, String deptFullName, String opinion, User actor) {
        Result _success = new Result();

        if (CollectionUtils.isEmpty(ids)) {
            return _success;
        }

        List<String> _subordinateIds = new ArrayList<>();
        List<Department> _subordinates = departmentRepository.subordinates(actor.getDeptId(), null, null);
        if (!CollectionUtils.isEmpty(_subordinates)) {
            _subordinateIds.addAll(_subordinates.stream()
                    .map(i -> i.getId())
                    .collect(Collectors.toList()));
        }

        List<TruckEnterRegistrationTicket> _items = stream(TruckEnterRegistrationTicket.class).where(i -> ids.contains(i.getId())).toList();
        for (TruckEnterRegistrationTicket i : _items) {
            // 已拒收、已关闭、已现场确认的则不允许指派
            if (TruckEnterRegistrationTicket.ENUM_STATUS.DENIED == i.getState() || TruckEnterRegistrationTicket.ENUM_STATUS.STREET == i.getState() || TruckEnterRegistrationTicket.ENUM_STATUS.PASSED == i.getState()) {
                continue;
            }

            // 非本人负责
            if (org.springframework.util.StringUtils.hasLength(i.getDispatchedCommunityId()) && !_subordinateIds.contains(i.getDispatchedCommunityId())) {
                if (org.springframework.util.StringUtils.hasLength(i.getDestinationRegionId()) && !_subordinateIds.contains(i.getDestinationRegionId())) {
                    if (org.springframework.util.StringUtils.hasLength(i.getOriginRegionId()) && !_subordinateIds.contains(i.getOriginRegionId())) {
                        continue;
                    }
                }
            }

            i.setDispatchedCommunityId(deptId);
            i.setDispatchedCommunityFullName(deptFullName);
            i.setState(TruckEnterRegistrationTicket.ENUM_STATUS.DISPATCHED);

            _success = super.update(i, actor.getId());
            if (!_success.isOK()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

                break;
            }

            // 记录历史
            formTraceRepository.add(i.getId(), FormTrace.ENUM_FORM_TYPE.货运司机报备, "任务指派", opinion, deptFullName, actor.getId());
        }

        return _success;
    }

    /**
     * 社区退回
     *
     * @param id
     * @param opinion
     * @param actor
     * @return
     */
    public Result reject(String id, String opinion, User actor) {
        Result _success = new Result();

        Result<TruckEnterRegistrationTicket> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非指派则不能退回
        if (TruckEnterRegistrationTicket.ENUM_STATUS.DISPATCHED != _item.data.getState()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 非指派对象不能退回
        if (!belongTo(_item.data, actor)) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        // 重置指派单位
        _item.data.setDispatchedCommunityId(null);
        _item.data.setDispatchedCommunityFullName(null);
        _item.data.setState(TruckEnterRegistrationTicket.ENUM_STATUS.REJECTED);
        _success = super.update(_item.data, actor.getId());
        if (!_success.isOK()) {
            return _success;
        }

        // 记录历史
        formTraceRepository.add(id, FormTrace.ENUM_FORM_TYPE.货运司机报备, "任务退回", opinion, actor.getDeptId(), actor.getId());

        return _success;
    }

    /**
     * 任务关闭
     *
     * @param id
     * @param opinion
     * @param actor
     * @return
     */
    public Result close(String id, String opinion, User actor) {
        Result _success = new Result();

        Result<TruckEnterRegistrationTicket> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 已拒收、已关闭则不能关闭
        if (TruckEnterRegistrationTicket.ENUM_STATUS.DENIED == _item.data.getState() || TruckEnterRegistrationTicket.ENUM_STATUS.STREET == _item.data.getState()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        if (!belongTo(_item.data, actor)) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        _item.data.setState(TruckEnterRegistrationTicket.ENUM_STATUS.STREET);
        _success = super.update(_item.data, actor.getId());
        if (!_success.isOK()) {
            return _success;
        }

        // 记录历史
        formTraceRepository.add(id, FormTrace.ENUM_FORM_TYPE.货运司机报备, "街道确认", opinion, null, actor.getId());

        return _success;
    }

    /**
     * 现场确认
     *
     * @param id
     * @param user
     * @return
     */
    public Result forcePass(String id, User user) {
        Result _success = new Result();

        Result<TruckEnterRegistrationTicket> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (!roleRepository.isUserInRoleCached(user.getId(), STATION_STAFF_ROLE)) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        _item.data.setState(TruckEnterRegistrationTicket.ENUM_STATUS.PASSED);
        _item.data.setExaminerId(user.getId());
        _item.data.setExaminerName(user.getName());
        _item.data.setExaminerAddress(user.getAddress());
        _item.data.setExamineTime(new Date());
        _success = update(_item.data, user.getId());
        if (!_success.isOK()) {
            return _success;
        }

        // 记录历史
        formTraceRepository.add(id, FormTrace.ENUM_FORM_TYPE.货运司机报备, "现场确认", null, user.getAddress(), user.getId());

        return _success;
    }

    TruckEnterRegistrationTicket editable(TruckEnterRegistrationTicket item, User user) {
        switch (item.getState()) {
            // 被退回时创建人可以修改
            case DENIED:
                item.setEditable(item.getCreatorId().equals(user.getId()));
                break;
            case DRAFT:
                // 如果是货主
                if (roleRepository.isUserInRoleCached(user.getId(), ZONE_DIRECTOR_ROLE)) {
                    item.setEditable(Objects.equals(item.getOriginContactMobileNumber(), user.getMp()));

                    break;
                }
                // 其它条件
            case ZONE:
                // 不太恰当的写法，需保证DARFT、ZONE状态下dispatchedCommunityId为空
            case DISPATCHED:
                if (roleRepository.isUserInRoleCached(user.getId(), COMMUNITY_STAFF_ROLE)) {
                    item.setEditable(Objects.equals(item.getDispatchedCommunityId(), user.getDeptId()));

                    break;
                }
            case REJECTED:
                if (roleRepository.isUserInRoleCached(user.getId(), SUBDISTRICT_STAFF_ROLE)) {
                    List<String> _subordinateIds = new ArrayList<>();
                    List<Department> _subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);
                    if (!CollectionUtils.isEmpty(_subordinates)) {
                        _subordinateIds.addAll(_subordinates.stream()
                                .map(i -> i.getId())
                                .collect(Collectors.toList()));
                    }

                    if (CollectionUtils.isEmpty(_subordinateIds)) {
                        item.setEditable(false);
                    } else if (org.springframework.util.StringUtils.hasLength(item.getDestinationRegionId())) {
                        item.setEditable(_subordinateIds.contains(item.getDestinationRegionId()));
                    } else if (org.springframework.util.StringUtils.hasLength(item.getOriginRegionId())) {
                        item.setEditable(_subordinateIds.contains(item.getOriginRegionId()));
                    } else {
                        item.setEditable(false);
                    }

                    break;
                }
            default:
                item.setEditable(false);
                break;
        }

        // 二维码访问路径
        item.setUrl(String.format(qrCodeTemplate, item.getId()));

        return item;
    }

    TruckEnterRegistrationTicket scanable(TruckEnterRegistrationTicket item, User actor) {
        // 爱心站或安保人员可扫码
        item.setEditable(item.getState() != TruckEnterRegistrationTicket.ENUM_STATUS.PASSED && (roleRepository.isUserInRoleCached(actor.getId(), STATION_STAFF_ROLE) || roleRepository.isUserInRoleCached(actor.getId(), SECURITY_GUARD_ROLE)));

        return item;
    }

    /**
     * 判断单是否归属货主、街道工作人员、社区工作人员
     *
     * @param ticket
     * @param user
     * @return
     */
    boolean belongTo(TruckEnterRegistrationTicket ticket, User user) {
        if (Objects.equals(ticket.getOriginContactMobileNumber(), user.getMp())) {
            return true;
        }

        // 优先判断指派地，再判断目的地
        String _deptId = ticket.getDispatchedCommunityId();
        if (org.springframework.util.StringUtils.isEmpty(_deptId)) {
            _deptId = ticket.getDestinationRegionId();
        }
        if (org.springframework.util.StringUtils.isEmpty(_deptId)) {
            _deptId = ticket.getOriginRegionId();
        }

        String _deptId2 = _deptId;
        List<Department> _subordinates = departmentRepository.subordinates(user.getDeptId(), null, null);
        return _subordinates.stream()
                .anyMatch(i -> Objects.equals(i.getId(), _deptId2));
    }

    public static class TruckEnterRegistrationTicketDto {
        String id;

        String originRegionId;

        String originRegionFullName;

        String originCompanyName;

        String originContactName;

        String originContactMobileNumber;

        String licensePlateNumber;

        String fromLocation;

        boolean risk;

        String destinationRegionId;

        String destinationRegionFullName;

        String destinationCompanyId;

        String destinationCompanyName;

        Date eta;

        Date etd;

        String driverName;

        TruckEnterRegistrationTicket.ENUM_CREDENTIAL_TYPE credentialType = TruckEnterRegistrationTicket.ENUM_CREDENTIAL_TYPE.身份证;

        String driverIdentityCardNumber;

        String driverMobileNumber;

        Boolean driverGreenHealthCode;

        Boolean driverNucleicAcidNegative;

        TruckEnterRegistrationTicket.ENUM_STATUS state;

        List<TruckEnterRegistrationPassenger> passengerList;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getOriginRegionId() {
            return originRegionId;
        }

        public void setOriginRegionId(String originRegionId) {
            this.originRegionId = originRegionId;
        }

        public String getOriginRegionFullName() {
            return originRegionFullName;
        }

        public void setOriginRegionFullName(String originRegionFullName) {
            this.originRegionFullName = originRegionFullName;
        }

        public String getOriginCompanyName() {
            return originCompanyName;
        }

        public void setOriginCompanyName(String originCompanyName) {
            this.originCompanyName = originCompanyName;
        }

        public String getOriginContactName() {
            return originContactName;
        }

        public void setOriginContactName(String originContactName) {
            this.originContactName = originContactName;
        }

        public String getOriginContactMobileNumber() {
            return originContactMobileNumber;
        }

        public void setOriginContactMobileNumber(String originContactMobileNumber) {
            this.originContactMobileNumber = originContactMobileNumber;
        }

        public String getLicensePlateNumber() {
            return licensePlateNumber;
        }

        public void setLicensePlateNumber(String licensePlateNumber) {
            this.licensePlateNumber = licensePlateNumber;
        }

        public String getFromLocation() {
            return fromLocation;
        }

        public void setFromLocation(String fromLocation) {
            this.fromLocation = fromLocation;
        }

        public boolean getRisk() {
            return risk;
        }

        public void setRisk(boolean risk) {
            this.risk = risk;
        }

        public String getDestinationRegionId() {
            return destinationRegionId;
        }

        public void setDestinationRegionId(String destinationRegionId) {
            this.destinationRegionId = destinationRegionId;
        }

        public String getDestinationRegionFullName() {
            return destinationRegionFullName;
        }

        public void setDestinationRegionFullName(String destinationRegionFullName) {
            this.destinationRegionFullName = destinationRegionFullName;
        }

        public String getDestinationCompanyId() {
            return destinationCompanyId;
        }

        public void setDestinationCompanyId(String destinationCompanyId) {
            this.destinationCompanyId = destinationCompanyId;
        }

        public String getDestinationCompanyName() {
            return destinationCompanyName;
        }

        public void setDestinationCompanyName(String destinationCompanyName) {
            this.destinationCompanyName = destinationCompanyName;
        }

        public Date getEta() {
            return eta;
        }

        public void setEta(Date eta) {
            this.eta = eta;
        }

        public Date getEtd() {
            return etd;
        }

        public void setEtd(Date etd) {
            this.etd = etd;
        }

        public String getDriverName() {
            return driverName;
        }

        public void setDriverName(String driverName) {
            this.driverName = driverName;
        }

        public TruckEnterRegistrationTicket.ENUM_CREDENTIAL_TYPE getCredentialType() {
            return credentialType;
        }

        public void setCredentialType(TruckEnterRegistrationTicket.ENUM_CREDENTIAL_TYPE credentialType) {
            this.credentialType = credentialType;
        }

        public String getDriverIdentityCardNumber() {
            return driverIdentityCardNumber;
        }

        public void setDriverIdentityCardNumber(String driverIdentityCardNumber) {
            this.driverIdentityCardNumber = driverIdentityCardNumber;
        }

        public String getDriverMobileNumber() {
            return driverMobileNumber;
        }

        public void setDriverMobileNumber(String driverMobileNumber) {
            this.driverMobileNumber = driverMobileNumber;
        }

        public Boolean getDriverGreenHealthCode() {
            return driverGreenHealthCode;
        }

        public void setDriverGreenHealthCode(Boolean driverGreenHealthCode) {
            this.driverGreenHealthCode = driverGreenHealthCode;
        }

        public Boolean getDriverNucleicAcidNegative() {
            return driverNucleicAcidNegative;
        }

        public void setDriverNucleicAcidNegative(Boolean driverNucleicAcidNegative) {
            this.driverNucleicAcidNegative = driverNucleicAcidNegative;
        }

        public TruckEnterRegistrationTicket.ENUM_STATUS getState() {
            return state;
        }

        public void setState(TruckEnterRegistrationTicket.ENUM_STATUS state) {
            this.state = state;
        }

        public List<TruckEnterRegistrationPassenger> getPassengerList() {
            return passengerList;
        }

        public void setPassengerList(List<TruckEnterRegistrationPassenger> passengerList) {
            this.passengerList = passengerList;
        }
    }

}