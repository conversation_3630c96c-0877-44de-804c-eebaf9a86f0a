package com.chinamobile.healthcode.repository.truck;

import com.chinamobile.healthcode.model.truck.TruckEnterRegistrationTicket;
import com.chinamobile.sparrow.domain.infra.orm.jing.MySqlFunctions;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

@Component(value = "truckStatisticService")
@Transactional(readOnly = true)
public class StatisticService extends AbstractJinqRepository {

    final TicketRepository ticketRepository;

    public StatisticService(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, TicketRepository ticketRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider);

        this.ticketRepository = ticketRepository;
    }

    /**
     * 新增工单
     *
     * @param begin
     * @param end
     * @param user
     * @return
     */
    public List<Pair<String, Long>> dnf(Date begin, Date end, User user) {
        List<Pair<String, Long>> _query = scope(begin, end, user)
                .group(i -> MySqlFunctions.dateFormat(i.getCreateTime(), "%Y-%m-%d"), (key, value) -> value.count())
                .toList();

        List<Pair<String, Long>> _nums = _query.stream()
                .sorted(Comparator.comparing(i -> DateUtil.from(i.getOne(), "yyyy-MM-dd")))
                .collect(Collectors.toList());

        List<Pair<String, Long>> _counts = new ArrayList<>();
        for (Date i = begin; i.before(end); i = DateUtil.addDays(i, 1)) {
            String _i = DateUtil.toString(i, "yyyy-MM-dd");
            Pair<String, Long> _num = _nums.stream()
                    .filter(j -> Objects.equals(_i, j.getOne()))
                    .findFirst().orElse(null);
            _counts.add(_num == null ? new Pair<>(_i, 0L) : new Pair<>(_num.getOne(), _num.getTwo().longValue()));
        }

        return _counts;
    }

    JinqStream<TruckEnterRegistrationTicket> scope(Date begin, Date end, User user) {
        JinqStream<TruckEnterRegistrationTicket> _query;
        // 全局范围
        if (user == null) {
            _query = stream(TruckEnterRegistrationTicket.class);

            if (begin != null) {
                _query = _query.where(i -> !i.getCreateTime().before(begin));
            }

            if (end != null) {
                _query = _query.where(i -> i.getCreateTime().before(end));
            }
        }
        // 个人权限范围
        else {
            _query = ticketRepository.stream(null, null, null, null, null, null, null, null, null, null, null, begin, end, user);
        }

        return _query;
    }

}