package com.chinamobile.healthcode.repository.truck;

import com.chinamobile.healthcode.model.truck.TruckEnterRegistrationPassenger;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManagerFactory;
import java.util.List;

@Repository
public class PassengerRepository extends AbstractEntityRepository<TruckEnterRegistrationPassenger> {

    public PassengerRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider) {
        super(entityManagerFactory, jinqJPAStreamProvider, TruckEnterRegistrationPassenger.class);
    }

    @Override
    public Result add(TruckEnterRegistrationPassenger item, String actorId) {
        Result _success = super.add(item, actorId);

        return _success;
    }

    public Result removeRangeByTicketId(String ticketId) {
        Result _success = new Result();

        List<TruckEnterRegistrationPassenger> _items = stream(TruckEnterRegistrationPassenger.class)
                .where(i -> ticketId.equals(i.getTicketId()))
                .toList();

        for (TruckEnterRegistrationPassenger i : _items) {
            getCurrentSession().remove(i);
        }

        return _success;
    }

}