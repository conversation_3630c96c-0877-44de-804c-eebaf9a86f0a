package com.chinamobile.healthcode.repository.grid;

import com.chinamobile.healthcode.model.grid.Attendant;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.stream.Collectors;

@Repository
@Transactional(readOnly = true)
public class AttendantRepository extends AbstractJinqRepository {

    final DefaultDepartmentRepository departmentRepository;

    public AttendantRepository(
            @Qualifier(value = "frSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "frJinqJPAStreamProvider") JinqJPAStreamProvider jinqJpaStreamProvider,
            DefaultDepartmentRepository departmentRepository
    ) {
        super(entityManagerFactory, jinqJpaStreamProvider);
        this.departmentRepository = departmentRepository;
    }

    public Long number(String regionFullName) {
        JinqStream<Attendant> _query = stream(Attendant.class);

        if (StringUtils.hasLength(regionFullName)) {
            Result<DefaultDepartment> _department = departmentRepository.getByFullName(regionFullName);
            if (!_department.isOK()) {
                return 0L;
            }

            List<String> _ids = departmentRepository.subordinates(_department.data.getId(), null, null)
                    .stream().map(Department::getId)
                    .collect(Collectors.toList());
            _ids.remove(_department.data.getId());

            _query = _query.where(i -> _ids.contains(i.getOrganizationId()));
        }

        return _query.count();
    }

}