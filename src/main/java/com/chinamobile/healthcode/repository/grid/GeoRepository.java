package com.chinamobile.healthcode.repository.grid;

import com.chinamobile.healthcode.model.grid.Geo;
import com.chinamobile.healthcode.repository.citizen.ProfileStatisticRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.util.POIUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Repository
@Transactional(readOnly = true)
public class GeoRepository extends AbstractJinqRepository {

    final ProfileStatisticRepository profileStatisticRepository;
    final DefaultDepartmentRepository departmentRepository;

    public GeoRepository(
            @Qualifier(value = "frSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "frJinqJPAStreamProvider") JinqJPAStreamProvider jinqJpaStreamProvider,
            ProfileStatisticRepository profileStatisticRepository,
            DefaultDepartmentRepository departmentRepository
    ) {
        super(entityManagerFactory, jinqJpaStreamProvider);
        this.profileStatisticRepository = profileStatisticRepository;
        this.departmentRepository = departmentRepository;
    }

    public long countDone(List<String> gridIds) {
        return stream(Geo.class).where(i -> gridIds.contains(i.getStfyGridId()))
                .map(i -> i.getStfyGridId())
                .distinct()
                .count();
    }

    public List<Geo> query(List<String> regionIds) {
        return stream(Geo.class).where(i -> regionIds.contains(i.getStfyGridId()))
                .toList();
    }

    public List<GridSurveyDTO> querySurveys(String rootDeptId, Integer min, Integer max, User user) {
        Stream<Tuple4<String, String, String, Long>> _query = profileStatisticRepository.thenTF(rootDeptId, null, user).stream();

        if (min != null) {
            _query = _query.filter(i -> i.getFour() >= min);
        }

        if (max != null) {
            _query = _query.filter(i -> i.getFour() < max);
        }

        List<Tuple4<String, String, String, Long>> _populations = _query.collect(Collectors.toList());

        List<GridSurveyDTO> _surveys = new ArrayList<>();
        for (Tuple4<String, String, String, Long> i : _populations) {
            GridSurveyDTO _survey = new GridSurveyDTO();

            String[] _names = i.getThree().split(Department.NAME_SEPARATOR);
            _survey.setDistrictName(_names[0]);

            if (_names.length > 1) {
                _survey.setSubdistrictName(_names[1]);
            }

            if (_names.length > 2) {
                _survey.setCommunityName(_names[2]);
            }

            if (_names.length > 3) {
                _survey.setGridName(_names[3]);
            }

            _survey.setPopulation(i.getFour());

            List<String> _subordinateIds = departmentRepository.subordinates(i.getTwo(), 4, null).stream()
                    .map(j -> j.getId())
                    .collect(Collectors.toList());
            if (_subordinateIds.size() > 1) {
                _subordinateIds.remove(0);
            }

            long _total = _subordinateIds.size();
            long _done = countDone(_subordinateIds);
            _survey.setDone(_done);
            _survey.setUndone(_total - _done);

            _surveys.add(_survey);
        }

        return _surveys;
    }

    public String exportSurveys(String rootDeptId, Integer min, Integer max, User user) throws IOException {
        List<GridSurveyDTO> _surveys = querySurveys(rootDeptId, min, max, user);

        return POIUtil.exportToWorkbookBase64String(book -> {
            Sheet _sheet = book.createSheet();

            // 设置标题行
            Row _row = _sheet.createRow(0);
            _row.createCell(0).setCellValue("区县");
            _row.createCell(1).setCellValue("镇/街道");
            _row.createCell(2).setCellValue("社区/村居");
            _row.createCell(3).setCellValue("网格");
            _row.createCell(4).setCellValue("入格人数");
            _row.createCell(5).setCellValue("已绘制网格");
            _row.createCell(6).setCellValue("未绘制网格");

            for (int i = 0; i < _surveys.size(); i++) {
                _row = _sheet.createRow(i + 1);
                _row.createCell(0).setCellValue(_surveys.get(i).getDistrictName());
                _row.createCell(1).setCellValue(_surveys.get(i).getSubdistrictName());
                _row.createCell(2).setCellValue(_surveys.get(i).getCommunityName());
                _row.createCell(3).setCellValue(_surveys.get(i).getGridName());
                _row.createCell(4).setCellValue(_surveys.get(i).getPopulation());
                _row.createCell(5).setCellValue(_surveys.get(i).getDone());
                _row.createCell(6).setCellValue(_surveys.get(i).getUndone());
            }
        }, "xlsx");
    }

    @Transactional(readOnly = true)
    public PagingItems<Geo> fuzzy(int count, int index, String stfyGridId) {
        JinqStream<Geo> _query = stream(Geo.class);

        _query = _query.where(i -> i.getStfyGridId().equals(stfyGridId));

        PagingItems<Geo> _page = new PagingItems<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.items = _query.toList();

        return _page;
    }

    public Result<Void> add(Geo item) {
        this.getCurrentSession().save(item);
        return new Result<>();
    }

    public Result<Void> update(Geo item) {
        this.getCurrentSession().update(item);
        return new Result<>();
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<Void> remove(String stfyGridId) {
        Geo _item = stream(Geo.class).where(i -> i.getStfyGridId().equals(stfyGridId)).findFirst().orElse(null);
        if (Objects.nonNull(_item)) {
            getCurrentSession().remove(_item);
        }
        return new Result<>();
    }

    public static class GridSurveyDTO {

        String districtName;
        String subdistrictName;
        String communityName;
        String gridName;
        Long population;
        Long done;
        Long undone;

        public String getDistrictName() {
            return districtName;
        }

        public void setDistrictName(String districtName) {
            this.districtName = districtName;
        }

        public String getSubdistrictName() {
            return subdistrictName;
        }

        public void setSubdistrictName(String subdistrictName) {
            this.subdistrictName = subdistrictName;
        }

        public String getCommunityName() {
            return communityName;
        }

        public void setCommunityName(String communityName) {
            this.communityName = communityName;
        }

        public String getGridName() {
            return gridName;
        }

        public void setGridName(String gridName) {
            this.gridName = gridName;
        }

        public Long getPopulation() {
            return population;
        }

        public void setPopulation(Long population) {
            this.population = population;
        }

        public Long getDone() {
            return done;
        }

        public void setDone(Long done) {
            this.done = done;
        }

        public Long getUndone() {
            return undone;
        }

        public void setUndone(Long undone) {
            this.undone = undone;
        }

    }

}
