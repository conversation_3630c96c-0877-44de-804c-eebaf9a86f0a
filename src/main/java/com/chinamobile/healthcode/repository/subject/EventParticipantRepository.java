package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.healthcode.model.subject.Participant;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;

@Repository
public class EventParticipantRepository extends AbstractEntityRepository<Participant> {

    public EventParticipantRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider) {
        super(entityManagerFactory, jinqJPAStreamProvider, Participant.class);
    }

    @Transactional(readOnly = true)
    public Result<Participant> get(String id) {
        if (StringUtils.isEmpty(id)) {
            return new Result<>(Result.DATABASE_RECORD_NOT_FOUND);
        }

        Result<Participant> _item = new Result<>();

        _item.data = getCurrentSession().get(Participant.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public List<Participant> query(String eventId) {
        return stream(Participant.class).where(i -> eventId.equals(i.getEventId())).toList();
    }

    public Result<String> save(Participant item, String userId) {
        Result<String> _id = new Result<>();

        boolean _isNew = false;
        Result<Participant> _item = get(item.getId());
        if (!_item.isOK()) {
            String _eventId = item.getEventId();
            String _credentialNo = item.getCredentialNo();
            if (stream(Participant.class).where(i -> i.getEventId().equals(_eventId) && i.getCredentialNo().equals(_credentialNo))
                    .findFirst().isPresent()) {
                _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST, new Object[]{Participant.class.getSimpleName()});
                return _id;
            }

            _item.data = new Participant();

            _isNew = true;
        }

        copyProperties(item, _item.data, new String[]{"id"});

        Result _success = _isNew ? add(_item.data, userId) : update(_item.data, userId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result remove(String id) {
        Result _success = new Result();

        Result<Participant> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        getCurrentSession().remove(_item.data);

        return _success;
    }

}