package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.healthcode.model.citizen.Profile;
import com.chinamobile.healthcode.model.subject.PlaceDescription;
import com.chinamobile.healthcode.model.subject.PlacePersonDescription;
import com.chinamobile.healthcode.repository.citizen.ProfileRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.google.common.collect.ImmutableList;
import org.hibernate.Transaction;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 5/22/2023 9:00
 */
@Repository
public class PlacePersonRepository extends AbstractEntityRepository<PlacePersonDescription> {
    private final DictionaryRepository dictionaryRepository;
    private final ProfileRepository profileRepository;

    public PlacePersonRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                                 @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJpaStreamProvider,
                                 DictionaryRepository dictionaryRepository,
                                 ProfileRepository profileRepository) {
        super(entityManagerFactory, jinqJpaStreamProvider, PlacePersonDescription.class);
        this.dictionaryRepository = dictionaryRepository;
        this.profileRepository = profileRepository;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<List<PlacePersonDescription>> save(String placeId, List<PlacePersonDescription> items, String actorId) {
        Result<List<PlacePersonDescription>> _result = new Result<>();

        if (removeByPlaceId(placeId).isOK() && items != null) {
            _result.data = new ArrayList<>();
            for (PlacePersonDescription i : items) {
                i.setPlaceId(placeId);

                Result<Profile> _existingProfile = profileRepository.getBriefByCredentialNo(i.getCredentialNo());
                if (_existingProfile.isOK()) {
                    i.setProfileId(_existingProfile.data.getId());
                } else {
                    Profile _newProfile = i.toProfile();
                    profileRepository.add(_newProfile, actorId);
                    i.setProfileId(_newProfile.getId());
                }

                Result<PlacePersonDescription> _addPersonResult = add(i, actorId);
                if (_addPersonResult.isOK()) {
                    _result.data.add(i);
                } else {
                    _result.pack(_addPersonResult);
                    return _result;
                }
            }
        }

        return _result;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<Void> removeByPlaceId(String placeId) {
        Result<Void> _result = new Result<>();

        List<PlacePersonDescription> _items = stream(PlacePersonDescription.class)
                .where(i -> i.getPlaceId().equals(placeId)).toList();

//        Transaction _transaction = getCurrentSession().getTransaction();
//        if (!_transaction.isActive()) {
//            _transaction.begin();
//        }

        try {
            for (PlacePersonDescription i : _items) {
                getCurrentSession().remove(i);
            }
        } catch (Exception e) {
            _result.setCode(Result.DATABASE_UNKNOWN);
//            _transaction.rollback();
        }
//        _transaction.commit();

        return _result;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public void convertDictionaryFields(List<PlacePersonDescription> placePersonDescriptionList) {
        if (placePersonDescriptionList == null || placePersonDescriptionList.isEmpty()) {
            return;
        }

        List<Dictionary> _specialCrowdDicList = dictionaryRepository.fuzzy("金凤码-特殊人群", null, true, null);
        List<Dictionary> _occupancyStateDicList = dictionaryRepository.fuzzy("重点场所-人员状态", null, true, null);

        placePersonDescriptionList.forEach(p -> {
            if (StringUtils.hasLength(p.getSubjectCategoryValue())) {
               p.setSubjectCategoryName(_specialCrowdDicList.stream()
                        .filter(d -> d.getVal().equals(p.getSubjectCategoryValue()))
                        .map(Dictionary::getName)
                        .findFirst()
                        .orElse(p.getSubjectCategoryValue()));
            }
            if (StringUtils.hasLength(p.getOccupancyStateValue())) {
                p.setOccupancyStateName(_occupancyStateDicList.stream()
                        .filter(d -> d.getVal().equals(p.getOccupancyStateValue()))
                        .map(Dictionary::getName)
                        .findFirst()
                        .orElse(p.getOccupancyStateValue()));
            }
        });
    }
}
