package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.healthcode.model.subject.*;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.AliasToEntityOrderedMapResultTransformer;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.POIUtil;
import org.apache.curator.shaded.com.google.common.reflect.TypeToken;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.hibernate.query.NativeQuery;
import org.jetbrains.annotations.Nullable;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.tuples.Tuple3;
import org.jinq.tuples.Tuple4;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 6/13/2023 14:47
 */
@Repository
public class SubjectRepository extends AbstractJinqRepository {
    public static final String ADMIN_ROLE = "专题管理员";

    public static final String SPECIAL_CROWD_DICT_GROUP_ID = "金凤码-特殊人群";

    public static final String SUBJECT_PLACE_DICT_GROUP_ID = "重点场所-类别";

    public static final String SUBJECT_EVENT_DICT_GROUP_ID = "重点事件";
    public static final String SUBJECT_EVENT_DICT_NAME = "类型";

    public static final String SUBJECT_PROPERTY_DICT_GROUP_ID = "重点物";
    public static final String SUBJECT_PROPERTY_DICT_NAME = "类别";

    public static final String SUBJECT_UNIT_DICT_GROUP_ID = "重点单位";
    public static final String SUBJECT_UNIT_DICT_NAME = "类别";

    private final DefaultDepartmentRepository departmentRepository;
    private final DefaultRoleRepository roleRepository;
    private final DictionaryRepository dictionaryRepository;

    protected SubjectRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                                @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
                                DefaultDepartmentRepository departmentRepository,
                                DefaultRoleRepository roleRepository,
                                DictionaryRepository dictionaryRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object> querySurvey(String subjectType, String rootDeptId, User user) {
        String _rootDeptId = departmentRepository.getRootRegionIdByUser(rootDeptId, ADMIN_ROLE, user);
        Department _rootDept = departmentRepository.get(_rootDeptId, true).data;

        String _sql;
        NativeQuery<Map<String, Object>> _query;
        List<Map<String, Object>> _resultList;
        switch (subjectType) {
            case "place":
                _sql = constructCommonSurveySql("subject_place", getPlaceColumnsSql());
                break;
            case "event":
                _sql = constructCommonSurveySql("event_descriptions", getEventColumnsSql());
                break;
            case "property":
                _sql = constructCommonSurveySql("property_descriptions", getPropertyColumnsSql());
                break;
            case "unit":
                _sql = constructCommonSurveySql("unit_descriptions", getUnitColumnsSql());
                break;
            case "person":
            default:
                _sql = constructPersonSurveySql();
                break;
        }

        if ("person".equals(subjectType)) {
//            StoredProcedureQuery _query = getCurrentSession().createStoredProcedureQuery("survey_person_stats");
//            _query.registerStoredProcedureParameter(0, String.class, ParameterMode.IN);
//            _query.registerStoredProcedureParameter(1, String.class, ParameterMode.IN);
//            _query.registerStoredProcedureParameter(2, String.class, ParameterMode.IN);
//            _query.registerStoredProcedureParameter(3, Integer.class, ParameterMode.IN);
//            _query.execute();
//            _resultList = _query.unwrap(Query.class).setResultTransformer(AliasToEntityOrderedMapResultTransformer.INSTANCE).getResultList();
            _query = getCurrentSession().createNativeQuery("{ call survey_person_stats(?0, ?1, ?2, ?3) }");

            _query.setParameter(0, _rootDeptId);
            _query.setParameter(1, _rootDept.getCode() + ".%");
            _query.setParameter(2, _rootDept.getFullName() + "/%");
            _query.setParameter(3, _rootDept.getLevel() + 1);
        } else {
            _query = getCurrentSession().createNativeQuery(_sql);
            _query.setParameter(1, _rootDeptId);
            _query.setParameter(2, _rootDept.getName());
        }
        _query.setResultTransformer(AliasToEntityOrderedMapResultTransformer.INSTANCE);
        _resultList = _query.getResultList();

        List<Object> _survey = new ArrayList<>();
        if (!_resultList.isEmpty() && !_resultList.get(0).isEmpty()) {
            // 首行为头部
            _resultList.stream().findFirst().ifPresent(_res -> _survey.add(_res.keySet()));

            // 数据行
            List<List<Object>> _dataList = _resultList.stream().map(Map::values).map(ArrayList::new).collect(Collectors.toList());
            final int colToSkip = 4;

            // 计算总数行
            List<Object> _sumRow = _dataList.stream().reduce((a, b) ->
                            IntStream.range(0, a.size())
                                    .mapToObj(i -> i < colToSkip ? null : Math.addExact(Long.parseLong(a.get(i).toString()), Long.parseLong(b.get(i).toString())))
                                    .collect(Collectors.toList()))
                    .orElse(new ArrayList<>(_dataList.get(0).size()));
            _sumRow.subList(0, colToSkip).clear();
            _sumRow.addAll(0, Stream.of("总计", null, null, null).collect(Collectors.toList()));
            _dataList.add(_sumRow);

            _survey.addAll(_dataList);
        }
        return _survey;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryStats(String subjectType, String rootDeptId, User user) {
        List<Object[]> _stats;
        switch (subjectType) {
            case "person":
                _stats = queryPersonStats(rootDeptId, user);
                break;
            case "place":
                _stats = queryPlaceStats(rootDeptId, user);
                break;
            case "event":
                _stats = queryEventStats(rootDeptId, user);
                break;
            case "property":
                _stats = queryPropertyStats(rootDeptId, user);
                break;
            case "unit":
                _stats = queryUnitStats(rootDeptId, user);
                break;
            default:
                _stats = new ArrayList<>();
        }
        return _stats;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryStatsWithTypes(String subjectType, String rootDeptId, User user) {
        List<Object[]> _stats;
        switch (subjectType) {
            case "person":
                _stats = queryPersonStatsWithTypes(rootDeptId, user);
                break;
            case "place":
                _stats = queryPlaceStatsWithTypes(rootDeptId, user);
                break;
            case "event":
                _stats = queryEventStatsWithTypes(rootDeptId, user);
                break;
            case "property":
                _stats = queryPropertyStatsWithTypes(rootDeptId, user);
                break;
            case "unit":
                _stats = queryUnitStatsWithTypes(rootDeptId, user);
                break;
            default:
                _stats = new ArrayList<>();
        }
        return _stats;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryPersonStats(String rootDeptId, User user) {
        List<String> _excludedRegionCodeList = dictionaryRepository
                .fuzzy("排除名单", "首页统计行政区域", true, null)
                .stream()
                .map(Dictionary::getVal)
                .collect(Collectors.toList());
        List<String> _regions = getStatsRegions(rootDeptId, user);


        return stream(Department.class)
                .leftOuterJoin(
                        (d, session) -> session.stream(PersonDescription.class),
                        (d, s) -> s.getRegionFullName().contains(d.getFullName()))
                .where(p -> p.getOne().getIsEnabled())
                .where(p -> !_excludedRegionCodeList.contains(p.getOne().getCode()))
                .where(p -> JPQL.isInList(p.getOne().getFullName(), _regions))
                .group(p -> new Tuple3<>(p.getOne().getSuperiorId(), p.getOne().getId(), p.getOne().getName()),
                        (key, stream) -> stream.select(p -> p.getTwo().getId()).count())
                .map(p -> new Object[]{p.getOne().getOne(), p.getOne().getTwo(), p.getOne().getThree(), p.getTwo()})
                .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryPlaceStats(String rootDeptId, User user) {
        List<String> _regions = getStatsRegions(rootDeptId, user);

        return stream(Department.class)
                .leftOuterJoin(
                        (d, session) -> session.stream(PlaceDescription.class),
                        (d, s) -> s.getRegionFullName().contains(d.getFullName()))
                .where(p -> p.getOne().getIsEnabled())
                .where(p -> JPQL.isInList(p.getOne().getFullName(), _regions))
                .group(p -> new Tuple3<>(p.getOne().getSuperiorId(), p.getOne().getId(), p.getOne().getName()),
                        (key, stream) -> stream.select(p -> p.getTwo().getId()).count())
                .map(p -> new Object[]{p.getOne().getOne(), p.getOne().getTwo(), p.getOne().getThree(), p.getTwo()})
                .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryEventStats(String rootDeptId, User user) {
        List<String> _regions = getStatsRegions(rootDeptId, user);

        return stream(Department.class)
                .leftOuterJoin(
                        (d, session) -> session.stream(EventDescription.class),
                        (d, s) -> s.getRegionFullName().contains(d.getFullName()))
                .where(p -> p.getOne().getIsEnabled())
                .where(p -> JPQL.isInList(p.getOne().getFullName(), _regions))
                .group(p -> new Tuple3<>(p.getOne().getSuperiorId(), p.getOne().getId(), p.getOne().getName()),
                        (key, stream) -> stream.select(p -> p.getTwo().getId()).count())
                .map(p -> new Object[]{p.getOne().getOne(), p.getOne().getTwo(), p.getOne().getThree(), p.getTwo()})
                .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryPropertyStats(String rootDeptId, User user) {
        List<String> _regions = getStatsRegions(rootDeptId, user);

        return stream(Department.class)
                .leftOuterJoin(
                        (d, session) -> session.stream(PropertyDescription.class),
                        (d, s) -> s.getRegionFullName().contains(d.getFullName()))
                .where(p -> p.getOne().getIsEnabled())
                .where(p -> JPQL.isInList(p.getOne().getFullName(), _regions))
                .group(p -> new Tuple3<>(p.getOne().getSuperiorId(), p.getOne().getId(), p.getOne().getName()),
                        (key, stream) -> stream.select(p -> p.getTwo().getId()).count())
                .map(p -> new Object[]{p.getOne().getOne(), p.getOne().getTwo(), p.getOne().getThree(), p.getTwo()})
                .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryUnitStats(String rootDeptId, User user) {
        List<String> _regions = getStatsRegions(rootDeptId, user);

        return stream(Department.class)
                .leftOuterJoin(
                        (d, session) -> session.stream(UnitDescription.class),
                        (d, s) -> s.getRegionFullName().contains(d.getFullName()))
                .where(p -> p.getOne().getIsEnabled())
                .where(p -> JPQL.isInList(p.getOne().getFullName(), _regions))
                .group(p -> new Tuple3(p.getOne().getSuperiorId(), p.getOne().getId(), p.getOne().getName()),
                        (key, stream) -> stream.select(p -> p.getTwo().getId()).count())
                .map(p -> new Object[]{p.getOne().getOne(), p.getOne().getTwo(), p.getOne().getThree(), p.getTwo()})
                .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryPersonStatsWithTypes(String rootDeptId, User user) {
        List<String> _regions = getStatsRegions(rootDeptId, user);

        return stream(Department.class)
                .leftOuterJoin(
                        (d, session) -> session.stream(PersonDescription.class),
                        (d, s) -> s.getRegionFullName().contains(d.getFullName()))
                .where(p -> p.getOne().getIsEnabled())
                .where(p -> JPQL.isInList(p.getOne().getFullName(), _regions))
                .group(p -> new Tuple4<>(p.getOne().getSuperiorId(), p.getOne().getId(), p.getOne().getName(), p.getTwo().getType()),
                        (key, stream) -> stream.select(p -> p.getTwo().getId()).count())
                .map(p -> new Object[]{p.getOne().getOne(), p.getOne().getTwo(), p.getOne().getThree(), p.getOne().getFour(), p.getTwo()})
                .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryPlaceStatsWithTypes(String rootDeptId, User user) {
        List<String> _regions = getStatsRegions(rootDeptId, user);

        return stream(Department.class)
                .leftOuterJoin(
                        (d, session) -> session.stream(PlaceDescription.class),
                        (d, s) -> s.getRegionFullName().contains(d.getFullName()))
                .leftOuterJoin(
                        (d, session) -> session.stream(Dictionary.class),
                        (p, dic) -> dic.getIsEnabled() && "重点场所-类别".equals(dic.getGroupId()) && p.getTwo().getPlaceTypeValue().equals(dic.getVal())
                )
                .where(p -> p.getOne().getOne().getIsEnabled())
                .where(p -> JPQL.isInList(p.getOne().getOne().getFullName(), _regions))
                .group(p -> new Tuple4<>(p.getOne().getOne().getSuperiorId(), p.getOne().getOne().getId(), p.getOne().getOne().getName(), p.getTwo().getName()),
                        (key, stream) -> stream.select(p -> p.getTwo().getId()).count())
                .map(p -> new Object[]{p.getOne().getOne(), p.getOne().getTwo(), p.getOne().getThree(), p.getOne().getFour(), p.getTwo()})
                .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryEventStatsWithTypes(String rootDeptId, User user) {
        List<String> _regions = getStatsRegions(rootDeptId, user);

        return stream(Department.class)
                .leftOuterJoin(
                        (d, session) -> session.stream(EventDescription.class),
                        (d, s) -> s.getRegionFullName().contains(d.getFullName()))
                .where(p -> p.getOne().getIsEnabled())
                .where(p -> JPQL.isInList(p.getOne().getFullName(), _regions))
                .group(p -> new Tuple4<>(p.getOne().getSuperiorId(), p.getOne().getId(), p.getOne().getName(), p.getTwo().getSubtype()),
                        (key, stream) -> stream.select(p -> p.getTwo().getId()).count())
                .map(p -> new Object[]{p.getOne().getOne(), p.getOne().getTwo(), p.getOne().getThree(), p.getOne().getFour(), p.getTwo()})
                .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryPropertyStatsWithTypes(String rootDeptId, User user) {
        List<String> _regions = getStatsRegions(rootDeptId, user);

        return stream(Department.class)
                .leftOuterJoin(
                        (d, session) -> session.stream(PropertyDescription.class),
                        (d, s) -> s.getRegionFullName().contains(d.getFullName()))
                .where(p -> p.getOne().getIsEnabled())
                .where(p -> JPQL.isInList(p.getOne().getFullName(), _regions))
                .group(p -> new Tuple4<>(p.getOne().getSuperiorId(), p.getOne().getId(), p.getOne().getName(), p.getTwo().getType()),
                        (key, stream) -> stream.select(p -> p.getTwo().getId()).count())
                .map(p -> new Object[]{p.getOne().getOne(), p.getOne().getTwo(), p.getOne().getThree(), p.getOne().getFour(), p.getTwo()})
                .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object[]> queryUnitStatsWithTypes(String rootDeptId, User user) {
        List<String> _regions = getStatsRegions(rootDeptId, user);

        return stream(Department.class)
                .leftOuterJoin(
                        (d, session) -> session.stream(UnitDescription.class),
                        (d, s) -> s.getRegionFullName().contains(d.getFullName()))
                .where(p -> p.getOne().getIsEnabled())
                .where(p -> JPQL.isInList(p.getOne().getFullName(), _regions))
                .group(p -> new Tuple4(p.getOne().getSuperiorId(), p.getOne().getId(), p.getOne().getName(), p.getTwo().getType()),
                        (key, stream) -> stream.select(p -> p.getTwo().getId()).count())
                .map(p -> new Object[]{p.getOne().getOne(), p.getOne().getTwo(), p.getOne().getThree(), p.getOne().getFour(), p.getTwo()})
                .collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object> queryRoutineInspection(String recordDateStr) {
        List<Map<String, Object>> _resultList;
        NativeQuery<Map<String, Object>> _query;

        _query = getCurrentSession().createNativeQuery("{ call routine_inspection(?0) }");
        _query.setParameter(0, recordDateStr);
        _query.setResultTransformer(AliasToEntityOrderedMapResultTransformer.INSTANCE);
        _resultList = _query.getResultList();

        List<Object> _survey = new ArrayList<>();
        if (!_resultList.isEmpty() && !_resultList.get(0).isEmpty()) {
            // 首行为头部
            _resultList.stream().findFirst().ifPresent(_res -> _survey.add(_res.keySet()));

            // 数据行
            List<List<Object>> _dataList = _resultList.stream().map(Map::values).map(ArrayList::new).collect(Collectors.toList());
            _survey.addAll(_dataList);
        }
        return _survey;
    }

    @Transactional(readOnly = true)
    public String exportRoutineInspection(String recordDateStr) throws IOException {
        List<Object> _items = queryRoutineInspection(recordDateStr);

        return POIUtil.exportToWorkbookBase64String((book) -> {
            Sheet _sheet = book.createSheet();
            for (int i = 0; i < _items.size(); i++) {
                Row _row = _sheet.createRow(i);
                List<Object> _item;
                if (i == 0) {
                    LinkedHashSet<String> set = new LinkedHashSet((Set<String>)_items.get(0));
                    _item = new ArrayList<>(set);
                } else {
                    _item = (List<Object>) _items.get(i);
                }

                for (int j = 0; j < _item.size(); j++) {
                    _row.createCell(j).setCellValue(_item.get(j).toString());
                }
            }
        }, "xlsx");
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public List<Object> queryDailyReport(String rootDepartmentId, String recordDateStr, User user) {
        List<Map<String, Object>> _resultList;
        NativeQuery<Map<String, Object>> _query;

        String _rootDepartmentFullName;
        if (!StringUtils.hasLength(rootDepartmentId)) {
            _rootDepartmentFullName = user.getDeptFullName();
        } else {
            Result<DefaultDepartment> _deptResult = departmentRepository.get(rootDepartmentId, true);
            if (_deptResult.isOK()) {
                _rootDepartmentFullName = _deptResult.data.getFullName();
            } else {
                return Collections.emptyList();
            }
        }

        _query = getCurrentSession().createNativeQuery("{ call daily_stats_report(?0, ?1) }");
        _query.setParameter(0, _rootDepartmentFullName);
        _query.setParameter(1, recordDateStr);
        _query.setResultTransformer(AliasToEntityOrderedMapResultTransformer.INSTANCE);
        _resultList = _query.getResultList();

        List<Object> _survey = new ArrayList<>();
        if (!_resultList.isEmpty() && !_resultList.get(0).isEmpty()) {
            // 首行为头部
            _resultList.stream().findFirst().ifPresent(_res -> _survey.add(_res.keySet()));

            // 数据行
            List<List<Object>> _dataList = _resultList.stream().map(Map::values).map(ArrayList::new).collect(Collectors.toList());
            _survey.addAll(_dataList);
        }
        return _survey;
    }

    @Transactional(readOnly = true)
    public String exportDailyReport(String rootDepartmentId, String recordDateStr, User user) throws IOException {
        List<Object> _items = queryDailyReport(rootDepartmentId, recordDateStr, user);

        return POIUtil.exportToWorkbookBase64String((book) -> {
            Sheet _sheet = book.createSheet();
            for (int i = 0; i < _items.size(); i++) {
                Row _row = _sheet.createRow(i);
                List<Object> _item;
                if (i == 0) {
                    LinkedHashSet<String> set = new LinkedHashSet((Set<String>)_items.get(0));
                    _item = new ArrayList<>(set);
                } else {
                    _item = (List<Object>) _items.get(i);
                }

                for (int j = 0; j < _item.size(); j++) {
                    _row.createCell(j).setCellValue(_item.get(j).toString());
                }
            }
        }, "xlsx");
    }

    @Nullable
    private String getRootDeptId(String rootDeptId, User user) {
        String _rootDeptId;
        if (StringUtils.hasLength(rootDeptId)) {
            if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
                _rootDeptId = rootDeptId;
            } else {
                _rootDeptId = departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                        .anyMatch(i -> Objects.equals(rootDeptId, i.getId())) ? rootDeptId : user.getDeptId();
            }
        } else {
            _rootDeptId = roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE) ? null : user.getDeptId();
        }
        return _rootDeptId;
    }

    private String constructCommonSurveySql(String subjectTableName, String subjectColumnsQuerySql) {
        String _commonSurveySqlTemplate = "SELECT \n" +
                "  SUBSTRING_INDEX( sd.fullName, '/', 1 ) AS '区县', \n" +
                "IF \n" +
                "  ( \n" +
                "    CHAR_LENGTH( sd.fullName ) - CHAR_LENGTH( \n" +
                "    REPLACE ( sd.fullName, '/', '' )) >= 1, \n" +
                "    SUBSTRING_INDEX( SUBSTRING_INDEX( sd.fullName, '/', 2 ), '/', - 1 ), \n" +
                "  NULL \n" +
                "  ) AS '镇/街道', \n" +
                "IF \n" +
                "  ( \n" +
                "    CHAR_LENGTH( sd.fullName ) - CHAR_LENGTH( \n" +
                "    REPLACE ( sd.fullName, '/', '' )) >= 2, \n" +
                "    SUBSTRING_INDEX( SUBSTRING_INDEX( sd.fullName, '/', 3 ), '/', - 1 ), \n" +
                "  NULL \n" +
                "  ) AS '社区/村居', \n" +
                "IF \n" +
                "  ( \n" +
                "    CHAR_LENGTH( sd.fullName ) - CHAR_LENGTH( \n" +
                "    REPLACE ( sd.fullName, '/', '' )) >= 3, \n" +
                "    SUBSTRING_INDEX( SUBSTRING_INDEX( sd.fullName, '/', 4 ), '/', - 1 ), \n" +
                "  NULL \n" +
                "  ) AS '网格' \n" +
                "%s \n" +
                "FROM \n" +
                "  sys_departments sd \n" +
                "  LEFT JOIN %s st ON st.regionFullName LIKE CONCAT( sd.fullName, '%%' ) \n" +
                "WHERE \n" +
                "  sd.isEnabled = 1 \n" +
                "  AND ( \n" +
                "    sd.superiorId = ?1 \n" +
                "    OR ( \n" +
                "      '" + DefaultDepartmentRepository.ROOT_DEPARTMENT_NAME + "' = ?2 \n" +
                "      AND sd.name <> '" + DefaultDepartmentRepository.ROOT_DEPARTMENT_NAME + "' \n" +
                "      AND sd.level = 1 \n" +
                "    ) \n" +
                "  ) \n" +
                "GROUP BY \n" +
                "  sd.id, \n" +
                "  sd.fullName \n" +
                "ORDER BY \n" +
                "  sd.seq;";
        return String.format(_commonSurveySqlTemplate, subjectColumnsQuerySql, subjectTableName);
    }

    private String constructPersonSurveySql() {
        List<Dictionary> _specialCrowdTypeList = dictionaryRepository.fuzzy(SPECIAL_CROWD_DICT_GROUP_ID,
                null, true, Arrays.asList(new SortField("seq", true), new SortField("val", true)));
        StringBuilder _typeColumnSql = new StringBuilder();
        StringBuilder _subjectPersonTypeColumnSql = new StringBuilder();
        StringBuilder _specialCrowdTypeColumnSql = new StringBuilder();
        // 重点人员
        for (PersonDescription.ENUM_TYPE _et : PersonDescription.ENUM_TYPE.values()) {
            _typeColumnSql.append(String.format(", sps.`%s`", _et.name()));
            _subjectPersonTypeColumnSql.append(String.format(", COUNT(IF(sp.type = '%s', sp.id, NULL)) AS '%s'", _et.ordinal(), _et.name()));
        }
        // 特殊人群
        for (Dictionary _dic: _specialCrowdTypeList) {
            _typeColumnSql.append(String.format(", cps.`%s`", _dic.getName()));
            _specialCrowdTypeColumnSql.append(String.format(", COUNT(IF(cp.crowdIdsJSON LIKE '%%%s%%', cp.id, NULL)) AS '%s'", _dic.getVal(), _dic.getName()));
        }

        String _personSurveySqlTemplate = "SELECT \n" +
                "  SUBSTRING_INDEX( sps.fullName, '/', 1 ) AS '区县', \n" +
                "IF \n" +
                "  ( \n" +
                "    CHAR_LENGTH( sps.fullName ) - CHAR_LENGTH( \n" +
                "    REPLACE ( sps.fullName, '/', '' )) >= 1, \n" +
                "    SUBSTRING_INDEX( SUBSTRING_INDEX( sps.fullName, '/', 2 ), '/', - 1 ), \n" +
                "  NULL \n" +
                "  ) AS '镇/街道', \n" +
                "IF \n" +
                "  ( \n" +
                "    CHAR_LENGTH( sps.fullName ) - CHAR_LENGTH( \n" +
                "    REPLACE ( sps.fullName, '/', '' )) >= 2, \n" +
                "    SUBSTRING_INDEX( SUBSTRING_INDEX( sps.fullName, '/', 3 ), '/', - 1 ), \n" +
                "  NULL \n" +
                "  ) AS '社区/村居', \n" +
                "IF \n" +
                "  ( \n" +
                "    CHAR_LENGTH( sps.fullName ) - CHAR_LENGTH( \n" +
                "    REPLACE ( sps.fullName, '/', '' )) >= 3, \n" +
                "    SUBSTRING_INDEX( SUBSTRING_INDEX( sps.fullName, '/', 4 ), '/', - 1 ), \n" +
                "  NULL \n" +
                "  ) AS '网格' \n" +
                "    %s \n" +
                "   FROM \n" +
                "    ( \n" +
                "    SELECT \n" +
                "     sd.fullName, \n" +
                "     sd.seq \n" +
                "     %s \n" +
                "    FROM \n" +
                "     sys_departments sd \n" +
                "     LEFT JOIN subject_persons sp ON sp.regionFullName LIKE CONCAT( sd.fullName, '%%' ) \n" +
                "    WHERE \n" +
                "     sd.superiorId = ?1 \n" +
                "    GROUP BY \n" +
                "     sd.id, \n" +
                "     sd.fullName \n" +
                "    ) sps, \n" +
                "    ( \n" +
                "    SELECT \n" +
                "     sd.fullName \n" +
                "     %s \n" +
                "    FROM \n" +
                "     sys_departments sd \n" +
                "     LEFT JOIN citizen_profiles cp ON sd.id = cp.subdistrictId \n" +
                "    WHERE \n" +
                "     sd.isEnabled = 1 \n" +
                "     AND sd.fullName IS NOT NULL \n" +
                "     AND sd.`code` LIKE ?1 \n" +
                "    GROUP BY \n" +
                "     sd.fullName \n" +
                "    ) cps \n" +
                "   WHERE\n" +
                "    sps.fullName = cps.fullName \n" +
                "  ORDER BY\n" +
                " sps.seq;";
        return String.format(_personSurveySqlTemplate, _typeColumnSql, _subjectPersonTypeColumnSql, _specialCrowdTypeColumnSql);
    }

    private String getPlaceColumnsSql() {
        List<Dictionary> _placeTypeList = dictionaryRepository.fuzzy(SUBJECT_PLACE_DICT_GROUP_ID,
                null, true, Arrays.asList(new SortField("seq", true), new SortField("val", true)));
        StringBuilder _typeColumnSql = new StringBuilder();
        for (Dictionary _dic: _placeTypeList) {
            _typeColumnSql.append(String.format(", COUNT(IF(st.placeTypeValue = '%s', st.id, NULL)) AS '%s'", _dic.getVal(), _dic.getName()));
        }
        return _typeColumnSql.toString();
    }

    private String getEventColumnsSql() {
        String _eventTypeJsonStr = dictionaryRepository.getVal(SUBJECT_EVENT_DICT_GROUP_ID, SUBJECT_EVENT_DICT_NAME, true);
        Map<String, Map<String, String>> _eventTypeMap = ConverterUtil.json2Object(_eventTypeJsonStr, new TypeToken<Map<String, Object>>() {}.getType());
        List<Map<String, String>> _eventTypeList = _eventTypeMap.entrySet().stream()
                .flatMap(e -> e.getValue().values().stream().map(v -> Collections.singletonMap(e.getKey(), v)))
                .collect(Collectors.toList());
        StringBuilder _typeColumnSql = new StringBuilder();
        for (Map<String, String> _map: _eventTypeList) {
            Map.Entry<String, String> _entry = _map.entrySet().iterator().next();
            _typeColumnSql.append(String.format(", COUNT(IF(st.type = '%s' AND st.subtype = '%s', st.id, NULL)) AS '%s'",
                    _entry.getKey(), _entry.getValue(), String.join("-", _entry.getKey(), _entry.getValue())));
        }
        return _typeColumnSql.toString();
    }

    private String getPropertyColumnsSql() {
        List<Dictionary> _propertyTypeList = dictionaryRepository.fuzzy(SUBJECT_PROPERTY_DICT_GROUP_ID,
                SUBJECT_PROPERTY_DICT_NAME, true, Collections.singletonList(new SortField("seq", true)));
        StringBuilder _typeColumnSql = new StringBuilder();
        for (Dictionary _dic: _propertyTypeList) {
            _typeColumnSql.append(String.format(", COUNT(IF(st.type = '%s', st.id, NULL)) AS '%s'", _dic.getVal(), _dic.getVal()));
        }
        return _typeColumnSql.toString();
    }

    private String getUnitColumnsSql() {
        List<Dictionary> _unitTypeList = dictionaryRepository.fuzzy(SUBJECT_UNIT_DICT_GROUP_ID,
                SUBJECT_UNIT_DICT_NAME, true, Collections.singletonList(new SortField("seq", true)));
        StringBuilder _typeColumnSql = new StringBuilder();
        for (Dictionary _dic: _unitTypeList) {
            _typeColumnSql.append(String.format(", COUNT(IF(st.type = '%s', st.id, NULL)) AS '%s'", _dic.getVal(), _dic.getVal()));
        }
        return _typeColumnSql.toString();
    }

    private List<String> getStatsRegions(String rootDeptId, User user) {
        String _rootDeptId = departmentRepository.getRootRegionIdByUser(rootDeptId, ADMIN_ROLE, user);

        List<DefaultDepartment> _subordinates = departmentRepository.subordinates(_rootDeptId, null, null);

        Department _root;
        int _temp;
        if (StringUtils.hasLength(_rootDeptId)) {
            _root = _subordinates.stream()
                    .filter(i -> Objects.equals(i.getId(), _rootDeptId))
                    .findFirst().orElse(null);

            _temp = _root.getLevel() + 1;
        } else {
            _temp = 1;
        }

        int _level = _temp;
        return _level == 1 ? Arrays.asList("金平区", "龙湖区", "濠江区", "澄海区", "潮阳区", "潮南区", "南澳县") : _subordinates.stream()
                .filter(i -> i.getLevel() == _level)
                .map(i -> i.getFullName()).collect(Collectors.toList());
    }
}
