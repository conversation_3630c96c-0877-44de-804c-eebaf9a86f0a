package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.healthcode.model.subject.ProjectDescription;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.User;

import java.util.List;

/**
 * <AUTHOR>
 * @date 9/26/2023 17:27
 */
public interface ProjectRepository<T extends ProjectDescription> {
    /**
     * 获取专项工作类型
     *
     * @return 专项工作类型
     */
    String getProjectType();

    /**
     * 专题数据列表
     *
     * @param type 专项工作子类型
     * @param user 用户信息
     * @return 可转换为专项工作数据的专题数据列表
     */
    List<T> fuzzyProjectDescription(String type, User user);

    /**
     * 专项表单数据转为专题数据
     *
     * @param subType 专项子类型->专题类型
     * @param form 专项表单
     * @return 专题数据
     */
    T fromForm(String subType, Form form);

    Result<Void> add(T item, String actorId);

    Result<Void> update(T item, String actorId);
}
