package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.apache.commons.lang3.StringUtils;
import org.jinq.jpa.JinqJPAStreamProvider;

import javax.persistence.EntityManagerFactory;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 10/9/2023 10:36
 */
public class DefaultAbstractEntityRepository<T extends AbstractEntity> extends AbstractEntityRepository<T> {
    final Validator validator;

    public DefaultAbstractEntityRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass, Validator validator) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass);
        this.validator = validator;
    }

    public Result<T> remove(String id, User user) {
        this.getCurrentSession().remove(id);
        this.getCurrentSession().flush();
        return new Result<>();
    }

    /**
     * 因Hibernate默认使用Default group，使用额外的方法进行指定group检查
     *
     * @param item   检查的对象
     * @param groups 检查分组
     */
    public void validate(T item, Class<?>... groups) {
        Set<ConstraintViolation<T>> violations = validator.validate(item, groups);
        if (!violations.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (ConstraintViolation<T> constraintViolation : violations) {
                sb.append("\n");
                sb.append(constraintViolation.getMessage());
            }
            throw new ConstraintViolationException(sb.toString(), violations);
        }
    }

    public String multiSelStrToArrStr(String multiSelStr) {
        String arrStr;
        if (StringUtils.isBlank(multiSelStr)) {
            arrStr = null;
        } else {
            String[] multiSelArr = multiSelStr.split("[,，|]");
            StringJoiner stringJoiner = new StringJoiner(",", "[", "]");
            Arrays.stream(multiSelArr).map(s -> String.format("\"%s\"", s))
                    .forEach(stringJoiner::add);
            arrStr = stringJoiner.toString();
        }
        return arrStr;
    }

    List<Integer> getDuplicatedLineNumbersFromExcel(Map<String, Integer> headerMap, List<String[]> lineList, String... uniqueColumnNames) {
        List<Integer> duplicatedLineNumbers = new ArrayList<>();
        HashSet<String> uniqueKeySet = new HashSet<>();

        for (int i = 0; i < lineList.size(); i++) {
            String[] line = lineList.get(i);
            String uniqueKey = Stream.of(uniqueColumnNames).map(colName -> line[headerMap.get(colName)]).collect(Collectors.joining("|"));
            if (!uniqueKeySet.add(uniqueKey)) {
                duplicatedLineNumbers.add(i);
            }
        }

        return duplicatedLineNumbers;
    }
}
