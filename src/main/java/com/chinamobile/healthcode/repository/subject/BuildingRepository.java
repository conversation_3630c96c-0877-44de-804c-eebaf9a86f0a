package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.healthcode.model.subject.BuildingDescription;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.GpsUtil;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Repository
public class BuildingRepository extends AbstractJinqRepository {

    public static final String ADMIN_ROLE = "专题管理员";

    final DepartmentRepository<Department> departmentRepository;
    final DefaultRoleRepository roleRepository;

    public BuildingRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
    }

    @Transactional(readOnly = true)
    public Result<BuildingDescription> get(String id) {
        Result<BuildingDescription> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{BuildingDescription.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(BuildingDescription.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{BuildingDescription.class.getSimpleName()});
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<BuildingDescription> fuzzy(int count, int index, List<SortField> sortFields, String regionFullName, String name, User user) {
        JinqStream<BuildingDescription> _query = stream(regionFullName, name, user);

        if (CollectionUtils.isEmpty(sortFields)) {
            _query = _query.sortedDescendingBy(i -> i.getName());
        } else {
            Collections.reverse(sortFields);
            for (SortField i : sortFields) {
                switch (i.getField()) {
                    case "name":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getName()) : _query.sortedDescendingBy(j -> j.getName());
                        break;
                    case "regionFullName":
                        _query = i.getAsc() ? _query.sortedBy(j -> j.getRegionFullName()) : _query.sortedDescendingBy(j -> j.getRegionFullName());
                        break;
                }
            }
        }

        PagingItems<BuildingDescription> _page = new PagingItems(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }
        _page.items = _query.toList();

        return _page;
    }

    public Result<String> save(BuildingDescription item, User user) {
        Result<String> _success = new Result<>();

        if (!validDataScope(item.getRegionId(), user)) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        Double _lng = item.getLng();
        Double _lat = item.getLat();
        if (stream(BuildingDescription.class)
                .where(b -> Objects.equals(b.getLng(), _lng)
                        && Objects.equals(b.getLat(), _lat))
                .findFirst().isPresent()) {
            _success.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _success;
        }
        if (Objects.nonNull(_lng) && Objects.nonNull(_lat)) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_lat, _lng);
            item.setLatInWgs(_latLngInWgs[0]);
            item.setLngInWgs(_latLngInWgs[1]);
        }

        getCurrentSession().save(item);

        return _success;
    }

    public Result update(String id, String name) {
        Result _success = new Result<>();

        Result<BuildingDescription> _item = get(id);
        if (!_item.isOK()) {
            return _success;
        }

        _item.data.setName(name);
        getCurrentSession().save(_item.data);

        return _success;
    }

    public Result remove(String id, User user) {
        Result _success = new Result();

        Result<BuildingDescription> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非下级单位
        if (!validDataScope(_item.data.getRegionId(), user)) {
            _item.setCode(Result.DATA_ACCESS_DENY);
            return _item;
        }

        getCurrentSession().remove(_item.data);

        return _success;
    }

    public JinqStream<BuildingDescription> stream(String regionFullName, String name, User user) {
        JinqStream<BuildingDescription> _query = stream(BuildingDescription.class);

        if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> _deptFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        return _query;
    }

    protected boolean validDataScope(String regionId, User user) {
        return departmentRepository.subordinates(user.getDeptId(), null, null)
                .stream().anyMatch(d -> d.getId().equals(regionId));
    }

}
