package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.healthcode.model.subject.PropertyDescription;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.GpsUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.reflect.TypeToken;
import org.hibernate.Transaction;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@ErrorCode(module = "057")
public class PropertyRepository extends DefaultAbstractEntityRepository<PropertyDescription> implements ProjectRepository<PropertyDescription> {

    public static final String ADMIN_ROLE = "专题管理员";

    final DepartmentRepository<Department> departmentRepository;
    final DefaultRoleRepository roleRepository;
    final DictionaryRepository dictionaryRepository;
    final AbstractMediaRepository mediaRepository;

    final DefaultResultParser resultParser;

    public PropertyRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, Validator validator, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository, DictionaryRepository dictionaryRepository, AbstractMediaRepository mediaRepository, DefaultResultParser resultParser) {
        super(entityManagerFactory, jinqJPAStreamProvider, PropertyDescription.class, validator);

        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.mediaRepository = mediaRepository;

        this.resultParser = resultParser;
    }

    @Transactional(readOnly = true)
    public Result<PropertyDescription> get(String id) {
        Result<PropertyDescription> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{PropertyDescription.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(PropertyDescription.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{PropertyDescription.class});
        } else {
            parseAttachments(Collections.singletonList(_item.data), true);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<PropertyDescription> fuzzy(int count, int index, String regionFullName, String name, String type, String address, User user) {
        JinqStream<PropertyDescription> _query = stream(regionFullName, name, type, address, user)
                .sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<PropertyDescription> _page = new PagingItems(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }
        _page.items = _query.toList();

        parseAttachments(_page.items, false);

        return _page;
    }

    @Transactional(readOnly = true)
    public List<PropertyDescription> query(List<String> ids) {
        return stream(PropertyDescription.class)
                .where(i -> ids.contains(i.getId()))
                .toList();
    }

    public Result<String> save(PropertyDescription item, User user) {
        Result<String> _id = new Result<>();

        boolean _isNew = false;

        Result<PropertyDescription> _item = get(item.getId());

        List<String> _ids = new ArrayList<>();
        if (_item.isOK()) {
            _ids = StringUtils.hasLength(_item.data.getAttachmentIdsJSON()) ? ConverterUtil.json2Object(_item.data.getAttachmentIdsJSON(), new TypeToken<List<String>>() {
            }.getType()) : new ArrayList<>();

            if (!CollectionUtils.isEmpty(item.getAttachmentIds())) {
                _ids.removeAll(item.getAttachmentIds());
            }
        } else {
            _item.data = new PropertyDescription();

            _isNew = true;
        }

        Result<Department> _region = departmentRepository.get(item.getRegionId(), true);
        if (!_region.isOK()) {
            return _id.pack(_region);
        }
        _item.data.setRegionId(_region.data.getId());
        _item.data.setRegionFullName(_region.data.getFullName());
        _item.data.setAttachmentIdsJSON(CollectionUtils.isEmpty(item.getAttachmentIds()) ? null : ConverterUtil.toJson(item.getAttachmentIds()));

        copyProperties(item, _item.data, new String[]{"id", "regionId", "regionFullName", "attachmentIdsJSON"});
        if (Objects.nonNull(_item.data.getLng()) && Objects.nonNull(_item.data.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.data.getLat(), _item.data.getLng());
            _item.data.setLatInWgs(_latLngInWgs[0]);
            _item.data.setLngInWgs(_latLngInWgs[1]);
        }

        Result _success = _isNew ? add(_item.data, user.getId()) : update(_item.data, user.getId());
        if (_success.isOK()) {
            for (String i : _ids) {
                mediaRepository.remove(i, true, user.getId());
            }
        } else {
            return _id.pack(_success);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result remove(String id, User user) {
        Result _success = new Result();

        Result<PropertyDescription> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非下级单位
        if (!departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                .anyMatch(i -> i.getId().equals(_item.data.getRegionId()))) {
            _item.setCode(Result.DATA_ACCESS_DENY);
            return _item;
        }

        // 删除附件
        if (!CollectionUtils.isEmpty(_item.data.getAttachmentIds())) {
            for (String i : _item.data.getAttachmentIds()) {
                mediaRepository.remove(i, true, user.getId());
            }
        }

        getCurrentSession().remove(_item.data);

        return _success;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<String> importFromExcel(File file, User actor) throws Exception {
        // 读取下级单位
        List<Department> _subordinates = departmentRepository.subordinates(actor.getDeptId(), null, null);

        // 读取部门
        List<Department> _departments = departmentRepository.query(null, null, null, true, null);

        List<String> _types = types().stream()
                .map(i -> i.getVal())
                .collect(Collectors.toList());

        return readExcel((header, lines, results) -> {
            for (int i = 0; i < lines.size(); i++) {
                if (!results.get(i).isOK()) {
                    continue;
                }

                String[] _line = lines.get(i);

                PropertyDescription _item = new PropertyDescription();

                String _text = _line[header.get("名称")];
                _item.setName(_text);

                String _name = _line[header.get("归属网格")];
                Department _region = _departments.stream()
                        .filter(j -> Objects.equals(_name, j.getFullName()))
                        .findFirst().orElse(null);
                if (_region == null) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 3);
                    results.set(i, _success);

                    continue;
                }

                // 非下级单位
                if (!_subordinates.stream().anyMatch(j -> Objects.equals(j.getId(), _region.getId()))) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 4);
                    results.set(i, _success);

                    continue;
                }
                _item.setRegionId(_region.getId());
                _item.setRegionFullName(_region.getFullName());

                _text = _line[header.get("类别")];
                if (StringUtils.isEmpty(_text) || !_types.contains(_text)) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 5);
                    results.set(i, _success);

                    continue;
                }
                _item.setType(_text);

                _text = _line[header.get("位置")];
                _item.setAddress(_text);

                try {
                    _text = _line[header.get("经度")];
                    _item.setLng(StringUtils.hasLength(_text) ? Double.valueOf(_text) : null);
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 6);
                    results.set(i, _success);

                    continue;
                }

                try {
                    _text = _line[header.get("纬度")];
                    _item.setLat(StringUtils.hasLength(_text) ? Double.valueOf(_text) : null);
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 7);
                    results.set(i, _success);

                    continue;
                }

                if (Objects.nonNull(_item.getLng()) && Objects.nonNull(_item.getLat())) {
                    double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.getLat(), _item.getLng());
                    _item.setLatInWgs(_latLngInWgs[0]);
                    _item.setLngInWgs(_latLngInWgs[1]);
                }

                Transaction _transaction = getCurrentSession().beginTransaction();
                try {
                    validate(_item, ValidationGroup.Insert.class);
                    add(_item, actor.getId());

                    _transaction.commit();
                } catch (Throwable e) {
                    e.printStackTrace();

                    Result _error = resultParser.fromException(e, false);
                    if (_error == null) {
                        _error = new Result();
                        _error.setCode(Result.ENUM_ERROR.P, 8, new Object[]{e.getMessage()});
                    }
                    results.set(i, _error);

                    _transaction.rollback();
                }
            }
        }, file, 0, 1);
    }

    public JinqStream<PropertyDescription> stream(String regionFullName, String name, String type, String address, User user) {
        JinqStream<PropertyDescription> _query = stream(PropertyDescription.class);

        if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> _deptFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (StringUtils.hasLength(type)) {
            _query = _query.where(i -> type.equals(i.getType()));
        }

        if (StringUtils.hasLength(address)) {
            _query = _query.where(i -> i.getAddress() != null && i.getAddress().contains(address));
        }

        return _query;
    }

    @Transactional(readOnly = true)
    public List<Dictionary> types() {
        return dictionaryRepository.fuzzy("重点物", "类别", true, ImmutableList.of(new SortField("createTime", false)));
    }

    @Override
    public String getProjectType() {
        return "重点设施";
    }

    @Override
    public List<PropertyDescription> fuzzyProjectDescription(String type, User user) {
        return StringUtils.hasLength(type) ? stream(null, null, type, null, user)
                .collect(Collectors.toList()) : Collections.emptyList();
    }

    @Override
    public PropertyDescription fromForm(String subType, Form form) {
        PropertyDescription _desc;
        if (StringUtils.hasLength(form.getRefId())) {
            // 更新
            Result<PropertyDescription> _r = get(form.getRefId());
            if (_r.isOK()
                    // 专题记录未更新过
                    && (_r.data.getMaintainTime() == null
                    // 初始化的专项记录被更新后更新时间不应为null，避免异常
                    // 专项记录更新时间在专题记录更新时间之后
                    || (form.getMaintainTime() != null && _r.data.getMaintainTime().before(form.getMaintainTime())))) {
                _desc = _r.data;
                Optional.ofNullable(form.getInstallationName()).ifPresent(_desc::setName);
                Optional.ofNullable(form.getInstallationAddress()).ifPresent(_desc::setAddress);
                Optional.ofNullable(form.getAttachmentIdsJSON()).ifPresent(_desc::setAttachmentIdsJSON);
            } else {
                // 不更新
                return null;
            }
        } else {
            // 新增
            if (StringUtils.hasLength(subType)) {
                _desc = new PropertyDescription();
                _desc.setType(subType);
                _desc.setName(Optional.ofNullable(form.getInstallationName()).orElse(""));
                _desc.setAddress(Optional.ofNullable(form.getInstallationAddress()).orElse(""));
                _desc.setAttachmentIdsJSON(form.getAttachmentIdsJSON());
            } else {
                _desc = null;
            }
        }
        return _desc;
    }

    public Result<List<Pair<String, Long>>> myStats(User user) {
        Result<List<Pair<String, Long>>> _result = new Result<>();
        String _userId = user.getId();

        List<Pair<String, Long>> _propertyCountList = stream(PropertyDescription.class)
                .where(i -> i.getCreatorId().equals(_userId))
                .group(i -> i.getType(), (type, stream) -> stream.count())
                .toList();
        List<Dictionary> _propertyTypeDicList = dictionaryRepository.fuzzy("重点物", "类别", true, ImmutableList.of(new SortField("createTime", false)));

        List<Pair<String, Long>> _countList = _propertyTypeDicList.stream()
                .map(propertyTypeDic -> _propertyCountList.stream().filter(p -> p.getOne().equals(propertyTypeDic.getVal()))
                        .findFirst()
                        .orElse(new Pair<>(propertyTypeDic.getVal(), 0L)))
                .collect(Collectors.toList());

        _result.data = _countList;
        return _result;
    }

    void parseAttachments(List<PropertyDescription> items, boolean brief) {
        for (PropertyDescription i : items) {
            i.setAttachmentIds(StringUtils.hasLength(i.getAttachmentIdsJSON()) ? ConverterUtil.json2Object(i.getAttachmentIdsJSON(), new TypeToken<List<String>>() {
            }.getType()) : null);
        }

        if (!brief) {
            List<String> _ids = items.stream()
                    .filter(i -> i.getAttachmentIds() != null)
                    .flatMap(i -> i.getAttachmentIds().stream())
                    .collect(Collectors.toList());

            List<Media> _medias = mediaRepository.query(_ids, null);

            for (PropertyDescription i : items) {
                i.setAttachments(CollectionUtils.isEmpty(i.getAttachmentIds()) ? null : _medias.stream()
                        .filter(j -> i.getAttachmentIds().contains(j.getId()))
                        .collect(Collectors.toList()));
            }
        }
    }

}
