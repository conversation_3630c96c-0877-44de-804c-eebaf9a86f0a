package com.chinamobile.healthcode.repository.subject;

import com.chinamobile.healthcode.model.ValidationGroup;
import com.chinamobile.healthcode.model.project.Form;
import com.chinamobile.healthcode.model.subject.UnitDescription;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.GpsUtil;
import org.hibernate.Transaction;
import org.jinq.jpa.JPQL;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.validation.Validator;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@ErrorCode(module = "055")
public class UnitRepository extends DefaultAbstractEntityRepository<UnitDescription> implements ProjectRepository<UnitDescription> {

    public static final String ADMIN_ROLE = "专题管理员";

    public static final String CENTER = "综治中心";

    final DepartmentRepository<Department> departmentRepository;
    final DefaultRoleRepository roleRepository;
    final DictionaryRepository dictionaryRepository;

    final DefaultResultParser resultParser;

    public UnitRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, Validator validator, DepartmentRepository departmentRepository, DefaultRoleRepository roleRepository, DictionaryRepository dictionaryRepository, DefaultResultParser resultParser) {
        super(entityManagerFactory, jinqJPAStreamProvider, UnitDescription.class, validator);

        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;

        this.resultParser = resultParser;
    }

    @Transactional(readOnly = true)
    public Result<UnitDescription> get(String id) {
        Result<UnitDescription> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{UnitDescription.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(UnitDescription.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{UnitDescription.class});
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<UnitDescription> fuzzy(int count, int index, String regionFullName, String name, String type, String address, User user) {
        JinqStream<UnitDescription> _query = stream(regionFullName, name, type, address, user)
                .sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<UnitDescription> _page = new PagingItems(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }
        _page.items = _query.toList();

        return _page;
    }

    public Result<String> save(UnitDescription item, User user) {
        Result<String> _id = new Result<>();

        boolean _isNew = false;
        Result<UnitDescription> _item = get(item.getId());
        if (!_item.isOK()) {
            _item.data = new UnitDescription();

            _isNew = true;
        }

        Result<Department> _region = departmentRepository.get(item.getRegionId(), true);
        if (!_region.isOK()) {
            return _id.pack(_region);
        }
        _item.data.setRegionId(_region.data.getId());
        _item.data.setRegionFullName(_region.data.getFullName());

        copyProperties(item, _item.data, new String[]{"id", "regionId", "regionFullName"});
        if (Objects.nonNull(_item.data.getLng()) && Objects.nonNull(_item.data.getLat())) {
            double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.data.getLat(), _item.data.getLng());
            _item.data.setLatInWgs(_latLngInWgs[0]);
            _item.data.setLngInWgs(_latLngInWgs[1]);
        }

        Result _success = _isNew ? add(_item.data, user.getId()) : update(_item.data, user.getId());
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result remove(String id, User user) {
        Result _success = new Result();

        Result<UnitDescription> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 非下级单位
        if (!departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                .anyMatch(i -> i.getId().equals(_item.data.getRegionId()))) {
            _item.setCode(Result.DATA_ACCESS_DENY);
            return _item;
        }

        getCurrentSession().remove(_item.data);

        return _success;
    }

    @Transactional(readOnly = true)
    public UnitDescription getDefaultCenter(User user) {
        JinqStream<UnitDescription> _query = stream(UnitDescription.class).where(i -> CENTER.equals(i.getType()));

        if (user != null) {
            String _regionFullName = user.getDeptFullName();
            _query = _query.where(i -> _regionFullName.equals(i.getRegionFullName()));
        }

        return _query.findFirst().orElse(null);
    }

    // 查询综治中心
    @Transactional(readOnly = true)
    public List<UnitDescription> queryCenters(List<String> ids) {
        JinqStream<UnitDescription> _query = stream(UnitDescription.class).where(i -> CENTER.equals(i.getType()));

        if (ids != null) {
            _query = _query.where(i -> ids.contains(i.getId()));
        }

        return _query.toList();
    }

    @Transactional(readOnly = true)
    public List<UnitDescription> suggestCenters(int count, String keyword) {
        List<String> _types = Arrays.asList("综治中心");

        JinqStream<UnitDescription> _query = stream(UnitDescription.class).where(i -> _types.contains(i.getType()));

        if (StringUtils.hasLength(keyword)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(keyword));
        }

        if (count >= 0) {
            _query = _query.limit(count);
        }

        return _query.toList();
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Result<String> importFromExcel(File file, User actor) throws Exception {
        // 读取下级单位
        List<Department> _subordinates = departmentRepository.subordinates(actor.getDeptId(), null, null);

        // 读取部门
        List<Department> _departments = departmentRepository.query(null, null, null, true, null);

        List<String> _types = types().stream()
                .map(i -> i.getVal())
                .collect(Collectors.toList());

        return readExcel((header, lines, results) -> {
            for (int i = 0; i < lines.size(); i++) {
                if (!results.get(i).isOK()) {
                    continue;
                }

                String[] _line = lines.get(i);

                UnitDescription _item = new UnitDescription();

                String _text = _line[header.get("名称")];
                _item.setName(_text);

                String _name = _line[header.get("归属网格")];
                Department _region = _departments.stream()
                        .filter(j -> Objects.equals(_name, j.getFullName()))
                        .findFirst().orElse(null);
                if (_region == null) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 3);
                    results.set(i, _success);

                    continue;
                }

                // 非下级单位
                if (!_subordinates.stream().anyMatch(j -> Objects.equals(j.getId(), _region.getId()))) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 4);
                    results.set(i, _success);

                    continue;
                }
                _item.setRegionId(_region.getId());
                _item.setRegionFullName(_region.getFullName());

                _text = _line[header.get("类别")];
                if (StringUtils.isEmpty(_text) || !_types.contains(_text)) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 5);
                    results.set(i, _success);

                    continue;
                }
                _item.setType(_text);

                _text = _line[header.get("详细地址")];
                _item.setAddress(_text);

                try {
                    _text = _line[header.get("经度")];
                    _item.setLng(StringUtils.hasLength(_text) ? Double.valueOf(_text) : null);
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 6);
                    results.set(i, _success);

                    continue;
                }

                try {
                    _text = _line[header.get("纬度")];
                    _item.setLat(StringUtils.hasLength(_text) ? Double.valueOf(_text) : null);
                } catch (Throwable e) {
                    Result _success = new Result();
                    _success.setCode(Result.ENUM_ERROR.P, 7);
                    results.set(i, _success);

                    continue;
                }

                if (Objects.nonNull(_item.getLng()) && Objects.nonNull(_item.getLat())) {
                    double[] _latLngInWgs = GpsUtil.gcj02ToGps84(_item.getLat(), _item.getLng());
                    _item.setLatInWgs(_latLngInWgs[0]);
                    _item.setLngInWgs(_latLngInWgs[1]);
                }

                Transaction _transaction = getCurrentSession().beginTransaction();
                try {
                    validate(_item, ValidationGroup.Insert.class);
                    add(_item, actor.getId());

                    _transaction.commit();
                } catch (Throwable e) {
                    e.printStackTrace();

                    Result _error = resultParser.fromException(e, false);
                    if (_error == null) {
                        _error = new Result();
                        _error.setCode(Result.ENUM_ERROR.P, 8, new Object[]{e.getMessage()});
                    }
                    results.set(i, _error);

                    _transaction.rollback();
                }
            }
        }, file, 0, 1);
    }

    public JinqStream<UnitDescription> stream(String regionFullName, String name, String type, String address, User user) {
        JinqStream<UnitDescription> _query = stream(UnitDescription.class);

        if (roleRepository.isUserInRoleCached(user.getId(), ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptFullName = user.getDeptFullName();
            String _pattern = user.getDeptFullName() + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> _deptFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR + "%";
            _query = _query.where(i -> regionFullName.equals(i.getRegionFullName()) || JPQL.like(i.getRegionFullName(), _pattern));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (StringUtils.hasLength(type)) {
            _query = _query.where(i -> type.equals(i.getType()));
        }

        if (StringUtils.hasLength(address)) {
            _query = _query.where(i -> i.getAddress() != null && i.getAddress().contains(address));
        }

        return _query;
    }

    @Transactional(readOnly = true)
    public List<Dictionary> types() {
        return dictionaryRepository.fuzzy("重点单位", "类别", true, null);
    }

    @Override
    public String getProjectType() {
        return "单位组织";
    }

    @Override
    public List<UnitDescription> fuzzyProjectDescription(String type, User user) {
        return StringUtils.hasLength(type) ? stream(null, null, type, null, user)
                .collect(Collectors.toList()) : Collections.emptyList();
    }

    @Override
    public UnitDescription fromForm(String subType, Form form) {
        UnitDescription _desc;
        if (StringUtils.hasLength(form.getRefId())) {
            // 更新
            Result<UnitDescription> _r = get(form.getRefId());
            if (_r.isOK()
                    // 专题记录未更新过
                    && (_r.data.getMaintainTime() == null
                    // 初始化的专项记录被更新后更新时间不应为null，避免异常
                    // 专项记录更新时间在专题记录更新时间之后
                    || (form.getMaintainTime() != null && _r.data.getMaintainTime().before(form.getMaintainTime())))) {
                _desc = _r.data;
                Optional.ofNullable(form.getUnitName()).ifPresent(_desc::setName);
                Optional.ofNullable(form.getUnitAddress()).ifPresent(_desc::setAddress);
            } else {
                // 不更新
                return null;
            }
        } else {
            if (StringUtils.hasLength(subType)) {
                // 新增
                _desc = new UnitDescription();
                _desc.setType(subType);
                _desc.setName(Optional.ofNullable(form.getUnitName()).orElse(""));
                _desc.setAddress(Optional.ofNullable(form.getUnitAddress()).orElse(""));
            } else {
                _desc = null;
            }
        }
        return _desc;
    }

    public Result<List<Pair<String, Long>>> myStats(User user) {
        Result<List<Pair<String, Long>>> _result = new Result<>();
        String _userId = user.getId();

        List<Pair<String, Long>> _unitCountList = stream(UnitDescription.class)
                .where(i -> i.getCreatorId().equals(_userId))
                .group(i -> i.getType(), (type, stream) -> stream.count())
                .toList();
        List<Dictionary> _unitTypeDicList = dictionaryRepository.fuzzy("重点单位", "类别", true, null);

        List<Pair<String, Long>> _countList = _unitTypeDicList.stream()
                .map(unitTypeDic -> _unitCountList.stream().filter(p -> p.getOne().equals(unitTypeDic.getVal()))
                        .findFirst()
                        .orElse(new Pair<>(unitTypeDic.getVal(), 0L)))
                .collect(Collectors.toList());

        _result.data = _countList;
        return _result;
    }
}
