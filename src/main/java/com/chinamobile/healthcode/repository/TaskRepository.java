package com.chinamobile.healthcode.repository;

import com.chinamobile.healthcode.model.Task;
import com.chinamobile.healthcode.model.instability.InstabilityNotification;
import com.chinamobile.healthcode.model.iot.Alarm;
import com.chinamobile.healthcode.model.project.Record;
import com.chinamobile.healthcode.repository.instability.InstabilityNotificationRepository;
import com.chinamobile.healthcode.repository.iot.AlarmRepository;
import com.chinamobile.healthcode.repository.project.FormRepository;
import com.chinamobile.healthcode.repository.project.RecordRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.sms.SentSmsRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Tuple3;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import javax.persistence.Query;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Repository
public class TaskRepository extends AbstractEntityRepository<Task> {

    final DepartmentRepository departmentRepository;
    final DefaultRoleRepository roleRepository;
    final DefaultUserRepository userRepository;
    final RecordRepository recordRepository;
    final FormRepository formRepository;
    final AlarmRepository alarmRepository;
    final InstabilityNotificationRepository instabilityNotificationRepository;
    final SentSmsRepository sentSmsRepository;

    public TaskRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
                          @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
                          DepartmentRepository departmentRepository,
                          DefaultRoleRepository roleRepository,
                          DefaultUserRepository userRepository,
                          @Lazy RecordRepository recordRepository,
                          FormRepository formRepository,
                          @Lazy AlarmRepository alarmRepository,
                          @Lazy InstabilityNotificationRepository instabilityNotificationRepository,
                          SentSmsRepository sentSmsRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, Task.class);
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.userRepository = userRepository;
        this.recordRepository = recordRepository;
        this.formRepository = formRepository;
        this.alarmRepository = alarmRepository;
        this.instabilityNotificationRepository = instabilityNotificationRepository;
        this.sentSmsRepository = sentSmsRepository;
    }

    @Transactional(readOnly = true)
    public Result<Task> getBrief(String id, User user) {
        Result<Task> _item = new Result<>();

        if (StringUtils.isEmpty(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Task.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(Task.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Task.class.getSimpleName()});
        } else {
            parseCreator(Collections.singletonList(_item.data), true, user);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public Result<Task> get(String id, User user) {
        Result<Task> _item = getBrief(id, user);
        if (!_item.isOK()) {
            return _item;
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<Task> fuzzy(int count, int index, Task.ENUM_TYPE subjectType, List<Task.ENUM_STATUS> status, User user) {
        JinqStream<Task> _query = stream(status, user);

        if (subjectType != null) {
            _query = _query.where(i -> subjectType == i.getSubjectType());
        }

        _query = _query.sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<Task> _page = new PagingItems(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }
        _page.items = _query.toList();

        List<String> _subjectIds = _page.items.stream()
                .filter(i -> Task.ENUM_TYPE.专项工作 == i.getSubjectType())
                .map(i -> i.getSubjectId())
                .distinct()
                .collect(Collectors.toList());
        List<Record> _projects = recordRepository.query(_subjectIds);

        _subjectIds = _page.items.stream()
                .filter(i -> Task.ENUM_TYPE.设备告警 == i.getSubjectType())
                .map(i -> i.getSubjectId())
                .distinct()
                .collect(Collectors.toList());
        List<Alarm> _alrams = alarmRepository.find(_subjectIds);

        _subjectIds = _page.items.stream()
                .filter(i -> Task.ENUM_TYPE.村社专项管理 == i.getSubjectType() || Task.ENUM_TYPE.综治专题管理 == i.getSubjectType())
                .map(Task::getSubjectId)
                .distinct()
                .collect(Collectors.toList());
        List<InstabilityNotification> _instabilityNotifications = instabilityNotificationRepository.listByIds(_subjectIds);

        for (Task i : _page.items) {
            String _title = null;

            switch (i.getSubjectType()) {
                case 专项工作:
                    _title = _projects.stream()
                            .filter(j -> Objects.equals(i.getSubjectId(), j.getId()))
                            .map(j -> j.getTitle())
                            .findFirst().orElse(null);
                    break;
                case 设备告警:
                    _title = _alrams.stream()
                            .filter(j -> Objects.equals(i.getSubjectId(), j.getId()))
                            .map(j -> j.getDeviceName())
                            .findFirst().orElse(null);
                    break;
                case 村社专项管理:
                case 综治专题管理:
                    InstabilityNotification notification = _instabilityNotifications.stream()
                            .filter(j -> Objects.equals(i.getSubjectId(), j.getId()))
                            .findFirst()
                            .orElse(null);
                    if (notification != null) {
                        _title = notification.getTitle();
                        i.setContent(notification.getContent());
                    }
                    break;
            }
            i.setTitle(_title);
        }

        parseCreator(_page.items, true, user);

        return _page;
    }

    @Transactional(readOnly = true)
    public List<Task> querySubsequent(String subjectId, String previousId, List<Task.ENUM_STATUS> status) {
        return querySubsequent(-1, -1, subjectId, previousId, status).items;
    }

    @Transactional(readOnly = true)
    public PagingItems<Task> querySubsequent(int count, int index, String subjectId, String previousId, List<Task.ENUM_STATUS> status) {
        JinqStream<Task> _query = stream(status, null)
                .where(i -> subjectId.equals(i.getSubjectId()));

        if (previousId == null) {
            _query = _query.where(i -> i.getPreviousId() == null);
        } else {
            _query = _query.where(i -> previousId.equals(i.getPreviousId()));
        }

        _query = _query.sortedDescendingBy(i -> i.getCreateTime());

        PagingItems<Task> _page = new PagingItems(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }
        _page.items = _query.toList();

        return _page;
    }

    @Transactional(readOnly = true)
    public long countTodo(User user) {
        Task.ENUM_STATUS _status = Task.ENUM_STATUS.TODO;
        return stream(null, user).where(i -> i.getStatus() == _status).count();
    }

    @Transactional(readOnly = true)
    public long countUnread(User user) {
        Task.ENUM_STATUS _status = Task.ENUM_STATUS.TODO;
        return stream(null, user).where(i -> i.getStatus() == _status && i.getReadTime() == null).count();
    }

    public long addGroupTasks(String previousId, Task.ENUM_TYPE subjectType, String subjectId, List<Department> departments, String userId) {
        return addRange(previousId, subjectType, subjectId, Task.ENUM_ASSIGNEE.GROUP,
                departments.stream()
                        .map(i -> new Tuple3<>(i.getId(), i.getId(), i.getFullName()))
                        .collect(Collectors.toList()), userId);
    }

    public long addUserTasks(String previousId, Task.ENUM_TYPE subjectType, String subjectId, List<DefaultUser> users, String userId) {
        return addRange(previousId, subjectType, subjectId, Task.ENUM_ASSIGNEE.USER,
                users.stream()
                        .map(i -> new Tuple3<>(i.getId(), i.getDeptId(), i.getDeptFullName()))
                        .collect(Collectors.toList()), userId);
    }

    long addRange(String previousId, Task.ENUM_TYPE subjectType, String subjectId, Task.ENUM_ASSIGNEE assigneeType, List<Tuple3<String, String, String>> assignees, String userId) {
        long _count = 0;

        for (int i = 0; i < assignees.size(); i++) {
            Task _item = new Task();
            _item.setSubjectType(subjectType);
            _item.setSubjectId(subjectId);
            _item.setPreviousId(previousId);
            _item.setAssignee(assigneeType);
            _item.setAssigneeId(assignees.get(i).getOne());
            _item.setDeptId(assignees.get(i).getTwo());
            _item.setDeptFullName(assignees.get(i).getThree());

            _item.setStatus(Task.ENUM_STATUS.TODO);

            super.add(_item, userId);

            _count++;
        }

        return _count;
    }

    /**
     * 签收任务
     *
     * @param id
     * @param user
     * @return
     */
    public Result read(String id, User user) {
        Result _success = new Result();

        Result<Task> _item = getBrief(id, user);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getReadonly()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        if (_item.data.getReadTime() != null) {
            return _success;
        }

        _item.data.setReadTime(new Date());
        return super.update(_item.data, user.getId());
    }

    /**
     * 签收任务
     *
     * @param id
     * @param user
     * @return
     */
    public Result readPeers(String id, User user) {
        Result _success = new Result();

        Result<Task> _item = getBrief(id, user);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getReadonly()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        if (_item.data.getReadTime() != null) {
            return _success;
        }

        List<String> _taskIds = stream(Task.ENUM_TYPE.专项工作, Collections.singletonList(_item.data.getSubjectId()), user.getDeptFullName(), Task.ENUM_ASSIGNEE.USER)
                .where(i -> i.getReadTime() == null)
                .select(i -> i.getId())
                .toList();
        if (!CollectionUtils.isEmpty(_taskIds)) {
            Query _query = getCurrentSession().createQuery("UPDATE Task SET readTime = ?1, maintainerId = ?2, maintainTime = ?3 WHERE id in ?4");
            _query.setParameter(1, new Date());
            _query.setParameter(2, user.getId());
            _query.setParameter(3, new Date());
            _query.setParameter(4, _taskIds);

            _query.executeUpdate();
        }

        return _success;
    }

    /**
     * 变更状态
     *
     * @param id
     * @param status
     * @param user
     * @return
     */
    public Result updateStatus(String id, String memo, Task.ENUM_STATUS status, User user) {
        Result _success = new Result();

        Result<Task> _item = getBrief(id, user);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        if (_item.data.getReadonly()) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        _item.data.setMemo(memo);
        _item.data.setStatus(status);

        if (Task.ENUM_STATUS.CLOSED == status) {
            _item.data.setCompleteTime(new Date());
        }

        return super.update(_item.data, user.getId());
    }

    public int doneChildren(String subjectId, String deptFullName, String userId) {
        Task.ENUM_STATUS _status = Task.ENUM_STATUS.TODO;

        List<String> _taskIds = stream(Task.ENUM_TYPE.专项工作, Collections.singletonList(subjectId), deptFullName, Task.ENUM_ASSIGNEE.USER)
                .where(i -> _status == i.getStatus())
                .select(i -> i.getId())
                .toList();
        if (CollectionUtils.isEmpty(_taskIds)) {
            return 0;
        }

        Query _query = getCurrentSession().createQuery("UPDATE Task SET status = ?1, readTime = ?2, completeTime = ?3, maintainerId = ?4, maintainTime = ?5 WHERE id in ?6");
        _query.setParameter(1, Task.ENUM_STATUS.DONE);
        _query.setParameter(2, new Date());
        _query.setParameter(3, new Date());
        _query.setParameter(4, userId);
        _query.setParameter(5, new Date());
        _query.setParameter(6, _taskIds);

        return _query.executeUpdate();
    }

    public int retractChildren(String subjectId, String deptFullName, String userId) {
        Task.ENUM_STATUS _status = Task.ENUM_STATUS.TODO;

        List<String> _taskIds = stream(Task.ENUM_TYPE.专项工作, Collections.singletonList(subjectId), deptFullName, Task.ENUM_ASSIGNEE.USER)
                .where(i -> _status != i.getStatus())
                .select(i -> i.getId())
                .toList();
        if (CollectionUtils.isEmpty(_taskIds)) {
            return 0;
        }

        Query _query = getCurrentSession().createQuery("UPDATE Task SET status = ?1, readTime = ?2, completeTime = ?3, maintainerId = ?4, maintainTime = ?5 WHERE id in ?6");
        _query.setParameter(1, Task.ENUM_STATUS.TODO);
        _query.setParameter(2, new Date());
        _query.setParameter(3, null);
        _query.setParameter(4, userId);
        _query.setParameter(5, new Date());
        _query.setParameter(6, _taskIds);

        return _query.executeUpdate();
    }

    /**
     * 批量关闭
     *
     * @param subjectId
     * @param userId
     * @return
     */
    public int closeBySubjectId(String subjectId, String userId) {
        Query _query = getCurrentSession().createQuery("UPDATE Task SET status = ?1, completeTime = ?2, maintainerId = ?3, maintainTime = ?4 WHERE subjectId = ?5 AND status = ?6");
        _query.setParameter(1, Task.ENUM_STATUS.CLOSED);
        _query.setParameter(2, new Date());
        _query.setParameter(3, userId);
        _query.setParameter(4, new Date());
        _query.setParameter(5, subjectId);
        _query.setParameter(6, Task.ENUM_STATUS.TODO);

        return _query.executeUpdate();
    }

    /**
     * 根据专项id和用户信息获取该用户在专项中的任务信息
     *
     * @param subjectId 专项id
     * @param user      用户信息
     * @return 该用户在专项中的任务
     */
    public Task getBySubjectIdAndAssigneeId(String subjectId, User user) {
        return stream(null, user)
                .where(t -> t.getSubjectId().equals(subjectId))
                .findFirst()
                .orElse(null);
    }

    public void urge(Supplier<List<Task>> supplier, String templateId, String sign, String userId) {
        urge(supplier, templateId, sign, null, userId);
    }

    public void urge(Supplier<List<Task>> supplier, String templateId, String sign, List<String> params, String userId) {
        List<Task> _tasks = supplier.get();
        if (CollectionUtils.isEmpty(_tasks)) {
            return;
        }

        List<DefaultUser> _users = new ArrayList<>();

        // 组任务
        List<String> _departmentIds = _tasks.stream()
                .filter(i -> Task.ENUM_ASSIGNEE.GROUP == i.getAssignee())
                .map(Task::getAssigneeId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(_departmentIds)) {
            _users.addAll(userRepository.query(null, _departmentIds, null, null, null, true, null).stream()
                    .filter(i -> StringUtils.hasLength(i.getMp()))
                    .filter(i -> !roleRepository.isUserInRole(i.getId(), RecordRepository.LEADER_ROLE))
                    .collect(Collectors.toList()));
        }

        // 个人任务
        List<String> _userIds = _tasks.stream()
                .filter(i -> Task.ENUM_ASSIGNEE.USER == i.getAssignee())
                .map(Task::getAssigneeId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(_userIds)) {
            _users.addAll(userRepository.query(_userIds, null, null, null, null, true, null).stream()
                    .filter(i -> StringUtils.hasLength(i.getMp()))
                    .filter(i -> !roleRepository.isUserInRole(i.getId(), RecordRepository.LEADER_ROLE))
                    .collect(Collectors.toList()));
        }

        List<DefaultUser> _temp = _users;
        new Thread(() -> {
            Map<String, List<User>> _map = _temp.stream()
                    .collect(Collectors.groupingBy(User::getDeptId));
            for (Map.Entry<String, List<User>> i : _map.entrySet()) {
                sentSmsRepository.inbox(i.getValue().stream()
                        .map(User::getMp)
                        .collect(Collectors.toList()), templateId, Objects.isNull(params) ? Collections.singletonList(i.getValue().get(0).getDeptName()) : params, sign, userId);
            }
        }).start();
    }

    public JinqStream<Task> stream(Task.ENUM_TYPE subjectType, List<String> subjectIds, String deptFullName, Task.ENUM_ASSIGNEE assignee) {
        JinqStream<Task> _query = stream(Task.class);

        if (subjectType != null) {
            _query = _query.where(i -> subjectType == i.getSubjectType());
        }

        if (subjectIds != null) {
            _query = _query.where(i -> subjectIds.contains(i.getSubjectId()));
        }

        if (StringUtils.hasLength(deptFullName)) {
            String _prefix = String.format("%s/", deptFullName);
            _query = _query.where(i -> deptFullName.equals(i.getDeptFullName()) || i.getDeptFullName().startsWith(_prefix));
        }

        if (assignee != null) {
            _query = _query.where(i -> assignee == i.getAssignee());
        }

        return _query;
    }

    JinqStream<Task> stream(List<Task.ENUM_STATUS> status, User user) {
        JinqStream<Task> _query = stream(Task.class);

        if (!CollectionUtils.isEmpty(status)) {
            _query = _query.where(i -> status.contains(i.getStatus()));
        }

        if (user != null) {
            Task.ENUM_ASSIGNEE _assignee1 = Task.ENUM_ASSIGNEE.GROUP;
            String _groupId = user.getDeptId();

            Task.ENUM_ASSIGNEE _assignee2 = Task.ENUM_ASSIGNEE.USER;
            String _userId = user.getId();

            _query = _query.where(i -> (_assignee1 == i.getAssignee() && _groupId.equals(i.getAssigneeId())) || (_assignee2 == i.getAssignee() && _userId.equals(i.getAssigneeId())));
        }

        return _query;
    }

    protected void parseCreator(List<Task> items, boolean brief, User user) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        for (Task i : items) {
            /* if (Task.ENUM_STATUS.TODO != i.getStatus()) {
                i.setReadonly(true);
                continue;
            } */

            switch (i.getAssignee()) {
                case GROUP:
                    i.setReadonly(!Objects.equals(i.getAssigneeId(), user.getDeptId()));
                    break;
                case USER:
                    i.setReadonly(!Objects.equals(i.getAssigneeId(), user.getId()));
                    break;
            }
        }
    }

}
