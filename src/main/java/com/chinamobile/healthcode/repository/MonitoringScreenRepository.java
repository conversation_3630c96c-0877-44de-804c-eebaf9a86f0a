package com.chinamobile.healthcode.repository;

import com.chinamobile.healthcode.model.subject.PersonDescription;
import com.chinamobile.healthcode.repository.citizen.ProfileRepository;
import com.chinamobile.healthcode.repository.grid.AttendantRepository;
import com.chinamobile.healthcode.repository.subject.*;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractJinqRepository;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.Arrays;
import java.util.List;

@Repository
@Transactional(readOnly = true)
public class MonitoringScreenRepository extends AbstractJinqRepository {

    final DepartmentRepository<DefaultDepartment> departmentRepository;
    final RoleRepository roleRepository;
    final ProfileRepository profileRepository;
    final PlaceRepository placeRepository;
    final UnitRepository unitRepository;
    final PersonRepository personRepository;
    final PropertyRepository propertyRepository;
    final EventRepository eventRepository;
    final AttendantRepository attendantRepository;

    public MonitoringScreenRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            DepartmentRepository<DefaultDepartment> departmentRepository,
            RoleRepository roleRepository,
            ProfileRepository profileRepository,
            PlaceRepository placeRepository,
            UnitRepository unitRepository,
            PersonRepository personRepository,
            PropertyRepository propertyRepository,
            EventRepository eventRepository,
            AttendantRepository attendantRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider);
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.profileRepository = profileRepository;
        this.placeRepository = placeRepository;
        this.unitRepository = unitRepository;
        this.personRepository = personRepository;
        this.propertyRepository = propertyRepository;
        this.eventRepository = eventRepository;
        this.attendantRepository = attendantRepository;
    }

    public OrganizationDTO organization(String regionFullName, User user) {
        String _regionFullName = getTopDeptFullName(regionFullName, user);

        Result<DefaultDepartment> _department = ((DefaultDepartmentRepository) departmentRepository).getByFullName(StringUtils.hasLength(regionFullName) ? regionFullName : DefaultDepartmentRepository.ROOT_DEPARTMENT_NAME);
        Double _area = _department.isOK() ? _department.data.getArea() : null;

        // 网格数
        int _level = DefaultDepartmentRepository.GRID_LEVEL;
        Long _gridNum = streamSubordinates(_regionFullName)
                .where(i -> _level == i.getLevel())
                .where(i -> i.getIsEnabled())
                .count();

        // 注册用户数
        Long _attendants = attendantRepository.number(_regionFullName);

        Long _registerUserNum = streamMembers(_regionFullName).count();

        // 在线用户数
        Long _onlineUserNum = streamMembers(_regionFullName)
                .where(i -> i.getOne().getIsOnline())
                .count();

        return new OrganizationDTO(_area, _gridNum, _attendants, _registerUserNum, _onlineUserNum);
    }

    public SubjectDTO subject(String regionFullName, User user) {
        // 实有人口
        Long _population = profileRepository.stream(regionFullName, null).count();

        // 重点单位
        Long _unitNum = unitRepository.stream(regionFullName, null, null, null, user).count();

        // 重点人员
        Long _personNum = personRepository.stream(regionFullName, null, null, null, user).count();

        // 重点设施
        Long _installationNum = propertyRepository.stream(regionFullName, null, null, null, user).count();

        return new SubjectDTO(_population, _unitNum, _personNum, _installationNum);
    }

    public List<Pair<PersonDescription.ENUM_TYPE, Long>> person(String regionFullName, User user) {
        return personRepository.stream(regionFullName, null, null, null, user)
                .group(i -> i.getType(), (type, stream) -> stream.count())
                .toList();
    }

    public List<Pair<String, Long>> event(String regionFullName, User user) {
        return eventRepository.stream(regionFullName,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        user)
                .group(i -> i.getType(), (type, stream) -> stream.count())
                .toList();
    }

    public List<Pair<String, Long>> windbreak(String regionFullName, User user) {
        List<String> _types = Arrays.asList("积水点", "危房", "应急避难场所");
        List<Pair<String, Long>> _count = placeRepository.stream(regionFullName, null, null, user)
                .where(i -> _types.contains(i.getPlaceName()))
                .group(i -> i.getPlaceName(), (type, stream) -> stream.count())
                .toList();

        List<String> _types2 = Arrays.asList("水闸");
        _count.addAll(propertyRepository.stream(regionFullName, null, null, null, user)
                .where(i -> _types2.contains(i.getType()))
                .group(i -> i.getType(), (type, stream) -> stream.count())
                .toList());

        return _count;
    }

    String getTopDeptFullName(String regionFullName, User user) {
        if (StringUtils.isEmpty(regionFullName)) {
            Result<DefaultDepartment> _department = departmentRepository.get(user.getDeptId(), true);
            return (_department.isOK() && DefaultDepartmentRepository.ROOT_DEPARTMENT_NAME.equals(_department.data.getName()) && 1 == _department.data.getLevel()
                    ? null
                    : user.getDeptFullName());
        }

        return roleRepository.isUserInRole(user.getId(), DefaultRoleRepository.ADMIN_ROLE)
                ? regionFullName
                : (
                regionFullName.equals(user.getDeptFullName()) || regionFullName.startsWith(user.getDeptFullName() + Department.NAME_SEPARATOR)
                        ? regionFullName
                        : user.getDeptFullName()
        );
    }

    JinqStream<Department> streamSubordinates(String regionFullName) {
        JinqStream<Department> _query = stream(Department.class);

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR;
            _query = _query.where(i -> i.getFullName().equals(regionFullName) || i.getFullName().startsWith(_pattern));
        }

        return _query;
    }

    JinqStream<Pair<User, Department>> streamMembers(String regionFullName) {
        JinqStream<Pair<User, Department>> _query = stream(User.class)
                .where(i -> i.getIsEnabled())
                .leftOuterJoin((i, source) -> source.stream(Department.class), (i, j) -> j.getId().equals(i.getDeptId()));

        if (StringUtils.hasLength(regionFullName)) {
            String _pattern = regionFullName + Department.NAME_SEPARATOR;
            _query = _query.where(i -> i.getTwo() != null)
                    .where(i -> i.getTwo().getFullName().equals(regionFullName) || i.getTwo().getFullName().startsWith(_pattern));
        }

        return _query;
    }

    public static class OrganizationDTO {

        Double area;
        Long grids;
        Long attendants;
        Long registerUsers;
        Long onlineUsers;

        public OrganizationDTO(Double area, Long grids, Long attendants, Long registerUsers, Long onlineUsers) {
            this.area = area;
            this.grids = grids;
            this.attendants = attendants;
            this.registerUsers = registerUsers;
            this.onlineUsers = onlineUsers;
        }

        public Double getArea() {
            return area;
        }

        public void setArea(Double area) {
            this.area = area;
        }

        public Long getGrids() {
            return grids;
        }

        public void setGrids(Long grids) {
            this.grids = grids;
        }

        public Long getAttendants() {
            return attendants;
        }

        public void setAttendants(Long attendants) {
            this.attendants = attendants;
        }

        public Long getRegisterUsers() {
            return registerUsers;
        }

        public void setRegisterUsers(Long registerUsers) {
            this.registerUsers = registerUsers;
        }

        public Long getOnlineUsers() {
            return onlineUsers;
        }

        public void setOnlineUsers(Long onlineUsers) {
            this.onlineUsers = onlineUsers;
        }

    }

    public static class SubjectDTO {

        Long populations;
        Long units;
        Long persons;
        Long installations;

        public SubjectDTO(Long populations, Long units, Long persons, Long installations) {
            this.populations = populations;
            this.units = units;
            this.persons = persons;
            this.installations = installations;
        }

        public Long getPopulations() {
            return populations;
        }

        public void setPopulations(Long populations) {
            this.populations = populations;
        }

        public Long getUnits() {
            return units;
        }

        public void setUnits(Long units) {
            this.units = units;
        }

        public Long getPersons() {
            return persons;
        }

        public void setPersons(Long persons) {
            this.persons = persons;
        }

        public Long getInstallations() {
            return installations;
        }

        public void setInstallations(Long installations) {
            this.installations = installations;
        }

    }

}
