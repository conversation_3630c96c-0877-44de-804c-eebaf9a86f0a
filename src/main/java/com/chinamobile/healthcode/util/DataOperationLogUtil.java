package com.chinamobile.healthcode.util;

import com.chinamobile.healthcode.annotation.DataOperationLog;
import com.chinamobile.healthcode.model.DataOperationLogEntity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;

/**
 * 数据操作日志工具类
 */
public class DataOperationLogUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 构建数据操作日志实体
     */
    public static DataOperationLogEntity buildLogEntity(DataOperationLog annotation, 
                                                      Method method, 
                                                      Object[] args, 
                                                      Object result, 
                                                      Object beforeData, 
                                                      Object afterData,
                                                      String operatorId,
                                                      String operatorName,
                                                      String operatorDept,
                                                      Long executionTime,
                                                      boolean success,
                                                      String errorMessage) {
        
        DataOperationLogEntity logEntity = new DataOperationLogEntity();
        
        // 设置基本信息
        logEntity.setOperationType(annotation.type().name());
        logEntity.setDescription(annotation.description());
        logEntity.setEntityName(annotation.entityName());
        logEntity.setOperationTime(new Date());
        logEntity.setExecutionTime(executionTime);
        logEntity.setSuccess(success);
        logEntity.setErrorMessage(errorMessage);
        
        // 设置操作人信息
        logEntity.setOperatorId(operatorId);
        logEntity.setOperatorName(operatorName);
        logEntity.setOperatorDept(operatorDept);
        
        // 设置实体ID
        if (annotation.entityIdParam() != null && !annotation.entityIdParam().isEmpty()) {
            String entityId = extractEntityId(args, annotation.entityIdParam());
            logEntity.setEntityId(entityId);
        }
        
        // 设置操作前后数据
        if (annotation.logParams()) {
            try {
                if (beforeData != null) {
                    logEntity.setBeforeData(objectMapper.writeValueAsString(beforeData));
                }
                if (afterData != null) {
                    logEntity.setAfterData(objectMapper.writeValueAsString(afterData));
                }
                if (args != null && args.length > 0) {
                    logEntity.setRequestParams(objectMapper.writeValueAsString(args));
                }
            } catch (JsonProcessingException e) {
                logEntity.setRequestParams("序列化失败: " + e.getMessage());
            }
        }
        
        // 设置响应结果
        if (annotation.logResult() && result != null) {
            try {
                logEntity.setResponseResult(objectMapper.writeValueAsString(result));
            } catch (JsonProcessingException e) {
                logEntity.setResponseResult("序列化失败: " + e.getMessage());
            }
        }
        
        // 设置请求信息
        setRequestInfo(logEntity);
        
        return logEntity;
    }

    /**
     * 提取实体ID
     */
    private static String extractEntityId(Object[] args, String entityIdParam) {
        if (args == null || args.length == 0) {
            return null;
        }
        
        for (Object arg : args) {
            if (arg != null) {
                try {
                    Method getIdMethod = arg.getClass().getMethod("getId");
                    Object id = getIdMethod.invoke(arg);
                    if (id != null) {
                        return id.toString();
                    }
                } catch (Exception e) {
                    // 忽略异常，继续查找
                }
            }
        }
        
        return null;
    }

    /**
     * 设置请求信息
     */
    private static void setRequestInfo(DataOperationLogEntity logEntity) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                logEntity.setClientIp(getClientIp(request));
                logEntity.setUserAgent(request.getHeader("User-Agent"));
                logEntity.setRequestUrl(request.getRequestURL().toString());
                logEntity.setRequestMethod(request.getMethod());
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }

    /**
     * 获取客户端IP地址
     */
    private static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 获取修改前的数据（用于UPDATE操作）
     */
    public static Object getBeforeData(Object entity, String entityId) {
        // 这里可以通过Repository查询数据库中的原始数据
        // 为了简化，这里返回null，实际使用时可以实现具体的查询逻辑
        return null;
    }

    /**
     * 判断是否为新增操作
     */
    public static boolean isCreateOperation(Object entity) {
        if (entity == null) {
            return false;
        }
        
        try {
            Method getIdMethod = entity.getClass().getMethod("getId");
            Object id = getIdMethod.invoke(entity);
            return id == null || id.toString().trim().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断是否为修改操作
     */
    public static boolean isUpdateOperation(Object entity) {
        if (entity == null) {
            return false;
        }
        
        try {
            Method getIdMethod = entity.getClass().getMethod("getId");
            Object id = getIdMethod.invoke(entity);
            return id != null && !id.toString().trim().isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
} 