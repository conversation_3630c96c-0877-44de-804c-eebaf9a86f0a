package com.chinamobile.healthcode.util;

import com.chinamobile.healthcode.annotation.DataOperationLog;
import com.chinamobile.healthcode.model.DataOperationLogEntity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.chinamobile.sparrow.domain.model.sys.User;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批量操作日志工具类
 * 用于构建批量操作的日志实体
 */
public class BatchOperationLogUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 创建批量操作日志实体
     */
    public static DataOperationLogEntity createBatchLog(DataOperationLog.OperationType operationType,
                                                       String description,
                                                       String entityName,
                                                       User user,
                                                       int totalCount,
                                                       int successCount,
                                                       int failedCount,
                                                       List<String> entityIds) {
        
        DataOperationLogEntity logEntity = new DataOperationLogEntity();
        
        // 设置基本信息
        logEntity.setOperationType(operationType.name());
        logEntity.setDescription(description);
        logEntity.setEntityName(entityName);
        logEntity.setOperationTime(new Date());
        logEntity.setSuccess(failedCount == 0);
        
        // 设置操作人信息
        if (user != null) {
            logEntity.setOperatorId(user.getId());
            logEntity.setOperatorName(user.getName());
            logEntity.setOperatorDept(user.getDeptFullName());
        }
        
        // 设置批量操作信息
        try {
            Map<String, Object> batchInfo = new HashMap<>();
            batchInfo.put("operationType", "BATCH");
            batchInfo.put("totalCount", totalCount);
            batchInfo.put("successCount", successCount);
            batchInfo.put("failedCount", failedCount);
            batchInfo.put("entityIds", entityIds);
            batchInfo.put("batchOperation", true);
            
            logEntity.setRequestParams(objectMapper.writeValueAsString(batchInfo));
        } catch (JsonProcessingException e) {
            logEntity.setRequestParams("{\"batchOperation\": true, \"totalCount\": " + totalCount + "}");
        }
        
        // 设置请求信息
        setRequestInfo(logEntity);
        
        return logEntity;
    }

    /**
     * 创建单个操作日志实体
     */
    public static DataOperationLogEntity createSingleLog(DataOperationLog.OperationType operationType,
                                                        String description,
                                                        String entityName,
                                                        String entityId,
                                                        Object beforeData,
                                                        Object afterData,
                                                        User user,
                                                        boolean success,
                                                        String errorMessage) {
        
        DataOperationLogEntity logEntity = new DataOperationLogEntity();
        
        // 设置基本信息
        logEntity.setOperationType(operationType.name());
        logEntity.setDescription(description);
        logEntity.setEntityName(entityName);
        logEntity.setEntityId(entityId);
        logEntity.setOperationTime(new Date());
        logEntity.setSuccess(success);
        logEntity.setErrorMessage(errorMessage);
        
        // 设置操作人信息
        if (user != null) {
            logEntity.setOperatorId(user.getId());
            logEntity.setOperatorName(user.getName());
            logEntity.setOperatorDept(user.getDeptFullName());
        }
        
        // 设置操作前后数据
        try {
            if (beforeData != null) {
                logEntity.setBeforeData(objectMapper.writeValueAsString(beforeData));
            }
            if (afterData != null) {
                logEntity.setAfterData(objectMapper.writeValueAsString(afterData));
            }
        } catch (JsonProcessingException e) {
            // 忽略序列化异常
        }
        
        // 设置请求信息
        setRequestInfo(logEntity);
        
        return logEntity;
    }

    /**
     * 创建错误操作日志实体
     */
    public static DataOperationLogEntity createErrorLog(DataOperationLog.OperationType operationType,
                                                       String description,
                                                       String entityName,
                                                       String entityId,
                                                       Object entity,
                                                       User user,
                                                       Exception e) {
        
        DataOperationLogEntity logEntity = new DataOperationLogEntity();
        
        // 设置基本信息
        logEntity.setOperationType(operationType.name());
        logEntity.setDescription(description);
        logEntity.setEntityName(entityName);
        logEntity.setEntityId(entityId);
        logEntity.setOperationTime(new Date());
        logEntity.setSuccess(false);
        logEntity.setErrorMessage(e.getMessage());
        
        // 设置操作人信息
        if (user != null) {
            logEntity.setOperatorId(user.getId());
            logEntity.setOperatorName(user.getName());
            logEntity.setOperatorDept(user.getDeptFullName());
        }
        
        // 设置操作前数据（删除操作记录删除前的数据）
        if (entity != null && operationType == DataOperationLog.OperationType.DELETE) {
            try {
                logEntity.setBeforeData(objectMapper.writeValueAsString(entity));
            } catch (JsonProcessingException ex) {
                // 忽略序列化异常
            }
        }
        
        // 设置请求信息
        setRequestInfo(logEntity);
        
        return logEntity;
    }

    /**
     * 设置请求信息
     */
    private static void setRequestInfo(DataOperationLogEntity logEntity) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                logEntity.setClientIp(getClientIp(request));
                logEntity.setUserAgent(request.getHeader("User-Agent"));
                logEntity.setRequestUrl(request.getRequestURL().toString());
                logEntity.setRequestMethod(request.getMethod());
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }

    /**
     * 获取客户端IP地址
     */
    private static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
