package com.chinamobile.sparrow.domain.repository.sec;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.jinq.tuples.Pair;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "cache-authorization", jobGroup = "default", triggerName = "cache-authorization", triggerGroup = "default", triggerCron = "0 0/10 * * * ?", triggerOnStart = false)
public class AuthorizationJob extends QuartzJobBean {

    final DefaultPermissionRepository permissionRepository;
    final DefaultRoleRepository roleRepository;
    final Logger logger;

    public AuthorizationJob(DefaultPermissionRepository permissionRepository, DefaultRoleRepository roleRepository) {
        this.permissionRepository = permissionRepository;
        this.roleRepository = roleRepository;
        this.logger = LoggerFactory.getLogger(AuthorizationJob.class);
    }

    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        logger.info("作业开始");

        try {
            Pair<Integer, Long> _count = permissionRepository.cacheUserPermissions();
            logger.info(String.format("已缓存%s位用户的%s项权限", _count.getOne(), _count.getTwo()));

            _count = roleRepository.cacheUserRoles();
            logger.info(String.format("已缓存%s位用户的%s个角色", _count.getOne(), _count.getTwo()));
        } catch (Throwable e) {
            this.logger.info("作业执行时程序异常", e);
        } finally {
            this.logger.info("作业结束");
        }
    }

}