package com.chinamobile.sparrow.domain.repository.media;

import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "clear-temporary-media", jobGroup = "default", triggerName = "clear-temporary-media", triggerGroup = "default", triggerCron = "0 0 0 * * ?")
public class ClearJob extends QuartzJobBean {

    protected final AbstractMediaRepository mediaRepository;
    protected final Logger logger;

    public ClearJob(AbstractMediaRepository mediaRepository) {
        this.mediaRepository = mediaRepository;
        this.logger = LoggerFactory.getLogger(ClearJob.class);
    }

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        logger.info("作业开始");

        try {
            int _count = mediaRepository.clearTemporary(null);
            this.logger.info(String.format("已清理%s件临时文件", _count));
        } catch (Throwable e) {
            logger.info("作业执行时程序异常", e);
        } finally {
            logger.info("作业结束");
        }
    }

}