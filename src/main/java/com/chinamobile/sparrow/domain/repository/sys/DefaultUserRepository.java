package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.util.CryptoUtil;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.*;
import java.util.stream.Collectors;

@ErrorCode(module = "002")
public class DefaultUserRepository extends UserRepository<DefaultUser> {

    protected final String passwordConstraint;
    protected final String rsaPrivateKey;

    protected final DefaultDepartmentRepository departmentRepository;
    protected final RoleRepository roleRepository;

    public DefaultUserRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            String passwordConstraint,
            String rsaPrivateKey,
            DefaultDepartmentRepository departmentRepository,
            RoleRepository roleRepository,
            AbstractMediaRepository mediaRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, DefaultUser.class);
        this.passwordConstraint = passwordConstraint;
        this.rsaPrivateKey = rsaPrivateKey;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
    }

    @Transactional(readOnly = true)
    public Result<DefaultUser> getBriefInWxMa(String openId, String mp, Boolean isEnabled) {
        Result<DefaultUser> _item = new Result<>();

        _item.data = stream(DefaultUser.class)
                .where(i -> openId.equals(i.getMaOpenId()) || mp.equals(i.getMp()))
                .where(i -> isEnabled == null || isEnabled == i.getIsEnabled())
                .sortedDescendingBy(DefaultUser::getMaOpenId)
                .findFirst().orElse(null);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{DefaultUser.class.getSimpleName()});
            return _item;
        }

        return _item;
    }

    @Override
    public Result<String> save(DefaultUser item, String actorId) throws InstantiationException, IllegalAccessException {
        Result<String> _id = new Result<>();

        boolean _isNew = false;

        Result<DefaultUser> _item = getBriefByIdOrAccountOrMp(item.getId(), null);
        if (!_item.isOK()) {
            _item.data = new DefaultUser();

            _isNew = true;
        }

        String _identifier = _item.data.getId();
        JinqStream<DefaultUser> _query = stream(DefaultUser.class).where(i -> _identifier == null || !_identifier.equals(i.getId()));

        String _account = item.getAccount();
        String _mp = item.getMp();
        if (_query.where(i -> _account.equals(i.getAccount()) || (_mp != null && _mp.equals(i.getMp())))
                .findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        }

        if (StringUtils.hasLength(item.getDeptId())) {
            _item.data.setDeptId(item.getDeptId());
        }

        _item.data.setMp(StringUtils.hasLength(item.getMp()) ? item.getMp() : null);

        copyProperties(item, _item.data, new String[]{"id", "password", "isEnabled", "deptId", "yzyId", "maOpenId", "unionId", "isOnline"});

        Result _success = _isNew ? add(_item.data, actorId) : update(_item.data, actorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result changePassword(String id, String originalPassword, String newPassword, String operatorId) {
        Result _success = new Result();

        if (Objects.equals(originalPassword, newPassword)) {
            _success.setCode(Result.ENUM_ERROR.P, 1);
            return _success;
        }

        Result<DefaultUser> _item = getBriefByIdOrAccountOrMp(id, null);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        String _originalPassword;
        try {
            _originalPassword = CryptoUtil.decryptRsa(originalPassword, rsaPrivateKey);
        } catch (Throwable e) {
            throw new IncorrectCredentialsException(e);
        }

        String _password;
        try {
            _password = CryptoUtil.decryptRsa(_item.data.getPassword(), rsaPrivateKey);
        } catch (Throwable e) {
            throw new IncorrectCredentialsException(e);
        }

        if (!Objects.equals(_originalPassword, _password)) {
            _success.setCode(Result.ENUM_ERROR.P, 2);
            return _success;
        }

        if (StringUtils.isEmpty(newPassword)) {
            _success.setCode(Result.ENUM_ERROR.P, 3);
            return _success;
        }

        _success = validatePassword(newPassword);
        if (!_success.isOK()) {
            return _success;
        }

        _item.data.setPassword(newPassword);
        return update(_item.data, operatorId);
    }

    public Result resetPassword(String id, String password, String operatorId) {
        Result _success = new Result();

        Result<DefaultUser> _record = getBriefByIdOrAccountOrMp(id, null);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        if (StringUtils.isEmpty(password)) {
            _success.setCode(Result.ENUM_ERROR.P, 3);
            return _success;
        }

        _success = validatePassword(password);
        if (!_success.isOK()) {
            return _success;
        }

        _record.data.setPassword(password);
        return update(_record.data, operatorId);
    }

    /* @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Result<Pair<Integer, Integer>> initAdmin(String actorId) {
        Result<Pair<Integer, Integer>> _count = new Result();

        List<DefaultUser> _items = stream(DefaultUser.class).toList();

        List<Department> _departments = stream(Department.class)
                .where(i -> i.getLevel() > 1 && i.getIsEnabled())
                .toList();

        List<DefaultUser> _itemsNew = new ArrayList<>();
        boolean _fail = false;
        String _password = "Cs123456";

        for (Department i : _departments) {
            List<DefaultUser> _members = _items.stream()
                    .filter(j -> Objects.equals(i.getId(), j.getDeptId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(_members)) {
                for (int j = 1; j < 11; j++) {
                    DefaultUser _user = new DefaultUser();

                    String _pinyin = ChineseUtil.CharacterToPinyin(i.getName());
                    if (StringUtils.isEmpty(_pinyin)) {
                        _pinyin = i.getId();
                    }

                    _user.setAccount(_pinyin + "_" + j);
                    _user.setName(i.getName() + "工作人员-" + j);
                    _user.setDeptId(i.getId());
                    _user.setIsMale(true);
                    _user.setIsEnabled(true);

                    // 新增账号
                    if (add(_user, actorId).isOK()) {
                        _itemsNew.add(_user);
                    } else {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

                        _fail = true;

                        break;
                    }

                    // 重置密码
                    resetPassword(_user.getId(), DigestUtils.md5DigestAsHex(_password.getBytes()), actorId);
                }
            }
        }

        if (_fail) {
            _count.setCode(Result.ENUM_ERROR.B, 1);
            return _count;
        }

        List<String> _roleNames = Arrays.asList(FormRepository.SUBDISTRICT_STAFF_ROLE, FormRepository.COMMUNITY_STAFF_ROLE);
        List<Role> _roles = stream(Role.class).where(i -> _roleNames.contains(i.getName())).toList();
        String _subdistrictStaffRoleId = _roles.stream()
                .filter(i -> Objects.equals(FormRepository.SUBDISTRICT_STAFF_ROLE, i.getName()))
                .map(i -> i.getId())
                .findFirst().orElse(null);
        String _communityStaffRoleId = _roles.stream()
                .filter(i -> Objects.equals(FormRepository.COMMUNITY_STAFF_ROLE, i.getName()))
                .map(i -> i.getId())
                .findFirst().orElse(null);

        List<UserRole> _userRoles = stream(UserRole.class).where(i -> _subdistrictStaffRoleId.equals(i.getRoleId()) || _communityStaffRoleId.equals(i.getRoleId()))
                .toList();

        int _relationCount = 0;
        for (User i : _itemsNew) {
            Department _department = _departments.stream()
                    .filter(j -> Objects.equals(i.getDeptId(), j.getId()))
                    .findFirst().orElse(null);
            switch (_department.getLevel()) {
                case 2:
                    // 已授权
                    if (_userRoles.stream().anyMatch(j -> Objects.equals(i.getId(), j.getUserId()) && _subdistrictStaffRoleId.equals(j.getRoleId()))) {
                        continue;
                    }

                    // 未授权则授权
                    UserRole _userRole = new UserRole();
                    _userRole.setUserId(i.getId());
                    _userRole.setRoleId(_subdistrictStaffRoleId);
                    _userRole.setCreatorId(actorId);
                    _userRole.setCreateTime(new Date());

                    getCurrentSession().save(_userRole);

                    _relationCount += 1;

                    break;
                case 3:
                    // 已授权
                    if (_userRoles.stream().anyMatch(j -> Objects.equals(i.getId(), j.getUserId()) && _communityStaffRoleId.equals(j.getRoleId()))) {
                        continue;
                    }

                    // 未授权则授权
                    _userRole = new UserRole();
                    _userRole.setUserId(i.getId());
                    _userRole.setRoleId(_communityStaffRoleId);
                    _userRole.setCreatorId(actorId);
                    _userRole.setCreateTime(new Date());

                    getCurrentSession().save(_userRole);

                    _relationCount += 1;

                    break;
            }
        }

        _count.data = new Pair<>(_itemsNew.size(), _relationCount);
        return _count;
    } */

    @Transactional(readOnly = true)
    public long registeredUserNum(String deptId) {
        List<String> _deptIds = departmentRepository.subordinates(deptId, null, null).stream()
                .map(i -> i.getId())
                .collect(Collectors.toList());

        return stream(DefaultUser.class)
                .where(i -> _deptIds.contains(i.getDeptId()) && i.getIsEnabled())
                .count();
    }

    @Transactional(readOnly = true)
    public List<Pair<Department, Integer>> registeredStatistic(String rootDeptId) throws InstantiationException, IllegalAccessException {
        // 根据根组织架构
        Result<DepartmentRepository.Organization<DefaultDepartment>> _organization = departmentRepository.getAsOrganization(rootDeptId);
        if (!_organization.isOK()) {
            return new ArrayList<>();
        }

        // 村居+级别则返回
        if (_organization.data.dept.getLevel() > 2) {
            return new ArrayList<>();
        }
        int _level = _organization.data.dept.getLevel() + 1;
        String _prefix = _organization.data.dept.getCode();

        // 读取人员id和对应的部门全称
        List<Pair<String, String>> _pairs = stream(DefaultUser.class)
                .where(i -> i.getIsEnabled())
                .leftOuterJoin((i, source) -> source.stream(Department.class), (i, j) -> j.getId().equals(i.getDeptId()))
                .where(i -> i.getTwo() != null && i.getTwo().getLevel() >= _level)
                .where(i -> i.getTwo() != null && i.getTwo().getCode().startsWith(_prefix))
                .select(i -> new Pair<>(i.getOne().getId(), i.getTwo().getFullName()))
                .toList();

        // 根据部门全称前缀分组
        Map<String, List<Pair<String, String>>> _map = _pairs.stream()
                .collect(Collectors.groupingBy(i -> {
                    List<String> _names = Arrays.stream(i.getTwo().split(Department.NAME_SEPARATOR)).collect(Collectors.toList());
                    if (_names.size() > _level) {
                        _names = _names.subList(0, _level);
                    }

                    return String.join(Department.NAME_SEPARATOR, _names);
                }));

        List<Pair<Department, Integer>> _counts = new ArrayList<>();
        for (Department i : _organization.data.subordinates) {
            int _count = _map.entrySet().stream()
                    .filter(j -> Objects.equals(j.getKey(), i.getFullName()))
                    .findFirst()
                    .map(j -> j.getValue().size())
                    .orElse(0);
            _counts.add(new Pair<>(i, _count));
        }

        return _counts;
    }

    @Transactional(readOnly = true)
    public List<DefaultUser> registeredUsers(String deptId) {
        List<String> _deptIds = departmentRepository.subordinates(deptId, null, null).stream()
                .map(i -> i.getId())
                .collect(Collectors.toList());

        return stream(DefaultUser.class)
                .where(i -> _deptIds.contains(i.getDeptId()) && i.getIsEnabled())
                .toList();
    }

    @Transactional(readOnly = true)
    public List<Pair<DefaultUser, DefaultDepartment>> registeredUsersWithDept(String deptId) {
        List<String> _deptIds = departmentRepository.subordinates(deptId, null, null)
                .stream()
                .filter(Department::getIsEnabled)
                .map(Department::getId)
                .collect(Collectors.toList());

        return stream(DefaultUser.class)
                .leftOuterJoin((i, source) -> source.stream(DefaultDepartment.class), (i, dept) -> i.getDeptId().equals(dept.getId()) && dept.getIsEnabled())
                .where(i -> _deptIds.contains(i.getOne().getDeptId()) && i.getOne().getIsEnabled())
                .toList();
    }

    @Transactional(readOnly = true)
    public long onlineUserNum(String deptId) {
        List<String> _deptIds = departmentRepository.subordinates(deptId, null, null).stream()
                .map(i -> i.getId())
                .collect(Collectors.toList());

        return stream(DefaultUser.class)
                .where(i -> _deptIds.contains(i.getDeptId()) && i.getIsEnabled() && i.getIsOnline())
                .count();
    }

    @Override
    public long onlineUserNum() {
        return this.stream(this.tClass)
                .where(User::getIsOnline)
                .where(User::getIsEnabled)
                .count();
    }

    @Transactional(readOnly = true)
    public List<Pair<Department, Integer>> onlineStatistic(String rootDeptId) throws InstantiationException, IllegalAccessException {
        // 根据根组织架构
        Result<DepartmentRepository.Organization<DefaultDepartment>> _organization = departmentRepository.getAsOrganization(rootDeptId);
        if (!_organization.isOK()) {
            return new ArrayList<>();
        }

        // 村居+级别则返回
        if (_organization.data.dept.getLevel() > 2) {
            return new ArrayList<>();
        }
        int _level = _organization.data.dept.getLevel() + 1;

        // 读取人员id和对应的部门全称
        List<Pair<String, String>> _pairs = stream(DefaultUser.class)
                .where(i -> i.getIsEnabled() && i.getIsOnline())
                .leftOuterJoin((i, source) -> source.stream(Department.class), (i, j) -> j.getId().equals(i.getDeptId()))
                .where(i -> i.getTwo() != null && i.getTwo().getLevel() >= _level)
                .select(i -> new Pair<>(i.getOne().getId(), i.getTwo().getFullName()))
                .toList();

        // 根据部门全称前缀分组
        Map<String, List<Pair<String, String>>> _map = _pairs.stream()
                .collect(Collectors.groupingBy(i -> {
                    List<String> _names = Arrays.stream(i.getTwo().split(Department.NAME_SEPARATOR)).collect(Collectors.toList());
                    if (_names.size() > _level) {
                        _names = _names.subList(0, _level);
                    }

                    return String.join(Department.NAME_SEPARATOR, _names);
                }));

        List<Pair<Department, Integer>> _counts = new ArrayList<>();
        for (Department i : _organization.data.subordinates) {
            int _count = _map.entrySet().stream()
                    .filter(j -> Objects.equals(j.getKey(), i.getFullName()))
                    .findFirst()
                    .map(j -> j.getValue().size())
                    .orElse(0);
            _counts.add(new Pair<>(i, _count));
        }

        return _counts;
    }

    @Transactional(readOnly = true)
    public List<DefaultUser> onlineUsers(String deptId) {
        return stream(DefaultUser.class)
                .where(i -> deptId.equals(i.getDeptId()) && i.getIsEnabled() && i.getIsOnline())
                .toList();
    }

    Result validatePassword(String password) {
        Result _success = new Result();

        String _password;
        try {
            _password = CryptoUtil.decryptRsa(password, rsaPrivateKey);
        } catch (Exception e) {
            _success.setCode(Result.ENUM_ERROR.P, 4, new Object[]{e.getMessage()});
            return _success;
        }

        if (!_password.matches(passwordConstraint)) {
            _success.setCode(Result.ENUM_ERROR.P, 3);
            return _success;
        } else {
            return _success;
        }
    }

}
