package com.chinamobile.sparrow.domain.repository;

import com.chinamobile.sparrow.domain.model.AssetEntity;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class AbstractAssetEntityRepository<T extends AssetEntity> extends AbstractEntityRepository<T> {

    protected final DepartmentRepository<DefaultDepartment> departmentRepository;
    protected final RoleRepository roleRepository;

    protected AbstractAssetEntityRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            Class<T> tClass,
            DepartmentRepository<DefaultDepartment> departmentRepository,
            RoleRepository roleRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass);
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
    }

    public JinqStream<T> stream(String regionCode, User user) {
        JinqStream<T> _query = stream(tClass);

        if (roleRepository.isUserInRole(user.getId(), DefaultRoleRepository.ADMIN_ROLE)) {
            _query = _query.where(i -> true);
        } else {
            String _deptCode = user.getDeptCode();
            String _pattern = user.getDeptCode() + Department.CODE_SEPARATOR;
            _query = _query.where(i -> _deptCode.equals(i.getRegionCode()) || i.getRegionCode().startsWith(_pattern));
        }

        if (StringUtils.hasLength(regionCode)) {
            String _pattern = regionCode + Department.CODE_SEPARATOR;
            _query = _query.where(i -> regionCode.equals(i.getRegionCode()) || i.getRegionCode().startsWith(_pattern));
        }

        return _query;
    }

    protected void parseRegion(List<T> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        List<String> _regionIds = items.stream()
                .filter(i -> StringUtils.hasLength(i.getRegionId()))
                .map(i -> i.getRegionId())
                .distinct()
                .collect(Collectors.toList());

        List<DefaultDepartment> _departments = departmentRepository.query(_regionIds, null, null, null, null);

        for (T i : items) {
            DefaultDepartment _department = _departments.stream()
                    .filter(j -> Objects.equals(i.getRegionId(), j.getId()))
                    .findFirst().orElse(null);
            if (_department != null) {
                i.setRegionName(_department.getName());
                i.setRegionFullName(_department.getFullName());
            }
        }
    }

}