package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.healthcode.model.grid.Geo;
import com.chinamobile.healthcode.repository.grid.GeoRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.DivisionCode;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class DefaultDepartmentRepository extends DepartmentRepository<DefaultDepartment> {

    public static final String ROOT_DEPARTMENT_NAME = "默认";

    // 村居级别
    public static final int RURAL_LEVEL = 3;

    // 网格级别
    public static final int GRID_LEVEL = 4;

    // 应急单元级别
    public static final int EU_LEVEL = 5;

    final RoleRepository roleRepository;
    final GeoRepository geoRepository;

    public DefaultDepartmentRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            UserRepository userRepository,
            RoleRepository roleRepository,
            GeoRepository geoRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, DefaultDepartment.class, userRepository);
        this.roleRepository = roleRepository;
        this.geoRepository = geoRepository;
    }

    @Transactional(readOnly = true)
    public Result<DefaultDepartment> getByFullName(String fullName) {
        Result<DefaultDepartment> _item = new Result<>();

        _item.data = stream(DefaultDepartment.class)
                .where(i -> fullName.equals(i.getFullName()) && i.getIsEnabled())
                .findFirst().orElse(null);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        }

        return _item;
    }


    @Transactional(readOnly = true)
    public Result<DefaultDepartment> getByDivisionCode(String divisionCode) {
        Result<DefaultDepartment> _item = new Result<>();

        String _pattern = "000000000000";
        String _divisionCode = divisionCode + _pattern.substring(divisionCode.length());

        _item.data = stream(DivisionCode.class)
                .where(i -> _divisionCode == i.getCode())
                .leftOuterJoin((i, session) -> session.stream(DefaultDepartment.class), (i, dept) -> i.getFullName().equals(dept.getFullName()))
                .where(i -> i.getTwo() != null)
                .select(i -> i.getTwo())
                .findFirst().orElse(null);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND);
        }

        return _item;
    }

    @Override
    public Result<String> save(DefaultDepartment item, String operatorId) throws InstantiationException, IllegalAccessException {
        Result<String> _id = new Result<>();

        boolean _isNew = false;

        Result<DefaultDepartment> _item = get(item.getId(), true);
        if (!_item.isOK()) {
            _item.data = tClass.newInstance();

            if (StringUtils.isEmpty(item.getSuperiorId())) {
                _item.data.setCode(_item.data.getId());
                _item.data.setFullName(item.getName());
                _item.data.setLevel(1);
            } else {
                Result<DefaultDepartment> _superior = get(item.getSuperiorId(), true);
                if (_superior.isOK()) {
                    _item.data.setSuperiorId(_superior.data.getId());
                    _item.data.setCode(_superior.data.getCode() + "." + _item.data.getId());
                    _item.data.setFullName(_superior.data.getFullName() + "/" + item.getName());
                    _item.data.setLevel(_superior.data.getLevel() + 1);
                }
            }

            _isNew = true;
        }

        String _identifier = _item.data.getId();
        String _name = item.getName();
        String _superiorId = item.getSuperiorId();

        if (stream(this.tClass)
                .where(i -> _identifier == null || !_identifier.equals(i.getId())).where(i -> _name == null || _name.equals(i.getName()))
                .where(i -> _superiorId == null || _superiorId.equals(i.getSuperiorId()))
                .where(i -> i.getIsEnabled())
                .findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        } else {
            if (!_isNew && !Objects.equals(_item.data.getName(), item.getName())) {
                int _index = _item.data.getLevel() - 1;

                subordinates(_item.data.getId(), null, null).forEach(i -> {
                    if (!Objects.equals(i.getId(), _item.data.getId())) {
                        List<String> _names = Arrays.asList(i.getFullName().split("/"));
                        if (_names.size() > _index) {
                            _names.set(_index, item.getName());
                        }

                        i.setFullName(String.join("/", _names));
                        this.update(i, operatorId);
                    }
                });
            }

            _item.data.setName(item.getName());
            _item.data.setSeq(item.getSeq() == null ? Integer.MAX_VALUE : item.getSeq());
            _item.data.setArea(item.getArea());
            _item.data.setDescription(item.getDescription());

            _id = (_isNew ? add(_item.data, operatorId) : update(_item.data, operatorId));

            if (StringUtils.hasLength(_item.data.getSuperiorId())) {
                Result<DefaultDepartment> _superior = get(_item.data.getSuperiorId(), true);
                if (_superior.isOK() && GRID_LEVEL == _superior.data.getLevel()) {
                    if (_isNew) {
                        Geo _geo = new Geo();
                        _geo.fromDepartment(_item.data);

                        geoRepository.add(_geo);
                    } else {
                        PagingItems<Geo> _geo = geoRepository.fuzzy(-1, -1, item.getId());
                        _geo.items.forEach(i -> {
                            i.fromDepartment(_item.data);
                            geoRepository.update(i);
                        });
                    }
                }
            }

            return _id;
        }
    }

    @Override
    public Result disable(String id, String operatorId) throws InstantiationException, IllegalAccessException {
        Result<DefaultDepartment> _item = get(id, true);

        Result _success = super.disable(id, operatorId);
        if (_success.isOK() && EU_LEVEL == _item.data.getLevel()) {
            geoRepository.remove(id);
        }

        return _success;
    }

    /**
     * 获取指定行政区域所有下属行政区域，包含指定的行政区域
     *
     * @param regionId 指定行政区域id
     * @param adminRoleName 相关超管角色
     * @param peerLv 指定层级
     * @param lowestLv 最低层级
     * @param user 用户信息
     * @return 下属行政区域对象
     */
    @Transactional(readOnly = true)
    public List<DefaultDepartment> subordinatesByUser(String regionId, String adminRoleName, Integer peerLv, Integer lowestLv, boolean includingRootRegion, User user) {
        String rootRegionId = getRootRegionIdByUser(regionId, adminRoleName, user);
        return subordinates(rootRegionId, peerLv, lowestLv).stream()
                // 解决框架subordinates未排除无效行政区域问题
                .filter(DefaultDepartment::getIsEnabled)
                .filter(d -> includingRootRegion || !Objects.equals(d.getId(), rootRegionId))
                .collect(Collectors.toList());
    }

    /**
     * 获取指定行政区域下一级行政区域全称
     *
     * @param regionId 指定行政区域id
     * @param includeRootRegion 是否包含根行政区域
     * @param user 用户信息
     * @return 下一级行政区域全称列表
     */
    @Transactional(readOnly = true)
    public List<String> subordinatesByUser(String regionId, boolean includeRootRegion, User user) {
        String _rootRegionId = getRootRegionIdByUser(regionId, null, user);

        List<DefaultDepartment> _subordinates = subordinates(_rootRegionId, null, null);

        List<String> _subordinateNames = new ArrayList<>();

        int _temp;
        if (StringUtils.hasLength(_rootRegionId)) {
            Department _root = _subordinates.stream()
                    .filter(i -> Objects.equals(i.getId(), _rootRegionId))
                    .findFirst().orElse(null);

            if (includeRootRegion) {
                _subordinateNames.add(_root.getFullName());
            }

            _temp = _root.getLevel() + 1;
        } else {
            _temp = 1;
        }

        int _level = _temp;

        _subordinateNames.addAll(_level == 1 ? Arrays.asList("金平区", "龙湖区", "濠江区", "澄海区", "潮阳区", "潮南区", "南澳县") : _subordinates.stream()
                .filter(i -> i.getLevel() == _level)
                .map(i -> i.getFullName())
                .collect(Collectors.toList()));
        return _subordinateNames;
    }

    /**
     * 根据用户角色与所属行政区域获取可访问的基础行政区域
     *
     * @param regionId 指定行政区域id
     * @param adminRole 相关超管角色
     * @param user 用户信息
     * @return 可访问的基础行政区域id
     */
    @Transactional(readOnly = true)
    public String getRootRegionIdByUser(String regionId, String adminRole, User user) {
        String _rootDeptId;

        if (!StringUtils.hasLength(adminRole)) {
            adminRole = DefaultRoleRepository.ADMIN_ROLE;
        }

        if (StringUtils.hasLength(regionId)) {
            if (roleRepository.isUserInRole(user.getId(), adminRole)) {
                _rootDeptId = regionId;
            } else {
                _rootDeptId = subordinates(user.getDeptId(), null, null).stream()
                        .anyMatch(i -> Objects.equals(regionId, i.getId())) ? regionId : user.getDeptId();
            }
        } else {
            _rootDeptId = roleRepository.isUserInRole(user.getId(), adminRole) ? null : user.getDeptId();
        }

        return _rootDeptId;
    }

}
