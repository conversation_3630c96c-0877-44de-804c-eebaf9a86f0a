package com.chinamobile.sparrow.domain.repository.sec;

import com.chinamobile.sparrow.domain.model.sec.Permission;
import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.model.sec.RolePermission;
import com.chinamobile.sparrow.domain.model.sec.UserRole;
import com.chinamobile.sparrow.domain.repository.sys.MenuRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.ClassUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.tuples.Pair;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.persistence.EntityManagerFactory;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

public class DefaultPermissionRepository extends PermissionRepository {

    final String redisPermissionKeyTemplate;
    final RedisTemplate<String, Object> redisTemplate;

    public DefaultPermissionRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            String redisPermissionKeyTemplate,
            UserRepository userRepository,
            MenuRepository menuRepository,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, userRepository, menuRepository);
        this.redisPermissionKeyTemplate = redisPermissionKeyTemplate;
        this.redisTemplate = redisTemplate;
    }

    @Transactional(readOnly = true)
    public Pair<Integer, Long> cacheUserPermissions() {
        Integer _num = 0;
        Long _count = 0L;

        List<Pair<Pair<Pair<UserRole, Role>, RolePermission>, Permission>> _pairs = stream(UserRole.class)
                .leftOuterJoin((i, session) -> session.stream(Role.class), (i, role) -> role.getId().equals(i.getRoleId()))
                .where(i -> i.getTwo() != null)
                .leftOuterJoin((i, session) -> session.stream(RolePermission.class), (i, relation) -> relation.getRoleId().equals(i.getTwo().getId()))
                .where(i -> i.getTwo() != null)
                .leftOuterJoin((i, session) -> session.stream(Permission.class), (i, permission) -> permission.getId().equals(i.getTwo().getPermissionId()))
                .where(i -> i.getTwo() != null)
                .toList();
        Map<String, List<Pair<Pair<Pair<UserRole, Role>, RolePermission>, Permission>>> _groups = _pairs.stream()
                .collect(Collectors.groupingBy(i -> i.getOne().getOne().getOne().getUserId()));
        for (String i : _groups.keySet()) {
            _num += 1;

            String _key = String.format(redisPermissionKeyTemplate, i);
            redisTemplate.delete(_key);

            List<String> _permissions = _groups.get(i).stream()
                    .filter(j -> j.getTwo() != null)
                    .map(j -> j.getTwo().getName())
                    .distinct()
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(_permissions)) {
                _count += redisTemplate.opsForList().leftPushAll(_key, _permissions.toArray());
            }
        }

        return new Pair<>(_num, _count);
    }

    @SuppressWarnings("all")
    @Override
    public Map<Class, String> readControllerPathMappings() throws NoSuchFieldException, IllegalAccessException {
        Map<Class, String> _map = new ConcurrentHashMap<>();
        Field _field = ClassLoader.class.getDeclaredField("classes");
        _field.setAccessible(true);
        CopyOnWriteArrayList<Class<?>> classList = new CopyOnWriteArrayList<>((Vector<Class<?>>)_field.get(ClassUtil.class.getClassLoader()));
        classList.forEach((i) -> {
            try {
                if (i.getAnnotation(Controller.class) == null && i.getAnnotation(RestController.class) == null) {
                    return;
                }

                String _path = Optional.ofNullable(i.getAnnotation(RequestMapping.class)).map(j ->
                    j.value().length == 0 ? "" : j.value()[0]
                ).orElse("");
                _map.put(i, StringUtils.hasLength(_path) ? "/" + _path : _path);
            } catch (Exception e) {
                this.logger.debug(String.format("读取Controller/RequestMapping注解类，无法解析%s类", i.getName()), e);
            }

        });
        return _map;
    }
}