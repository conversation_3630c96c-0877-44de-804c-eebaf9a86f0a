package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.Bulletin;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

public class BulletinRepository extends AbstractEntityRepository<Bulletin> {

    final AbstractMediaRepository mediaRepository;

    public BulletinRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            AbstractMediaRepository mediaRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Bulletin.class);
        this.mediaRepository = mediaRepository;
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<Bulletin> sort(JinqStream<Bulletin> query, BiFunction<JinqStream<Bulletin>, JinqStream.CollectComparable<Bulletin, V>, JinqStream<Bulletin>> compare, String field) {
        switch (field) {
            case "title":
                return compare.apply(query, i -> (V) i.getTitle());
            case "seq":
                return compare.apply(query, i -> (V) i.getSeq());
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            default:
                return query;
        }
    }

    @Transactional(readOnly = true)
    public Result<Bulletin> get(String id) {
        Result<Bulletin> _item = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Bulletin.class.getSimpleName()});
            return _item;
        }

        _item.data = getCurrentSession().get(Bulletin.class, id);
        if (_item.data == null) {
            _item.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Bulletin.class.getSimpleName()});
        } else {
            parseAttachments(Collections.singletonList(_item.data), false);
        }

        return _item;
    }

    @Transactional(readOnly = true)
    public PagingItems<Bulletin> fuzzy(int count, int index, List<SortField> fields, String title, Boolean isEnabled) {
        JinqStream<Bulletin> _query = stream(Bulletin.class);

        if (StringUtils.hasLength(title)) {
            _query = _query.where(i -> i.getTitle() != null && i.getTitle().contains(title));
        }

        if (isEnabled != null) {
            _query = _query.where(i -> isEnabled == i.getIsEnabled());
        }

        if (CollectionUtils.isEmpty(fields)) {
            _query = _query.sortedDescendingBy(i -> i.getCreateTime())
                    .sortedDescendingBy(i -> i.getSeq());
        } else {
            _query = sort(_query, fields);
        }

        PagingItems<Bulletin> _page = new PagingItems<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip(count * index).limit(count);
        }
        _page.items = _query.toList();

        parseAttachments(_page.items, false);

        return _page;
    }

    public Result<String> save(Bulletin item, String userId) {
        Result<String> _id = new Result<>();

        boolean _isNew = false;

        Result<Bulletin> _item = get(item.getId());

        List<String> _ids = new ArrayList<>();
        if (_item.isOK()) {
            _ids = StringUtils.hasLength(_item.data.getAttachmentIdsJSON()) ? ConverterUtil.json2Object(_item.data.getAttachmentIdsJSON(), new TypeToken<List<String>>() {
            }.getType()) : new ArrayList<>();

            if (!CollectionUtils.isEmpty(item.getAttachmentIds())) {
                _ids.removeAll(item.getAttachmentIds());
            }
        } else {
            _item.data = new Bulletin();

            _isNew = true;
        }

        copyProperties(item, _item.data, new String[]{"id", "attachmentIdsJSON"});

        _item.data.setAttachmentIdsJSON(CollectionUtils.isEmpty(item.getAttachmentIds()) ? null : ConverterUtil.toJson(item.getAttachmentIds()));

        Result _success = _isNew ? add(_item.data, userId) : update(_item.data, userId);
        if (_success.isOK()) {
            // 删除旧文件
            for (String i : _ids) {
                mediaRepository.remove(i, true, userId);
            }
        } else {
            return _id.pack(_success);
        }

        _id.data = _item.data.getId();
        return _id;
    }

    public Result remove(String id, String userId) {
        Result _success = new Result();

        Result<Bulletin> _item = get(id);
        if (!_item.isOK()) {
            return _success.pack(_item);
        }

        // 删除附件
        if (!CollectionUtils.isEmpty(_item.data.getAttachmentIds())) {
            for (String i : _item.data.getAttachmentIds()) {
                mediaRepository.remove(i, true, userId);
            }
        }

        getCurrentSession().remove(_item.data);

        return _success;
    }

    void parseAttachments(List<Bulletin> items, boolean brief) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        for (Bulletin i : items) {
            i.setAttachmentIds(StringUtils.hasLength(i.getAttachmentIdsJSON()) ? ConverterUtil.json2Object(i.getAttachmentIdsJSON(), new TypeToken<List<String>>() {
            }.getType()) : null);
        }

        if (!brief) {
            List<String> _ids = items.stream()
                    .filter(i -> i.getAttachmentIds() != null)
                    .flatMap(i -> i.getAttachmentIds().stream())
                    .collect(Collectors.toList());

            List<Media> _medias = mediaRepository.query(_ids, null);

            for (Bulletin i : items) {
                i.setAttachments(CollectionUtils.isEmpty(i.getAttachmentIds()) ? null : _medias.stream()
                        .filter(j -> i.getAttachmentIds().contains(j.getId()))
                        .collect(Collectors.toList()));
            }
        }
    }

}