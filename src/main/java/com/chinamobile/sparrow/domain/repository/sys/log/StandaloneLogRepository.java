package com.chinamobile.sparrow.domain.repository.sys.log;

import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.jinq.jpa.JinqJPAStreamProvider;

import javax.persistence.EntityManagerFactory;

public class StandaloneLogRepository extends LogRepository {

    public StandaloneLogRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            UserRepository userRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, userRepository);
    }

}