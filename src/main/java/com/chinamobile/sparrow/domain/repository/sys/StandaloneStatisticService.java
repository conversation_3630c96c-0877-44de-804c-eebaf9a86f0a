package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.healthcode.model.stats.DailyStatsReport;
import com.chinamobile.healthcode.repository.citizen.ProfileRepository;
import com.chinamobile.healthcode.repository.project.RecordRepository;
import com.chinamobile.sparrow.domain.infra.log.AbstractLog;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.*;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.POIUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManagerFactory;

import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class StandaloneStatisticService extends StatisticService {

    final DefaultDepartmentRepository departmentRepository;
    final RoleRepository roleRepository;
    final DictionaryRepository dictionaryRepository;

    public StandaloneStatisticService(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            UserRepository userRepository,
            RoleRepository roleRepository,
            DictionaryRepository dictionaryRepository,
            DefaultDepartmentRepository departmentRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, userRepository);
        this.roleRepository = roleRepository;
        this.dictionaryRepository = dictionaryRepository;
        this.departmentRepository = departmentRepository;
    }

    public long mpu(User user) {
        String _rootDeptId;

        Date _end = DateUtil.addDays(new Date(), 1);
        _end = DateUtil.getDate(_end);
        Date _begin = DateUtil.addMonths(_end, -1);

        JinqStream<InfoLog> _query = streamInfoLogs(_begin, _end, null);

        List<String> _excludedAccountIdList = this.dictionaryRepository.fuzzy("排除名单", "统计账号", true, null)
                .stream()
                .map(Dictionary::getVal)
                .collect(Collectors.toList());
        if (((DefaultRoleRepository) roleRepository).isUserInRoleCached(user.getId(), ProfileRepository.ADMIN_ROLE)
                || ((DefaultRoleRepository) roleRepository).isUserInRoleCached(user.getId(), RecordRepository.ADMIN_ROLE)
                || user.getDeptName().equals(DefaultDepartmentRepository.ROOT_DEPARTMENT_NAME)) {
            _rootDeptId = null;
        } else {
            _rootDeptId = user.getDeptId();
        }
        List<String> _actorIds = ((DefaultUserRepository) userRepository).registeredUsers(_rootDeptId)
                .stream()
                .map(User::getId)
                .filter(id -> !_excludedAccountIdList.contains(id))
                .collect(Collectors.toList());

        long _registeredUserNum = _actorIds.size();
        _query = _query.where(i -> _actorIds.contains(i.getActorId()));
        long _activeUserCount = _query.select(AbstractLog::getActorId).distinct().count();

        return _registeredUserNum - _activeUserCount;
    }

    public PagingItems<Tuple3<String, String, String>> mpuDetails(int count, int index, String regionId, Date recordStartDate, Date recordEndDate, User user) {
        List<Tuple3<String, String, String>> _inactiveUserList;

        if (recordStartDate == null && recordEndDate == null) {
            recordEndDate = DateUtil.addDays(new Date(), 1);
            recordEndDate = DateUtil.getDate(recordEndDate);
            recordStartDate = DateUtil.addMonths(new Date(), -1);
        } else if (recordStartDate == null) {
            recordEndDate = DateUtil.addDays(recordEndDate, 1);
            recordStartDate = DateUtil.addMonths(recordEndDate, -1);
        } else if (recordEndDate == null) {
            recordEndDate = DateUtil.addMonths(recordStartDate, 1);
        } else {
            recordEndDate = DateUtil.addDays(recordEndDate, 1);
        }

        String _rootDeptId = null;

        PagingItems<Tuple3<String, String, String>> _page = new PagingItems<>(count, index);

        JinqStream<InfoLog> _query = streamInfoLogs(recordStartDate, recordEndDate, null);

        // 排除名单
        List<String> _excludedAccountIdList = this.dictionaryRepository.fuzzy("排除名单", "统计账号", true, null)
                .stream()
                .map(Dictionary::getVal)
                .collect(Collectors.toList());

        // 权限处理
        if (((DefaultRoleRepository) roleRepository).isUserInRoleCached(user.getId(), ProfileRepository.ADMIN_ROLE)
                || ((DefaultRoleRepository) roleRepository).isUserInRoleCached(user.getId(), RecordRepository.ADMIN_ROLE)
                || user.getDeptFullName().equals(DefaultDepartmentRepository.ROOT_DEPARTMENT_NAME)) {
            _rootDeptId = regionId;
        } else {
            _rootDeptId = departmentRepository.subordinates(user.getDeptId(), null, null).stream()
                    .anyMatch(i -> Objects.equals(regionId, ((Department) i).getId())) ? regionId : user.getDeptId();
        }

        // 简单区分账号层级，只有市、区、街道层级获取数据
        if (user.getDeptCode().chars().filter(c -> c == '.').count() > 1) {
            _inactiveUserList = Collections.emptyList();
        } else {
            // 月活跃账户id列表
            List<String> _activeUserIdList = _query.select(AbstractLog::getActorId)
                    .distinct()
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            // 注册账户关联部门列表
            List<Pair<DefaultUser, DefaultDepartment>> _registedUserList = ((DefaultUserRepository)this.userRepository).registeredUsersWithDept(_rootDeptId);
            // 月未登录账户列表
            _inactiveUserList = _registedUserList.stream()
                    .filter(p -> !_excludedAccountIdList.contains((p.getOne()).getId()))
                    .filter(p -> !_activeUserIdList.contains((p.getOne()).getId()))
                    .sorted(Comparator.<Pair<DefaultUser, DefaultDepartment>>comparingInt(p -> p.getTwo().getLevel())
                            .thenComparingInt(p -> p.getTwo().getSeq())
                            .thenComparing(p -> p.getTwo().getFullName()))
                    .map(p -> new Tuple3<>(p.getOne().getAccount(), p.getOne().getName(), p.getTwo().getFullName()))
                    .collect(Collectors.toList());
        }

        // 分页处理
        _page.total = _inactiveUserList.size();
        if (count >= 0 && index >= 0) {
            _page.items = _inactiveUserList.stream().skip((long) index * count).limit(count).collect(Collectors.toList());
        } else {
            _page.items = _inactiveUserList;
        }

        return _page;
    }

    public PagingItems<Tuple3<String, String, String>> mpuDetails(int count, int index, User user) {
        return mpuDetails(count, index, null, null, null, user);
    }

    @Transactional(readOnly = true)
    public String mpuDetailsExport(String rootDepartmentId, Date recordStartDate, Date recordEndDate, User user) throws IOException {
        List<Tuple3<String, String, String>> _items = mpuDetails(-1, -1, rootDepartmentId, recordStartDate, recordEndDate, user).items;

        return POIUtil.exportToWorkbookBase64String((book) -> {
            Sheet _sheet = book.createSheet();

            // 设置标题行
            Row _row = _sheet.createRow(0);
            _row.createCell(0).setCellValue("行政区域");
            _row.createCell(1).setCellValue("账号名");
            _row.createCell(2).setCellValue("姓名");

            for (int i = 0; i < _items.size(); i++) {
                _row = _sheet.createRow(i + 1);
                _row.createCell(0).setCellValue(_items.get(i).getOne());
                _row.createCell(1).setCellValue(_items.get(i).getTwo());
                _row.createCell(2).setCellValue(_items.get(i).getThree());
            }
        }, "xlsx");
    }

}
