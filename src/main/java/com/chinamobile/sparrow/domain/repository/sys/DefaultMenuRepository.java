package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.model.sys.Menu;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import org.jinq.jpa.JinqJPAStreamProvider;

import javax.persistence.EntityManagerFactory;
import java.util.List;

public class DefaultMenuRepository extends ReflectedMenuRepository {

    public DefaultMenuRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            PermissionRepository permissionRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, permissionRepository);
    }

    @Override
    protected void customize(List<Menu> items) {
        Menu _item = items.stream()
                .filter(i -> "个人信息收集".equals(i.getTitle()) && i.getLevel() == 1)
                .findFirst().orElse(null);
        String _id = _item == null ? add("个人信息收集", null, null, 1, 1, true) : _item.getId();

        if (!items.stream().anyMatch(i -> "网格管理".equals(i.getTitle()) && _id.equals(i.getParentId()))) {
            add("网格管理", _id, "mdi-grid", 2, 2, true);
        }

        String _zzMenuId = items.stream()
                .filter(i -> "综治专题管理".equals(i.getTitle()) && i.getLevel() == 1)
                .map(Menu::getId)
                .findAny()
                .orElseGet(() ->
                        add("综治专题管理", null, null, 1, 2, true)
                );
        if (items.stream().noneMatch(i -> "综治数据统计".equals(i.getTitle()) && i.getLevel() == 2 && i.getParentId().equals(_zzMenuId))) {
            add("综治数据统计", _zzMenuId, "mdi-chart-tree", 2, 7, true);
        }

        if (!items.stream().anyMatch(i -> "综治中心工作管理".equals(i.getTitle()) && i.getLevel() == 1)) {
            add("综治中心工作管理", null, null, 1, 3, true);
        }

        if (items.stream().noneMatch(i -> "村（社）专项管理".equals(i.getTitle()) && i.getLevel() == 1)) {
            add("村（社）专项管理", null, null, 1, 4, true);
        }
        String _parentId = items.stream()
                .filter(i -> "村（社）专项管理".equals(i.getTitle()) && i.getLevel() == 1)
                .map(Menu::getId)
                .findFirst().orElse(null);
        if (_parentId != null && items.stream().noneMatch(i -> "重点事件情况".equals(i.getTitle()) && i.getLevel() == 2 && i.getParentId().equals(_parentId))) {
            add("重点事件情况", _parentId, "mdi-calendar-star", 2, 5, true);
        }

        _item = items.stream()
                .filter(i -> "防疫管理".equals(i.getTitle()) && i.getLevel() == 1)
                .findFirst().orElse(null);
        String _id2 = _item == null ? add("防疫管理", null, null, 1, 3, true) : _item.getId();
        if (!items.stream().anyMatch(i -> "金凤码".equals(i.getTitle()) && _id2.equals(i.getParentId()))) {
            add("金凤码", _id2, "mdi-city", 2, 1, true);
        }

        if (!items.stream().anyMatch(i -> "抵（返）汕人员报备".equals(i.getTitle()) && _id2.equals(i.getParentId()))) {
            add("抵（返）汕人员报备", _id2, "mdi-train", 2, 2, true);
        }

        if (!items.stream().anyMatch(i -> "货运司机报备".equals(i.getTitle()) && _id2.equals(i.getParentId()))) {
            add("货运司机报备", _id2, "mdi-truck", 2, 3, true);
        }

        super.customize(items);
    }

    @Override
    protected void updateBuildIn(List<Menu> menus) {
        update(menus, "/citizen/profile", "个人信息收集", "个人信息管理", "mdi-account-multiple", 2, 1, true, true);

        update(menus, "/citizen/grid/board", "网格管理", "在线绘图", "mdi-map-legend", 3, 1, true, true);
        update(menus, "/citizen/grid/survey", "网格管理", "人员入格统计", "mdi-view-grid-plus-outline", 3, 2, true, true);
        update(menus, "/emergency/unit/index", "网格管理", "应急单元负责人管理", "mdi-account-alert", 3, 4, true, true);

        update(menus, "/project/record", "综治专题管理", "专项工作管理", "mdi-archive-outline", 2, 1, true, true);

        update(menus, "/subject/unit", "综治专题管理", "单位组织管理", "mdi-bank-outline", 2, 2, true, true);
        update(menus, "/subject/person", "综治专题管理", "重点人员管理", "mdi-target-account", 2, 3, true, true);
        update(menus, "/subject/building", "综治专题管理", "实有楼栋管理", "mdi-office-building-marker-outline", 2, 4, true, true);
        update(menus, "/subject/event", "综治专题管理", "重点事件管理", "mdi-archive-marker-outline", 2, 5, true, true);
        update(menus, "/subject/property", "综治专题管理", "重点物品管理", "mdi-cctv", 2, 6, true, true);
        update(menus, "/subject/place", "综治专题管理", "重点场所管理", "mdi-home-account", 2, 7, true, true);
        update(menus, "/subject/survey", "综治数据统计", "五要素查询表", "mdi-chart-tree", 3, 1, true, true);
        update(menus, "/subject/survey/daily-report", "综治数据统计", "数据日报表", "mdi-file-chart-outline", 3, 2, true, true);
        update(menus, "/subject/survey/routine-inspection", "综治数据统计", "日常巡查表", "mdi-chart-box-plus-outline", 3, 3, true, true);
        update(menus, "/sys/stat/inactive-accounts", "综治数据统计", "未登录账号表", "mdi-account-arrow-down", 3, 4, true, true);

        update(menus, "/cmc/masses-visit", "综治中心工作管理", "群众来访管理", "mdi-scale-balance", 2, 1, true, true);
        update(menus, "/cmc/counseling", "综治中心工作管理", "心理服务室接访管理", "mdi-charity", 2, 2, true, true);
        update(menus, "/cmc/cadre-training", "综治中心工作管理", "领导下沉管理", "mdi-account-supervisor", 2, 3, true, true);

        update(menus, "/instability/basic-info", "村（社）专项管理", "村（社）基本情况", "mdi-information-outline", 2, 1, true, true);
        update(menus, "/instability/place-info", "村（社）专项管理", "重点场所情况", "mdi-fireplace-off", 2, 2, true, true);
        update(menus, "/instability/personnel-info", "村（社）专项管理", "重点人员情况", "mdi-account-star-outline", 2, 3, true, true);
        update(menus, "/instability/organization-info", "村（社）专项管理", "民间组织及活动情况", "mdi-account-multiple-check", 2, 4, true, true);
        update(menus, "/instability/event-crime-info", "重点事件情况", "2021年以来发生刑事案件情况", "mdi-calendar-star", 3, 1, true, true);
        update(menus, "/instability/event-petition-info", "重点事件情况", "2021年以来到市、省、京信访情况", "mdi-calendar-star", 3, 2, true, true);
        update(menus, "/instability/event-online-opinion-info", "重点事件情况", "2021年以来网络舆情及网络重点人情况", "mdi-calendar-star", 3, 3, true, true);
        update(menus, "/instability/event-crowd-gathering-info", "重点事件情况", "2021年以来群众在村委聚集情况", "mdi-calendar-star", 3, 4, true, true);
        update(menus, "/instability/event-committees-punishment-info", "重点事件情况", "2021年以来“两委”干部涉治安、刑事处罚情况", "mdi-calendar-star", 3, 5, true, true);
        update(menus, "/instability/event-committees-disciplinary-info", "重点事件情况", "2021年换届以来“两委干部受党纪政纪处分情况", "mdi-calendar-star", 3, 6, true, true);
        update(menus, "/instability/event-regional-crime-info", "重点事件情况", "地域性犯罪案件情况", "mdi-calendar-star", 3, 7, true, true);
        update(menus, "/instability/event-issues-challenges-info", "重点事件情况", "群众集中反映问题及基层治理难点、痛点", "mdi-calendar-star", 3, 8, true, true);
        update(menus, "/instability/event-other-info", "重点事件情况", "其他情况", "mdi-calendar-star", 3, 9, true, true);
        update(menus, "/instability/statistics", "村（社）专项管理", "统计汇总", "mdi-file-document-outline", 2, 6, true, true);
        update(menus, "/instability/comparison", "村（社）专项管理", "数据量对比", "mdi-compare-horizontal", 2, 7, true, true);

        update(menus, "/citizen/form", "金凤码", "打卡管理", "mdi-location-enter", 3, 1, true, true);
        update(menus, "/citizen/vaccine", "金凤码", "新冠疫苗信息管理", "mdi-needle", 3, 2, true, true);
        update(menus, "/citizen/grids", "金凤码", "虚拟网格管理", "mdi-dots-grid", 3, 3, true, true);

        update(menus, "/migrant/visit-form", "抵（返）汕人员报备", "报备单", "mdi-timeline-text-outline", 3, 1, true, true);

        update(menus, "/truck/ticket", "货运司机报备", "报备单", "mdi-timeline-text-outline", 3, 1, true, true);
        update(menus, "/truck/ticket/passed", "货运司机报备", "现场确认查询", "mdi-timeline-check-outline", 3, 2, true, true);

        update(menus, "/sys/bulletin", "系统管理", "公告管理", "mdi-bulletin-board", 2, 4, true, true);

        super.updateBuildIn(menus);
    }

}
