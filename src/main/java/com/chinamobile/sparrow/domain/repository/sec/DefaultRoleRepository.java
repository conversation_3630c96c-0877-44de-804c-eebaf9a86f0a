package com.chinamobile.sparrow.domain.repository.sec;

import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.model.sec.UserRole;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.tuples.Pair;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class DefaultRoleRepository extends RoleRepository {

    public static final String ADMIN_ROLE = "市管理员";

    final String redisRoleKeyTemplate;
    final RedisTemplate<String, Object> redisTemplate;

    public DefaultRoleRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            String redisRoleKeyTemplate,
            UserRepository userRepository,
            PermissionRepository permissionRepository,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, userRepository, permissionRepository);
        this.redisRoleKeyTemplate = redisRoleKeyTemplate;
        this.redisTemplate = redisTemplate;
    }

    @Transactional(readOnly = true)
    public Pair<Integer, Long> cacheUserRoles() {
        Integer _num = 0;
        Long _count = 0L;

        List<Pair<UserRole, Role>> _pairs = stream(UserRole.class)
                .leftOuterJoin((i, session) -> session.stream(Role.class), (i, role) -> role.getId().equals(i.getRoleId())).toList();
        Map<String, List<Pair<UserRole, Role>>> _groups = _pairs.stream()
                .collect(Collectors.groupingBy(i -> i.getOne().getUserId()));
        for (String i : _groups.keySet()) {
            _num += 1;

            String _key = String.format(redisRoleKeyTemplate, i);
            redisTemplate.delete(_key);

            List<String> _roles = _groups.get(i).stream()
                    .filter(j -> j.getTwo() != null)
                    .map(j -> j.getTwo().getName())
                    .distinct()
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(_roles)) {
                _count += redisTemplate.opsForList().leftPushAll(_key, _roles.toArray());
            }
        }

        return new Pair<>(_num, _count);
    }

    public boolean isUserInRoleCached(String userId, String roleName) {
        String _key = String.format(redisRoleKeyTemplate, userId);
        return redisTemplate.hasKey(_key) && redisTemplate.opsForList().range(_key, 0, -1).stream()
                .anyMatch(i -> i.toString().equals(roleName));
    }

}