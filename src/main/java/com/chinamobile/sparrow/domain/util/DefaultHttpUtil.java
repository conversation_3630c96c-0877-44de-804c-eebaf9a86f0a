package com.chinamobile.sparrow.domain.util;

import com.chinamobile.sparrow.domain.infra.code.Result;
import okhttp3.ConnectionPool;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.http.HttpHeaders;
import org.springframework.util.FileCopyUtils;

import java.io.File;
import java.io.IOException;
import java.net.Proxy;
import java.util.Optional;

public class DefaultHttpUtil extends HttpUtil {

    public DefaultHttpUtil(
            ConnectionPool connectionPool,
            Proxy proxy
    ) {
        super(connectionPool, proxy);
    }

    public DefaultHttpUtil(
            ConnectionPool connectionPool,
            CookieJarImpl cookieJar,
            Proxy proxy
    ) {
        super(connectionPool, cookieJar, proxy);
    }

    public Result<File> downloadFile(String url, String fileName) throws IOException {
        Result<File> _file = new Result<>();

        Request _request = new Request.Builder().url(url).build();

        Response _response = client.newCall(_request).execute();
        if (_response.isSuccessful()) {
            try (ResponseBody body = _response.body()) {
                byte[] _bytes = body.bytes();

                String _fileName = fileName;

                String[] _name = _fileName.split("\\.");
                if (_name.length != 2) {
                    String _contentType = Optional.ofNullable(_response.header(HttpHeaders.CONTENT_TYPE)).orElse("");
                    if (_contentType.startsWith("image/")) {
                        String[] _parts = _contentType.split("/");
                        _fileName = _name[0] + "." + (_parts.length > 1 ? _parts[1] : "jpg");
                    } else {
                        _fileName = _name[0] + ".jpg";
                    }
                }

                _name = _fileName.split("\\.");
                File _temp = File.createTempFile(_name[0], "." + _name[1]);

                FileCopyUtils.copy(_bytes, _temp);

                _file.data = _temp;
            }
        } else {
            _file.setCode(String.valueOf(_response.code()));
            _file.message = _response.message();
        }

        return _file;
    }

}