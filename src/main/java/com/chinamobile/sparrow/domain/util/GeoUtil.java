package com.chinamobile.sparrow.domain.util;

import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class GeoUtil extends GpsUtil {

    private static final GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory(null);
    private static final WKTReader wktReader = new WKTReader(geometryFactory);

    public static boolean contains(Point point, Point[] polygon) throws ParseException {
        String _template = "POINT (%s %s)";
        Geometry _point = wktReader.read(String.format(_template, point.getLongitude(), point.getLatitude()));

        List<String> _temp = new ArrayList<>();
        for (Point i : polygon) {
            _temp.add(i.getLongitude() + " " + i.getLatitude());
        }
        if (!CollectionUtils.isEmpty(_temp) && !_temp.get(0).equals(_temp.get(_temp.size() -1))) {
            _temp.add(_temp.get(0));
        }

        _template = "POLYGON ((%s))";
        Geometry _polygon = wktReader.read(String.format(_template, String.join(",", _temp)));

        return _polygon.contains(_point);
    }

    public static class Point {

        Double longitude;
        Double latitude;

        public Point(Double longitude, Double latitude) {
            this.longitude = longitude;
            this.latitude = latitude;
        }

        public Double getLongitude() {
            return longitude;
        }

        public void setLongitude(Double longitude) {
            this.longitude = longitude;
        }

        public Double getLatitude() {
            return latitude;
        }

        public void setLatitude(Double latitude) {
            this.latitude = latitude;
        }

    }

}
