package com.chinamobile.sparrow.domain.util;

import com.google.zxing.LuminanceSource;

import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;

public class BufferedImageLuminanceSource extends LuminanceSource {

    final BufferedImage image;
    final int left;
    final int top;

    public BufferedImageLuminanceSource(BufferedImage image) {
        this(image, 0, 0, image.getWidth(), image.getHeight());
    }

    public BufferedImageLuminanceSource(BufferedImage image, int left, int top, int width, int height) {
        super(width, height);
        int _sourceWidth = image.getWidth(), _sourceHeight = image.getHeight();
        if (left + width > _sourceWidth || top + height > _sourceHeight) {
            throw new IllegalArgumentException("Crop rectangle does not fit within image data");
        }

        for (int y = top; y < top + height; y++) {
            for (int x = left; x < left + width; x++) {
                if ((image.getRGB(x, y) & 0xFF000000) == 0) {
                    image.setRGB(x, y, 0xFFFFFFFF);
                }
            }
        }

        this.image = new BufferedImage(_sourceWidth, _sourceHeight, BufferedImage.TYPE_BYTE_GRAY);
        this.image.getGraphics().drawImage(image, 0, 0, null);
        this.left = left;
        this.top = top;
    }


    @Override
    public byte[] getRow(int y, byte[] row) {
        if (y < 0 || y >= getHeight()) {
            throw new IllegalArgumentException("Requested row is outside the image: " + y);
        }

        int _width = getWidth();
        if (row == null || row.length < _width) {
            row = new byte[_width];
        }

        image.getRaster().getDataElements(left, top + y, _width, 1, row);
        return row;
    }

    @Override
    public byte[] getMatrix() {
        int _width = getWidth();
        int _height = getHeight();
        int _area = _width * _height;
        byte[] _matrix = new byte[_area];

        image.getRaster().getDataElements(left, top, _width, _height, _matrix);

        return _matrix;
    }

    @Override
    public boolean isCropSupported() {
        return true;
    }

    @Override
    public LuminanceSource crop(int left, int top, int width, int height) {
        return new BufferedImageLuminanceSource(image, this.left + left, this.top + top, width, height);
    }

    @Override
    public boolean isRotateSupported() {
        return true;
    }

    @Override
    public LuminanceSource rotateCounterClockwise() {
        int _sourceWidth = image.getWidth(), _sourceHeight = image.getHeight();

        BufferedImage _rotatedImage = new BufferedImage(_sourceHeight, _sourceWidth, BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D _graphics = _rotatedImage.createGraphics();

        AffineTransform _transform = new AffineTransform(0.0, -1.0, 1.0, 0.0, 0.0, _sourceWidth);
        _graphics.drawImage(image, _transform, null);
        _graphics.dispose();

        int _width = getWidth();
        return new BufferedImageLuminanceSource(_rotatedImage, top, _sourceWidth - (left + _width), getHeight(), _width);
    }

}