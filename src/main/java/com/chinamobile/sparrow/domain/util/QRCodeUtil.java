package com.chinamobile.sparrow.domain.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.Hashtable;

public class QRCodeUtil {

    static final String CHARSET = "utf-8";

    public static BufferedImage createImage(String content, int qrCodeWidth, int qrCodeHeight, int margin) throws Exception {
        Hashtable<EncodeHintType, Object> _hints = new Hashtable<>();
        _hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        _hints.put(EncodeHintType.CHARACTER_SET, CHARSET);
        _hints.put(EncodeHintType.MARGIN, 1);

        BitMatrix _matrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, qrCodeWidth, qrCodeHeight, _hints);
        int _width = _matrix.getWidth();
        int _height = _matrix.getHeight();
        BufferedImage _image = new BufferedImage(_width + 2 * margin, _height + 2 * margin, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < _width + 2 * margin; x++) {
            for (int y = 0; y < _height + 2 * margin; y++) {
                if (x < margin || x >= _width + margin || y < margin || y >= _height + margin) {
                    _image.setRGB(x, y, 0xFFFFFFFF);
                } else {
                    _image.setRGB(x, y, _matrix.get(x - margin, y - margin) ? 0xFF000000 : 0xFFFFFFFF);
                }
            }
        }

        return _image;
    }

    public static void pressImage(BufferedImage image, File logo, int width, int height) throws Exception {
        if (!logo.exists()) {
            return;
        }

        Image _logo = ImageIO.read(logo);
        int _width = _logo.getWidth(null);
        int _height = _logo.getHeight(null);

        // 压缩LOGO
        if (width > 0 && height > 0) {
            if (_width > width) {
                _width = width;
            }

            if (_height > height) {
                _height = height;
            }

            Image _compressed = _logo.getScaledInstance(_width, _height, Image.SCALE_SMOOTH);

            Graphics _graphics = new BufferedImage(_width, _height, BufferedImage.TYPE_INT_RGB).getGraphics();
            _graphics.drawImage(_compressed, 0, 0, null);
            _graphics.dispose();
            _logo = _compressed;
        }

        // 插入LOGO
        Graphics2D _graphics = image.createGraphics();
        int _x = (image.getWidth() - _width) / 2;
        int _y = (image.getHeight() - _height) / 2;
        _graphics.drawImage(_logo, _x, _y, _width, _height, null);

        Shape _shape = new RoundRectangle2D.Float(_x, _y, _width, _width, 6, 6);
        _graphics.setStroke(new BasicStroke(3f));
        _graphics.draw(_shape);
        _graphics.dispose();
    }

    public static void pressText(BufferedImage image, String text, Color color, Font font) throws UnsupportedEncodingException {
        String _text = new String(text.getBytes(), CHARSET);

        Graphics2D _graphics = image.createGraphics();
        _graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        _graphics.setBackground(Color.WHITE);
        _graphics.setPaint(color);
        _graphics.setFont(font);

        FontMetrics _metrics = _graphics.getFontMetrics(font);

        // 文字在图片中的坐标 这里设置在中间
        int _x = (image.getWidth() - _metrics.stringWidth(_text)) / 2;
        int _y = image.getHeight() - font.getSize() / 2;

        _graphics.drawString(text, _x, _y);
        _graphics.dispose();
    }

}