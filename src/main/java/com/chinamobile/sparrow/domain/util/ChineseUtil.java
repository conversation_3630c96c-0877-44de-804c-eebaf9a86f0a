package com.chinamobile.sparrow.domain.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

public class ChineseUtil {

    protected final static Logger logger = LoggerFactory.getLogger(ChineseUtil.class);

    public static String CharacterToPinyin(String characters) {
        if (StringUtils.isEmpty(characters)) {
            return characters;
        }

        HanyuPinyinOutputFormat _formart = new HanyuPinyinOutputFormat();
        _formart.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        _formart.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        _formart.setVCharType(HanyuPinyinVCharType.WITH_V);

        String _pinyin = "";
        try {
            char[] _chars = characters.trim().toCharArray();
            for (int i = 0; i < _chars.length; i++) {
                char _i = _chars[i];

                // 是中文
                if (Character.toString(_i).matches("[\\u4e00-\\u9fa5]")) {
                    String[] _one = PinyinHelper.toHanyuPinyinStringArray(_i, _formart);
                    _pinyin += _one.length > 0 ? _one[0] : _i;
                }
                // 非中文
                else {
                    _pinyin += _i;
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            logger.debug(String.format("汉字[%s]转拼音失败", characters), e);
        }

        return _pinyin;
    }

    public static boolean containsChinese(String str) {
        if (!StringUtils.hasLength(str)) {
            return false;
        }
        Pattern pattern = Pattern.compile("[\\u4e00-\\u9fa5]");
        Matcher matcher = pattern.matcher(str);
        return matcher.find();
    }

    public static boolean isOnlyChinese(String str) {
        if (!StringUtils.hasLength(str)) {
            return false;
        }
        String regex = "^[\\u4e00-\\u9fa5]+$";
        return str.matches(regex);
    }

}