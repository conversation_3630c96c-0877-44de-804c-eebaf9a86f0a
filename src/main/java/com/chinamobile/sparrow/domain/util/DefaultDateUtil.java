package com.chinamobile.sparrow.domain.util;

import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DefaultDateUtil extends DateUtil {
    public static Date from(String str, String format, boolean throwException) throws ParseException {
        if (!StringUtils.hasLength(str)) {
            return null;
        } else {
            try {
                return (new SimpleDateFormat(StringUtils.hasLength(format) ? format : DEFAULT_FORMAT)).parse(str);
            } catch (ParseException parseException) {
                LOGGER.error(String.format("转换日期[%s]失败", str), parseException);
                if (throwException) {
                    throw parseException;
                } else {
                    return null;
                }
            }
        }
    }

    public static Date from(String str, boolean throwException) throws ParseException {
        return from(str, null, throwException);
    }
}
