package com.chinamobile.sparrow.domain.model.sys;

import org.springframework.util.StringUtils;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

@Entity
@DiscriminatorValue(value = "Default")
public class DefaultDepartment extends Department {

    Double area;

    @Column(columnDefinition = "text")
    String description;

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String desc) {
        this.description = StringUtils.trimWhitespace(desc);
    }

}