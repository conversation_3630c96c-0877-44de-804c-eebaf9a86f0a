package com.chinamobile.sparrow.domain.model.sys;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "sys_bulletins")
public class Bulletin extends AbstractEntity {

    @Id
    @Column(length = 18)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 128)
    String title;

    @Column(columnDefinition = "text")
    String description;

    Integer seq;

    @Column(columnDefinition = "text")
    String attachmentIdsJSON;

    @Transient
    List<String> attachmentIds;

    @Transient
    List<Media> attachments;

    boolean isEnabled = true;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String name) {
        this.title = StringUtils.trimWhitespace(name);
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String code) {
        this.description = code;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public List<String> getAttachmentIds() {
        return attachmentIds;
    }

    public void setAttachmentIds(List<String> attachmentIds) {
        this.attachmentIds = attachmentIds;
    }

    public String getAttachmentIdsJSON() {
        return attachmentIdsJSON;
    }

    public void setAttachmentIdsJSON(String memberIdsJson) {
        this.attachmentIdsJSON = memberIdsJson;
    }

    public List<Media> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Media> attachments) {
        this.attachments = attachments;
    }

    public boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(boolean enabled) {
        isEnabled = enabled;
    }

}