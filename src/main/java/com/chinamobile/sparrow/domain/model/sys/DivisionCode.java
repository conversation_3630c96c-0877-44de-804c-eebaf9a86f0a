package com.chinamobile.sparrow.domain.model.sys;

import com.chinamobile.sparrow.domain.model.AbstractEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "sys_division_codes")
public class DivisionCode extends AbstractEntity {

    @Id
    @Column(length = 12)
    String code;

    @Column(length = 128)
    String fullName;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

}