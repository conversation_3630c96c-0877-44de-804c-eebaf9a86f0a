package com.chinamobile.sparrow.domain.model;

import org.springframework.util.StringUtils;

import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;

@MappedSuperclass
public class AssetEntity extends AbstractEntity {

    protected String regionId;
    protected String regionCode;

    @Transient
    protected String regionName;

    @Transient
    protected String regionFullName;

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = StringUtils.trimWhitespace(regionId);
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = StringUtils.trimWhitespace(regionCode);
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getRegionFullName() {
        return regionFullName;
    }

    public void setRegionFullName(String regionFullName) {
        this.regionFullName = regionFullName;
    }

}