package com.chinamobile.sparrow.domain.service.poc.base;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.poc.base.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.poc.base.lang.Device;
import com.chinamobile.sparrow.domain.service.poc.base.lang.DeviceLocation;
import com.chinamobile.sparrow.domain.service.poc.base.lang.DeviceStatus;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;

import java.io.IOException;
import java.util.List;

public class DeviceFacade extends AbstractService {

    public DeviceFacade(String baseUrl, ConnectionPool connectionPool) {
        super(baseUrl, connectionPool);
    }

    public Result<List<Device>> devices() throws IOException {
        Result<List<Device>> _devices = new Result<>();

        Result<JsonObject> _response = post("10115", null);
        if (!_response.isOK()) {
            return _devices.pack(_response);
        }

        _devices.data = ConverterUtil.json2Object(_response.data.get("Users").toString(), new TypeToken<List<Device>>() {
        }.getType());
        return _devices;
    }

    public Result<List<DeviceStatus>> status(List<String> ids) throws IOException {
        JsonArray _array = new JsonArray();
        ids.forEach(i -> _array.add(i));

        JsonObject _data = new JsonObject();
        _data.add("Uids", _array);

        Result<List<DeviceStatus>> _status = new Result<>();

        Result<JsonObject> _response = post("10150", _data);
        if (!_response.isOK()) {
            return _status.pack(_response);
        }

        _status.data = ConverterUtil.json2Object(_response.data.get("Users").toString(), new TypeToken<List<DeviceStatus>>() {
        }.getType());
        return _status;
    }

    public Result<List<DeviceLocation>> locations(List<String> ids) throws IOException {
        JsonArray _array = new JsonArray();
        ids.forEach(i -> _array.add(i));

        JsonObject _data = new JsonObject();
        _data.add("Uids", _array);

        Result<List<DeviceLocation>> _locations = new Result<>();

        Result<JsonObject> _response = post("10200", _data);
        if (!_response.isOK()) {
            return _locations.pack(_response);
        }

        _locations.data = ConverterUtil.json2Object(_response.data.get("Locations").toString(), new TypeToken<List<DeviceLocation>>() {
        }.getType());
        return _locations;
    }

}