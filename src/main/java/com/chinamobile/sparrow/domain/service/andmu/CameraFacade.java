package com.chinamobile.sparrow.domain.service.andmu;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.service.andmu.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.andmu.infra.TokenStore;
import com.chinamobile.sparrow.domain.service.andmu.lang.*;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;

import java.io.IOException;
import java.util.Date;

public class CameraFacade extends AbstractService {

    public CameraFacade(
            String baseUrl,
            String appId,
            String rsaPrivateKey,
            TokenStore tokenStore,
            ConnectionPool connectionPool
    ) {
        super(baseUrl, appId, rsaPrivateKey, tokenStore, connectionPool);
    }

    public Result<SmokeDetector> get(String id) throws IOException {
        JsonObject _data = new JsonObject();
        _data.addProperty("deviceId", id);

        String _url = baseUrl + "/v3/open/api/device/base/info";

        return post(_url, _data, TypeToken.of(SmokeDetector.class));
    }

    public Result<PagingItems<Camera>> find(int count, int index) throws IOException {
        Result<PagingItems<Camera>> _page = new Result<>();

        JsonObject _data = new JsonObject();
        _data.addProperty("pageSize", count);
        _data.addProperty("page", index + 1);

        String _url = baseUrl + "/v3/open/api/device/list";
        Result<List<Camera>> _dto = postForList(_url, _data, new TypeToken<List<Camera>>() {
        });
        if (!_dto.isOK()) {
            return _page.pack(_dto);
        }

        _page.data = new PagingItems<>(count, index);
        _page.data.total = _dto.data.getTotal();
        _page.data.items = _dto.data.getData();
        return _page;
    }

    public Result<String> live(String deviceId, Integer expiresId) throws IOException {
        return play("/v3/open/api/websdk/live", deviceId, expiresId);
    }

    public Result<String> player(String deviceId, Integer expiresId) throws IOException {
        return play("/v3/open/api/websdk/player", deviceId, expiresId);
    }

    public Result<String> playback(String deviceId, Date from, Date to) throws IOException {
        Result<String> _player = new Result<>();

        JsonObject _data = new JsonObject();
        _data.addProperty("deviceId", deviceId);
        _data.addProperty("startTime", from.getTime());
        _data.addProperty("endTime", to.getTime());

        String _url = baseUrl + "/v3/open/api/device/hls/palyback";
        Result<Playback> _dto = post(_url, _data, TypeToken.of(Playback.class));
        if (!_dto.isOK()) {
            return _player.pack(_dto);
        }

        _player.data = _dto.data.getHlsUrl();
        return _player;
    }

    Result<String> play(String url, String deviceId, Integer expiresIn) throws IOException {
        Result<String> _player = new Result<>();

        JsonObject _data = new JsonObject();
        _data.addProperty("deviceId", deviceId);

        if (expiresIn != null) {
            Date _expiresIn = DateUtil.addMinutes(new Date(), expiresIn);
            _data.addProperty("endTime", _expiresIn.getTime());
        }

        String _url = baseUrl + url;
        Result<Player> _dto = post(_url, _data, TypeToken.of(Player.class));
        if (!_dto.isOK()) {
            return _player.pack(_dto);
        }

        _player.data = _dto.data.getUrl();
        return _player;
    }

}