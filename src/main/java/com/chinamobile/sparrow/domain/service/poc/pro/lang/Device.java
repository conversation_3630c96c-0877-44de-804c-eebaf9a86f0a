package com.chinamobile.sparrow.domain.service.poc.pro.lang;

import java.util.Date;

public class Device {

    String key;
    String account;
    String name;
    int terminalType;
    String online;

    int positionType;
    String baiduLongtitude;
    String baiduLatitude;
    String gpsLongtitude;
    String gpsLatitude;
    String sparkLongtitude;
    String sparkLatitude;

    Date reportTime;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(int terminalType) {
        this.terminalType = terminalType;
    }

    public String getOnline() {
        return online;
    }

    public void setOnline(String online) {
        this.online = online;
    }

    public int getPositionType() {
        return positionType;
    }

    public void setPositionType(int positionType) {
        this.positionType = positionType;
    }

    public String getBaiduLongtitude() {
        return baiduLongtitude;
    }

    public void setBaiduLongtitude(String baiduLongtitude) {
        this.baiduLongtitude = baiduLongtitude;
    }

    public String getBaiduLatitude() {
        return baiduLatitude;
    }

    public void setBaiduLatitude(String baiduLatitude) {
        this.baiduLatitude = baiduLatitude;
    }

    public String getGpsLongtitude() {
        return gpsLongtitude;
    }

    public void setGpsLongtitude(String gpsLongtitude) {
        this.gpsLongtitude = gpsLongtitude;
    }

    public String getGpsLatitude() {
        return gpsLatitude;
    }

    public void setGpsLatitude(String gpsLatitude) {
        this.gpsLatitude = gpsLatitude;
    }

    public String getSparkLongtitude() {
        return sparkLongtitude;
    }

    public void setSparkLongtitude(String sparkLongtitude) {
        this.sparkLongtitude = sparkLongtitude;
    }

    public String getSparkLatitude() {
        return sparkLatitude;
    }

    public void setSparkLatitude(String sparkLatitude) {
        this.sparkLatitude = sparkLatitude;
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

}