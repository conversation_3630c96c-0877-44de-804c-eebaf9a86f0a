package com.chinamobile.sparrow.domain.service.andmu;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.service.andmu.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.andmu.lang.List;
import com.chinamobile.sparrow.domain.service.andmu.lang.SmokeDetector;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;

import java.io.IOException;

public class SmokeDetectorFacade extends AbstractService {

    public SmokeDetectorFacade(
            String baseUrl,
            String appId,
            String rsaPrivateKey,
            ConnectionPool connectionPool
    ) {
        super(baseUrl, appId, rsaPrivateKey, null, connectionPool);
    }

    public Result<SmokeDetector> get(String id) throws IOException {
        JsonObject _data = new JsonObject();
        _data.addProperty("deviceId", id);

        String _url = baseUrl + "/v3/open/api/device/base/info";

        return post(_url, _data, TypeToken.of(SmokeDetector.class));
    }

    public Result<PagingItems<SmokeDetector>> find(int count, int index, Integer type, Integer status) throws IOException {
        Result<PagingItems<SmokeDetector>> _page = new Result<>();

        JsonObject _data = new JsonObject();
        _data.addProperty("pageSize", count);
        _data.addProperty("page", index + 1);

        if (type != null) {
            _data.addProperty("deviceType", type);
        }

        if (status != null) {
            _data.addProperty("onlineStatus", status);
        }

        String _url = baseUrl + "/v3/open/api/fire/device/list/query";
        Result<List<SmokeDetector>> _dto = postForList(_url, _data, new TypeToken<List<SmokeDetector>>() {
        });
        if (!_dto.isOK()) {
            return _page.pack(_dto);
        }

        _page.data = new PagingItems<>(count, index);
        _page.data.total = _dto.data.getTotal();
        _page.data.items = _dto.data.getData();
        return _page;
    }

}