package com.chinamobile.sparrow.domain.service.acs;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.acs.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.acs.infra.HttpDigestAuthenticator;
import com.chinamobile.sparrow.domain.service.acs.lang.Alarm;
import com.chinamobile.sparrow.domain.service.acs.lang.AlarmSnap;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

public class AlarmFacade extends AbstractService {

    public AlarmFacade(String baseUrl, ConnectionPool connectionPool, HttpDigestAuthenticator authenticator) {
        super(baseUrl, connectionPool, authenticator);
    }

    public Result<List<Alarm>> find(int count, int index, String source, List<String> deviceIds, Date from, Date to) throws IOException {
        Result<List<Alarm>> _alarms = new Result<>();

        String _url = String.format(baseUrl, "ecs", "alarm/list");

        JsonObject _page = new JsonObject();
        _page.addProperty("pageNum", index + 1);
        _page.addProperty("pageSize", count);

        JsonObject _search = new JsonObject();
        _search.addProperty("alarmGroupType", source);
        _search.addProperty("beginTime", DateUtil.toString(from, "yyyy-MM-dd HH:mm:ss"));
        _search.addProperty("endTime", DateUtil.toString(to, "yyyy-MM-dd HH:mm:ss"));

        if (!CollectionUtils.isEmpty(deviceIds)) {
            JsonArray _devices = new JsonArray();
            _devices.forEach(i -> {
                JsonObject _device = new JsonObject();
                _device.add("devId", i);
                _devices.add(_device);
            });
            _search.add("devList", _devices);
        }

        JsonObject _data = new JsonObject();
        _data.add("pageInfo", _page);
        _data.add("searchInfo", _search);

        Result<JsonObject> _dto = post(_url, _data, TypeToken.of(JsonObject.class));
        if (!_dto.isOK()) {
            return _alarms.pack(_dto);
        }

        _alarms.data = (List<Alarm>) Optional.ofNullable(_dto.data.get("alarmList"))
                .map(i -> ConverterUtil.json2Object(i.toString(), new TypeToken<List<Alarm>>() {
                }.getType())).orElse(new ArrayList<Alarm>());
        return _alarms;
    }

    public Result<AlarmSnap> snap(String alarmId, Date alarmTime) throws IOException {
        String _url = String.format(baseUrl, "ecs", "alarm/snap/get");

        JsonObject _data = new JsonObject();
        _data.addProperty("alarmId", alarmId);
        _data.addProperty("alarmTime", DateUtil.toString(alarmTime, "yyyy-MM-dd HH:mm:ss"));

        return post(_url, _data, TypeToken.of(AlarmSnap.class));
    }

}