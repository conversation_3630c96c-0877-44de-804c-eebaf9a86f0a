package com.chinamobile.sparrow.domain.service.acs.infra;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.*;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public abstract class AbstractService {

    protected final String baseUrl;
    protected final OkHttpClient client;

    protected String nonce;

    protected AbstractService(String baseUrl, ConnectionPool connectionPool, HttpDigestAuthenticator authenticator) {
        this.baseUrl = baseUrl;
        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .connectTimeout(5L, TimeUnit.SECONDS)
                .readTimeout(1L, TimeUnit.MINUTES)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .authenticator(authenticator)
                .build();
    }

    protected <T1, T2> Result<T2> post(String url, T1 data, TypeToken<T2> type) throws IOException {
        Result<T2> _result = new Result<>();

        String _json = ConverterUtil.toJson(data == null ? new HashMap() : data);
        RequestBody _body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), _json);

        Request.Builder _builder = new Request.Builder()
                .url(url);

        // 携带认证头
        if (StringUtils.hasLength(nonce)) {
            String _digest = ((HttpDigestAuthenticator) client.authenticator()).digest(url, nonce, null);
            _builder = _builder.header(HttpDigestAuthenticator.REQUEST_AUTHENTICATION_HEADER, _digest);
        }

        _builder = _builder.post(_body);

        Response _response = this.client.newCall(_builder.build()).execute();
        if (_response.isSuccessful()) {
            Map<String, String> _authentication = ((HttpDigestAuthenticator) client.authenticator()).resolveAuthentication(_response);
            this.nonce = Optional.ofNullable(_authentication.get("nextnonce")).orElse(null);

            _json = _response.body().string();
            JsonObject _dto = ConverterUtil.json2Object(_json, JsonObject.class);

            if (Optional.ofNullable(_dto.get("resultCode"))
                    .filter(i -> "0".equals(i.getAsString()))
                    .isPresent()) {
                _dto.remove("resultCode");
                _dto.remove("resultDesc");
                _result.data = ConverterUtil.json2Object(_dto.toString(), type.getType());
            } else {
                _result.setCode(Optional.ofNullable(_dto.get("resultCode"))
                        .map(i -> i.getAsString()).orElse(null));
                _result.message = Optional.ofNullable(_dto.get("resultDesc"))
                        .map(i -> i.getAsString()).orElse(null);
            }
        } else {
            _result.setCode(String.valueOf((_response.code())));
            _result.message = _response.message();
        }

        return _result;
    }

}