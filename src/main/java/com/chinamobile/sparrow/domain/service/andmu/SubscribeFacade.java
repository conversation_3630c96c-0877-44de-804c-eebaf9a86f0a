package com.chinamobile.sparrow.domain.service.andmu;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.andmu.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.andmu.infra.TokenStore;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;

import java.io.IOException;
import java.util.List;

public class SubscribeFacade extends AbstractService {

    public SubscribeFacade(
            String baseUrl,
            String appId,
            String rsaPrivateKey,
            TokenStore tokenStore,
            ConnectionPool connectionPool
    ) {
        super(baseUrl, appId, rsaPrivateKey, tokenStore, connectionPool);
    }

    public Result<JsonObject> subscribe(List<String> ids, List<String> events) throws IOException {
        return subscribe("/v3/open/api/app/alarm/subscribe", ids, events);
    }

    public Result<JsonObject> subscribeFireSafety(List<String> ids, List<String> events) throws IOException {
        return subscribe("/v3/open/api/app/anxiao/alarm/subscribe", ids, events);
    }

    Result<JsonObject> subscribe(String path, List<String> ids, List<String> events) throws IOException {
        JsonArray _array = new JsonArray();

        for (String i : ids) {
            JsonObject _record = new JsonObject();
            _record.addProperty("deviceId", i);

            String _events = ConverterUtil.toJson(events);
            _record.addProperty("event", _events);
            _array.add(_record);
        }

        JsonObject _data = new JsonObject();
        _data.add("subscribeInfos", _array);

        String _url = baseUrl + path;

        return post(_url, _data, TypeToken.of(JsonObject.class));
    }

}