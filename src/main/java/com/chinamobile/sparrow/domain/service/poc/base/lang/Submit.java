package com.chinamobile.sparrow.domain.service.poc.base.lang;

import java.util.Date;
import java.util.List;

public class Submit {

    String Id;
    String ResId;
    String Uid;
    String Name;
    String ResUrl;
    List<String> ResUrls;
    int ResType;
    String GpsLongitude;
    String GpsLatitude;
    String BaiduLongitude;
    String BaiduLatitude;
    Date Time;
    int ResCount;
    String Detail;
    int ReadStatus;
    String Content;
    long FileSize;
    int Collected;
    String CollectId;
    String ThumbnailURL;

    public String getId() {
        return Id;
    }

    public void setId(String id) {
        Id = id;
    }

    public String getResId() {
        return ResId;
    }

    public void setResId(String resId) {
        ResId = resId;
    }

    public String getUid() {
        return Uid;
    }

    public void setUid(String uid) {
        Uid = uid;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getResUrl() {
        return ResUrl;
    }

    public void setResUrl(String resUrl) {
        ResUrl = resUrl;
    }

    public List<String> getResUrls() {
        return ResUrls;
    }

    public void setResUrls(List<String> resUrls) {
        ResUrls = resUrls;
    }

    public int getResType() {
        return ResType;
    }

    public void setResType(int resType) {
        ResType = resType;
    }

    public String getGpsLongitude() {
        return GpsLongitude;
    }

    public void setGpsLongitude(String gpsLongitude) {
        GpsLongitude = gpsLongitude;
    }

    public String getGpsLatitude() {
        return GpsLatitude;
    }

    public void setGpsLatitude(String gpsLatitude) {
        GpsLatitude = gpsLatitude;
    }

    public String getBaiduLongitude() {
        return BaiduLongitude;
    }

    public void setBaiduLongitude(String baiduLongitude) {
        BaiduLongitude = baiduLongitude;
    }

    public String getBaiduLatitude() {
        return BaiduLatitude;
    }

    public void setBaiduLatitude(String baiduLatitude) {
        BaiduLatitude = baiduLatitude;
    }

    public Date getTime() {
        return Time;
    }

    public void setTime(Date time) {
        Time = time;
    }

    public int getResCount() {
        return ResCount;
    }

    public void setResCount(int resCount) {
        ResCount = resCount;
    }

    public String getDetail() {
        return Detail;
    }

    public void setDetail(String detail) {
        Detail = detail;
    }

    public int getReadStatus() {
        return ReadStatus;
    }

    public void setReadStatus(int readStatus) {
        ReadStatus = readStatus;
    }

    public String getContent() {
        return Content;
    }

    public void setContent(String content) {
        Content = content;
    }

    public long getFileSize() {
        return FileSize;
    }

    public void setFileSize(long fileSize) {
        FileSize = fileSize;
    }

    public int getCollected() {
        return Collected;
    }

    public void setCollected(int collected) {
        Collected = collected;
    }

    public String getCollectId() {
        return CollectId;
    }

    public void setCollectId(String collectId) {
        CollectId = collectId;
    }

    public String getThumbnailURL() {
        return ThumbnailURL;
    }

    public void setThumbnailURL(String thumbnailURL) {
        ThumbnailURL = thumbnailURL;
    }

}