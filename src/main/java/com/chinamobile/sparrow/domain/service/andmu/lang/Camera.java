package com.chinamobile.sparrow.domain.service.andmu.lang;

public class Camera {

    String deviceId;
    String deviceName;
    String camModelName;
    String deviceLongitude;
    String deviceLatitude;
    int deviceSwitch;
    int deviceStatus;
    Long createTime;

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getCamModelName() {
        return camModelName;
    }

    public void setCamModelName(String camModelName) {
        this.camModelName = camModelName;
    }

    public String getDeviceLongitude() {
        return deviceLongitude;
    }

    public void setDeviceLongitude(String deviceLongitude) {
        this.deviceLongitude = deviceLongitude;
    }

    public String getDeviceLatitude() {
        return deviceLatitude;
    }

    public void setDeviceLatitude(String deviceLatitude) {
        this.deviceLatitude = deviceLatitude;
    }

    public int getDeviceSwitch() {
        return deviceSwitch;
    }

    public void setDeviceSwitch(int deviceSwitch) {
        this.deviceSwitch = deviceSwitch;
    }

    public int getDeviceStatus() {
        return deviceStatus;
    }

    public void setDeviceStatus(int deviceStatus) {
        this.deviceStatus = deviceStatus;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

}