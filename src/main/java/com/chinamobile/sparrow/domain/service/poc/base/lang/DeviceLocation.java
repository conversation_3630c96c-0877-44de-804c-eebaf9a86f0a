package com.chinamobile.sparrow.domain.service.poc.base.lang;

import org.springframework.util.StringUtils;

public class DeviceLocation {

    String Uid;
    int Type;
    String GpsLongitude;
    String GpsLatitude;
    String BaiduLongitude;
    String BaiduLatitude;
    String Time;
    String Speed;

    public String getUid() {
        return Uid;
    }

    public void setUid(String uid) {
        Uid = uid;
    }

    public int getType() {
        return Type;
    }

    public void setType(int type) {
        Type = type;
    }

    public String getGpsLongitude() {
        return GpsLongitude;
    }

    public void setGpsLongitude(String gpsLongitude) {
        GpsLongitude = gpsLongitude;
    }

    public String getGpsLatitude() {
        return GpsLatitude;
    }

    public void setGpsLatitude(String gpsLatitude) {
        GpsLatitude = gpsLatitude;
    }

    public String getBaiduLongitude() {
        return BaiduLongitude;
    }

    public void setBaiduLongitude(String baiduLongitude) {
        BaiduLongitude = baiduLongitude;
    }

    public String getBaiduLatitude() {
        return BaiduLatitude;
    }

    public void setBaiduLatitude(String baiduLatitude) {
        BaiduLatitude = baiduLatitude;
    }

    public String getTime() {
        return Time;
    }

    public void setTime(String time) {
        Time = StringUtils.hasLength(time) ? time : null;
    }

    public String getSpeed() {
        return Speed;
    }

    public void setSpeed(String speed) {
        Speed = speed;
    }

}