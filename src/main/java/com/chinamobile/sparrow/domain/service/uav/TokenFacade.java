package com.chinamobile.sparrow.domain.service.uav;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.uav.infra.AbstractService;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;

import java.io.IOException;

public class TokenFacade extends AbstractService {

    final String appId;
    final String appSecret;

    public TokenFacade(
            String baseUrl,
            String appId,
            String appSecret,
            ConnectionPool connectionPool
    ) {
        super(baseUrl, null, connectionPool);
        this.appId = appId;
        this.appSecret = appSecret;
    }

    public Result<String> get() throws IOException {
        JsonObject _data = new JsonObject();
        _data.addProperty("username", appId);
        _data.addProperty("password", appSecret);

        return post(baseUrl + "/api/external/authCode", _data, TypeToken.of(String.class));
    }

}