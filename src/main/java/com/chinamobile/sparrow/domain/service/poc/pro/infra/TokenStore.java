package com.chinamobile.sparrow.domain.service.poc.pro.infra;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.poc.pro.LoginFacade;
import org.springframework.util.StringUtils;

import java.io.IOException;

public class TokenStore {

    String token = null;

    final LoginFacade loginFacade;

    public TokenStore(LoginFacade loginFacade) {
        this.loginFacade = loginFacade;
    }

    public String getToken() throws IOException {
        if (StringUtils.isEmpty(token)) {
            refreshToken();
        }

        return token;
    }

    public void refreshToken() throws IOException {
        Result<String> _token = loginFacade.login();
        if (_token.isOK()) {
            token = _token.data;
        }
    }

}