package com.chinamobile.sparrow.domain.service.uav;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.uav.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.uav.infra.TokenStore;
import com.chinamobile.sparrow.domain.service.uav.lang.Drone;
import com.chinamobile.sparrow.domain.service.uav.lang.Route;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import okhttp3.ConnectionPool;

import java.io.IOException;
import java.util.Date;
import java.util.List;

public class DroneFacade extends AbstractService {

    public DroneFacade(
            String baseUrl,
            TokenStore tokenStore,
            ConnectionPool connectionPool
    ) {
        super(baseUrl, tokenStore, connectionPool);
    }

    public Result<List<Drone>> drones() throws IOException {
        return get(baseUrl + "/api/drone/info", null, new TypeToken<List<Drone>>() {
        });
    }

    public Result<Drone> drone(String id) throws IOException {
        return get(baseUrl + "/api/drone/info/" + id, null, TypeToken.of(Drone.class));
    }

    public Result<String> live(String droneId) throws IOException {
        return get(baseUrl + "/api/drone/" + droneId + "/live", null, TypeToken.of(String.class));
    }

    public Result<List<Route>> routes(Date startTime, Date endTime) throws IOException {
        return get(baseUrl + "/api/drone/route", null, new TypeToken<List<Route>>() {
        });
    }

    public Result<List<Route.Coordinate>> route(String routeId) throws IOException {
        Result<List<Route.Coordinate>> _route = new Result<>();

        Result<String> _json = get(baseUrl + "/api/drone/route/" + routeId, null, TypeToken.of(String.class));
        if (!_json.isOK()) {
            return _route.pack(_json);
        }

        _route.data = ConverterUtil.json2Object(_json.data, new TypeToken<List<Route.Coordinate>>() {
        }.getType());
        return _route;
    }

    public Result<String> vod(String routeId) throws IOException {
        return get(baseUrl + "/api/route/" + routeId + "/vod", null, TypeToken.of(String.class));
    }

}