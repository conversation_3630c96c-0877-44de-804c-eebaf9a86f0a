package com.chinamobile.sparrow.domain.service.poc.pro;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.poc.pro.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.poc.pro.infra.TokenStore;
import com.chinamobile.sparrow.domain.service.poc.pro.lang.PagingData;
import com.chinamobile.sparrow.domain.service.poc.pro.lang.User;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;

public class UserFacade extends AbstractService {

    public UserFacade(String baseUrl, String account, TokenStore tokenStore, ConnectionPool connectionPool) {
        super(baseUrl, account, tokenStore, connectionPool);
    }

    public Result<List<User>> search(int count, int index, String account) throws IOException {
        Result<List<User>> _users = new Result<>();

        JsonObject _data = new JsonObject();

        if (StringUtils.hasLength(account)) {
            _data.addProperty("account", account);
        }

        _data.addProperty("pageNum", index + 1);
        _data.addProperty("pageSize", count);

        String _url = baseUrl + "/userInfo/search";
        Result<PagingData<User>> _page = post(_url, _data, new TypeToken<PagingData<User>>() {
        });
        if (!_page.isOK()) {
            return _users.pack(_page);
        }

        _users.data = _page.data.getList();
        return _users;
    }

}