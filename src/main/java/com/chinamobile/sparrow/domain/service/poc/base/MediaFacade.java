package com.chinamobile.sparrow.domain.service.poc.base;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.service.poc.base.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.poc.base.lang.MediaURL;
import com.chinamobile.sparrow.domain.service.poc.base.lang.Submit;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class MediaFacade extends AbstractService {

    public MediaFacade(String baseUrl, ConnectionPool connectionPool) {
        super(baseUrl, connectionPool);
    }

    public Result<PagingItems<Submit>> find(int count, int index, String deviceId, int type, String keyword, Date from, Date to) throws IOException {
        JsonObject _data = new JsonObject();
        _data.addProperty("Uid", StringUtils.hasLength(deviceId) ? deviceId : "");
        _data.addProperty("PageSize", count);
        _data.addProperty("PageIndex", index);
        _data.addProperty("ResType", type);
        _data.addProperty("Collected", 0);
        _data.addProperty("Version", "V1");

        if (StringUtils.hasLength(keyword)) {
            _data.addProperty("SearchContent", keyword);
        }

        if (from != null) {
            _data.addProperty("TimeFrom", DateUtil.toString(from, "yyyy-MM-dd HH:mm:ss"));
        }

        if (to != null) {
            _data.addProperty("TimeTo", DateUtil.toString(to, "yyyy-MM-dd HH:mm:ss"));
        }

        Result<PagingItems<Submit>> _page = new Result<>();

        Result<JsonObject> _response = post("10400", _data);
        if (!_response.isOK()) {
            return _page.pack(_response);
        }

        _page.data = new PagingItems<>(count, index);
        _page.data.total = _response.data.get("TotalCount").getAsInt();
        _page.data.items = ConverterUtil.json2Object(_response.data.get("Reports").toString(), new TypeToken<List<Submit>>() {
        }.getType());


        for (Submit i : _page.data.items) {
            if (i.getResCount() > 1) {
                // 获取所有资源
                Result<MediaURL> _media = getURLs(i.getId(), type);
                if (_media.isOK()) {
                    i.setResUrls(_media.data.getResUrls());
                }
            } else {
                i.setResUrls(Collections.singletonList(i.getResUrl()));
            }
        }

        return _page;
    }

    public Result<MediaURL> getURLs(String id, int type) throws IOException {
        JsonObject _data = new JsonObject();
        _data.addProperty("ResId", id);
        _data.addProperty("ResType", type);

        Result<MediaURL> _media = new Result<>();

        Result<JsonObject> _response = post("11410", _data);
        if (!_response.isOK()) {
            return _media.pack(_response);
        }

        _media.data = ConverterUtil.json2Object(_response.data.toString(), MediaURL.class);
        return _media;
    }

    /* public Result get(String resourceId, int type) {
        JsonObject _data = new JsonObject();
        _data.addProperty("ResId", resourceId);
        _data.addProperty("ResType", type);

        Result<PagingItems<Media>> _page = new Result<>();

        Result<JsonObject> _response = post("11410", _data);
        if (!_response.isOK()) {
            return _page.pack(_response);
        }

        _page.data = new PagingItems<>(count, index);
        _page.data.total = _response.data.get("TotalCount").getAsInt();
        _page.data.items = ConverterUtil.json2Object(_response.data.get("Reports").toString(), new TypeToken<List<Media>>() {
        }.getType());
        return _page;
    } */

}