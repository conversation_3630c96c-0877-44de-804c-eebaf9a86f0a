package com.chinamobile.sparrow.domain.service.poc.pro;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.poc.pro.infra.AbstractService;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;
import org.apache.commons.codec.digest.HmacUtils;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

public class LoginFacade extends AbstractService {

    final String account;
    final String password;

    public LoginFacade(
            String baseUrl,
            String account,
            String password,
            ConnectionPool connectionPool
    ) {
        super(baseUrl, account, null, connectionPool);
        this.account = account;
        this.password = password;
    }

    public Result<String> login() throws IOException {
        Result<String> _token = new Result<>();

        JsonObject _data = new JsonObject();
        _data.addProperty("account", account);

        String _password = DigestUtils.md5DigestAsHex(password.getBytes(StandardCharsets.UTF_8));
        byte[] _bytes = HmacUtils.hmacSha1( "dispatcher", _password);
        _password = Base64Utils.encodeToString(_bytes);
        _data.addProperty("password", _password);

        String _url = baseUrl + "/app/auth/login";
        Result<JsonObject> _dto = post(_url, _data, TypeToken.of(JsonObject.class));
        if (!_dto.isOK()) {
            return _token.pack(_dto);
        }

        _token.data = Optional.ofNullable(_dto.data.get("token"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        return _token;
    }

}