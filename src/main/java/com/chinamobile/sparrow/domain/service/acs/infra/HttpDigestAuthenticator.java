package com.chinamobile.sparrow.domain.service.acs.infra;

import okhttp3.Authenticator;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.Route;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class HttpDigestAuthenticator implements Authenticator {

    public static final String REQUEST_AUTHENTICATION_HEADER = "Authorization";
    public static final String RESPONSE_AUTHENTICATION_HEADER = "WWW-Authenticate";

    final String REALM = "allcam";
    final String username;
    final String password;

    public HttpDigestAuthenticator(String username, String password) {
        this.username = username;
        this.password = password;
    }

    @Override
    public Request authenticate(Route route, Response response) throws IOException {
        if (response.priorResponse() != null) {
            return null;
        }

        Map<String, String> _map = resolveAuthentication(response);

        String _uri = response.request().url().toString();
        String _nonce = _map.get("nonce");
        String _opaque = _map.get("opaque=");
        return response.request().newBuilder()
                .header(REQUEST_AUTHENTICATION_HEADER, digest(_uri, _nonce, _opaque))
                .build();
    }

    public Map<String, String> resolveAuthentication(Response response) {
        String _authentication = response.header(RESPONSE_AUTHENTICATION_HEADER);

        Map<String, String> _map = new HashMap<>();
        for (String i : _authentication.split(",")) {
            String[] _pair = i.split("=");
            if (_pair.length == 2) {
                _map.put(_pair[0], _pair[1]);
            }
        }

        return _map;
    }

    public String digest(String url, String nonce, String opaque) {
        String _nc = "00000001";
        String _cnonce = "00000001";
        String _response = generateResponse(url, username, password, REALM, nonce, "auth", _nc, _cnonce, null);

        return String.format("Digest username=%s,realm=%s,nonce=%s,uri=%s,response=%s,cnonce=%s,opaque=%s,qop=%s,nc=%s", username, REALM, nonce, url, _response, _cnonce, opaque, "auth", _nc);
    }

    String generateResponse(String url, String username, String password, String realm, String nonce, String qop, String nc, String cnonce, byte[] body) {

        String _a1 = String.format("%s:%s:%s", unquote(username), unquote(realm), unquote(password));
        _a1 = DigestUtils.md5DigestAsHex(_a1.getBytes(StandardCharsets.UTF_8));

        String _b = String.format("%s:%s:%s:%s", unquote(nonce), nc, unquote(cnonce), unquote(qop));

        String _a2 = "";
        switch (qop) {
            case "auth-int":
                String _temp = new String(body, StandardCharsets.UTF_8);
                _a2 = String.format("POST:%s:%s", url, DigestUtils.md5DigestAsHex(_temp.getBytes(StandardCharsets.UTF_8)));

                break;
            case "auth":
                _a2 = String.format("POST:%s", url);

                break;
        }
        _a2 = DigestUtils.md5DigestAsHex(_a2.getBytes(StandardCharsets.UTF_8));

        String _digest = String.format("%s:%s:%s", _a1, _b, _a2);
        return DigestUtils.md5DigestAsHex(_digest.getBytes(StandardCharsets.UTF_8));
    }

    String unquote(String value) {
        return value.replace("\"", "");
    }

}