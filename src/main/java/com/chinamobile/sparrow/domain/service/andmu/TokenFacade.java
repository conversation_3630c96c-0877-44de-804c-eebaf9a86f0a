package com.chinamobile.sparrow.domain.service.andmu;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.andmu.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.andmu.lang.Token;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class TokenFacade extends AbstractService {

    final String appId;
    final String appSecret;

    public TokenFacade(
            String baseUrl,
            String appId,
            String appSecret,
            String rsaPrivateKey,
            ConnectionPool connectionPool
    ) {
        super(baseUrl, appId, rsaPrivateKey, null, connectionPool);
        this.appId = appId;
        this.appSecret = appSecret;
    }

    public Result<Token> get() throws IOException {
        JsonObject _data = new JsonObject();
        _data.addProperty("operatorType", 1);

        String _signature = DigestUtils.md5DigestAsHex((appId + appSecret).getBytes(StandardCharsets.UTF_8));
        _data.addProperty("sig", _signature);

        String _url = baseUrl + "/v3/open/api/token";

        return post(_url, _data, TypeToken.of(Token.class));
    }

}