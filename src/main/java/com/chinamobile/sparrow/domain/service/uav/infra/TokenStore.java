package com.chinamobile.sparrow.domain.service.uav.infra;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.uav.TokenFacade;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Date;

public class TokenStore {

    String token = null;
    Date exp = new Date();

    final TokenFacade tokenFacade;

    public TokenStore(TokenFacade tokenFacade) {
        this.tokenFacade = tokenFacade;
    }

    public String getToken() throws IOException {
        if (!StringUtils.hasLength(token)) {
            Result<String> _token = tokenFacade.get();
            if (_token.isOK()) {
                token = _token.data;
            }
        }

        return token;
    }

    public void clear() {
        token = null;
        exp = new Date();
    }

}