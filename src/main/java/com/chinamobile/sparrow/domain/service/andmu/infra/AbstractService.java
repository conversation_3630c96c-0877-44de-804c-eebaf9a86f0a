package com.chinamobile.sparrow.domain.service.andmu.infra;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public abstract class AbstractService {

    protected final String baseUrl;
    protected final OkHttpClient client;
    protected final TokenStore tokenStore;

    protected AbstractService(
            String baseUrl,
            String appId,
            String rsaPrivateKey,
            TokenStore tokenStore,
            ConnectionPool connectionPool
    ) {
        this.baseUrl = baseUrl;
        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .connectTimeout(5L, TimeUnit.SECONDS)
                .readTimeout(1L, TimeUnit.MINUTES)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .addInterceptor(new AuthenticatedInterceptor(appId, rsaPrivateKey, tokenStore))
                .build();
        this.tokenStore = tokenStore;
    }

    protected <T1, T2> Result<T2> post(String url, T1 data, TypeToken<T2> type) throws IOException {
        return post(json -> json.has("data") ? ConverterUtil.json2Object(json.get("data").toString(), type.getType()) : null, url, data);
    }

    protected <T1, T2> Result<T2> postForList(String url, T1 data, TypeToken<T2> type) throws IOException {
        return post(json -> ConverterUtil.json2Object(json.toString(), type.getType()), url, data);
    }

    <T1, T2> Result<T2> post(Function<JsonObject, T2> func, String url, T1 data) throws IOException {
        Result<T2> _result = new Result<>();

        String _json = ConverterUtil.toJson(data == null ? new HashMap() : data);
        RequestBody _body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), _json);

        Request.Builder _builder = new Request.Builder()
                .url(url)
                .post(_body);

        Response _response = client.newCall(_builder.build()).execute();
        if (_response.isSuccessful()) {
            _json = _response.body().string();
            JsonObject _dto = ConverterUtil.json2Object(_json, JsonObject.class);

            String _resultCode = Optional.ofNullable(_dto.get("resultCode"))
                    .map(i -> i.getAsString()).orElse(null);
            switch (_resultCode) {
                case "000000":
                    _result.data = func.apply(_dto);

                    break;

                    // token过期
                case "11504":
                    tokenStore.clear();
                    return post(func, url, data);

                default:
                    _result.setCode(Optional.ofNullable(_dto.get("resultCode"))
                            .map(i -> i.getAsString()).orElse(null));
                    _result.message = Optional.ofNullable(_dto.get("resultMsg"))
                            .map(i -> i.getAsString()).orElse(null);

                    break;
            }
        } else {
            _result.setCode(String.valueOf((_response.code())));
            _result.message = _response.message();
        }

        return _result;
    }

}