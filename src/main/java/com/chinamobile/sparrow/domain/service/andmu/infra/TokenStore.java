package com.chinamobile.sparrow.domain.service.andmu.infra;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.andmu.TokenFacade;
import com.chinamobile.sparrow.domain.service.andmu.lang.Token;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Date;

public class TokenStore {

    String token = null;
    Date exp = new Date();

    final TokenFacade tokenFacade;

    public TokenStore(TokenFacade tokenFacade) {
        this.tokenFacade = tokenFacade;
    }

    public String getToken() throws IOException {
        if (StringUtils.isEmpty(token) || new Date().after(exp)) {
            Result<Token> _token = tokenFacade.get();
            if (_token.isOK()) {
                token = _token.data.getToken();
                exp = DateUtil.addSeconds(new Date(), _token.data.getExpires_in());
            }
        }

        return token;
    }

    public void clear() {
        token = null;
        exp = new Date();
    }

}