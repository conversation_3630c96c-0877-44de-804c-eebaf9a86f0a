package com.chinamobile.sparrow.domain.service.uav.infra;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;

import java.io.IOException;

public class AuthenticatedInterceptor implements Interceptor {

    static final String TOKEN_HEADER = "authCode";

    static final String RESULT_CODE_UNAUTHENTICATED = "401";

    final TokenStore tokenStore;

    public AuthenticatedInterceptor(TokenStore tokenStore) {
        this.tokenStore = tokenStore;
    }

    @NotNull
    @Override
    public Response intercept(@NotNull Chain chain) throws IOException {
        Response _response = chain.proceed(newRequest(chain.request()));
        if (HttpStatus.UNAUTHORIZED.value() == _response.code() && tokenStore != null) {
            _response.close();

            tokenStore.clear();
            return chain.proceed(newRequest(chain.request()));
        }

        return _response;
    }

    Request newRequest(Request origin) throws IOException {
        Request.Builder _builder = origin.newBuilder();

        if (tokenStore != null) {
            String _token = tokenStore.getToken();
            if (StringUtils.hasLength(_token)) {
                _builder = _builder.header(TOKEN_HEADER, _token);
            }
        }

        return _builder.build();
    }

}