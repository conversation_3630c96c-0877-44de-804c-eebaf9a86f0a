package com.chinamobile.sparrow.domain.service.acs;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.acs.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.acs.infra.HttpDigestAuthenticator;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;

import java.io.IOException;
import java.util.Date;
import java.util.Optional;

public class MediaFacade extends AbstractService {

    public MediaFacade(String baseUrl, ConnectionPool connectionPool, HttpDigestAuthenticator authenticator) {
        super(baseUrl, connectionPool, authenticator);
    }

    public Result<String> preview(String cameraId, int urlType) throws IOException {
        Result<String> _uri = new Result<>();

        String _url = String.format(baseUrl, "uas", "media/live");

        JsonObject _data = new JsonObject();
        _data.addProperty("cameraId", cameraId);
        _data.addProperty("streamType", 1);
        _data.addProperty("urlType", urlType);
        _data.addProperty("agentType", 1);

        Result<JsonObject> _dto = post(_url, _data, TypeToken.of(JsonObject.class));
        if (!_dto.isOK()) {
            return _uri.pack(_dto);
        }

        _uri.data = Optional.ofNullable(_dto.data.get("url"))
                .map(i -> i.getAsString())
                .orElse(null);
        return _uri;
    }

    public Result<String> playback(String cameraId, int urlType, Date from, Date to) throws IOException {
        Result<String> _uri = new Result<>();

        String _url = String.format(baseUrl, "mss", "record/url");

        JsonObject _data = new JsonObject();
        _data.addProperty("cameraId", cameraId);
        _data.addProperty("streamType", 1);
        _data.addProperty("urlType", urlType);
        _data.addProperty("agentType", 1);
        _data.addProperty("vodType", "vod");

        JsonObject _vod = new JsonObject();
        _vod.addProperty("contentId", "");
        _vod.addProperty("beginTime", DateUtil.toString(from, "yyyy-MM-dd HH:mm:ss"));
        _vod.addProperty("endTime", DateUtil.toString(to, "yyyy-MM-dd HH:mm:ss"));
        _vod.addProperty("location", 0);
        _data.add("vodInfo", _vod);

        Result<JsonObject> _dto = post(_url, _data, TypeToken.of(JsonObject.class));
        if (!_dto.isOK()) {
            return _uri.pack(_dto);
        }

        _uri.data = Optional.ofNullable(_dto.data.get("url"))
                .map(i -> i.getAsString())
                .orElse(null);
        return _uri;
    }

}