package com.chinamobile.sparrow.domain.service.poc.pro.infra;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okio.Buffer;
import org.apache.commons.codec.digest.HmacUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

public class AuthenticatedInterceptor implements Interceptor {

    final String account;
    final TokenStore tokenStore;

    public AuthenticatedInterceptor(String account, TokenStore tokenStore) {
        this.account = account;
        this.tokenStore = tokenStore;
    }

    @NotNull
    @Override
    public Response intercept(@NotNull Chain chain) throws IOException {
        Response _response = chain.proceed(newRequest(chain.request()));

        List<Integer> _filters = Arrays.asList(HttpStatus.UNAUTHORIZED.value(), HttpStatus.PAYMENT_REQUIRED.value());
        if (!_filters.contains(_response.code())) {
            return _response;
        }

        // token过期，刷新token
        tokenStore.refreshToken();

        _response.close();
        return chain.proceed(newRequest(chain.request()));
    }

    Request newRequest(Request origin) throws IOException {
        String _timestamp = String.valueOf(System.currentTimeMillis());

        String _md5 = "";
        if (origin.body() != null) {
            Buffer _buffer = new Buffer();
            origin.body().writeTo(_buffer);
            Charset _charset = origin.body().contentType().charset();
            String _temp = _buffer.readString(_charset);
            _md5 = DigestUtils.md5DigestAsHex(_temp.getBytes(StandardCharsets.UTF_8));
        }

        String _signature = null;
        if (tokenStore != null) {
            String _token = tokenStore.getToken();
            _signature = Base64Utils.encodeToString(HmacUtils.hmacSha1(_token, _timestamp + _md5 + account));
        }

        // 构建新的请求体
        Request.Builder _builder = origin.newBuilder()
                .header("account", account)
                .header("timestamp", _timestamp);

        if (StringUtils.hasLength(_md5)) {
            _builder = _builder.header("md5", _md5);
        }

        if (StringUtils.hasLength(_signature)) {
            _builder = _builder.header("signature", _signature);
        }

        switch (origin.method()) {
            case "GET":
                _builder = _builder.get();
                break;
            case "POST":
                _builder = _builder.post(origin.body());
                break;
        }

        return _builder.build();
    }

}
