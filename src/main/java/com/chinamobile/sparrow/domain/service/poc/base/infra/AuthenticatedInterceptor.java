package com.chinamobile.sparrow.domain.service.poc.base.infra;

import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.gson.JsonObject;
import okhttp3.*;
import okio.Buffer;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Optional;

public class AuthenticatedInterceptor implements Interceptor {

    protected String sessionId;

    @NotNull
    @Override
    public Response intercept(@NotNull Chain chain) throws IOException {
        Request _origin = chain.request();

        Buffer _buffer = new Buffer();
        _origin.body().writeTo(_buffer);

        Charset _charset = _origin.body().contentType().charset();
        String _temp = _buffer.readString(_charset);
        JsonObject _json = ConverterUtil.json2Object(_temp, JsonObject.class);

        if (StringUtils.hasLength(sessionId)) {
            JsonObject _body = Optional.ofNullable(_json.get("Body"))
                    .map(i -> i.getAsJsonObject())
                    .orElse(null);
            if (_body == null) {
                _body = new JsonObject();
                _json.add("Body", _body);
            }

            _body.addProperty("SessionId", sessionId);
        }

        // 构建新的请求体
        Request _request = _origin.newBuilder()
                .post(RequestBody.create(_json.toString(), MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE)))
                .build();

        return chain.proceed(_request);
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

}