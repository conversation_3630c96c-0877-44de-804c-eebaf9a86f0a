package com.chinamobile.sparrow.domain.service.poc.pro;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.poc.pro.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.poc.pro.infra.TokenStore;
import com.chinamobile.sparrow.domain.service.poc.pro.lang.Device;
import com.chinamobile.sparrow.domain.service.poc.pro.lang.PagingData;
import com.google.common.reflect.TypeToken;
import okhttp3.ConnectionPool;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DeviceFacade extends AbstractService {

    public DeviceFacade(String baseUrl, String account, TokenStore tokenStore, ConnectionPool connectionPool) {
        super(baseUrl, account, tokenStore, connectionPool);
    }

    public Result<List<Device>> locations(int count, int index, int deviceType) throws IOException {
        Result<List<Device>> _devices = new Result<>();

        Map<String, String> _data = new HashMap<>();
        _data.put("pageSize", String.valueOf(count));
        _data.put("pageNum", String.valueOf(index + 1));
        _data.put("terminalType", String.valueOf(deviceType));

        String _url = baseUrl + "/location/terminal/info/search";
        Result<PagingData<Device>> _page = get(_url, _data, new TypeToken<PagingData<Device>>() {
        });
        if (!_page.isOK()) {
            return _devices.pack(_page);
        }

        _devices.data = _page.data.getList();
        return _devices;
    }

}