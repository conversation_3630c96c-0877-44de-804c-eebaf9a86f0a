package com.chinamobile.sparrow.domain.service.uav.infra;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import okhttp3.*;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public abstract class AbstractService {

    final String RESULT_CODE_OK = "200";

    protected final String baseUrl;
    protected final OkHttpClient client;
    protected final TokenStore tokenStore;

    public AbstractService(
            String baseUrl,
            TokenStore tokenStore,
            ConnectionPool connectionPool
    ) {
        this.baseUrl = baseUrl;
        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .addInterceptor(new AuthenticatedInterceptor(tokenStore))
                .build();
        this.tokenStore = tokenStore;
    }

    protected <T> Result<T> get(String url, Map<String, String> data, TypeToken<T> type) throws IOException {
        List<String> _query = new ArrayList<>();
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach((key, value) -> _query.add(String.format("%s=%s", key, value)));
        }

        if (!CollectionUtils.isEmpty(_query)) {
            String _temp = String.join("&", _query);
            url += (url.contains("?") ? "&" : "?") + _temp;
        }

        Request _request = new Request.Builder()
                .url(url)
                .get()
                .build();

        Response _response = client.newCall(_request).execute();
        return parseResponse(_response, type);
    }

    protected <T1, T2> Result<T2> post(String url, T1 data, TypeToken<T2> type) throws IOException {
        String _json = ConverterUtil.toJson(data == null ? new HashMap<>() : data);
        RequestBody _body = RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), _json);

        Request.Builder _builder = new Request.Builder()
                .url(url)
                .post(_body);

        Response _response = client.newCall(_builder.build()).execute();
        return parseResponse(_response, type);
    }

    <T> Result<T> parseResponse(Response response, TypeToken<T> type) throws IOException {
        Result<T> _result = new Result<>();

        if (response.isSuccessful()) {
            String _json = response.body().string();
            JsonObject _dto = ConverterUtil.json2Object(_json, JsonObject.class);

            if (Optional.ofNullable(_dto.get("code"))
                    .filter(i -> RESULT_CODE_OK.equals(i.getAsString()))
                    .isPresent()) {
                _result.data = ConverterUtil.json2Object(_dto.get("data").toString(), type.getType());
            } else {
                _result.setCode(Optional.ofNullable(_dto.get("code"))
                        .map(JsonElement::getAsString).orElse(null));
                _result.message = Optional.ofNullable(_dto.get("msg"))
                        .map(JsonElement::getAsString).orElse(null);
            }
        } else {
            _result.setCode(String.valueOf((response.code())));
            _result.message = response.message();
        }

        return _result;
    }

}