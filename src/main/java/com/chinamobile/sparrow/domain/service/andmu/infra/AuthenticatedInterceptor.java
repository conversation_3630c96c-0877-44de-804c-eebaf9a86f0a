package com.chinamobile.sparrow.domain.service.andmu.infra;

import com.google.gson.JsonObject;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okio.Buffer;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;

public class AuthenticatedInterceptor implements Interceptor {

    final String appId;
    final String rsaPrivateKey;
    final TokenStore tokenStore;

    public AuthenticatedInterceptor(
            String appId,
            String rsaPrivateKey,
            TokenStore tokenStore
    ) {
        this.appId = appId;
        this.rsaPrivateKey = rsaPrivateKey;
        this.tokenStore = tokenStore;
    }

    @NotNull
    @Override
    public Response intercept(@NotNull Chain chain) throws IOException {
        Request _origin = chain.request();

        JsonObject _json = new JsonObject();
        _json.addProperty("appid", appId);

        Buffer _buffer = new Buffer();
        _origin.body().writeTo(_buffer);
        Charset _charset = _origin.body().contentType().charset();
        String _temp = _buffer.readString(_charset);
        String _md5 = DigestUtils.md5DigestAsHex(_temp.getBytes(StandardCharsets.UTF_8));
        _json.addProperty("md5", _md5);

        _json.addProperty("timestamp", String.valueOf(System.currentTimeMillis()));

        if (tokenStore != null) {
            String _token = tokenStore.getToken();
            _json.addProperty("token", _token);
        }

        _json.addProperty("version", "1.0.0");

        String __signature = null;
        try {
            PKCS8EncodedKeySpec _keySpec = new PKCS8EncodedKeySpec(Base64Utils.decode(rsaPrivateKey.getBytes(StandardCharsets.UTF_8)));
            PrivateKey _privateKey = KeyFactory.getInstance("RSA").generatePrivate(_keySpec);

            Signature _signature = Signature.getInstance("SHA1WithRSA");
            _signature.initSign(_privateKey);
            _signature.update(_json.toString().getBytes(StandardCharsets.UTF_8));
            __signature = Base64Utils.encodeToString(_signature.sign());
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (SignatureException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e);
        }

        // 构建新的请求体
        Request.Builder _builder = _origin.newBuilder()
                .header("appid", _json.get("appid").getAsString())
                .header("md5", _json.get("md5").getAsString())
                .header("timestamp", _json.get("timestamp").getAsString());

        if (_json.has("token")) {
            _builder = _builder.header("token", _json.get("token").getAsString());
        }

        _builder.header("version", _json.get("version").getAsString())
                .header("signature", __signature)
                .post(_origin.body())
                .build();

        return chain.proceed(_builder.build());
    }

}