package com.chinamobile.sparrow.domain.service.acs;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.acs.infra.AbstractService;
import com.chinamobile.sparrow.domain.service.acs.infra.HttpDigestAuthenticator;
import com.chinamobile.sparrow.domain.service.acs.lang.Device;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class DeviceFacade extends AbstractService {

    public DeviceFacade(String baseUrl, ConnectionPool connectionPool, HttpDigestAuthenticator authenticator) {
        super(baseUrl, connectionPool, authenticator);
    }

    public Result<List<Device>> find(String type, String clientId) throws IOException {
        Result<List<Device>> _devices = new Result<>();

        JsonObject _data = new JsonObject();
        _data.addProperty("type", StringUtils.hasLength(type) ? type : "1");

        if (StringUtils.hasLength(clientId)) {
            _data.addProperty("clientId", clientId);
        }

        String _url = String.format(baseUrl, "uas", "dev/list");
        Result<JsonObject> _dto = post(_url, _data, TypeToken.of(JsonObject.class));
        if (!_dto.isOK()) {
            return _devices.pack(_dto);
        }

        _devices.data = (List<Device>) Optional.ofNullable(_dto.data.get("devList"))
                .map(i -> ConverterUtil.json2Object(i.toString(), new TypeToken<List<Device>>() {
                }.getType())).orElse(new ArrayList<Device>());
        return _devices;
    }

}