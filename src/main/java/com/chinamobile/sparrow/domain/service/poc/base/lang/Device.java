package com.chinamobile.sparrow.domain.service.poc.base.lang;

public class Device {

    String Uid;
    String Name;
    int RemoteCtl;
    int LimitCallIn;
    int LimitCallOut;
    int LimitCallChannel;
    int VideoPermissions;
    int Role;
    int BroadCastRole;
    int LimitStatus;
    String Phone;
    int OrgId;
    String OrgName;
    int Monitored;

    public String getUid() {
        return Uid;
    }

    public void setUid(String uid) {
        Uid = uid;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public int getRemoteCtl() {
        return RemoteCtl;
    }

    public void setRemoteCtl(int remoteCtl) {
        RemoteCtl = remoteCtl;
    }

    public int getLimitCallIn() {
        return LimitCallIn;
    }

    public void setLimitCallIn(int limitCallIn) {
        LimitCallIn = limitCallIn;
    }

    public int getLimitCallOut() {
        return LimitCallOut;
    }

    public void setLimitCallOut(int limitCallOut) {
        LimitCallOut = limitCallOut;
    }

    public int getLimitCallChannel() {
        return LimitCallChannel;
    }

    public void setLimitCallChannel(int limitCallChannel) {
        LimitCallChannel = limitCallChannel;
    }

    public int getVideoPermissions() {
        return VideoPermissions;
    }

    public void setVideoPermissions(int videoPermissions) {
        VideoPermissions = videoPermissions;
    }

    public int getRole() {
        return Role;
    }

    public void setRole(int role) {
        Role = role;
    }

    public int getBroadCastRole() {
        return BroadCastRole;
    }

    public void setBroadCastRole(int broadCastRole) {
        BroadCastRole = broadCastRole;
    }

    public int getLimitStatus() {
        return LimitStatus;
    }

    public void setLimitStatus(int limitStatus) {
        LimitStatus = limitStatus;
    }

    public String getPhone() {
        return Phone;
    }

    public void setPhone(String phone) {
        Phone = phone;
    }

    public int getOrgId() {
        return OrgId;
    }

    public void setOrgId(int orgId) {
        OrgId = orgId;
    }

    public String getOrgName() {
        return OrgName;
    }

    public void setOrgName(String orgName) {
        OrgName = orgName;
    }

    public int getMonitored() {
        return Monitored;
    }

    public void setMonitored(int monitored) {
        Monitored = monitored;
    }

}