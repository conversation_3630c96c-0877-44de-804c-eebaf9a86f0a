package com.chinamobile.sparrow.domain.service.acs.lang;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class Device {

    @SerializedName(value = "devId")
    String id;

    @SerializedName(value = "devName")
    String name;

    @SerializedName(value = "deviceIp")
    String ip;

    String clientId;
    String status;

    @SerializedName(value = "cameraList")
    List<Camera> cameras;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Camera> getCameras() {
        return cameras;
    }

    public void setCameras(List<Camera> cameras) {
        this.cameras = cameras;
    }

}