package com.chinamobile.sparrow.domain.service.poc.base.infra;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.google.gson.JsonObject;
import okhttp3.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public abstract class AbstractService {

    @Resource
    DictionaryRepository dictionaryRepository;

    protected final String baseUrl;
    protected final AuthenticatedInterceptor authenticatedInterceptor;
    protected final OkHttpClient client;

    protected AbstractService(String baseUrl, ConnectionPool connectionPool) {
        this.baseUrl = baseUrl;

        this.authenticatedInterceptor = new AuthenticatedInterceptor();

        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .connectTimeout(5L, TimeUnit.SECONDS)
                .readTimeout(1L, TimeUnit.MINUTES)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                // 自定义拦截器
                .addInterceptor(this.authenticatedInterceptor)
                .build();
    }

    protected Result<JsonObject> post(String code, JsonObject data) throws IOException {
        Result<JsonObject> _result = new Result<>();

        JsonObject _data = new JsonObject();
        _data.addProperty("Code", code);

        if (data != null) {
            _data.add("Body", data);
        }

        String _json = ConverterUtil.toJson(_data);
        RequestBody _body = RequestBody.create(_json, MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE));

        Request.Builder _builder = new Request.Builder()
                .url(baseUrl)
                .post(_body);

        Response _response = client.newCall(_builder.build()).execute();

        // 认证失败
        if (_response.isSuccessful()) {
            _json = _response.body().string();

            JsonObject _dto = ConverterUtil.json2Object(_json, JsonObject.class);

            String _code = Optional.ofNullable(_dto.get("Result"))
                    .map(i -> i.isJsonNull() ? "" : i.getAsString())
                    .orElse("");
            switch (_code) {
                case "400":
                case "403":
                    authenticate();

                    _response = client.newCall(_builder.build()).execute();

                    _json = _response.body().string();
                    break;
            }
        }

        if (_response.isSuccessful()) {
            JsonObject _dto = ConverterUtil.json2Object(_json, JsonObject.class);

            String _code = Optional.ofNullable(_dto.get("Result"))
                    .map(i -> i.isJsonNull() ? "" : i.getAsString())
                    .orElse("");
            if ("200".equals(_code)) {
                _dto.remove("Result");
                _result.data = ConverterUtil.json2Object(_dto.toString(), JsonObject.class);
            } else {
                _result.setCode(_code);
            }
        } else {
            _result.setCode(String.valueOf((_response.code())));
            _result.message = _response.message();
        }

        return _result;
    }

    protected void authenticate() {
        String _sessionId = dictionaryRepository.getVal("POC", "SessionId", true);
        authenticatedInterceptor.setSessionId(_sessionId);
    }

}