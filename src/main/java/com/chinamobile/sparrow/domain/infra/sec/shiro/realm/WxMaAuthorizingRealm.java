package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.WxMaToken;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.data.redis.core.RedisTemplate;

public class WxMaAuthorizingRealm extends AuthorizingRealm {

    public static final String REALM_TYPE = "WXMA";

    public static final String HEADER_ATTRIBUTE = "Authorization";
    public static final String CLIENT_STFY = "stfy";
    public static final String CLIENT_JFM = "jfm";

    final String redisPermissionKeyTemplate;
    final String redisRoleKeyTemplate;
    final DefaultUserRepository userRepository;
    final AccessFacade wxMaSTFYAccessFacade;
    final AccessFacade wxMaJFMAccessFacade;
    final RedisTemplate<String, Object> redisTemplate;

    public WxMaAuthorizingRealm(String redisPermissionKeyTemplate, String redisRoleKeyTemplate, DefaultUserRepository userRepository, AccessFacade wxMaSTFYAccessFacade, AccessFacade wxMaJFMAccessFacade, RedisTemplate<String, Object> redisTemplate) {
        this.redisPermissionKeyTemplate = redisPermissionKeyTemplate;
        this.redisRoleKeyTemplate = redisRoleKeyTemplate;
        this.userRepository = userRepository;
        this.wxMaSTFYAccessFacade = wxMaSTFYAccessFacade;
        this.wxMaJFMAccessFacade = wxMaJFMAccessFacade;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof WxMaToken;
    }

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        return new SimpleAuthorizationInfo();
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        if (token.getPrincipal() == null || token.getCredentials() == null) {
            return null;
        }

        String _json = new String((char[]) token.getCredentials());
        String[] _array = ConverterUtil.json2Object(_json, String[].class);
        /* String _client = _array[3];

        com.chinamobile.sparrow.domain.service.wx.ma.dto.User _dto;
        try {
            _dto = (CLIENT_JFM.equals(_client) ? wxMaJFMAccessFacade : wxMaSTFYAccessFacade).getUserWithDecryptedMp(_array[0], _array[1], _array[2]);
        } catch (WxErrorException e) {
            throw new AuthenticationException(e);
        }

        Result<DefaultUser> _user = userRepository.getBriefInWxMa(_dto.openId, _dto.mp, true);
        if (!_user.isOK()) {
            throw new UnknownAccountException("您未申请系统账号，登录失败");
        }

        if (StringUtils.hasLength(_user.data.getMaOpenId())) {
            // openId不匹配
            if (!Objects.equals(_user.data.getMaOpenId(), _dto.openId)) {
                throw new AuthenticationException("您的OpenId与系统注册不匹配，登录失败");
            }
        } else {
            // 更新openId
            _user.data.setMaOpenId(_dto.openId);

            userRepository.update(_user.data, _user.data.getId());
        }

        return new SimpleAuthenticationInfo(_user.data, token.getCredentials(), this.getClass().toString()); */

        String _client = _array[2];

        // com.chinamobile.sparrow.domain.service.wx.ma.dto.User _dto;
        String _mp;
        try {
            // _dto = (CLIENT_JFM.equals(_client) ? wxMaJFMAccessFacade : wxMaSTFYAccessFacade).getUser(_array[0]);

            _mp = (CLIENT_JFM.equals(_client) ? wxMaJFMAccessFacade : wxMaSTFYAccessFacade).getMp(_array[1]);

            //_dto.mp = _mp;
        } catch (WxErrorException e) {
            throw new AuthenticationException(e);
        }

        Result<DefaultUser> _user = userRepository.getBriefByMp(_mp, true);
        if (!_user.isOK()) {
            throw new AccountException(String.format("您未申请系统账号[%s]，登录失败", _mp));
        }

        /* if (StringUtils.hasLength(_user.data.getMaOpenId())) {
            // openId不匹配
            if (!Objects.equals(_user.data.getMaOpenId(), _dto.openId)) {
                throw new AccountException(String.format("您的OpenId[%s]与系统注册[%s]不匹配，登录失败", _dto.openId, _user.data.getMaOpenId()));
            }
        } else {
            // 更新openId
            _user.data.setMaOpenId(_dto.openId);

            userRepository.update(_user.data, _user.data.getId());
        } */

        return new SimpleAuthenticationInfo(_user.data, token.getCredentials(), this.getClass().toString());
    }

}