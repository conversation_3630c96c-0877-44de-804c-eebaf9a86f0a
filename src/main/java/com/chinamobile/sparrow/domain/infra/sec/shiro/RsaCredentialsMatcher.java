package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.util.CryptoUtil;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.credential.CredentialsMatcher;

import java.util.Objects;

public class RsaCredentialsMatcher implements CredentialsMatcher {

    final String privateKey;

    public RsaCredentialsMatcher(String privateKey) {
        this.privateKey = privateKey;
    }

    @Override
    public boolean doCredentialsMatch(AuthenticationToken token, AuthenticationInfo info) {
        String _credentials = new String((char[]) token.getCredentials());

        try {
            _credentials = CryptoUtil.decryptRsa(_credentials, privateKey);
        } catch (Exception e) {
            return false;
        }


        if (!(info.getPrincipals().getPrimaryPrincipal() instanceof DefaultUser)) {
            return false;
        }

        String _password = ((DefaultUser) info.getPrincipals().getPrimaryPrincipal()).getPassword();
        try {
            _password = CryptoUtil.decryptRsa(_password, privateKey);
        } catch (Exception e) {
            return false;
        }

        return Objects.equals(_credentials, _password);
    }

}