package com.chinamobile.sparrow.domain.infra.sec.shiro;

import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

public class FineReportAuthenticationFilter extends DefaultAuthenticationFilter {

    final String AUTHORIZATION_HEADER = "Authorization";
    final String AUTHORIZATION_HEADER_PREFIX = "Basic ";

    public FineReportAuthenticationFilter(String userName, String loginUrl, LoginUtil loginUtil) {
        super(userName, loginUrl, loginUtil);
    }

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) {
        HttpServletRequest _request = (HttpServletRequest) request;

        String _authorization = _request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.isEmpty(_authorization) || !_authorization.startsWith(AUTHORIZATION_HEADER_PREFIX)) {
            return null;
        }

        _authorization = _authorization.substring(AUTHORIZATION_HEADER_PREFIX.length());
        _authorization = new String(Base64Utils.decodeFromString(_authorization));
        String[] _temp = _authorization.split(":");
        if (_temp.length != 2) {
            return null;
        }

        return new UsernamePasswordToken(_temp[0], _temp[1]);
    }

}