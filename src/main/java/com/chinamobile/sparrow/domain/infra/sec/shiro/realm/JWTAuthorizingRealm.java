package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.data.redis.core.RedisTemplate;

public class JWTAuthorizingRealm extends AuthorizingRealm {

    public static final String REALM_TYPE = "JWT";

    final String sha256Secret;
    final String redisPermissionKeyTemplate;
    final String redisRoleKeyTemplate;
    final UserRepository userRepository;
    final RedisTemplate<String, Object> redisTemplate;

    public JWTAuthorizingRealm(String sha256Secret, String redisPermissionKeyTemplate, String redisRoleKeyTemplate, UserRepository userRepository, RedisTemplate<String, Object> redisTemplate) {
        this.sha256Secret = sha256Secret;
        this.redisPermissionKeyTemplate = redisPermissionKeyTemplate;
        this.redisRoleKeyTemplate = redisRoleKeyTemplate;
        this.userRepository = userRepository;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof com.chinamobile.sparrow.domain.infra.sec.shiro.token.JWT;
    }

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        return new SimpleAuthorizationInfo();
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        if (token.getPrincipal() == null) {
            return null;
        }

        String _token = token.getPrincipal().toString();

        // token过期
        try {
            JWTVerifier _verifier = JWT.require(Algorithm.HMAC256(sha256Secret)).build();
            _verifier.verify(_token);
        } catch (JWTVerificationException e) {
            throw new ExpiredCredentialsException(e);
        }

        DecodedJWT _jwt = JWT.decode(_token);
        String _id = _jwt.getClaim("id").asString();

        Result<User> _user = userRepository.getByIdOrAccountOrMp(_id, true);
        if (!_user.isOK()) {
            throw new UnknownAccountException();
        }

        return new SimpleAuthenticationInfo(_user.data, com.chinamobile.sparrow.domain.infra.sec.shiro.token.JWT.DEFAULT_PASSWORD, this.getClass().toString());
    }

}