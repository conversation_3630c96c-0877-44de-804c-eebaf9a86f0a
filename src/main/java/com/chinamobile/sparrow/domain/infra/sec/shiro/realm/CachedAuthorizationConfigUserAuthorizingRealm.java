package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.stream.Collectors;

public class CachedAuthorizationConfigUserAuthorizingRealm extends ConfigUserAuthorizingRealm {

    final String redisPermissionKeyTemplate;
    final String redisRoleKeyTemplate;
    final RedisTemplate<String, Object> redisTemplate;

    public CachedAuthorizationConfigUserAuthorizingRealm(String redisPermissionKeyTemplate, String redisRoleKeyTemplate, UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, RedisTemplate<String, Object> redisTemplate) {
        super(userRepository, roleRepository, permissionRepository);
        this.redisPermissionKeyTemplate = redisPermissionKeyTemplate;
        this.redisRoleKeyTemplate = redisRoleKeyTemplate;
        this.redisTemplate = redisTemplate;
    }

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        User _user = (User) SecurityUtils.getSubject().getPrincipal();

        String _key = String.format(redisRoleKeyTemplate, _user.getId());
        List<Object> _roles = redisTemplate.opsForList().range(_key, 0, -1);
        SimpleAuthorizationInfo _authorization = new SimpleAuthorizationInfo();
        if (!_roles.isEmpty()) {
            _authorization.addRoles(_roles.stream()
                    .map(i -> i.toString())
                    .collect(Collectors.toList()));
        }

        _key = String.format(redisPermissionKeyTemplate, _user.getId());
        List<Object> _permissions = redisTemplate.opsForList().range(_key, 0, -1);
        if (!_permissions.isEmpty()) {
            _authorization.addStringPermissions(_permissions.stream()
                    .map(i -> i.toString())
                    .collect(Collectors.toList()));
        }

        return _authorization;
    }

}