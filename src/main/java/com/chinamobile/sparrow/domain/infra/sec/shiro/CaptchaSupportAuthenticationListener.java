package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.springboot.web.controller.sec.DefaultLoginController;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.UsernamePasswordToken;

public class CaptchaSupportAuthenticationListener extends DefaultAuthenticationListener {

    public CaptchaSupportAuthenticationListener(DefaultUserRepository userRepository) {
        super(userRepository);
    }

    @Override
    public void onFailure(AuthenticationToken authenticationToken, AuthenticationException e) {
        if (authenticationToken instanceof UsernamePasswordToken) {
            // 重置验证码
            SecurityUtils.getSubject().getSession().removeAttribute(DefaultLoginController.CAPTCHA_SESSION_ATTRIBUTE);
        }

        super.onFailure(authenticationToken, e);
    }

}