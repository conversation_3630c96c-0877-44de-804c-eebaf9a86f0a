package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.*;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.*;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.apache.shiro.subject.Subject;

public class DefaultLoginUtil extends LoginUtil {

    public DefaultLoginUtil(String rsaPrivateKey, String username, UserRepository userRepository, SessionDAO sessionDAO) {
        super(rsaPrivateKey, username, userRepository, sessionDAO);
    }

    @Override
    public void login(AuthenticationToken token) {
        Subject _subject = SecurityUtils.getSubject();
        _subject.login(token);

        if (token instanceof CMPassportToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, CMPassportAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof JWT) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, JWTAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof SMSToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, SMSAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof YzyToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, YzyAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof WxCpToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, WxCpAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof WxMaToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, WxMaAuthorizingRealm.REALM_TYPE);
        } else if (token instanceof DefaultUsernamePasswordToken) {
            _subject.getSession().setAttribute(REALM_SESSION_ATTRIBUTE, UsernamePasswordAuthorizingRealm.REALM_TYPE);
        }
    }

}