package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.ConfigUserAuthorizingRealm;
import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.JWTAuthorizingRealm;
import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.WxMaAuthorizingRealm;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.ConfigUserToken;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.JWT;
import org.apache.shiro.ShiroException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.util.StringUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;

public class DefaultAuthenticationFilter extends FormAuthenticationFilter {

    protected final String ERROR_ATTRIBUTE = DefaultAuthenticationFilter.class.getName() + ".ERROR";

    protected final String userName;
    protected final String loginUrl;
    protected final LoginUtil loginUtil;
    protected final Logger logger;

    public DefaultAuthenticationFilter(String userName, String loginUrl, LoginUtil loginUtil) {
        this.userName = userName;
        this.loginUrl = loginUrl;
        this.loginUtil = loginUtil;
        this.logger = LoggerFactory.getLogger(DefaultAuthenticationFilter.class);
    }

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) {
        HttpServletRequest _request = (HttpServletRequest) request;

        if (ConfigUserAuthorizingRealm.REALM_TYPE.equals(loginUtil.getRealmType(_request))) {
            return new ConfigUserToken(userName, false, request.getRemoteHost());
        }

        String _jwt = _request.getHeader(WxMaAuthorizingRealm.HEADER_ATTRIBUTE);
        if (StringUtils.hasLength(_jwt)) {
            return new JWT(_jwt, false, request.getRemoteHost());
        }

        return null;
    }

    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        Subject _subject = this.getSubject(request, response);
        if (_subject.getPrincipal() != null || !this.isLoginRequest(request, response) && this.isPermissive(mappedValue)) {
            return true;
        }

        try {
            AuthenticationToken _token = this.createToken(request, response);
            if (_token instanceof ConfigUserToken || _token instanceof JWT) {
                _subject.login(_token);

                return true;
            }
        } catch (Throwable e) {
            logger.error("token验证失败", e);

            request.setAttribute(ERROR_ATTRIBUTE, e);
        }

        return false;
    }

    // 重写重定向登录方法，修改重定向地址
    @Override
    protected void redirectToLogin(ServletRequest request, ServletResponse response) throws IOException {
        // 对于非GET的请求，验证失败则抛出异常到ErrorController处理，不能执行重定向
        HttpServletRequest _request = (HttpServletRequest) request;
        if (HttpMethod.valueOf(_request.getMethod()) != HttpMethod.GET) {
            throwException(_request);
        }

        if (Arrays.asList(ConfigUserAuthorizingRealm.REALM_TYPE, JWTAuthorizingRealm.REALM_TYPE).contains(loginUtil.getRealmType(_request))) {
            // 已登录但验证失败，则抛出异常到ErrorController处理，不重定向到登录页面
            throwException(_request);
        }

        if (StringUtils.hasLength(loginUrl)) {
            setLoginUrl(loginUrl);
            super.redirectToLogin(request, response);
        } else {
            throwException(_request);
        }
    }

    protected void throwException(HttpServletRequest request) {
        Object e = request.getAttribute(ERROR_ATTRIBUTE);
        if (e == null) {
            throw new AuthenticationException();
        } else if (e instanceof ShiroException) {
            throw (ShiroException) e;
        } else {
            throw new AuthenticationException(((Exception) e).getMessage());
        }
    }

}