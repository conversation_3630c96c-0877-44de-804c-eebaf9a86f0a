package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.DisabledAccountException;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

public class RoleSMSAuthorizingRealm extends SMSAuthorizingRealm {

    final DictionaryRepository dictionaryRepository;

    public RoleSMSAuthorizingRealm(String clientId, UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, VerificationCodeRepository verificationCodeRepository, DictionaryRepository dictionaryRepository) {
        super(clientId, userRepository, roleRepository, permissionRepository, verificationCodeRepository);
        this.dictionaryRepository = dictionaryRepository;
    }

    @Override
    protected AuthenticationInfo getAuthenticationInfo(AuthenticationToken token, User user) {
        List<String> _names = dictionaryRepository.fuzzy("短信验证码登录", "准入角色", true, null).stream()
                .map(i -> i.getVal())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(_names)) {
            return super.getAuthenticationInfo(token, user);
        }

        List<Role> _roles = roleRepository.getUserRoles(user.getId());
        if (_roles.stream()
                .anyMatch(i -> _names.contains(i.getName()))) {
            return super.getAuthenticationInfo(token, user);
        }

        throw new DisabledAccountException("您的账号不支持短信验证码登录");
    }

}