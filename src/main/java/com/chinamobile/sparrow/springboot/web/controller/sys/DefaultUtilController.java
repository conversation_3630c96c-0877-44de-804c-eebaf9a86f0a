package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.healthcode.model.subject.PersonDescription;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.NotLog;
import com.chinamobile.sparrow.domain.util.ClassUtil;
import com.chinamobile.sparrow.springboot.web.controller.UtilController;
import com.google.gson.JsonObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.persistence.Column;
import javax.persistence.Id;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 6/19/2023 11:58
 */
@RestController
public class DefaultUtilController extends UtilController {
    public DefaultUtilController(
            String cmPassportAppId,
            String aMapJsKey,
            String qqMapJsKey
    ) {
        super(cmPassportAppId, aMapJsKey, qqMapJsKey);
    }

    @PostMapping({"/validation/jquery"})
    @NotLog
    public Result validation(@RequestBody JsonObject data) throws ClassNotFoundException {
        String _str = data.get("class").getAsString();
        Class _tClass = Class.forName(_str);
        Result<JsonObject> _rules = new Result();
        _rules.data = new JsonObject();
        List<Field> _fields = ClassUtil.getFields(_tClass);
        Iterator var6 = _fields.iterator();

        while (var6.hasNext()) {
            Field i = (Field) var6.next();
            JsonObject _json = new JsonObject();
            JsonObject _rule = new JsonObject();
            boolean _required = false;
            Id _id = (Id) i.getAnnotation(Id.class);
            if (_id != null) {
                _required = true;
                _rule.addProperty("required", true);
            }

            Column _column = (Column) i.getAnnotation(Column.class);
            if (_column != null) {
                if (_id == null && !_column.nullable()) {
                    _required = true;
                    _rule.addProperty("required", true);
                }

                int _length = _column.length();
                if ("text".equalsIgnoreCase(_column.columnDefinition())) {
                    _length = 65535;
                } else if ("mediumtext".equalsIgnoreCase(_column.columnDefinition())) {
                    _length = 16777215;
                } else if ("longtext".equalsIgnoreCase(_column.columnDefinition())) {
                    _length = Integer.MAX_VALUE;
                }

                _rule.addProperty("maxlength", _length);
            }

            if (i.getType() != Short.class && i.getType() != Integer.class && i.getType() != Long.class) {
                JsonObject _options;
                if (i.getType() == Boolean.class) {
                    _options = new JsonObject();
                    _options.addProperty("true", "是");
                    _options.addProperty("false", "否");
                    if (!_required) {
                        _options.addProperty("", "无");
                    }

                    _json.add("options", _options);
                } else if (i.getType() != Float.class && i.getType() != Double.class && i.getType() != BigDecimal.class) {
                    if (i.getType() == Date.class) {
                        _rule.addProperty("date", true);
                    } else if (i.getType().isEnum()) {
                        _options = new JsonObject();
                        Enum[] _types = (Enum[]) ((Enum[]) i.getType().getEnumConstants());
                        Arrays.stream(_types).forEach((j) -> {
                            try {
                                Field displayNameField = j.getClass().getDeclaredField("displayName");
                                displayNameField.setAccessible(true);
                                String displayName = displayNameField.get(j).toString();
                                _options.addProperty(j.name(), displayName);
                            } catch (NoSuchFieldException | IllegalAccessException e) {
                                _options.addProperty(j.name(), j.name());
                            }
                        });
                        if (!_required) {
                            _options.addProperty("", "无");
                        }

                        _json.add("options", _options);
                    }
                } else {
                    _rule.addProperty("number", true);
                }
            } else {
                _rule.addProperty("digits", true);
            }

            _json.add("rule", _rule);
            _rules.data.add(i.getName(), _json);
        }

        return _rules;
    }
}