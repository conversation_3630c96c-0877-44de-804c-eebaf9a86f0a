package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.lang.SortField;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.Bulletin;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sys.BulletinRepository;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.io.FileUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "sys/bulletin")
public class BulletinController {

    final BulletinRepository bulletinRepository;
    final AbstractMediaRepository mediaRepository;
    final LoginUtil loginUtil;

    public BulletinController(
            BulletinRepository bulletinRepository,
            AbstractMediaRepository mediaRepository,
            LoginUtil loginUtil
    ) {
        this.bulletinRepository = bulletinRepository;
        this.mediaRepository = mediaRepository;
        this.loginUtil = loginUtil;
    }

    @GetMapping
    @RequiresPermissions(value = "sys:bulletin:index")
    public String forms() {
        return "sys/bulletins";
    }

    @PostMapping(value = "/get")
    @ResponseBody
    public Result<Bulletin> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return bulletinRepository.get(_id);
    }

    @PostMapping(value = "/fuzzy")
    @ResponseBody
    public Result<PagingItems<Bulletin>> fuzzy(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<SortField> _sortBy = (List<SortField>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : new TypeToken<List<SortField>>() {
                }.getType())
                .orElse(null);
        String _title = Optional.ofNullable(data.get("title"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);
        Boolean _isEnabled = Optional.ofNullable(data.get("isEnabled"))
                .map(i -> i.isJsonNull() ? null : i.getAsBoolean())
                .orElse(null);

        Result<PagingItems<Bulletin>> _page = new Result<>();
        _page.data = bulletinRepository.fuzzy(_count, _index, _sortBy, _title, _isEnabled);
        bulletinRepository.parseActors(_page.data.items);
        return _page;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "sys:bulletin:save")
    public Result<String> save(@RequestPart(value = "files", required = false) MultipartFile[] files, @Validated @RequestPart Bulletin item) throws IOException {
        List<File> _files = new ArrayList<>();
        if (files != null) {
            for (MultipartFile i : files) {
                File _file = new File(FileUtils.getTempDirectory() + File.separator + i.getOriginalFilename());
                FileUtils.copyInputStreamToFile(i.getInputStream(), _file);

                _files.add(_file);
            }
        }

        List<String> _attachmentIds = new ArrayList<>();
        for (File i : _files) {
            Result<Media> _media = mediaRepository.add("bulletin", i, true, loginUtil.getUserId());
            if (_media.isOK()) {
                _attachmentIds.add(_media.data.getId());
            }
        }

        if (CollectionUtils.isEmpty(item.getAttachmentIds())) {
            item.setAttachmentIds(_attachmentIds);
        } else {
            item.getAttachmentIds().addAll(_attachmentIds);
        }

        return bulletinRepository.save(item, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "sys:bulletin:remove")
    public Result<Void> remove(@RequestBody JsonObject data) {
        String _id = Optional.ofNullable(data.get("id"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        return bulletinRepository.remove(_id, loginUtil.getUserId());
    }

}
