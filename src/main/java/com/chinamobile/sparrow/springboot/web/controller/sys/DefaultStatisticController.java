package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.healthcode.repository.citizen.ProfileRepository;
import com.chinamobile.healthcode.repository.project.RecordRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PagingItems;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.StandaloneStatisticService;
import com.chinamobile.sparrow.domain.repository.sys.StatisticService;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "sys/stat")
public class DefaultStatisticController extends StatisticPageController {

    protected final DefaultRoleRepository roleRepository;
    protected final LoginUtil loginUtil;

    public DefaultStatisticController(
            UserRepository userRepository,
            StatisticService statisticService,
            DefaultRoleRepository roleRepository,
            LoginUtil loginUtil
    ) {
        super(userRepository, statisticService);
        this.roleRepository = roleRepository;
        this.loginUtil = loginUtil;
    }

    @Override
    public Result<OperationIndicator> operation() {
        User _user = loginUtil.getUser();

        // 管理员查看全局数据
        if (roleRepository.isUserInRoleCached(_user.getId(), ProfileRepository.ADMIN_ROLE) || roleRepository.isUserInRoleCached(_user.getId(), RecordRepository.ADMIN_ROLE)) {
            return super.operation();
        }

        Result<OperationIndicator> _num = new Result();
        _num.data = new OperationIndicator();
        _num.data.setRu(((DefaultUserRepository) userRepository).registeredUserNum(loginUtil.getUser().getDeptId()));
        _num.data.setCu(((DefaultUserRepository) userRepository).onlineUserNum(loginUtil.getUser().getDeptId()));
        _num.data.setDar(statisticService.dar(null, null, null).stream()
                .map(i -> i.getTwo())
                .findFirst().orElse(BigDecimal.ZERO));
        _num.data.setUrr(statisticService.urr(null, null, 5));
        return _num;
    }

    @PostMapping(value = "/register")
    @ResponseBody
    public Result<List<Pair<Department, Integer>>> register(@RequestBody JsonObject data) throws InstantiationException, IllegalAccessException {
        String _rootDeptId = Optional.ofNullable(data.get("rootDeptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        Result<List<Pair<Department, Integer>>> _counts = new Result();
        _counts.data = ((DefaultUserRepository) userRepository).registeredStatistic(_rootDeptId);
        return _counts;
    }

    @PostMapping(value = "/register/users")
    @ResponseBody
    public Result<List<DefaultUser>> registerUsers(@RequestBody JsonObject data) {
        String _deptId = Optional.ofNullable(data.get("deptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        Result<List<DefaultUser>> _users = new Result();
        _users.data = ((DefaultUserRepository) userRepository).registeredUsers(_deptId);
        return _users;
    }

    @PostMapping(value = "/online")
    @ResponseBody
    public Result<List<Pair<Department, Integer>>> online(@RequestBody JsonObject data) throws InstantiationException, IllegalAccessException {
        String _rootDeptId = Optional.ofNullable(data.get("rootDeptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        Result<List<Pair<Department, Integer>>> _count = new Result();
        _count.data = ((DefaultUserRepository) userRepository).onlineStatistic(_rootDeptId);
        return _count;
    }

    @PostMapping(value = "/online/users")
    @ResponseBody
    public Result<List<DefaultUser>> onlineUsers(@RequestBody JsonObject data) {
        String _deptId = Optional.ofNullable(data.get("deptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        Result<List<DefaultUser>> _users = new Result();
        _users.data = ((DefaultUserRepository) userRepository).onlineUsers(_deptId);
        return _users;
    }

    @PostMapping("/mpu")
    @ResponseBody
    public Result<Long> mpu() {
        Result<Long> _result = new Result<>();
        _result.data = ((StandaloneStatisticService) statisticService).mpu(loginUtil.getUser());
        return _result;
    }

    @PostMapping("/mpu/details")
    @ResponseBody
    public Result<PagingItems<Tuple3<String, String, String>>> mpuDetails(@RequestBody JsonObject data) {
        Result<PagingItems<Tuple3<String, String, String>>> _result = new Result<>();
        int _count = Optional.ofNullable(data.get("count"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(10);
        int _index = Optional.ofNullable(data.get("index"))
                .filter(j -> !j.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(0);
        Date _startDate = Optional.ofNullable(data.get("startDate"))
                .filter(j -> !j.isJsonNull())
                .map(i -> DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(null);
        Date _endDate = Optional.ofNullable(data.get("endDate"))
                .filter(j -> !j.isJsonNull())
                .map(i -> DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(null);
        String _regionId = Optional.ofNullable(data.get("regionId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);
        _result.data = ((StandaloneStatisticService) statisticService).mpuDetails(_count, _index, _regionId, _startDate, _endDate, loginUtil.getUser());
        return _result;
    }

    @GetMapping("/inactive-accounts")
    @RequiresPermissions("sys:stat:inactive-accounts")
    public String inactiveAccounts() {
        return "sys/stat/inactive-accounts";
    }

    @PostMapping("/mpu/details/export/base64")
    @ResponseBody
    public Result<String> mpuDetailsExport(@RequestBody JsonObject data) throws IOException {
        Result<String> _result = new Result<>();
        Date _startDate = Optional.ofNullable(data.get("startDate"))
                .filter(j -> !j.isJsonNull())
                .map(i -> DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(null);
        Date _endDate = Optional.ofNullable(data.get("endDate"))
                .filter(j -> !j.isJsonNull())
                .map(i -> DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(null);
        String _regionId = Optional.ofNullable(data.get("regionId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);
        _result.data = ((StandaloneStatisticService) statisticService).mpuDetailsExport(_regionId, _startDate, _endDate, loginUtil.getUser());
        return _result;
    }
}
