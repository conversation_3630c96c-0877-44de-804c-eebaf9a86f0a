package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
public class DefaultProfileController extends ProfilePageController {

    public DefaultProfileController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil
    ) {
        super(userRepository, loginUtil);
    }

    @Override
    public Result<String> save(@RequestBody User item) throws InstantiationException, IllegalAccessException {
        throw new UnsupportedOperationException();
    }

    @PostMapping(value = "/save/default")
    @ResponseBody
    public Result<String> save(@RequestBody @Validated DefaultUser user) throws InstantiationException, IllegalAccessException {
        User _user = loginUtil.getUser();

        user.setId(_user.getId());
        user.setAccount(_user.getAccount());
        return userRepository.save(user, loginUtil.getUserId());
    }

}