package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.healthcode.repository.FormTraceRepository;
import com.chinamobile.healthcode.repository.TaskRepository;
import com.chinamobile.healthcode.repository.citizen.ProfileValidationRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.Menu;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.MenuRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.google.gson.JsonObject;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Controller
public class DefaultSettingController extends SettingPageController {

    final TaskRepository taskRepository;
    final ProfileValidationRepository profileValidationRepository;
    final FormTraceRepository formTraceRepository;

    public DefaultSettingController(
            UserRepository userRepository,
            DepartmentRepository departmentRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            MenuRepository menuRepository,
            LoginUtil loginUtil,
            TaskRepository taskRepository,
            ProfileValidationRepository profileValidationRepository,
            FormTraceRepository formTraceRepository
    ) {
        super(userRepository, departmentRepository, roleRepository, permissionRepository, menuRepository, loginUtil);
        this.taskRepository = taskRepository;
        this.profileValidationRepository = profileValidationRepository;
        this.formTraceRepository = formTraceRepository;
    }

    @Override
    public Result<Header> header(@Value(value = "${spring.application.name}") String app, @RequestBody JsonObject data) {
        Result<Header> _header = new Result();
        _header.data = new Header();
        _header.data.setApp(app);
        if (SecurityUtils.getSubject().getPrincipal() != null) {
            _header.data.setUser(this.loginUtil.getUser());
        }

        List<Menu> _menus = this.menuRepository.query();
        if (data.get("root").getAsBoolean()) {
            _menus = _menus.stream()
                    .filter(i -> i.getLevel() == 1)
                    .collect(Collectors.toList());
        }

        _header.data.setMenus(_menus);
        return _header;
    }

    @Override
    public Result<List<Badge>> badge() {
        Result<List<Badge>> _badge = new Result();
        _badge.data = new ArrayList<>();
        _badge.data.add(new Badge("/", taskRepository.countTodo(loginUtil.getUser())));
        _badge.data.add(new Badge("/citizen/profile", profileValidationRepository.count(loginUtil.getUser())));
        return _badge;
    }

    @PostMapping(value = "/app/ws")
    @ResponseBody
    public Result<String> ws(@Value(value = "${ws.base-url}") String baseUrl) {
        Result<String> _url = new Result<>();
        _url.data = baseUrl;
        return _url;
    }

}