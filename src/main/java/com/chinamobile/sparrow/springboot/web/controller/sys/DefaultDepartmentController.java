package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.healthcode.repository.citizen.ProfileRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.DefaultDepartment;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultDepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = {"sys/dept", "sec/dept"})
public class
DefaultDepartmentController extends DepartmentController {

    final DefaultRoleRepository roleRepository;

    public DefaultDepartmentController(
            DefaultDepartmentRepository departmentRepository,
            DefaultRoleRepository roleRepository,
            LoginUtil loginUtil
    ) {
        super(departmentRepository, loginUtil);
        this.roleRepository = roleRepository;
    }

    @Override
    public Result<String> save(@RequestBody @Validated Department item) {
        throw new UnsupportedOperationException();
    }

    @PostMapping(value = {"/organization/tree", "/tree"})
    @ResponseBody
    public Result<List<OrganizationTreeNode<DefaultDepartment>>> tree() {
        List<DefaultDepartment> _items = departmentRepository.subordinates(null, null, 3);

        int _root = _items.stream()
                .min(Comparator.comparing(Department::getLevel))
                .map(i -> i.getLevel()).orElse(1);
        int _leaf = _items.stream()
                .max(Comparator.comparing(Department::getLevel))
                .map(i -> i.getLevel()).orElse(_root);

        List<OrganizationTreeNode<DefaultDepartment>> _subordinates = _items.stream()
                .filter(j -> _leaf == j.getLevel())
                .map(j -> new OrganizationTreeNode<>(j))
                .collect(Collectors.toList());
        List<OrganizationTreeNode<DefaultDepartment>> _superiors;
        while (true) {
            Map<String, List<OrganizationTreeNode<DefaultDepartment>>> _groups = _subordinates.stream()
                    .filter(j -> j.getDept().getSuperiorId() != null)
                    .collect(Collectors.groupingBy(j -> j.getDept().getSuperiorId()));
            if (_groups.isEmpty()) {
                break;
            }

            _superiors = new ArrayList<>();
            for (String j : _groups.keySet()) {
                OrganizationTreeNode<DefaultDepartment> _superior = _items.stream()
                        .filter(k -> Objects.equals(j, k.getId())).map(k -> new OrganizationTreeNode<>(k))
                        .findFirst().orElse(null);
                if (_superior != null) {
                    _superior.setSubordinates(_groups.get(j));
                }

                _superiors.add(_superior);
            }

            // 读取父级节点的兄弟叶子节点
            int _lv = _superiors.stream()
                    .map(i -> i.getDept().getLevel())
                    .findFirst().orElse(null);
            List<String> _deptIds = _superiors.stream()
                    .map(i -> i.getDept().getId())
                    .collect(Collectors.toList());
            List<OrganizationTreeNode<DefaultDepartment>> _siblings = _items.stream()
                    .filter(i -> CollectionUtils.isEmpty(_deptIds) || !_deptIds.contains(i.getId()))
                    .filter(i -> _lv == i.getLevel())
                    .map(i -> new OrganizationTreeNode<>(i))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(_siblings)) {
                _superiors.addAll(_siblings);
            }

            _subordinates = _superiors;
        }

        List<String> _names = Arrays.asList("金平区", "龙湖区", "潮阳区", "潮南区", "澄海区", "濠江区", "南澳县");
        _superiors = _subordinates.stream()
                .filter(i -> _names.contains(i.getDept().getName()))
                .collect(Collectors.toList());

        Result<List<OrganizationTreeNode<DefaultDepartment>>> _nodes = new Result<>();
        _nodes.data = _superiors;
        return _nodes;
    }

    @PostMapping(value = "/organization/restricted")
    @ResponseBody
    public Result<DepartmentRepository.Organization<DefaultDepartment>> restrictedOrganization(@RequestBody JsonObject data) throws InstantiationException, IllegalAccessException {
        String _id = Optional.ofNullable(data.get("id"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _roleName = Optional.ofNullable(data.get("roleName"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        if (StringUtils.isEmpty(_id)) {
            if (StringUtils.hasLength(_roleName)) {
                if (!roleRepository.isUserInRole(loginUtil.getUserId(), _roleName)) {
                    _id = loginUtil.getUser().getDeptId();
                }
            } else {
                if (!roleRepository.isUserInRole(loginUtil.getUserId(), ProfileRepository.ADMIN_ROLE)) {
                    _id = loginUtil.getUser().getDeptId();
                }
            }
        }

        return departmentRepository.getAsOrganization(_id);
    }

    @PostMapping(value = "/save/default")
    @ResponseBody
    @RequiresPermissions(value = "sys:dept:save")
    public Result<String> saveDefault(@RequestBody @Validated DefaultDepartment item) throws InstantiationException, IllegalAccessException {
        return departmentRepository.save(item, this.loginUtil.getUserId());
    }

    @PostMapping(value = "/get-by-full-name")
    @ResponseBody
    public Result<DefaultDepartment> get(@RequestBody JsonObject data) {
        String _fullName = data.get("fullName").getAsString();

        return ((DefaultDepartmentRepository)departmentRepository).getByFullName(_fullName);
    }

    @PostMapping(value = "/query")
    @ResponseBody
    public Result<List<DefaultDepartment>> query(@RequestBody JsonObject data) {
        List<String> _ids = ConverterUtil.json2Object(data.get("ids").toString(), new TypeToken<List<String>>() {
        }.getType());

        Result<List<DefaultDepartment>> _items = new Result<>();
        _items.data = departmentRepository.query(_ids, null, null, null, null);
        return _items;
    }

    public static class OrganizationTreeNode<T extends Department> {

        T dept;
        List<OrganizationTreeNode<T>> subordinates;

        public OrganizationTreeNode(T dept) {
            this.dept = dept;
        }

        public T getDept() {
            return dept;
        }

        public void setDept(T dept) {
            this.dept = dept;
        }

        public List<OrganizationTreeNode<T>> getSubordinates() {
            return subordinates;
        }

        public void setSubordinates(List<OrganizationTreeNode<T>> subordinates) {
            this.subordinates = subordinates;
        }

    }

}