package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.healthcode.repository.truck.TicketRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Collections;

@Controller
@RequestMapping(value = "sys/user")
public class DefaultUserController extends UserController {

    final DepartmentRepository<Department> departmentRepository;
    final DefaultRoleRepository roleRepository;

    public DefaultUserController(
            DefaultUserRepository userRepository,
            DepartmentRepository<Department> departmentRepository,
            DefaultRoleRepository roleRepository,
            LoginUtil loginUtil
    ) {
        super(userRepository, loginUtil);
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
    }

    @Override
    public Result<String> save(@RequestBody User item) throws InstantiationException, IllegalAccessException {
        throw new UnsupportedOperationException();
    }

    @PostMapping(value = "/save/default")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:save")
    public Result<String> save(@RequestBody DefaultUser item) throws InstantiationException, IllegalAccessException {
        return userRepository.save(item, this.loginUtil.getUserId());
    }

    /* @PostMapping(value = "/init/admin/migrant")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:init:admin")
    public Result<Pair<Integer, Integer>> initAdmin() {
        return ((DefaultUserRepository) userRepository).initAdmin(loginUtil.getUserId());
    } */

    @PostMapping(value = "/register/zone-staff")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:register")
    @Transactional(transactionManager = "mainTransactionManager")
    public Result register(@RequestBody JsonObject data) throws InstantiationException, IllegalAccessException {
        Result _success = new Result();

        RegisterDTO _item = ConverterUtil.json2Object(data.get("item").toString(), RegisterDTO.class);

        User _user = new User();
        _user.setAccount(_item.getMp());
        _user.setName(_item.getName());
        _user.setMp(_item.getMp());
        _user.setIsMale(true);

        // 设置部门id
        Department _zone = departmentRepository.query(null, Collections.singletonList("产业园区"), null, true, null).stream()
                .filter(i -> i.getLevel() == 1)
                .findFirst().orElse(null);
        if (_zone == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND);
            return _success;
        }
        _user.setDeptId(_zone.getId());

        Result<Department> _department = departmentRepository.get(loginUtil.getUser().getDeptId(), true);

        // 设置商户上级部门id
        String _id = _department.isOK() ? _department.data.getId() : null;
        _user.setDeptId(_id);

        // 设置商户名称
        String _fullName = _department.isOK() ? _department.data.getFullName() : "";
        _user.setAddress(_fullName + "/" + _item.getCompanyName());

        Result<String> _userId = userRepository.save(_user, loginUtil.getUserId());
        if (!_userId.isOK()) {
            return _success.pack(_userId);
        }

        // 授权为货主
        Result<Role> _role = roleRepository.getByName(TicketRepository.ZONE_DIRECTOR_ROLE);
        if (_role.isOK()) {
            roleRepository.addUser(_role.data.getId(), _userId.data, loginUtil.getUserId());
        }

        return _success;
    }

    class RegisterDTO {

        String name;
        String mp;
        String companyName;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getMp() {
            return mp;
        }

        public void setMp(String mp) {
            this.mp = mp;
        }

        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }

    }

}