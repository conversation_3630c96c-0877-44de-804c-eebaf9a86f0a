package com.chinamobile.sparrow.springboot.web.controller.sec;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 7/8/2023 18:10
 */
@Controller
@RequestMapping({"sec/role"})
public class DefaultRoleController extends RolePageController {

    public DefaultRoleController(
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            LoginUtil loginUtil
    ) {
        super(roleRepository, permissionRepository, loginUtil);
    }

    @PostMapping({"/me"})
    @ResponseBody
    public Result<List<Role>> me() {
        Result<List<Role>> _result = new Result();
        _result.data = roleRepository.getUserRoles(loginUtil.getUserId());
        return _result;
    }
}
