package com.chinamobile.sparrow.springboot.web.controller.sec;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.chinamobile.healthcode.repository.truck.TicketRepository;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.NotLog;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.UsernamePasswordAuthorizingRealm;
import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.WxMaAuthorizingRealm;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.WxMaToken;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.DefaultRoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.service.cmpassport.Facade;
import com.chinamobile.sparrow.domain.service.wx.cp.AccessFacade;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import com.wf.captcha.SpecCaptcha;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Controller
@RequestMapping(value = "sec")
@ErrorCode(module = "001")
public class DefaultLoginController extends LoginPageController {

    public static final String CAPTCHA_SESSION_ATTRIBUTE = LoginController.CAPTCHA_SESSION_ATTRIBUTE;

    final int jwtExpiresIn;
    final String sha256Secret;
    final boolean captchaEnabled;
    final String captchaRedisKeyTemplate;
    final int captchaRedisExpiresIn;
    final DepartmentRepository<Department> departmentRepository;
    final DefaultRoleRepository roleRepository;
    final RedisTemplate<String, Object> redisTemplate;

    public DefaultLoginController(
            int jwtExpiresIn,
            String rsaPrivateKey,
            String sha256Secret,
            boolean captchaEnabled,
            String captchaRedisKeyTemplate,
            int captchaRedisExpiresIn,
            UserRepository userRepository,
            VerificationCodeRepository verificationCodeRepository,
            Facade cmpassportFacade,
            AccessFacade wxAccessFacade,
            AccessFacade yzyAccessFacade,
            LoginUtil loginUtil,
            DepartmentRepository<Department> departmentRepository,
            DefaultRoleRepository roleRepository,
            RedisTemplate<String, Object> redisTemplate
    ) {
        super(rsaPrivateKey, userRepository, verificationCodeRepository, cmpassportFacade, wxAccessFacade, yzyAccessFacade, loginUtil);
        this.jwtExpiresIn = jwtExpiresIn;
        this.sha256Secret = sha256Secret;
        this.captchaEnabled = captchaEnabled;
        this.captchaRedisKeyTemplate = captchaRedisKeyTemplate;
        this.captchaRedisExpiresIn = captchaRedisExpiresIn;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.redisTemplate = redisTemplate;
    }

    @PostMapping(value = "/login/crypto/rsa/public-key")
    @ResponseBody
    public Result<String> rsaPublicKey(@Value("${sec.rsa.default.public-key}") String publicKey) {
        Result<String> _key = new Result<>();
        _key.data = publicKey;
        return _key;
    }

    @PostMapping(value = "/login/mini-app")
    @ResponseBody
    public Result<String> miniApp(HttpServletRequest request, @RequestBody JsonObject data) throws UnsupportedEncodingException {
        Result<String> _jwt = new Result<>();

        String _nickname = data.get("nickname").getAsString();

        /* String _code = data.get("code").getAsString();
        String _encryptedData = data.get("encryptedData").getAsString();
        String _iv = data.get("iv").getAsString();
        String _client = Optional.ofNullable(data.get("client"))
                .map(i -> i.isJsonNull() ? WxMaAuthorizingRealm.CLIENT_STFY : i.getAsString())
                .orElse(WxMaAuthorizingRealm.CLIENT_STFY);
        String _credentials = ConverterUtil.toJson(new String[]{_code, _encryptedData, _iv, _client}); */

        String _sessionCode = data.get("sessionCode").getAsString();
        String _phoneNumberCode = data.get("phoneNumberCode").getAsString();

        String _client = Optional.ofNullable(data.get("client"))
                .map(i -> i.isJsonNull() ? WxMaAuthorizingRealm.CLIENT_STFY : i.getAsString())
                .orElse(WxMaAuthorizingRealm.CLIENT_STFY);

        String _credentials = ConverterUtil.toJson(new String[]{_sessionCode, _phoneNumberCode, _client});
        WxMaToken _token = new WxMaToken(_nickname, _credentials, false, request.getRemoteHost());

        loginUtil.login(request, _token, null);

        Map<String, Object> _header = new HashMap<String, Object>() {{
            put("typ", "JWT");
            put("alg", "HS256");
        }};

        User _user = loginUtil.getUser();

        Result<Department> _department = departmentRepository.get(_user.getDeptId(), true);
        String _deptCode = _department.isOK() ? _department.data.getCode() : null;
        String _deptFullName = _department.isOK() ? _department.data.getFullName() : null;

        _jwt.data = JWT.create().withHeader(_header)
                .withClaim("id", _user.getId())
                .withClaim("name", _user.getName())
                .withClaim("mp", _user.getMp())
                .withClaim("deptCode", _deptCode)
                .withClaim("deptFullName", _deptFullName)
                .withExpiresAt(DateUtil.addSeconds(new Date(), jwtExpiresIn))
                .sign(Algorithm.HMAC256(sha256Secret));
        return _jwt;
    }

    /**
     * 获取验证码
     *
     * @param data
     * @return
     * @throws IOException
     * @throws FontFormatException
     */
    @PostMapping(value = "/login/client/captcha")
    @ResponseBody
    @NotLog
    public Result<JsonObject> clientCaptcha(@RequestBody JsonObject data) throws IOException, FontFormatException {
        int _width = data.get("width").getAsInt();
        int _height = data.get("height").getAsInt();
        int _length = Optional.ofNullable(data.get("length"))
                .map(i -> i.isJsonNull() ? 4 : i.getAsInt()).orElse(4);
        SpecCaptcha _captcha = new SpecCaptcha(_width, _height, _length);
        String[] _fonts = new String[]{"epilog.ttf"};
        _captcha.setFont(Font.createFont(0, this.getClass().getResourceAsStream("/" + _fonts[(new Random()).nextInt(_fonts.length)])).deriveFont(1, 20.0F));
        _captcha.setCharType(1);

        // 缓存验证码
        String _token = UUID.randomUUID().toString();
        String _key = String.format(captchaRedisKeyTemplate, _token);
        redisTemplate.opsForValue().set(_key, _captcha.text(), captchaRedisExpiresIn, TimeUnit.MINUTES);

        Result<JsonObject> _json = new Result();
        _json.data = new JsonObject();
        _json.data.addProperty("token", _token);
        _json.data.addProperty("image", _captcha.toBase64());
        return _json;
    }

    @PostMapping(value = "/login/jinfengma/password")
    @ResponseBody
    public Result<String> clientLoginByPassword(HttpServletRequest request, @RequestBody JsonObject data) throws Exception {
        Result<String> _jwt = new Result<>();

        loginUtil.login(request, data, UsernamePasswordAuthorizingRealm.REALM_TYPE, captchaEnabled, false);

        Map<String, Object> _header = new HashMap<String, Object>() {{
            put("typ", "JWT");
            put("alg", "HS256");
        }};

        Result<Department> _department = departmentRepository.get(loginUtil.getUser().getDeptId(), true);
        String _subdistrictCode = _department.isOK() ? _department.data.getCode() : null;
        String _subdistrictFullName = _department.isOK() ? _department.data.getFullName() : null;

        String _userId = loginUtil.getUserId();
        boolean _supportRegister = roleRepository.isUserInRoleCached(_userId, TicketRepository.DISTRICT_STAFF_ROLE) || roleRepository.isUserInRoleCached(_userId, TicketRepository.SUBDISTRICT_STAFF_ROLE) || roleRepository.isUserInRoleCached(_userId, TicketRepository.COMMUNITY_STAFF_ROLE);

        User _user = loginUtil.getUser();

        _jwt.data = JWT.create().withHeader(_header)
                .withClaim("id", _user.getId())
                .withClaim("name", _user.getName())
                .withClaim("mp", _user.getMp())
                .withClaim("deptCode", _subdistrictCode)
                .withClaim("deptFullName", _subdistrictFullName)
                .withClaim("supportRegister", _supportRegister)
                .withExpiresAt(DateUtil.addSeconds(new Date(), jwtExpiresIn))
                .sign(Algorithm.HMAC256(sha256Secret));
        return _jwt;
    }

    @Override
    public Result<String> loginByPassword(HttpServletRequest request, @RequestBody JsonObject data) throws Exception {
        return loginUtil.login(request, data, UsernamePasswordAuthorizingRealm.REALM_TYPE, captchaEnabled, false);
    }

    Result validateCaptcha(JsonObject data) {
        Result _success = new Result();

        JsonObject _captcha = data.get("captcha").getAsJsonObject();
        String _token = _captcha.get("token").getAsString();
        String _text = _captcha.get("text").getAsString();

        String _key = String.format(captchaRedisKeyTemplate, _token);
        Object _cached = redisTemplate.opsForValue().get(_key);
        if (_cached == null || !_cached.toString().equalsIgnoreCase(_text)) {
            _success.setCode(Result.ENUM_ERROR.P, 1);
        }

        return _success;
    }

}